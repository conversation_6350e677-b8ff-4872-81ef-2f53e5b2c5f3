@echo off
echo ========================================
echo Google Sign-In Troubleshooting Guide
echo ========================================
echo.

echo 🔍 CURRENT ISSUE: ApiException: 10 persists
echo.

echo 📋 REQUIRED ACTIONS TO FIX:
echo.

echo 1. VERIFY FIREBASE CONSOLE HAS BOTH SHA-1 FINGERPRINTS:
echo    🌐 Go to: https://console.firebase.google.com/project/drive-on-b2af8/settings/general/
echo    📱 Find Android app: com.driver.drive_on
echo    🔑 Ensure BOTH fingerprints are added:
echo.
echo    DEBUG SHA-1:
echo    9A:8E:90:68:D4:97:C6:E2:5A:EF:B0:51:75:BC:CB:B5:CC:BD:55:DC
echo.
echo    PRODUCTION SHA-1:
echo    85:C3:07:8A:88:35:3D:1D:15:88:76:93:84:F8:D7:17:41:A9:4B:A3
echo.

echo 2. CLEAR APP DATA ON DEVICE:
echo    📱 On your Samsung device:
echo    - Go to Settings ^> Apps ^> Drive-On
echo    - Tap "Storage"
echo    - Tap "Clear Data" and "Clear Cache"
echo.

echo 3. CLEAR GOOGLE PLAY SERVICES CACHE:
echo    📱 On your Samsung device:
echo    - Go to Settings ^> Apps ^> Google Play Services
echo    - Tap "Storage"
echo    - Tap "Clear Cache" ^(NOT Clear Data^)
echo.

echo 4. RESTART GOOGLE SERVICES:
echo    📱 On your Samsung device:
echo    - Go to Settings ^> Apps ^> Google Play Services
echo    - Tap "Force Stop"
echo    - Wait 10 seconds
echo    - Open any Google app to restart services
echo.

echo 5. WAIT FOR FIREBASE PROPAGATION:
echo    ⏰ Firebase configuration changes can take 5-10 minutes to propagate
echo    🔄 If you just added the SHA-1, wait 10 minutes before testing
echo.

echo 6. ALTERNATIVE: BUILD RELEASE APK FOR TESTING:
echo    🔨 flutter build apk --release
echo    📱 flutter install --release -d RFCT31AY38E
echo    🧪 Test Google Sign-In with production keystore
echo.

echo 🚨 MOST LIKELY CAUSE:
echo Firebase Console needs BOTH debug and production SHA-1 fingerprints
echo configured simultaneously for the same app.
echo.

echo 📞 VERIFICATION STEPS:
echo 1. Check Firebase Console has both SHA-1 fingerprints
echo 2. Clear app data and Google Play Services cache
echo 3. Wait 10 minutes for propagation
echo 4. Test Google Sign-In again
echo.

echo Press any key to open Firebase Console...
pause >nul

start https://console.firebase.google.com/project/drive-on-b2af8/settings/general/

echo.
echo 🔑 COPY THESE SHA-1 FINGERPRINTS TO FIREBASE:
echo.
echo DEBUG:
echo 9A:8E:90:68:D4:97:C6:E2:5A:EF:B0:51:75:BC:CB:B5:CC:BD:55:DC
echo.
echo PRODUCTION:
echo 85:C3:07:8A:88:35:3D:1D:15:88:76:93:84:F8:D7:17:41:A9:4B:A3
echo.

echo Press any key to exit...
pause >nul
