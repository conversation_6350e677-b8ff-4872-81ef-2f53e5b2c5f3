'use client'

import { motion } from 'framer-motion'
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Cell,
  <PERSON><PERSON><PERSON>,
  Bar,
  Legend
} from 'recharts'

interface AnalyticsChartProps {
  type: 'line' | 'pie' | 'bar'
  data?: any[]
  height?: number
}

// Sample data for different chart types
const lineData = [
  { month: 'Jan', users: 400, drivers: 240 },
  { month: 'Feb', users: 300, drivers: 139 },
  { month: 'Mar', users: 500, drivers: 980 },
  { month: 'Apr', users: 780, drivers: 390 },
  { month: 'May', users: 890, drivers: 480 },
  { month: 'Jun', users: 1234, drivers: 567 },
]

const pieData = [
  { name: 'Verified', value: 567, color: '#22C55E' },
  { name: 'Pending', value: 123, color: '#F59E0B' },
  { name: 'Rejected', value: 45, color: '#EF4444' },
  { name: 'Under Review', value: 89, color: '#3B82F6' },
]

const barData = [
  { category: 'InDrive', jobs: 45 },
  { category: 'Household', jobs: 32 },
  { category: 'Company', jobs: 28 },
  { category: 'Part-time', jobs: 18 },
]

const COLORS = ['#22C55E', '#F59E0B', '#EF4444', '#3B82F6', '#8B5CF6']

export function AnalyticsChart({ type, data, height = 300 }: AnalyticsChartProps) {
  const chartVariants = {
    hidden: { opacity: 0, scale: 0.8 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.6,
        ease: 'easeOut'
      }
    }
  }

  const renderLineChart = () => (
    <ResponsiveContainer width="100%" height={height}>
      <LineChart data={data || lineData}>
        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
        <XAxis
          dataKey="month"
          stroke="#6b7280"
          fontSize={12}
        />
        <YAxis
          stroke="#6b7280"
          fontSize={12}
        />
        <Tooltip
          contentStyle={{
            backgroundColor: 'white',
            border: '1px solid #e5e7eb',
            borderRadius: '8px',
            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
          }}
        />
        <Legend
          verticalAlign="top"
          height={36}
          iconType="line"
        />
        <Line
          type="monotone"
          dataKey="users"
          name="Users"
          stroke="#F59E0B"
          strokeWidth={3}
          dot={{ fill: '#F59E0B', strokeWidth: 2, r: 4 }}
          activeDot={{ r: 6, stroke: '#F59E0B', strokeWidth: 2 }}
        />
        <Line
          type="monotone"
          dataKey="drivers"
          name="Drivers"
          stroke="#22C55E"
          strokeWidth={3}
          dot={{ fill: '#22C55E', strokeWidth: 2, r: 4 }}
          activeDot={{ r: 6, stroke: '#22C55E', strokeWidth: 2 }}
        />
      </LineChart>
    </ResponsiveContainer>
  )

  const renderPieChart = () => (
    <ResponsiveContainer width="100%" height={height}>
      <PieChart>
        <Pie
          data={data || pieData}
          cx="50%"
          cy="50%"
          innerRadius={60}
          outerRadius={100}
          paddingAngle={5}
          dataKey="value"
        >
          {(data || pieData).map((entry, index) => (
            <Cell
              key={`cell-${index}`}
              fill={entry.color || COLORS[index % COLORS.length]}
            />
          ))}
        </Pie>
        <Tooltip
          contentStyle={{
            backgroundColor: 'white',
            border: '1px solid #e5e7eb',
            borderRadius: '8px',
            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
          }}
        />
        <Legend
          verticalAlign="bottom"
          height={36}
          iconType="circle"
        />
      </PieChart>
    </ResponsiveContainer>
  )

  const renderBarChart = () => (
    <ResponsiveContainer width="100%" height={height}>
      <BarChart data={data || barData}>
        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
        <XAxis
          dataKey="category"
          stroke="#6b7280"
          fontSize={12}
        />
        <YAxis
          stroke="#6b7280"
          fontSize={12}
        />
        <Tooltip
          contentStyle={{
            backgroundColor: 'white',
            border: '1px solid #e5e7eb',
            borderRadius: '8px',
            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
          }}
        />
        <Bar
          dataKey="jobs"
          fill="#F59E0B"
          radius={[4, 4, 0, 0]}
        />
      </BarChart>
    </ResponsiveContainer>
  )

  const renderChart = () => {
    switch (type) {
      case 'line':
        return renderLineChart()
      case 'pie':
        return renderPieChart()
      case 'bar':
        return renderBarChart()
      default:
        return renderLineChart()
    }
  }

  return (
    <motion.div
      variants={chartVariants}
      initial="hidden"
      animate="visible"
      className="w-full"
    >
      {renderChart()}
    </motion.div>
  )
}
