import 'package:cloud_firestore/cloud_firestore.dart';

class Driver {
  final String id;
  final String name;
  final String mobile;
  final String city;
  final String maritalStatus;
  final String education;
  final int experience;
  final bool isVerified;
  final DateTime? createdAt;
  final Map<String, String> documents;
  final String? profilePictureUrl;
  final String? status; // verified, suspended, rejected
  final String? userEmail; // Email for uniqueness constraint

  Driver({
    required this.id,
    required this.name,
    required this.mobile,
    required this.city,
    required this.maritalStatus,
    required this.education,
    required this.experience,
    this.isVerified = false,
    this.createdAt,
    this.documents = const {},
    this.profilePictureUrl,
    this.status,
    this.userEmail,
  });

  // Create a Driver from Firestore document
  factory Driver.fromMap(Map<String, dynamic> map, String documentId) {
    final documents = Map<String, String>.from(map['documents'] ?? {});
    // Extract profile picture from documents (Passport Photo)
    final profilePictureUrl = documents['Passport Photo'] ?? map['profilePictureUrl'];

    return Driver(
      id: documentId,
      name: map['name'] ?? '',
      mobile: map['mobile'] ?? '',
      city: map['city'] ?? '',
      maritalStatus: map['maritalStatus'] ?? 'Single',
      education: map['education'] ?? '',
      experience: map['experience'] ?? 0,
      isVerified: map['isVerified'] ?? false,
      createdAt: (map['createdAt'] as Timestamp?)?.toDate(),
      documents: documents,
      profilePictureUrl: profilePictureUrl,
      status: map['status'] as String?,
      userEmail: map['userEmail'] as String?,
    );
  }

  // Convert Driver to a map for Firestore
  Map<String, dynamic> toMap() {
    final map = {
      'name': name,
      'mobile': mobile,
      'city': city,
      'maritalStatus': maritalStatus,
      'education': education,
      'experience': experience,
      'isVerified': isVerified,
      'createdAt': createdAt != null ? Timestamp.fromDate(createdAt!) : Timestamp.now(),
      'documents': documents,
      'profilePictureUrl': profilePictureUrl,
    };

    // Only include status if it's not null
    if (status != null) {
      map['status'] = status!;
    }

    // Only include userEmail if it's not null
    if (userEmail != null) {
      map['userEmail'] = userEmail!;
    }

    return map;
  }

  // Create a copy of this Driver with optional field updates
  Driver copyWith({
    String? id,
    String? name,
    String? mobile,
    String? city,
    String? maritalStatus,
    String? education,
    int? experience,
    bool? isVerified,
    DateTime? createdAt,
    Map<String, String>? documents,
    String? profilePictureUrl,
    String? status,
    String? userEmail,
  }) {
    return Driver(
      id: id ?? this.id,
      name: name ?? this.name,
      mobile: mobile ?? this.mobile,
      city: city ?? this.city,
      maritalStatus: maritalStatus ?? this.maritalStatus,
      education: education ?? this.education,
      experience: experience ?? this.experience,
      isVerified: isVerified ?? this.isVerified,
      createdAt: createdAt ?? this.createdAt,
      documents: documents ?? this.documents,
      profilePictureUrl: profilePictureUrl ?? this.profilePictureUrl,
      status: status ?? this.status,
      userEmail: userEmail ?? this.userEmail,
    );
  }
}