import { create } from 'zustand'
import { persist } from 'zustand/middleware'

interface AdminUser {
  uid: string
  email: string | null
  displayName: string | null
  photoURL: string | null
  role: 'admin'
  permissions: string[]
  lastLoginAt: Date | null
}

interface AuthState {
  user: AdminUser | null
  isLoading: boolean
  isAuthenticated: boolean
  error: string | null

  // Actions
  login: (email: string, password: string) => Promise<void>
  logout: () => Promise<void>
  setUser: (user: AdminUser | null) => void
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
  clearError: () => void
  initialize: () => Promise<void>
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      isLoading: false, // Start with loading false to prevent infinite loops
      isAuthenticated: false,
      error: null,

      login: async (email: string, password: string) => {
        set({ isLoading: true, error: null })

        try {
          // Validate inputs
          if (!email || !password) {
            throw new Error('Email and password are required')
          }

          if (!email.includes('@')) {
            throw new Error('Please enter a valid email address')
          }

          // Simple credential validation for admin panel
          const validCredentials = [
            { email: '<EMAIL>', password: 'admin123' },
            { email: '<EMAIL>', password: 'admin123' },
            { email: '<EMAIL>', password: 'admin123' }
          ]

          // Trim whitespace and normalize email to lowercase
          const normalizedEmail = email.trim().toLowerCase()
          const normalizedPassword = password.trim()

          const isValidCredential = validCredentials.some(cred => {
            const credEmailNormalized = cred.email.toLowerCase()
            const credPasswordNormalized = cred.password
            const emailMatch = credEmailNormalized === normalizedEmail
            const passwordMatch = credPasswordNormalized === normalizedPassword
            return emailMatch && passwordMatch
          })

          if (!isValidCredential) {
            throw new Error('Invalid email or password. Please check your credentials and try again.')
          }

          // Create mock user object
          const mockUser = {
            uid: 'admin-user-123',
            email: email,
            displayName: 'Drive-On Admin',
            photoURL: null
          }

          // Set user immediately for faster response
          const adminUser: AdminUser = {
            uid: mockUser.uid,
            email: mockUser.email,
            displayName: mockUser.displayName,
            photoURL: mockUser.photoURL,
            role: 'admin',
            permissions: [
              'all',
              'users.read',
              'users.write',
              'users.delete',
              'drivers.read',
              'drivers.write',
              'drivers.delete',
              'drivers.verify',
              'queries.read',
              'queries.write',
              'queries.delete',
              'analytics.read',
              'settings.read',
              'settings.write',
              'system.admin'
            ],
            lastLoginAt: new Date()
          }

          // Set session cookie with 8 hour expiration
          const expirationTime = new Date()
          expirationTime.setHours(expirationTime.getHours() + 8)
          document.cookie = `admin-token=valid-token; path=/; expires=${expirationTime.toUTCString()}`

          set({
            user: adminUser,
            isAuthenticated: true,
            isLoading: false,
            error: null
          })

        } catch (error: any) {
          console.error('Login error:', error)

          // Provide more specific error messages
          let errorMessage = 'Login failed. Please check your credentials.'

          if (error.code) {
            switch (error.code) {
              case 'auth/user-not-found':
                errorMessage = 'No admin account found with this email address.'
                break
              case 'auth/wrong-password':
                errorMessage = 'Incorrect password. Please try again.'
                break
              case 'auth/invalid-email':
                errorMessage = 'Invalid email address format.'
                break
              case 'auth/user-disabled':
                errorMessage = 'This admin account has been disabled.'
                break
              case 'auth/too-many-requests':
                errorMessage = 'Too many failed attempts. Please try again later.'
                break
              case 'auth/network-request-failed':
                errorMessage = 'Network error. Please check your connection and try again.'
                break
              default:
                errorMessage = error.message || 'Login failed. Please check your credentials.'
            }
          } else if (error.message) {
            errorMessage = error.message
          }

          set({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            error: errorMessage
          })
          throw error
        }
      },

      logout: async () => {
        set({ isLoading: true })

        try {
          // Clear cookie
          document.cookie = 'admin-token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;'

          // Clear session storage
          sessionStorage.removeItem('admin-auth-storage')

          // Clear store
          set({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            error: null
          })

          // Redirect to login
          window.location.href = '/login'
        } catch (error: any) {
          console.error('Logout error:', error)
          set({
            isLoading: false,
            error: error.message || 'Logout failed'
          })
        }
      },

      setUser: (user: AdminUser | null) => {
        set({
          user,
          isAuthenticated: !!user
        })
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading })
      },

      setError: (error: string | null) => {
        set({ error })
      },

      clearError: () => {
        set({ error: null })
      },

      // Initialize auth state on app start
      initialize: async () => {
        try {
          // Check if we have a valid session token
          const hasValidToken = document.cookie.includes('admin-token=valid-token')
          const state = get()

          if (hasValidToken && state.user && state.isAuthenticated) {
            // Keep user logged in during session
            set({ isLoading: false })
          } else {
            // Clear any stale auth state
            set({
              user: null,
              isAuthenticated: false,
              isLoading: false
            })
            // Clear the cookie
            document.cookie = 'admin-token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;'
          }
        } catch (error) {
          console.error('Error initializing auth state:', error)
          set({
            user: null,
            isAuthenticated: false,
            isLoading: false
          })
        }
      }
    }),
    {
      name: 'admin-auth-storage',
      storage: {
        getItem: (name) => {
          // Use sessionStorage for session-based persistence
          const value = sessionStorage.getItem(name)
          return value ? JSON.parse(value) : null
        },
        setItem: (name, value) => {
          sessionStorage.setItem(name, JSON.stringify(value))
        },
        removeItem: (name) => {
          sessionStorage.removeItem(name)
        }
      },
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated,
        isLoading: false,
        error: null,
        login: state.login,
        logout: state.logout,
        clearError: state.clearError,
        setUser: state.setUser,
        setLoading: state.setLoading,
        setError: state.setError,
        initialize: state.initialize
      }),
      onRehydrateStorage: () => (state) => {
        if (state) {
          state.isLoading = false
          // Initialize auth state after rehydration
          setTimeout(() => {
            const authStore = useAuthStore.getState()
            authStore.initialize()
          }, 100)
        }
      }
    }
  )
)

// Helper hooks
export const useAuth = () => {
  const store = useAuthStore()
  return {
    user: store.user,
    isLoading: store.isLoading,
    isAuthenticated: store.isAuthenticated,
    error: store.error,
    login: store.login,
    logout: store.logout,
    clearError: store.clearError
  }
}

export const usePermissions = () => {
  const user = useAuthStore(state => state.user)

  const hasPermission = (permission: string) => {
    return user?.permissions.includes(permission) || false
  }

  const hasAnyPermission = (permissions: string[]) => {
    return permissions.some(permission => hasPermission(permission))
  }

  const hasAllPermissions = (permissions: string[]) => {
    return permissions.every(permission => hasPermission(permission))
  }

  return {
    permissions: user?.permissions || [],
    hasPermission,
    hasAnyPermission,
    hasAllPermissions
  }
}
