import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:share_plus/share_plus.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:timeago/timeago.dart' as timeago;
import '../../data/models/news_article.dart';
import '../../data/repositories/news_repository.dart';

class ArticleDetailPage extends StatefulWidget {
  final NewsArticle article;

  const ArticleDetailPage({
    super.key,
    required this.article,
  });

  @override
  State<ArticleDetailPage> createState() => _ArticleDetailPageState();
}

class _ArticleDetailPageState extends State<ArticleDetailPage> {
  late final WebViewController _webViewController;
  bool _isLoading = true;
  bool _isError = false;
  String? _userReaction;
  final NewsRepository _newsRepository = NewsRepository();

  @override
  void initState() {
    super.initState();
    _incrementViewCount();
    _loadUserReaction();
    _initWebView();
  }

  Future<void> _incrementViewCount() async {
    try {
      await _newsRepository.incrementViewCount(widget.article.id);
    } catch (e) {
      // Ignore view count errors - non-critical feature
    }
  }

  Future<void> _loadUserReaction() async {
    try {
      final reaction = await _newsRepository.getUserReaction(widget.article.id);
      setState(() {
        _userReaction = reaction;
      });
    } catch (e) {
      // Ignore reaction loading errors - non-critical feature
    }
  }

  Future<void> _toggleReaction(String reactionType) async {
    try {
      await _newsRepository.toggleReaction(widget.article.id, reactionType);

      // Refresh user reaction
      await _loadUserReaction();
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to save reaction: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _shareArticle() {
    Share.share(
      '${widget.article.title}\n\nRead more: ${widget.article.url}',
      subject: widget.article.title,
    );
  }

  Future<void> _showAdminCommentDialog() async {
    final TextEditingController controller = TextEditingController(
      text: widget.article.adminComment,
    );

    return showDialog<void>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Admin Comment'),
          content: TextField(
            controller: controller,
            maxLines: 3,
            decoration: const InputDecoration(
              hintText: 'Add admin comment...',
            ),
          ),
          actions: <Widget>[
            TextButton(
              child: const Text('CANCEL'),
              onPressed: () => Navigator.of(context).pop(),
            ),
            TextButton(
              child: const Text('SAVE'),
              onPressed: () async {
                final navigator = Navigator.of(context);
                await _newsRepository.updateAdminComment(
                  widget.article.id,
                  controller.text.isEmpty ? null : controller.text,
                );
                if (mounted) {
                  navigator.pop();
                }

                // Refresh the page
                // In a real app, you would refresh the article data
              },
            ),
          ],
        );
      },
    );
  }

  void _initWebView() {
    // Check if this is an admin article
    if (widget.article.source == 'Admin' || widget.article.url.startsWith('admin://')) {
      // For admin articles, don't load WebView - we'll show content directly
      setState(() {
        _isLoading = false;
        _isError = false;
      });
      return;
    }

    _webViewController = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageStarted: (url) {
            setState(() {
              _isLoading = true;
              _isError = false;
            });
          },
          onPageFinished: (url) {
            setState(() {
              _isLoading = false;
            });
          },
          onWebResourceError: (error) {
            setState(() {
              _isError = true;
              _isLoading = false;
            });
          },
          onNavigationRequest: (request) {
            if (request.url != widget.article.url) {
              return NavigationDecision.prevent;
            }
            return NavigationDecision.navigate;
          },
        ),
      )
      ..loadRequest(Uri.parse(widget.article.url));
  }

  @override
  Widget build(BuildContext context) {
    final user = FirebaseAuth.instance.currentUser;
    final bool isAdmin = user != null &&
        (user.email == '<EMAIL>' || user.uid == 'ADMIN_UID');
    // Replace with your admin check logic

    return Scaffold(
      body: CustomScrollView(
        slivers: [
          // App bar with image
          SliverAppBar(
            expandedHeight: 200.0,
            floating: false,
            pinned: true,
            stretch: true,
            systemOverlayStyle: SystemUiOverlayStyle.light,
            flexibleSpace: FlexibleSpaceBar(
              background: widget.article.imageUrl.isNotEmpty
                  ? Stack(
                      fit: StackFit.expand,
                      children: [
                        Image.network(
                          widget.article.imageUrl,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) =>
                              Container(
                            color: Colors.grey[300],
                            child: const Icon(Icons.broken_image, size: 50),
                          ),
                        ),
                        // Gradient overlay for better readability
                        Container(
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topCenter,
                              end: Alignment.bottomCenter,
                              colors: [
                                Colors.transparent,
                                Colors.black.withOpacity(0.7),
                              ],
                              stops: const [0.7, 1.0],
                            ),
                          ),
                        ),
                      ],
                    )
                  : Container(
                      color: Theme.of(context).primaryColor.withOpacity(0.2),
                      child: Icon(
                        Icons.article,
                        size: 80,
                        color: Theme.of(context).primaryColor.withOpacity(0.5),
                      ),
                    ),
            ),
            actions: [
              if (isAdmin)
                IconButton(
                  icon: const Icon(Icons.comment),
                  onPressed: _showAdminCommentDialog,
                  tooltip: 'Add admin comment',
                ),
              IconButton(
                icon: const Icon(Icons.share),
                onPressed: _shareArticle,
                tooltip: 'Share article',
              ),
            ],
          ),

          // Content
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Category
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.grey[200],
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      widget.article.category,
                      style: TextStyle(
                        color: Theme.of(context).primaryColor,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),

                  const SizedBox(height: 12),

                  // Title
                  Text(
                    widget.article.title,
                    style: const TextStyle(
                      fontSize: 22,
                      fontWeight: FontWeight.bold,
                      height: 1.3,
                    ),
                  ),

                  const SizedBox(height: 12),

                  // Source and date
                  Row(
                    children: [
                      Text(
                        widget.article.source,
                        style: TextStyle(
                          color: Colors.grey[700],
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        '•',
                        style: TextStyle(color: Colors.grey[700]),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        timeago.format(widget.article.publishedAt),
                        style: TextStyle(color: Colors.grey[700]),
                      ),
                    ],
                  ),

                  const SizedBox(height: 12),

                  // Admin comment if present
                  if (widget.article.adminComment != null &&
                      widget.article.adminComment!.isNotEmpty) ...[
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.amber[50],
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: Colors.amber[200]!,
                          width: 1,
                        ),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(
                                Icons.admin_panel_settings,
                                size: 16,
                                color: Colors.amber[800],
                              ),
                              const SizedBox(width: 8),
                              Text(
                                'ADMIN NOTE',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color: Colors.amber[800],
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Text(
                            widget.article.adminComment!,
                            style: TextStyle(color: Colors.amber[900]),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 16),
                  ],

                  // Description (only for non-admin articles)
                  if (widget.article.source != 'Admin' && widget.article.description.isNotEmpty) ...[
                    Text(
                      widget.article.description,
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.grey[800],
                        height: 1.4,
                      ),
                    ),
                    const SizedBox(height: 16),
                  ],

                  // Reactions
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      _buildReactionButton(
                        'like',
                        Icons.thumb_up,
                        widget.article.reactionCounts['like'] ?? 0,
                      ),
                      const SizedBox(width: 16),
                      _buildReactionButton(
                        'love',
                        Icons.favorite,
                        widget.article.reactionCounts['love'] ?? 0,
                      ),
                      const SizedBox(width: 16),
                      _buildReactionButton(
                        'sad',
                        Icons.sentiment_dissatisfied,
                        widget.article.reactionCounts['sad'] ?? 0,
                      ),
                    ],
                  ),

                  const SizedBox(height: 24),

                  // Article content - Admin content or WebView
                  if (widget.article.source == 'Admin' && widget.article.adminContent != null)
                    // Show admin content directly
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: Colors.grey[300]!,
                          width: 1,
                        ),
                        borderRadius: BorderRadius.circular(8),
                        color: Colors.grey[50],
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(Icons.admin_panel_settings,
                                   size: 16, color: Colors.orange[700]),
                              const SizedBox(width: 8),
                              Text(
                                'ADMIN ARTICLE',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color: Colors.orange[700],
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          Text(
                            widget.article.adminContent!,
                            style: const TextStyle(
                              fontSize: 16,
                              height: 1.5,
                              color: Colors.black87,
                            ),
                          ),
                        ],
                      ),
                    )
                  else
                    // Show WebView for regular articles
                    Container(
                      height: 400,
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: Colors.grey[300]!,
                          width: 1,
                        ),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Stack(
                        children: [
                          WebViewWidget(
                            controller: _webViewController,
                          ),
                          if (_isLoading)
                            const Center(
                              child: CircularProgressIndicator(),
                            ),
                          if (_isError)
                            Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  const Icon(
                                    Icons.error_outline,
                                    color: Colors.red,
                                    size: 50,
                                  ),
                                  const SizedBox(height: 16),
                                  Text(
                                    'Failed to load article content',
                                    style: TextStyle(color: Colors.red[700]),
                                  ),
                                  const SizedBox(height: 16),
                                  ElevatedButton(
                                    onPressed: () {
                                      _webViewController.reload();
                                    },
                                    child: const Text('Retry'),
                                  ),
                                ],
                              ),
                            ),
                        ],
                      ),
                    ),

                  const SizedBox(height: 16),

                  // Read more button (only for non-admin articles)
                  if (widget.article.source != 'Admin')
                    Center(
                      child: ElevatedButton.icon(
                        onPressed: () {
                          // Expand WebView or open in browser
                          // This is a placeholder for your implementation
                        },
                        icon: const Icon(Icons.open_in_browser),
                        label: const Text('READ FULL ARTICLE'),
                      ),
                    ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReactionButton(
    String type,
    IconData icon,
    int count,
  ) {
    final bool isSelected = _userReaction == type;

    return InkWell(
      onTap: () => _toggleReaction(type),
      borderRadius: BorderRadius.circular(24),
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: 12,
          vertical: 6,
        ),
        decoration: BoxDecoration(
          color: isSelected ? Theme.of(context).primaryColor.withOpacity(0.1) : null,
          borderRadius: BorderRadius.circular(24),
        ),
        child: Row(
          children: [
            Icon(
              icon,
              color: isSelected
                  ? Theme.of(context).primaryColor
                  : Colors.grey[600],
              size: 18,
            ),
            const SizedBox(width: 4),
            Text(
              count.toString(),
              style: TextStyle(
                color: isSelected
                    ? Theme.of(context).primaryColor
                    : Colors.grey[600],
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }
}