import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/message_model.dart';

class MessageBubble extends StatelessWidget {
  final Message message;
  final bool isUserMessage;

  const MessageBubble({
    Key? key,
    required this.message,
    required this.isUserMessage,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final timeFormat = DateFormat('h:mm a');
    final timeString = timeFormat.format(message.timestamp);
    
    return Align(
      alignment: isUserMessage ? Alignment.centerRight : Alignment.centerLeft,
      child: Container(
        margin: EdgeInsets.only(
          top: 4,
          bottom: 4, 
          left: isUserMessage ? 50 : 8,
          right: isUserMessage ? 8 : 50,
        ),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: isUserMessage 
              ? Theme.of(context).primaryColor.withOpacity(0.9)
              : Colors.grey[200],
          borderRadius: BorderRadius.circular(16),
        ),
        child: <PERSON>umn(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Sender name
            Text(
              message.isAdmin ? 'Admin' : message.senderName,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 12,
                color: isUserMessage ? Colors.white70 : Colors.black54,
              ),
            ),
            const SizedBox(height: 4),
            // Message content
            Text(
              message.message,
              style: TextStyle(
                color: isUserMessage ? Colors.white : Colors.black87,
              ),
            ),
            const SizedBox(height: 2),
            // Time
            Align(
              alignment: Alignment.bottomRight,
              child: Text(
                timeString,
                style: TextStyle(
                  fontSize: 10,
                  color: isUserMessage ? Colors.white70 : Colors.black54,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
} 