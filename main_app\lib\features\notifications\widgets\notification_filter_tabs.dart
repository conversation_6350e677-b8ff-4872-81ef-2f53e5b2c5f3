import 'package:flutter/material.dart';
import '../../../core/theme/colors.dart';

class NotificationFilterTabs extends StatelessWidget {
  final String selectedFilter;
  final Function(String) onFilterChanged;
  final Map<String, int> notificationCounts;

  const NotificationFilterTabs({
    Key? key,
    required this.selectedFilter,
    required this.onFilterChanged,
    this.notificationCounts = const {},
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Container(
      height: 50,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: ListView(
        scrollDirection: Axis.horizontal,
        children: [
          _buildFilterChip(
            context,
            'all',
            'All',
            Icons.notifications,
            notificationCounts['total'] ?? 0,
            isDarkMode,
          ),
          const SizedBox(width: 8),
          _buildFilterChip(
            context,
            'message',
            'Messages',
            Icons.message,
            notificationCounts['message'] ?? 0,
            isDarkMode,
          ),
          const SizedBox(width: 8),
          _buildFilterChip(
            context,
            'verification',
            'Verification',
            Icons.verified,
            notificationCounts['verification'] ?? 0,
            isDarkMode,
          ),
          const SizedBox(width: 8),
          _buildFilterChip(
            context,
            'system',
            'System',
            Icons.system_update,
            notificationCounts['system'] ?? 0,
            isDarkMode,
          ),
          const SizedBox(width: 8),
          _buildFilterChip(
            context,
            'basic',
            'Basic',
            Icons.info,
            notificationCounts['basic'] ?? 0,
            isDarkMode,
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(
    BuildContext context,
    String filter,
    String label,
    IconData icon,
    int count,
    bool isDarkMode,
  ) {
    final isSelected = selectedFilter == filter;

    return GestureDetector(
      onTap: () => onFilterChanged(filter),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected
              ? AppColors.primaryYellow
              : (isDarkMode ? AppColors.darkSurface : AppColors.lightSurface),
          borderRadius: BorderRadius.circular(25),
          border: Border.all(
            color: isSelected
                ? AppColors.primaryYellow
                : (isDarkMode ? AppColors.darkDivider : AppColors.lightDivider),
            width: 1,
          ),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color: AppColors.primaryYellow.withOpacity(0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ]
              : null,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 16,
              color: isSelected
                  ? Colors.black
                  : (isDarkMode ? AppColors.darkText : AppColors.lightText),
            ),
            const SizedBox(width: 6),
            Text(
              label,
              style: TextStyle(
                fontSize: 14,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.w500,
                color: isSelected
                    ? Colors.black
                    : (isDarkMode ? AppColors.darkText : AppColors.lightText),
              ),
            ),
            if (count > 0) ...[
              const SizedBox(width: 6),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: isSelected
                      ? Colors.black.withOpacity(0.2)
                      : AppColors.primaryYellow,
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Text(
                  count > 99 ? '99+' : count.toString(),
                  style: TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                    color: isSelected ? Colors.black : Colors.black,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
