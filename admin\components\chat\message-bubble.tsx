'use client'

import { useState, useRef } from 'react'
import { motion } from 'framer-motion'
import {
  Play,
  Pause,
  Download,
  Reply,
  MoreVertical,
  User,
  Shield
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { ChatMessage } from '@/lib/services/chat-service'
import { formatRelativeTime } from '@/lib/utils'

interface MessageBubbleProps {
  message: ChatMessage
  isOwn: boolean
  onReply?: (message: ChatMessage) => void
  showAvatar?: boolean
}

export function MessageBubble({
  message,
  isOwn,
  onReply,
  showAvatar = true
}: MessageBubbleProps) {
  // In admin panel message positioning:
  // - Admin messages (sent by admin) → Right side with yellow bubble
  // - User messages (received by admin) → Left side with gray bubble
  const isOnRight = message.isAdmin;
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentTime, setCurrentTime] = useState(0)
  const [showActions, setShowActions] = useState(false)
  const [imageError, setImageError] = useState<{ [key: string]: boolean }>({})

  const audioRef = useRef<HTMLAudioElement | null>(null)

  // Format time for voice messages
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  // Play/pause voice message
  const toggleVoicePlayback = () => {
    if (!message.voiceNote) return

    if (!audioRef.current) {
      audioRef.current = new Audio(message.voiceNote.url)
      audioRef.current.onended = () => {
        setIsPlaying(false)
        setCurrentTime(0)
      }
      audioRef.current.ontimeupdate = () => {
        if (audioRef.current) {
          setCurrentTime(audioRef.current.currentTime)
        }
      }
    }

    if (isPlaying) {
      audioRef.current.pause()
      setIsPlaying(false)
    } else {
      audioRef.current.play()
      setIsPlaying(true)
    }
  }

  // Download attachment
  const downloadFile = (url: string, filename: string) => {
    const link = document.createElement('a')
    link.href = url
    link.download = filename
    link.target = '_blank'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  // Handle image error
  const handleImageError = (url: string) => {
    setImageError(prev => ({ ...prev, [url]: true }))
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      className={`flex ${isOnRight ? 'justify-end' : 'justify-start'} mb-4`}
      onMouseEnter={() => setShowActions(true)}
      onMouseLeave={() => setShowActions(false)}
    >
      <div className={`flex ${isOnRight ? 'flex-row-reverse' : 'flex-row'} items-end space-x-2 max-w-xs lg:max-w-md`}>
        {/* Avatar - show for user messages (left side) */}
        {showAvatar && !isOnRight && (
          <div className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center flex-shrink-0">
            {message.senderAvatar ? (
              <img
                src={message.senderAvatar}
                alt={message.senderName}
                className="w-8 h-8 rounded-full"
              />
            ) : (
              <div className="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center">
                <User className="w-4 h-4 text-gray-600" />
              </div>
            )}
          </div>
        )}

        {/* Avatar - show for admin messages (right side) */}
        {showAvatar && isOnRight && (
          <div className="w-8 h-8 rounded-full bg-yellow-100 flex items-center justify-center flex-shrink-0">
            <Shield className="w-4 h-4 text-yellow-600" />
          </div>
        )}

        {/* Message content */}
        <div className={`relative ${isOnRight ? 'mr-2' : 'ml-2'}`}>
          {/* Reply indicator */}
          {message.replyTo && (
            <div className={`mb-2 p-2 rounded border-l-4 text-xs ${
              isOnRight
                ? 'bg-yellow-50 border-yellow-300'
                : 'bg-gray-50 border-gray-300'
            }`}>
              <div className="font-medium text-gray-600">
                Replying to {message.replyTo.senderName}
              </div>
              <div className="text-gray-500 truncate">
                {message.replyTo.text}
              </div>
            </div>
          )}

          {/* Message bubble */}
          <div
            className={`px-4 py-2 rounded-lg ${
              isOnRight
                ? 'bg-yellow-500 text-white'
                : 'bg-gray-100 text-gray-900'
            }`}
          >
            {/* Sender name for non-right messages */}
            {!isOnRight && (
              <div className="flex items-center space-x-1 mb-1">
                <span className="text-xs font-medium text-gray-600">
                  {message.senderName}
                </span>
                {message.isAdmin && (
                  <Shield className="w-3 h-3 text-yellow-500" />
                )}
              </div>
            )}

            {/* Text message */}
            {message.type === 'text' && message.text && (
              <p className="text-sm whitespace-pre-wrap break-words">
                {message.text}
              </p>
            )}

            {/* Voice message */}
            {message.type === 'voice' && message.voiceNote && (
              <div className="flex items-center space-x-3 min-w-[200px]">
                <Button
                  onClick={toggleVoicePlayback}
                  size="sm"
                  variant={isOnRight ? "secondary" : "outline"}
                  className="flex-shrink-0"
                >
                  {isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
                </Button>

                <div className="flex-1">
                  <div className="flex justify-between text-xs mb-1">
                    <span>{formatTime(currentTime)}</span>
                    <span>{formatTime(message.voiceNote.duration)}</span>
                  </div>
                  <div className={`w-full rounded-full h-1 ${isOnRight ? 'bg-yellow-300' : 'bg-gray-300'}`}>
                    <div
                      className={`h-1 rounded-full transition-all duration-100 ${
                        isOnRight ? 'bg-white' : 'bg-yellow-500'
                      }`}
                      style={{
                        width: `${message.voiceNote.duration > 0 ? (currentTime / message.voiceNote.duration) * 100 : 0}%`
                      }}
                    />
                  </div>
                </div>
              </div>
            )}

            {/* Image message */}
            {message.type === 'image' && message.imageUrls && message.imageUrls.length > 0 && (
              <div className="space-y-2">
                {message.text && (
                  <p className="text-sm whitespace-pre-wrap break-words">
                    {message.text}
                  </p>
                )}
                <div className={`grid gap-2 ${
                  message.imageUrls.length === 1 ? 'grid-cols-1' :
                  message.imageUrls.length === 2 ? 'grid-cols-2' :
                  'grid-cols-2'
                }`}>
                  {message.imageUrls.map((url, index) => (
                    <div key={index} className="relative group">
                      {!imageError[url] ? (
                        <img
                          src={url}
                          alt={`Image ${index + 1}`}
                          className="w-full h-32 object-cover rounded cursor-pointer hover:opacity-90 transition-opacity"
                          onClick={() => window.open(url, '_blank')}
                          onError={() => handleImageError(url)}
                        />
                      ) : (
                        <div className="w-full h-32 bg-gray-200 rounded flex items-center justify-center">
                          <span className="text-gray-500 text-xs">Failed to load</span>
                        </div>
                      )}

                      <Button
                        onClick={() => downloadFile(url, `image_${index + 1}.jpg`)}
                        size="sm"
                        variant="secondary"
                        className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity"
                      >
                        <Download className="w-3 h-3" />
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Timestamp and status */}
            <div className={`flex items-center justify-between mt-2 text-xs ${
              isOnRight ? 'text-yellow-100' : 'text-gray-500'
            }`}>
              <span>{formatRelativeTime(message.timestamp)}</span>
              {message.isEdited && (
                <span className="italic">edited</span>
              )}
            </div>
          </div>

          {/* Actions */}
          {showActions && onReply && (
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              className={`absolute top-0 ${isOnRight ? 'left-0' : 'right-0'} -translate-y-2`}
            >
              <Button
                onClick={() => onReply(message)}
                size="sm"
                variant="outline"
                className="bg-white shadow-md"
              >
                <Reply className="w-3 h-3" />
              </Button>
            </motion.div>
          )}
        </div>
      </div>
    </motion.div>
  )
}
