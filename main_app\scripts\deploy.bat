@echo off
REM Enhanced Deploy script for Firebase Hosting (Windows)
REM This script builds the Flutter web app and deploys it to Firebase Hosting
REM with environment support and enhanced error handling

setlocal enabledelayedexpansion

REM Default values
set ENVIRONMENT=production
set SKIP_TESTS=false
set SKIP_BUILD=false
set CHANNEL=live

REM Parse command line arguments
:parse_args
if "%~1"=="" goto :args_done
if "%~1"=="--env" (
    set ENVIRONMENT=%~2
    shift
    shift
    goto :parse_args
)
if "%~1"=="--channel" (
    set CHANNEL=%~2
    shift
    shift
    goto :parse_args
)
if "%~1"=="--skip-tests" (
    set SKIP_TESTS=true
    shift
    goto :parse_args
)
if "%~1"=="--skip-build" (
    set SKIP_BUILD=true
    shift
    goto :parse_args
)
if "%~1"=="--help" (
    echo Usage: deploy.bat [options]
    echo Options:
    echo   --env ^<environment^>    Environment: development, staging, production ^(default: production^)
    echo   --channel ^<channel^>     Firebase channel: live, staging ^(default: live^)
    echo   --skip-tests           Skip running tests
    echo   --skip-build           Skip building the app
    echo   --help                 Show this help message
    exit /b 0
)
shift
goto :parse_args
:args_done

echo 🚀 Starting deployment process for %ENVIRONMENT% environment...
echo 📋 Configuration:
echo    Environment: %ENVIRONMENT%
echo    Channel: %CHANNEL%
echo    Skip Tests: %SKIP_TESTS%
echo    Skip Build: %SKIP_BUILD%
echo.

REM Check if Firebase CLI is installed
firebase --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Firebase CLI is not installed. Please install it first:
    echo npm install -g firebase-tools
    exit /b 1
)

REM Check if Flutter is installed
flutter --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Flutter is not installed. Please install Flutter first.
    exit /b 1
)

REM Check if environment file exists
if not exist ".env.%ENVIRONMENT%" (
    echo ❌ Environment file .env.%ENVIRONMENT% not found
    echo Please create the environment file or use --env with a valid environment
    exit /b 1
)

REM Get Flutter dependencies
echo 📦 Getting Flutter dependencies...
flutter pub get
if %errorlevel% neq 0 (
    echo ❌ Failed to get Flutter dependencies
    exit /b 1
)

REM Run tests (unless skipped)
if "%SKIP_TESTS%"=="false" (
    echo 🧪 Running tests...
    flutter test
    if %errorlevel% neq 0 (
        echo ❌ Tests failed. Deployment aborted.
        echo Use --skip-tests to bypass test failures
        exit /b 1
    )
    echo ✅ All tests passed!
)

REM Build web app (unless skipped)
if "%SKIP_BUILD%"=="false" (
    echo 🔨 Building web app for %ENVIRONMENT%...
    flutter build web --release --web-renderer html --dart-define=ENVIRONMENT=%ENVIRONMENT%
    if %errorlevel% neq 0 (
        echo ❌ Build failed
        exit /b 1
    )
    echo ✅ Build completed successfully!
)

REM Deploy to Firebase
echo 🚀 Deploying to Firebase Hosting...
if "%CHANNEL%"=="live" (
    firebase deploy --only hosting:driveon
) else (
    firebase hosting:channel:deploy %CHANNEL% --expires 30d --only driveon
)

if %errorlevel% neq 0 (
    echo ❌ Deployment failed
    exit /b 1
)

echo.
echo ✅ Deployment completed successfully!
if "%CHANNEL%"=="live" (
    echo 🌐 Your app is now live at: https://driveon.web.app
) else (
    echo 🌐 Your preview is available at the URL shown above
)
echo 📊 Firebase Console: https://console.firebase.google.com/project/drive-on-b2af8
echo.
pause
