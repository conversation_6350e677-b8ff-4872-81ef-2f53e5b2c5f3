/** @type {import('next').NextConfig} */
const nextConfig = {
  // Basic configuration
  // Only use static export in production to avoid chunk loading issues in development
  ...(process.env.NODE_ENV === 'production' && { output: 'export' }),
  trailingSlash: true,
  images: {
    unoptimized: true,
    domains: [
      'firebasestorage.googleapis.com',
      'lh3.googleusercontent.com',
      'drive-on-b2af8.firebasestorage.app',
    ],
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'firebasestorage.googleapis.com',
        port: '',
        pathname: '/v0/b/**',
      },
      {
        protocol: 'https',
        hostname: 'drive-on-b2af8.firebasestorage.app',
        port: '',
        pathname: '/**',
      },
    ],
    formats: ['image/webp', 'image/avif'],
  },
  serverExternalPackages: ['firebase-admin'],
  experimental: {
    optimizePackageImports: ['@heroicons/react', 'lucide-react', 'recharts'],
  },
  env: {
    FIREBASE_PROJECT_ID: 'drive-on-b2af8',
    FIREBASE_DATABASE_URL: 'https://drive-on-b2af8-default-rtdb.firebaseio.com',
  },
  webpack: (config, { dev, isServer }) => {
    // Performance optimizations
    if (!dev && !isServer) {
      config.optimization.splitChunks = {
        chunks: 'all',
        cacheGroups: {
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            chunks: 'all',
            priority: 10,
          },

          firebase: {
            test: /[\\/]node_modules[\\/](@firebase|firebase)[\\/]/,
            name: 'firebase',
            chunks: 'all',
            priority: 15,
          },
          framer: {
            test: /[\\/]node_modules[\\/]framer-motion[\\/]/,
            name: 'framer',
            chunks: 'all',
            priority: 12,
          },
        },
      }
    }

    // Handle fallbacks for client-side only modules
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,

        fs: false,
        net: false,
        tls: false,
        crypto: false,
      };
    }
    return config;
  },
  // Enable compression
  compress: true,
  // Security headers
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin',
          },
          {
            key: 'Permissions-Policy',
            value: 'camera=(), microphone=(), geolocation=()',
          },
        ],
      },
    ];
  },
};

module.exports = nextConfig;
