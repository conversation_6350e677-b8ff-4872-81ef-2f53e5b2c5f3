import 'package:flutter/material.dart';

/// A wrapper widget that adds debugging and monitoring widgets around the app
class AppWrapper extends StatelessWidget {
  /// The app to wrap
  final Widget child;

  /// Creates an app wrapper
  const AppWrapper({
    Key? key,
    required this.child,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Simply return the child without any performance monitoring
    return child;
  }
}