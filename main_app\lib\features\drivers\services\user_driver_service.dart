import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../../../core/services/email_uniqueness_service.dart';
import '../../../core/utils/app_logger.dart';
import '../models/driver.dart';

class UserDriverService {
  static final UserDriverService _instance = UserDriverService._internal();
  static UserDriverService get instance => _instance;
  UserDriverService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final AppLogger _logger = AppLogger('UserDriverService');

  /// Check if user has submitted a driver request (any status)
  Future<bool> hasUserSubmittedDriverRequest() async {
    try {
      final userEmail = await EmailUniquenessService.instance.getCurrentUserEmail();
      if (userEmail == null) {
        _logger.debug('No user email found');
        return false;
      }

      _logger.debug('Checking for driver request with email: $userEmail');

      // Check driver_requests collection
      final requestSnapshot = await _firestore
          .collection('driver_requests')
          .where('userEmail', isEqualTo: userEmail)
          .limit(1)
          .get();

      if (requestSnapshot.docs.isNotEmpty) {
        _logger.info('Found driver request for email $userEmail');
        return true;
      }

      // Check drivers collection (approved drivers)
      final driverSnapshot = await _firestore
          .collection('drivers')
          .where('userEmail', isEqualTo: userEmail)
          .limit(1)
          .get();

      final hasDriver = driverSnapshot.docs.isNotEmpty;
      _logger.debug('Email $userEmail has driver: $hasDriver');
      return hasDriver;
    } catch (e) {
      _logger.error('Error checking user driver request', error: e);
      return false;
    }
  }

  /// Get current user's driver if exists (only approved drivers)
  Future<Driver?> getCurrentUserDriver() async {
    try {
      final userEmail = await EmailUniquenessService.instance.getCurrentUserEmail();
      if (userEmail == null) return null;

      final querySnapshot = await _firestore
          .collection('drivers')
          .where('userEmail', isEqualTo: userEmail)
          .limit(1)
          .get();

      if (querySnapshot.docs.isNotEmpty) {
        // Return the user's driver regardless of status (verified or suspended)
        // This allows the button to show for both verified and suspended drivers
        final doc = querySnapshot.docs.first;
        final data = doc.data();
        final status = data['status'] as String?;

        // Don't return rejected drivers, but return verified and suspended drivers
        if (status != 'rejected') {
          final driver = Driver.fromMap(data, doc.id);
          _logger.info('Found driver "${driver.name}" with status "$status" for email $userEmail');
          return driver;
        }
      }

      _logger.debug('No driver found for email $userEmail');
      return null;
    } catch (e) {
      _logger.error('Error getting current user driver', error: e);
      return null;
    }
  }

  /// Get current user's driver as a stream for real-time updates
  Stream<Driver?> getCurrentUserDriverStream() {
    return Stream.fromFuture(_getUserEmail()).asyncExpand((email) {
      if (email == null) {
        _logger.debug('No user email found');
        return Stream.value(null);
      }

      _logger.debug('Querying drivers for email: $email');

      // First try to find by userEmail
      return _firestore
          .collection('drivers')
          .where('userEmail', isEqualTo: email)
          .limit(1)
          .snapshots()
          .handleError((error) {
            _logger.error('Error in email query', error: error);
            return null;
          })
          .asyncExpand((snapshot) {
            _logger.debug('Email query returned ${snapshot.docs.length} documents');

            if (snapshot.docs.isNotEmpty) {
              final doc = snapshot.docs.first;
              final data = doc.data();
              final status = data['status'] as String?;
              _logger.debug('Found driver by email with status: $status');

              // Don't return rejected drivers, but return verified and suspended drivers
              if (status != 'rejected') {
                try {
                  final driver = Driver.fromMap(data, doc.id);
                  _logger.info('Found driver "${driver.name}" with status "$status" for email $email');
                  return Stream.value(driver);
                } catch (e) {
                  _logger.error('Error creating driver from map', error: e);
                  return Stream.value(null);
                }
              } else {
                _logger.debug('Driver is rejected, not returning');
                return Stream.value(null);
              }
            } else {
              // Fallback: try to find by submittedBy (for legacy drivers without userEmail)
              _logger.debug('No driver found by email, trying fallback by UID');
              final currentUser = FirebaseAuth.instance.currentUser;
              if (currentUser != null) {
                return _firestore
                    .collection('drivers')
                    .where('submittedBy', isEqualTo: currentUser.uid)
                    .limit(1)
                    .snapshots()
                    .map((fallbackSnapshot) {
                      _logger.debug('Fallback query returned ${fallbackSnapshot.docs.length} documents');
                      if (fallbackSnapshot.docs.isNotEmpty) {
                        final doc = fallbackSnapshot.docs.first;
                        final data = doc.data();
                        final status = data['status'] as String?;
                        _logger.debug('Found driver by UID with status: $status');

                        if (status != 'rejected') {
                          try {
                            final driver = Driver.fromMap(data, doc.id);
                            _logger.info('Found fallback driver "${driver.name}" with status "$status"');
                            return driver;
                          } catch (e) {
                            _logger.error('Error creating fallback driver from map', error: e);
                            return null;
                          }
                        }
                      }
                      _logger.debug('No valid driver found by UID either');
                      return null;
                    });
              } else {
                _logger.debug('No current user for fallback');
                return Stream.value(null);
              }
            }
          });
    }).handleError((error) {
      _logger.error('Outer error in getCurrentUserDriverStream', error: error);
      return null;
    });
  }

  /// Get current user's driver request status
  Future<Map<String, dynamic>?> getCurrentUserDriverRequest() async {
    try {
      final userEmail = await EmailUniquenessService.instance.getCurrentUserEmail();
      if (userEmail == null) return null;

      final querySnapshot = await _firestore
          .collection('driver_requests')
          .where('userEmail', isEqualTo: userEmail)
          .limit(1)
          .get();

      if (querySnapshot.docs.isNotEmpty) {
        final doc = querySnapshot.docs.first;
        final data = doc.data();
        return {
          'id': doc.id,
          'status': data['status'],
          'name': data['name'],
          'createdAt': data['createdAt'],
          ...data,
        };
      }

      return null;
    } catch (e) {
      _logger.error('Error getting current user driver request', error: e);
      return null;
    }
  }

  /// Get user email helper
  Future<String?> _getUserEmail() async {
    try {
      return await EmailUniquenessService.instance.getCurrentUserEmail();
    } catch (e) {
      _logger.error('Error getting user email', error: e);
      return null;
    }
  }

  /// Suspend driver (only available for verified drivers)
  Future<void> suspendDriver(String driverId) async {
    try {
      await _firestore.collection('drivers').doc(driverId).update({
        'status': 'suspended',
        'suspendedAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      _logger.error('Error suspending driver', error: e);
      rethrow;
    }
  }

  /// Reactivate driver (go live)
  Future<void> reactivateDriver(String driverId) async {
    try {
      await _firestore.collection('drivers').doc(driverId).update({
        'status': 'verified',
        'reactivatedAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      _logger.error('Error reactivating driver', error: e);
      rethrow;
    }
  }

  /// Delete driver completely
  Future<void> deleteDriver(String driverId) async {
    try {
      // Delete the driver document
      await _firestore.collection('drivers').doc(driverId).delete();
    } catch (e) {
      _logger.error('Error deleting driver', error: e);
      rethrow;
    }
  }
}
