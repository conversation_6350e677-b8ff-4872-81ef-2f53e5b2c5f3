import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../../core/core.dart';
import '../models/job_model.dart';
import '../services/user_job_service.dart';
import '../job_details_screen.dart';
import '../edit_job_screen.dart';
import '../../chat/chat_list_screen.dart';

class MyJobButton extends StatelessWidget {
  final JobModel job;

  const MyJobButton({
    Key? key,
    required this.job,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<DocumentSnapshot>(
      stream: FirebaseFirestore.instance
          .collection('jobs')
          .doc(job.jobId)
          .snapshots(),
      builder: (context, jobSnapshot) {
        // Get current job status from real-time stream
        String? currentStatus;
        if (jobSnapshot.hasData && jobSnapshot.data!.exists) {
          final data = jobSnapshot.data!.data() as Map<String, dynamic>?;
          currentStatus = data?['status'] as String?;
        }

        return StreamBuilder<int>(
          stream: UserJobService.instance.getUnreadMessagesCountStream(job.jobId),
          builder: (context, messageSnapshot) {
            final unreadCount = messageSnapshot.data ?? 0;

            return FloatingActionButton(
              onPressed: () => _showJobOptions(context, currentStatus),
              backgroundColor: AppColors.primaryYellow,
              child: Stack(
                children: [
                  const Icon(
                    Icons.work,
                    color: Colors.black,
                    size: 28,
                  ),
                  if (unreadCount > 0)
                    Positioned(
                      right: 0,
                      top: 0,
                      child: Container(
                        padding: const EdgeInsets.all(2),
                        decoration: const BoxDecoration(
                          color: Colors.red,
                          shape: BoxShape.circle,
                        ),
                        constraints: const BoxConstraints(
                          minWidth: 16,
                          minHeight: 16,
                        ),
                        child: Text(
                          unreadCount > 99 ? '99+' : unreadCount.toString(),
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  void _showJobOptions(BuildContext context, String? currentStatus) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: Theme.of(context).brightness == Brightness.dark
              ? AppColors.darkSurface
              : Colors.white,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        ),
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 20),
            Text(
              'My Job Post',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).brightness == Brightness.dark
                    ? AppColors.darkText
                    : AppColors.lightText,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              job.title,
              style: TextStyle(
                fontSize: 14,
                color: Theme.of(context).brightness == Brightness.dark
                    ? AppColors.darkTextSecondary
                    : AppColors.lightTextSecondary,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            _buildOptionTile(
              context,
              icon: Icons.visibility,
              title: 'View Job Details',
              onTap: () {
                Navigator.pop(context);
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => JobDetailsScreen(jobId: job.jobId),
                  ),
                );
              },
            ),
            _buildOptionTile(
              context,
              icon: Icons.edit,
              title: 'Edit Job Post',
              onTap: () {
                Navigator.pop(context);
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => EditJobScreen(job: job),
                  ),
                );
              },
            ),
            _buildOptionTile(
              context,
              icon: Icons.chat,
              title: 'View Messages',
              onTap: () {
                Navigator.pop(context);
                _openJobChats(context);
              },
            ),
            const Divider(),
            // Show different options based on real-time job status
            if (currentStatus == 'suspended') ...[
              _buildOptionTile(
                context,
                icon: Icons.play_circle,
                title: 'Go Live',
                subtitle: 'Make job visible to job seekers again',
                onTap: () {
                  Navigator.pop(context);
                  _goLiveJob(context);
                },
              ),
            ] else ...[
              _buildOptionTile(
                context,
                icon: Icons.pause_circle,
                title: 'Suspend Job Post',
                subtitle: 'Temporarily hide from job seekers',
                onTap: () {
                  Navigator.pop(context);
                  _suspendJob(context);
                },
              ),
            ],
            _buildOptionTile(
              context,
              icon: Icons.delete,
              title: 'Delete Job Post',
              subtitle: 'Permanently remove this job',
              isDestructive: true,
              onTap: () {
                Navigator.pop(context);
                _deleteJob(context);
              },
            ),
            const SizedBox(height: 10),
          ],
        ),
      ),
    );
  }

  Widget _buildOptionTile(
    BuildContext context, {
    required IconData icon,
    required String title,
    String? subtitle,
    bool isDestructive = false,
    required VoidCallback onTap,
  }) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final iconColor = isDestructive ? Colors.red : AppColors.primaryYellow;
    final titleColor = isDestructive
        ? Colors.red
        : (isDarkMode ? AppColors.darkText : AppColors.lightText);

    return ListTile(
      leading: Icon(
        icon,
        color: iconColor,
      ),
      title: Text(
        title,
        style: TextStyle(
          color: titleColor,
          fontWeight: FontWeight.w500,
        ),
      ),
      subtitle: subtitle != null
          ? Text(
              subtitle,
              style: TextStyle(
                color: isDarkMode ? AppColors.darkTextSecondary : AppColors.lightTextSecondary,
                fontSize: 12,
              ),
            )
          : null,
      onTap: onTap,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10),
      ),
    );
  }

  void _openJobChats(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const ChatListScreen(),
      ),
    );
  }

  void _goLiveJob(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Go Live'),
          content: const Text(
            'Are you sure you want to make this job post visible to job seekers again?',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () async {
                Navigator.pop(context);
                await _performGoLiveJob(context);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
              ),
              child: const Text('Go Live'),
            ),
          ],
        );
      },
    );
  }

  Future<void> _performGoLiveJob(BuildContext context) async {
    try {
      // Update job status to active
      await FirebaseFirestore.instance
          .collection('jobs')
          .doc(job.jobId)
          .update({
        'status': 'active',
        'reactivatedAt': FieldValue.serverTimestamp(),
      });

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Job post is now live and visible to job seekers'),
            backgroundColor: Colors.green,
          ),
        );

        // Navigate back to refresh the jobs list
        Navigator.pop(context);
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error reactivating job: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _suspendJob(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Suspend Job Post'),
          content: const Text(
            'Are you sure you want to suspend this job post? It will be hidden from job seekers but can be reactivated later.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () async {
                Navigator.pop(context);
                await _performSuspendJob(context);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
              ),
              child: const Text('Suspend'),
            ),
          ],
        );
      },
    );
  }

  Future<void> _performSuspendJob(BuildContext context) async {
    try {
      // Update job status to suspended
      await FirebaseFirestore.instance
          .collection('jobs')
          .doc(job.jobId)
          .update({
        'status': 'suspended',
        'suspendedAt': FieldValue.serverTimestamp(),
      });

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Job post suspended successfully'),
            backgroundColor: Colors.orange,
          ),
        );

        // Navigate back to refresh the jobs list
        Navigator.pop(context);
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error suspending job: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _deleteJob(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Delete Job Post'),
          content: const Text(
            'Are you sure you want to permanently delete this job post? This action cannot be undone and all related messages will also be deleted.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () async {
                Navigator.pop(context);
                await _performDeleteJob(context);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: const Text('Delete'),
            ),
          ],
        );
      },
    );
  }

  Future<void> _performDeleteJob(BuildContext context) async {
    try {
      // Delete all related chats first
      final chatsQuery = await FirebaseFirestore.instance
          .collection('chats')
          .where('jobId', isEqualTo: job.jobId)
          .get();

      // Delete all chat documents and their messages
      for (final chatDoc in chatsQuery.docs) {
        // Delete all messages in this chat
        final messagesQuery = await chatDoc.reference
            .collection('messages')
            .get();

        for (final messageDoc in messagesQuery.docs) {
          await messageDoc.reference.delete();
        }

        // Delete the chat document
        await chatDoc.reference.delete();
      }

      // Delete the job post
      await FirebaseFirestore.instance
          .collection('jobs')
          .doc(job.jobId)
          .delete();

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Job post deleted successfully'),
            backgroundColor: Colors.red,
          ),
        );

        // Navigate back to refresh the jobs list
        Navigator.pop(context);
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error deleting job: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
