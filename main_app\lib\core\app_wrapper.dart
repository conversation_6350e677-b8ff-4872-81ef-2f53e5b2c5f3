import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'providers/theme_provider.dart';
// import 'theme/ios_theme.dart'; // Not needed
import 'theme/colors.dart';

/// A wrapper widget that applies iOS-style UI to the entire app
class IOSAppWrapper extends StatelessWidget {
  final Widget child;
  final String title;
  final bool enableDarkMode;

  const IOSAppWrapper({
    super.key,
    required this.child,
    required this.title,
    this.enableDarkMode = true,
  });

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => ThemeProvider(),
      child: Consumer<ThemeProvider>(
        builder: (context, themeProvider, _) {
          // Apply iOS-style system UI based on theme
          final isDark = themeProvider.isDarkMode;
          SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle(
            statusBarColor: Colors.transparent,
            statusBarIconBrightness: isDark ? Brightness.light : Brightness.dark,
            statusBarBrightness: isDark ? Brightness.dark : Brightness.light,
            systemNavigationBarColor: isDark ? AppColors.darkBackground : Colors.white,
            systemNavigationBarIconBrightness: isDark ? Brightness.light : Brightness.dark,
          ));

          // Build the app with the theme
          return MaterialApp(
            title: title,
            theme: themeProvider.theme,
            debugShowCheckedModeBanner: false,
            home: child,
            builder: (context, child) {
              // Apply global text scaling
              return MediaQuery(
                data: MediaQuery.of(context).copyWith(
                  textScaler: const TextScaler.linear(1.0), // iOS keeps text size consistent
                ),
                child: ScrollConfiguration(
                  // Apply iOS-style scrolling physics globally
                  behavior: const ScrollBehavior().copyWith(
                    physics: const BouncingScrollPhysics(
                      parent: AlwaysScrollableScrollPhysics(),
                    ),
                    scrollbars: false, // iOS doesn't show scrollbars by default
                    overscroll: true,
                  ),
                  child: child!,
                ),
              );
            },
          );
        },
      ),
    );
  }
}
