const admin = require('firebase-admin');

// Initialize Firebase Admin SDK
const serviceAccount = require('../serviceAccountKey.json');

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
  databaseURL: "https://drive-on-b2af8-default-rtdb.firebaseio.com"
});

async function updateAdminPermissions() {
  try {
    const adminEmail = '<EMAIL>';
    
    console.log('🚀 Updating Admin Permissions for Drive-On');
    console.log('==========================================\n');
    console.log(`📧 Target Email: ${adminEmail}`);
    
    // First, find the user by email
    let userRecord;
    try {
      userRecord = await admin.auth().getUserByEmail(adminEmail);
      console.log('✅ User found in Firebase Auth');
      console.log(`🆔 UID: ${userRecord.uid}`);
    } catch (error) {
      if (error.code === 'auth/user-not-found') {
        console.log('❌ User not found in Firebase Auth');
        console.log('💡 Creating new user...');
        
        // Create the user if they don't exist
        userRecord = await admin.auth().createUser({
          email: adminEmail,
          password: 'Drive@on0750##',
          displayName: 'Drive-On Admin',
          emailVerified: true,
        });
        console.log('✅ New user created in Firebase Auth');
      } else {
        throw error;
      }
    }

    // Set custom claims for super admin role
    const adminClaims = {
      admin: true,
      superAdmin: true,
      role: 'super_admin',
      permissions: [
        'all',
        'users.read',
        'users.write',
        'users.delete',
        'drivers.read',
        'drivers.write',
        'drivers.delete',
        'drivers.verify',
        'queries.read',
        'queries.write',
        'queries.delete',
        'chats.read',
        'chats.write',
        'chats.moderate',
        'analytics.read',
        'settings.read',
        'settings.write',
        'system.admin',
        'manage_users',
        'manage_drivers',
        'manage_partners',
        'manage_jobs',
        'manage_news',
        'manage_forums',
        'manage_queries',
        'manage_chats',
        'view_analytics',
        'manage_settings'
      ]
    };

    await admin.auth().setCustomUserClaims(userRecord.uid, adminClaims);
    console.log('✅ Super Admin claims set in Firebase Auth');

    // Create/Update admin document in Firestore
    const adminData = {
      email: adminEmail,
      displayName: 'Drive-On Admin',
      role: 'super_admin',
      isActive: true,
      isAdmin: true,
      superAdmin: true,
      permissions: adminClaims.permissions,
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
      lastLoginAt: null,
      profilePicture: null,
      department: 'Administration',
      phone: null,
      timezone: 'UTC',
      language: 'en',
      twoFactorEnabled: false,
      sessionTimeout: 30,
      passwordExpiry: 90,
      loginNotifications: true,
      ipWhitelist: [],
      apiKeyRotation: 30,
      createdBy: 'admin-update-script'
    };

    // Update admin document in Firestore
    await admin.firestore().collection('admins').doc(userRecord.uid).set(adminData, { merge: true });
    console.log('✅ Admin document updated in Firestore (admins collection)');

    // Also create/update in users collection for backup
    await admin.firestore().collection('users').doc(userRecord.uid).set({
      ...adminData,
      userType: 'admin',
      isVerified: true,
      status: 'active'
    }, { merge: true });
    console.log('✅ Admin document updated in Firestore (users collection)');

    // Create a document with email as ID for easy lookup
    const emailDocId = adminEmail.replace('@', '_').replace(/\./g, '_');
    await admin.firestore().collection('admins').doc(emailDocId).set({
      ...adminData,
      uid: userRecord.uid,
      lookupType: 'email'
    }, { merge: true });
    console.log('✅ Email lookup document created');

    console.log('\n🎉 Admin permissions updated successfully!');
    console.log('==========================================');
    console.log(`📧 Email: ${adminEmail}`);
    console.log(`🔑 Password: Drive@on0750##`);
    console.log(`🆔 UID: ${userRecord.uid}`);
    console.log(`🛡️ Role: Super Admin`);
    console.log(`✨ Permissions: ALL ADMIN POWERS GRANTED`);
    console.log('\n📝 You can now login to the admin panel with these credentials.');
    console.log('🌐 Admin Panel URL: http://localhost:3001');
    console.log('\n🔐 Available Permissions:');
    adminClaims.permissions.forEach(permission => {
      console.log(`   ✓ ${permission}`);
    });

  } catch (error) {
    console.error('\n❌ Error updating admin permissions:', error.message);
    console.error('Full error:', error);
  } finally {
    process.exit();
  }
}

// Check if Firebase is properly initialized
console.log('🔧 Checking Firebase connection...');
admin.firestore().collection('test').limit(1).get()
  .then(() => {
    console.log('✅ Firebase connection successful\n');
    updateAdminPermissions();
  })
  .catch((error) => {
    console.error('❌ Firebase connection failed:', error.message);
    console.log('\n💡 Please check your serviceAccountKey.json file and Firebase configuration.');
    process.exit(1);
  });
