import 'package:flutter/material.dart';
import 'colors.dart';

class AppStyles {
  // Text Styles - Light Theme
  static TextStyle lightHeadingLarge = const TextStyle(
    color: AppColors.lightText,
    fontSize: 28,
    fontWeight: FontWeight.bold,
  );

  static TextStyle lightHeadingMedium = const TextStyle(
    color: AppColors.lightText,
    fontSize: 24,
    fontWeight: FontWeight.bold,
  );

  static TextStyle lightHeadingSmall = const TextStyle(
    color: AppColors.lightText,
    fontSize: 20,
    fontWeight: FontWeight.bold,
  );

  static TextStyle lightBodyLarge = const TextStyle(
    color: AppColors.lightText,
    fontSize: 16,
    fontWeight: FontWeight.normal,
  );

  static TextStyle lightBodyMedium = const TextStyle(
    color: AppColors.lightText,
    fontSize: 14,
    fontWeight: FontWeight.normal,
  );

  static TextStyle lightBodySmall = const TextStyle(
    color: AppColors.lightTextSecondary,
    fontSize: 12,
    fontWeight: FontWeight.normal,
  );

  // Text Styles - Dark Theme
  static TextStyle darkHeadingLarge = const TextStyle(
    color: AppColors.darkText,
    fontSize: 28,
    fontWeight: FontWeight.bold,
  );

  static TextStyle darkHeadingMedium = const TextStyle(
    color: AppColors.darkText,
    fontSize: 24,
    fontWeight: FontWeight.bold,
  );

  static TextStyle darkHeadingSmall = const TextStyle(
    color: AppColors.darkText,
    fontSize: 20,
    fontWeight: FontWeight.bold,
  );

  static TextStyle darkBodyLarge = const TextStyle(
    color: AppColors.darkText,
    fontSize: 16,
    fontWeight: FontWeight.normal,
  );

  static TextStyle darkBodyMedium = const TextStyle(
    color: AppColors.darkText,
    fontSize: 14,
    fontWeight: FontWeight.normal,
  );

  static TextStyle darkBodySmall = const TextStyle(
    color: AppColors.darkTextSecondary,
    fontSize: 12,
    fontWeight: FontWeight.normal,
  );

  // Common UI Sizes
  static const double paddingSmall = 8.0;
  static const double paddingMedium = 16.0;
  static const double paddingLarge = 24.0;
  static const double paddingXLarge = 32.0;

  static const double borderRadiusSmall = 4.0;
  static const double borderRadiusMedium = 8.0;
  static const double borderRadiusLarge = 12.0;
  static const double borderRadiusXLarge = 16.0;

  // Button Styles
  static ButtonStyle primaryButtonStyle = ButtonStyle(
    backgroundColor: WidgetStateProperty.all(AppColors.primaryYellow),
    foregroundColor: WidgetStateProperty.all(AppColors.lightText),
    padding: WidgetStateProperty.all(
      const EdgeInsets.symmetric(
        horizontal: paddingMedium,
        vertical: paddingSmall,
      ),
    ),
    shape: WidgetStateProperty.all(
      RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(borderRadiusMedium),
      ),
    ),
  );

  static ButtonStyle secondaryButtonStyle = ButtonStyle(
    backgroundColor: WidgetStateProperty.all(Colors.transparent),
    foregroundColor: WidgetStateProperty.all(AppColors.primaryYellow),
    padding: WidgetStateProperty.all(
      const EdgeInsets.symmetric(
        horizontal: paddingMedium,
        vertical: paddingSmall,
      ),
    ),
    shape: WidgetStateProperty.all(
      RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(borderRadiusMedium),
        side: const BorderSide(color: AppColors.primaryYellow, width: 1.5),
      ),
    ),
  );

  // Card Decorations
  static BoxDecoration cardDecoration = BoxDecoration(
    color: AppColors.lightSurface,
    borderRadius: BorderRadius.circular(borderRadiusLarge),
    boxShadow: [
      BoxShadow(
        color: Colors.black.withOpacity(0.05),
        blurRadius: 10,
        offset: const Offset(0, 2),
      ),
    ],
  );

  static BoxDecoration darkCardDecoration = BoxDecoration(
    color: AppColors.darkSurface,
    borderRadius: BorderRadius.circular(borderRadiusLarge),
    boxShadow: [
      BoxShadow(
        color: Colors.black.withOpacity(0.2),
        blurRadius: 10,
        offset: const Offset(0, 2),
      ),
    ],
  );
} 