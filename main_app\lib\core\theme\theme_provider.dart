import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'app_theme.dart';

class ThemeProvider extends ChangeNotifier {
  bool _isDarkMode = false;

  bool get isDarkMode => _isDarkMode;
  ThemeData get themeData => _isDarkMode ? AppTheme.darkTheme : AppTheme.lightTheme;

  ThemeProvider() {
    _initTheme();
  }

  void _initTheme() {
    // Get the system brightness (light or dark)
    final window = WidgetsBinding.instance.platformDispatcher;
    _isDarkMode = window.platformBrightness == Brightness.dark;

    // Update system UI overlay
    _updateSystemUIOverlay();
  }

  void toggleTheme() {
    _isDarkMode = !_isDarkMode;
    _updateSystemUIOverlay();
    notifyListeners();
  }

  void setDarkMode(bool isDarkMode) {
    if (_isDarkMode != isDarkMode) {
      _isDarkMode = isDarkMode;
      _updateSystemUIOverlay();
      notifyListeners();
    }
  }

  void _updateSystemUIOverlay() {
    SystemChrome.setSystemUIOverlayStyle(
      _isDarkMode
          ? SystemUiOverlayStyle.light.copyWith(
              statusBarColor: Colors.transparent,
              systemNavigationBarColor: AppTheme.darkTheme.scaffoldBackgroundColor,
              systemNavigationBarDividerColor: Colors.transparent,
            )
          : SystemUiOverlayStyle.dark.copyWith(
              statusBarColor: Colors.transparent,
              systemNavigationBarColor: AppTheme.lightTheme.scaffoldBackgroundColor,
              systemNavigationBarDividerColor: Colors.transparent,
            ),
    );
  }
} 