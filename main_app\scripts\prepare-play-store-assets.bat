@echo off
REM Prepare Google Play Store Assets for Drive-On App
REM This script helps organize and validate all required assets for Play Store submission

setlocal enabledelayedexpansion

echo 🎨 Drive-On Play Store Assets Preparation
echo ==========================================

REM Create required directories
echo 📁 Creating asset directories...

mkdir "fastlane\metadata\android\en-US\images\icon" 2>nul
mkdir "fastlane\metadata\android\en-US\images\featureGraphic" 2>nul
mkdir "fastlane\metadata\android\en-US\images\phoneScreenshots" 2>nul
mkdir "fastlane\metadata\android\en-US\images\sevenInchScreenshots" 2>nul
mkdir "fastlane\metadata\android\en-US\images\tenInchScreenshots" 2>nul
mkdir "play-store-assets\icons" 2>nul
mkdir "play-store-assets\screenshots" 2>nul
mkdir "play-store-assets\graphics" 2>nul

echo ✅ Directories created successfully!
echo.

REM Check for existing app icon
echo 🔍 Checking for app icons...
if exist "android\app\src\main\res\mipmap-xxxhdpi\ic_launcher.png" (
    echo ✅ Found app icon in mipmap-xxxhdpi
    copy "android\app\src\main\res\mipmap-xxxhdpi\ic_launcher.png" "play-store-assets\icons\app-icon-192.png" >nul
) else (
    echo ⚠️  App icon not found in expected location
)

if exist "android\play_store_512.png" (
    echo ✅ Found 512x512 Play Store icon
    copy "android\play_store_512.png" "play-store-assets\icons\play-store-icon-512.png" >nul
    copy "android\play_store_512.png" "fastlane\metadata\android\en-US\images\icon\icon.png" >nul
) else (
    echo ⚠️  512x512 Play Store icon not found
)

echo.
echo 📋 GOOGLE PLAY STORE ASSET REQUIREMENTS:
echo.
echo 🎯 REQUIRED ASSETS:
echo.
echo 1. APP ICON (512x512 PNG):
echo    - High-resolution app icon
echo    - 32-bit PNG with alpha
echo    - Location: fastlane\metadata\android\en-US\images\icon\icon.png
if exist "fastlane\metadata\android\en-US\images\icon\icon.png" (
    echo    ✅ FOUND
) else (
    echo    ❌ MISSING - Please add 512x512 PNG icon
)
echo.

echo 2. FEATURE GRAPHIC (1024x500 PNG):
echo    - Store banner/header image
echo    - 24-bit PNG (no alpha)
echo    - Location: fastlane\metadata\android\en-US\images\featureGraphic\featureGraphic.png
if exist "fastlane\metadata\android\en-US\images\featureGraphic\featureGraphic.png" (
    echo    ✅ FOUND
) else (
    echo    ❌ MISSING - Please add 1024x500 PNG feature graphic
)
echo.

echo 3. PHONE SCREENSHOTS (minimum 2, maximum 8):
echo    - 16:9 or 9:16 aspect ratio
echo    - Minimum 320px on any side
echo    - Maximum 3840px on any side
echo    - 24-bit PNG or JPEG
echo    - Location: fastlane\metadata\android\en-US\images\phoneScreenshots\
set /a screenshot_count=0
for %%f in ("fastlane\metadata\android\en-US\images\phoneScreenshots\*.png") do set /a screenshot_count+=1
for %%f in ("fastlane\metadata\android\en-US\images\phoneScreenshots\*.jpg") do set /a screenshot_count+=1
if %screenshot_count% geq 2 (
    echo    ✅ FOUND (%screenshot_count% screenshots)
) else (
    echo    ❌ MISSING - Need at least 2 phone screenshots
)
echo.

echo 4. TABLET SCREENSHOTS (optional but recommended):
echo    - 7-inch: fastlane\metadata\android\en-US\images\sevenInchScreenshots\
echo    - 10-inch: fastlane\metadata\android\en-US\images\tenInchScreenshots\
echo.

echo 📝 STORE LISTING METADATA:
echo.
echo 1. App Title: fastlane\metadata\android\en-US\title.txt
if exist "fastlane\metadata\android\en-US\title.txt" (
    echo    ✅ FOUND
) else (
    echo    ❌ MISSING
)

echo 2. Short Description: fastlane\metadata\android\en-US\short_description.txt
if exist "fastlane\metadata\android\en-US\short_description.txt" (
    echo    ✅ FOUND
) else (
    echo    ❌ MISSING
)

echo 3. Full Description: fastlane\metadata\android\en-US\full_description.txt
if exist "fastlane\metadata\android\en-US\full_description.txt" (
    echo    ✅ FOUND
) else (
    echo    ❌ MISSING
)

echo 4. What's New: distribution\whatsnew\en-US\default.txt
if exist "distribution\whatsnew\en-US\default.txt" (
    echo    ✅ FOUND
) else (
    echo    ❌ MISSING
)
echo.

echo 🔧 NEXT STEPS TO COMPLETE ASSETS:
echo.
echo 1. CREATE APP ICON (512x512):
echo    - Design a high-quality 512x512 PNG icon
echo    - Save as: fastlane\metadata\android\en-US\images\icon\icon.png
echo.
echo 2. CREATE FEATURE GRAPHIC (1024x500):
echo    - Design an attractive banner for your app
echo    - Save as: fastlane\metadata\android\en-US\images\featureGraphic\featureGraphic.png
echo.
echo 3. TAKE SCREENSHOTS:
echo    - Use Android emulator or real device
echo    - Capture 2-8 screenshots showing key features
echo    - Save as: fastlane\metadata\android\en-US\images\phoneScreenshots\screenshot_1.png, etc.
echo.
echo 4. REVIEW METADATA:
echo    - Edit store descriptions in fastlane\metadata\android\en-US\
echo    - Update what's new text in distribution\whatsnew\en-US\
echo.
echo 5. VALIDATE ASSETS:
echo    - Run this script again to check completion
echo    - Use Google Play Console asset validator
echo.

echo 📱 SCREENSHOT CAPTURE TIPS:
echo.
echo • Use consistent device frame and orientation
echo • Show your app's key features and benefits
echo • Avoid including status bar with personal info
echo • Use high-quality, clear images
echo • Consider adding captions or highlights
echo.

echo 🎨 DESIGN GUIDELINES:
echo.
echo • App Icon: Simple, recognizable, scalable design
echo • Feature Graphic: Eye-catching, represents your app's purpose
echo • Screenshots: Clean, professional, showcase functionality
echo • Consistent branding across all assets
echo.

echo 🚀 WHEN READY:
echo.
echo 1. Run: scripts\generate-keystore.bat (if not done)
echo 2. Run: scripts\build-production.bat
echo 3. Upload to Google Play Console
echo 4. Complete store listing with these assets
echo.

pause
