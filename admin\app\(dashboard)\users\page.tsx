'use client'

import { useState, useEffect } from 'react'
import {
  Users,
  Search,
  Filter,
  Download,
  Plus,
  MoreHorizontal,
  Edit,
  Trash2,
  Ban,
  CheckCircle,
  XCircle,
  Eye,
} from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { DataTable } from '@/components/ui/data-table'
import { UserModal } from '@/components/users/user-modal'
import { UserFilters } from '@/components/users/user-filters'
import { LoadingSpinner } from '@/components/ui/loading-spinner'
import { UsersTableSkeleton } from '@/components/ui/skeleton-screen'
import { useUsers } from '@/lib/hooks/use-users'
import { formatDate, formatRelativeTime } from '@/lib/utils'

interface User {
  id: string
  email: string
  displayName: string
  role: 'user' | 'admin'
  isActive: boolean
  createdAt: Date
  lastLoginAt: Date | null
  profileImage?: string
  phoneNumber?: string
  city?: string
}

export default function UsersPage() {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedUser, setSelectedUser] = useState<User | null>(null)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [modalMode, setModalMode] = useState<'view' | 'edit' | 'create'>('view')
  const [filters, setFilters] = useState({
    role: 'all',
    status: 'all',
    dateRange: 'all'
  })

  const { users, isLoading, error, updateUser, deleteUser, createUser } = useUsers()

  const filteredUsers = users?.filter(user => {
    const matchesSearch = user.displayName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         user.email.toLowerCase().includes(searchQuery.toLowerCase())

    const matchesRole = filters.role === 'all' || user.role === filters.role
    const matchesStatus = filters.status === 'all' ||
                         (filters.status === 'active' && user.isActive) ||
                         (filters.status === 'inactive' && !user.isActive)

    return matchesSearch && matchesRole && matchesStatus
  }) || []

  const handleCreateUser = () => {
    setSelectedUser(null)
    setModalMode('create')
    setIsModalOpen(true)
  }

  const handleViewUser = (user: User) => {
    setSelectedUser(user)
    setModalMode('view')
    setIsModalOpen(true)
  }

  const handleEditUser = (user: User) => {
    setSelectedUser(user)
    setModalMode('edit')
    setIsModalOpen(true)
  }

  const handleDeleteUser = async (user: User) => {
    if (window.confirm(`Are you sure you want to delete ${user.displayName}?`)) {
      try {
        await deleteUser(user.id)
      } catch (error) {
        console.error('Error deleting user:', error)
      }
    }
  }

  const handleToggleStatus = async (user: User) => {
    try {
      await updateUser(user.id, { isActive: !user.isActive })
    } catch (error) {
      console.error('Error updating user status:', error)
    }
  }

  const columns = [
    {
      key: 'user',
      label: 'User',
      render: (user: User) => (
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
            {user.profileImage ? (
              <img src={user.profileImage} alt={user.displayName} className="w-10 h-10 rounded-full" />
            ) : (
              <span className="text-sm font-medium text-primary-700">
                {user.displayName.charAt(0).toUpperCase()}
              </span>
            )}
          </div>
          <div>
            <p className="font-medium text-gray-900">{user.displayName}</p>
            <p className="text-sm text-gray-500">{user.email}</p>
          </div>
        </div>
      )
    },
    {
      key: 'role',
      label: 'Role',
      render: (user: User) => (
        <Badge variant={user.role === 'admin' ? 'success' : 'secondary'}>
          {user.role}
        </Badge>
      )
    },
    {
      key: 'status',
      label: 'Status',
      render: (user: User) => (
        <Badge variant={user.isActive ? 'success' : 'error'}>
          {user.isActive ? 'Active' : 'Inactive'}
        </Badge>
      )
    },
    {
      key: 'city',
      label: 'City',
      render: (user: User) => user.city || 'N/A'
    },

    {
      key: 'createdAt',
      label: 'Joined',
      render: (user: User) => formatRelativeTime(user.createdAt)
    },
    {
      key: 'lastLoginAt',
      label: 'Last Login',
      render: (user: User) => user.lastLoginAt ? formatRelativeTime(user.lastLoginAt) : 'Never'
    },
    {
      key: 'actions',
      label: 'Actions',
      render: (user: User) => (
        <div className="flex items-center space-x-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleViewUser(user)}
          >
            <Eye className="w-4 h-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleEditUser(user)}
          >
            <Edit className="w-4 h-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleToggleStatus(user)}
          >
            {user.isActive ? (
              <Ban className="w-4 h-4 text-red-600" />
            ) : (
              <CheckCircle className="w-4 h-4 text-green-600" />
            )}
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleDeleteUser(user)}
          >
            <Trash2 className="w-4 h-4 text-red-600" />
          </Button>
        </div>
      )
    }
  ]

  // Show skeleton immediately if no cached data
  if (isLoading && (!users || users.length === 0)) {
    return <UsersTableSkeleton />
  }

  return (
    <div className="space-y-6 instant-load">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-foreground">User Management</h1>
          <p className="text-muted-foreground">Manage and monitor app users</p>
        </div>
        <Button onClick={handleCreateUser} className="gradient-primary">
          <Plus className="w-4 h-4 mr-2" />
          Add User
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Users</p>
                <p className="text-2xl font-bold text-gray-900">{users?.length || 0}</p>
              </div>
              <Users className="w-8 h-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Active Users</p>
                <p className="text-2xl font-bold text-gray-900">
                  {filteredUsers.filter(u => u.isActive).length}
                </p>
              </div>
              <CheckCircle className="w-8 h-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Inactive Users</p>
                <p className="text-2xl font-bold text-gray-900">
                  {filteredUsers.filter(u => !u.isActive).length}
                </p>
              </div>
              <XCircle className="w-8 h-8 text-red-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Admins</p>
                <p className="text-2xl font-bold text-gray-900">
                  {filteredUsers.filter(u => u.role === 'admin').length}
                </p>
              </div>
              <Badge className="w-8 h-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <CardTitle>Users</CardTitle>
          <CardDescription>
            Manage user accounts, roles, and permissions
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search users by name or email..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <UserFilters filters={filters} onFiltersChange={setFilters} />

            <Button variant="outline">
              <Download className="w-4 h-4 mr-2" />
              Export
            </Button>
          </div>

          <DataTable
            data={filteredUsers}
            columns={columns}
            searchQuery={searchQuery}
            isLoading={isLoading}
          />
        </CardContent>
      </Card>

      {/* User Modal */}
      <UserModal
        user={selectedUser}
        mode={modalMode}
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onSave={async (userData) => {
          try {
            if (modalMode === 'create') {
              await createUser(userData)
            } else if (modalMode === 'edit' && selectedUser) {
              await updateUser(selectedUser.id, userData)
            }
            setIsModalOpen(false)
          } catch (error) {
            console.error('Error saving user:', error)
          }
        }}
      />
    </div>
  )
}
