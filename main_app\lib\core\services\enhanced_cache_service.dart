import 'dart:convert';
import 'offline_storage_service.dart';
import '../utils/app_logger.dart';

/// Enhanced cache service that combines memory, Hive, and SQLite storage
/// 
/// Features:
/// - Multi-layer caching (Memory -> Hive -> SQLite -> Network)
/// - Intelligent cache invalidation
/// - Offline-first data access
/// - Background synchronization
/// - Conflict resolution
class EnhancedCacheService {
  static final EnhancedCacheService _instance = EnhancedCacheService._internal();
  factory EnhancedCacheService() => _instance;
  EnhancedCacheService._internal();

  static const String _tag = 'EnhancedCacheService';
  static final AppLogger _logger = AppLogger(_tag);

  final OfflineStorageService _offlineStorage = OfflineStorageService();
  
  // Memory cache for frequently accessed data
  final Map<String, _CacheEntry> _memoryCache = {};
  
  // Cache configuration
  static const int _maxMemoryCacheSize = 200;
  static const int _maxMemoryCacheBytes = 20 * 1024 * 1024; // 20MB
  static const Duration _defaultMemoryTtl = Duration(minutes: 15);
  static const Duration _defaultHiveTtl = Duration(hours: 6);
  static const Duration _defaultSqliteTtl = Duration(days: 7);

  int _currentMemoryBytes = 0;

  /// Initialize the enhanced cache service
  Future<void> initialize() async {
    try {
      await _offlineStorage.initialize();
      _cleanupExpiredMemoryCache();
      _logger.info('Enhanced cache service initialized');
    } catch (e) {
      _logger.error('Failed to initialize enhanced cache service', error: e);
      rethrow;
    }
  }

  /// Get data with multi-layer caching strategy
  Future<T?> get<T>({
    required String key,
    required Future<T> Function() fetcher,
    Duration? memoryTtl,
    Duration? hiveTtl,
    Duration? sqliteTtl,
    bool useMemoryCache = true,
    bool useHiveCache = true,
    bool useSqliteCache = true,
    bool forceRefresh = false,
  }) async {
    memoryTtl ??= _defaultMemoryTtl;
    hiveTtl ??= _defaultHiveTtl;
    sqliteTtl ??= _defaultSqliteTtl;

    if (forceRefresh) {
      return await _fetchAndCache(key, fetcher, memoryTtl, hiveTtl, sqliteTtl, 
          useMemoryCache, useHiveCache, useSqliteCache);
    }

    // Try memory cache first
    if (useMemoryCache) {
      final memoryResult = _getFromMemoryCache<T>(key);
      if (memoryResult != null) {
        _logger.debug('Cache hit (memory): $key');
        return memoryResult;
      }
    }

    // Try Hive cache
    if (useHiveCache) {
      final hiveResult = _offlineStorage.getCache<T>(key);
      if (hiveResult != null) {
        _logger.debug('Cache hit (Hive): $key');
        // Store in memory cache for faster access
        if (useMemoryCache) {
          _putInMemoryCache(key, hiveResult, memoryTtl);
        }
        return hiveResult;
      }
    }

    // Try SQLite cache for structured data
    if (useSqliteCache) {
      final sqliteResult = await _getFromSqliteCache<T>(key);
      if (sqliteResult != null) {
        _logger.debug('Cache hit (SQLite): $key');
        // Store in upper cache layers
        if (useHiveCache) {
          await _offlineStorage.putCache(key, sqliteResult, ttl: hiveTtl);
        }
        if (useMemoryCache) {
          _putInMemoryCache(key, sqliteResult, memoryTtl);
        }
        return sqliteResult;
      }
    }

    // Cache miss - fetch from network
    return await _fetchAndCache(key, fetcher, memoryTtl, hiveTtl, sqliteTtl,
        useMemoryCache, useHiveCache, useSqliteCache);
  }

  /// Fetch data and store in all cache layers
  Future<T?> _fetchAndCache<T>(
    String key,
    Future<T> Function() fetcher,
    Duration memoryTtl,
    Duration hiveTtl,
    Duration sqliteTtl,
    bool useMemoryCache,
    bool useHiveCache,
    bool useSqliteCache,
  ) async {
    _logger.debug('Cache miss: $key');
    
    try {
      final data = await fetcher();
      
      // Store in all cache layers
      if (useMemoryCache) {
        _putInMemoryCache(key, data, memoryTtl);
      }
      if (useHiveCache) {
        await _offlineStorage.putCache(key, data, ttl: hiveTtl);
      }
      if (useSqliteCache) {
        await _putInSqliteCache(key, data, sqliteTtl);
      }
      
      return data;
    } catch (e) {
      _logger.error('Failed to fetch data for key: $key', error: e);
      
      // If online fetch fails, try to return stale data from cache
      if (!_offlineStorage.isOnline) {
        return await _getStaleData<T>(key);
      }
      
      rethrow;
    }
  }

  /// Get stale data from any available cache layer
  Future<T?> _getStaleData<T>(String key) async {
    // Try Hive first (even if expired)
    final hiveData = _offlineStorage.getCache<T>('stale_$key');
    if (hiveData != null) {
      _logger.debug('Returning stale data from Hive: $key');
      return hiveData;
    }

    // Try SQLite
    final sqliteData = await _getFromSqliteCache<T>('stale_$key');
    if (sqliteData != null) {
      _logger.debug('Returning stale data from SQLite: $key');
      return sqliteData;
    }

    return null;
  }

  /// Get data from memory cache
  T? _getFromMemoryCache<T>(String key) {
    final entry = _memoryCache[key];
    if (entry == null) return null;

    if (entry.isExpired) {
      _memoryCache.remove(key);
      _currentMemoryBytes -= entry.sizeBytes;
      return null;
    }

    return entry.data as T?;
  }

  /// Put data in memory cache
  void _putInMemoryCache<T>(String key, T data, Duration ttl) {
    try {
      final serialized = _serializeData(data);
      final sizeBytes = serialized.length;
      
      // Check if data is too large
      if (sizeBytes > _maxMemoryCacheBytes) {
        _logger.warning('Data too large for memory cache: $key ($sizeBytes bytes)');
        return;
      }
      
      // Remove existing entry if present
      final existing = _memoryCache[key];
      if (existing != null) {
        _currentMemoryBytes -= existing.sizeBytes;
      }
      
      // Evict items if necessary
      _evictMemoryCacheIfNeeded(sizeBytes);
      
      // Add new entry
      _memoryCache[key] = _CacheEntry(
        data: data,
        expiresAt: DateTime.now().add(ttl),
        sizeBytes: sizeBytes,
      );
      _currentMemoryBytes += sizeBytes;
      
    } catch (e) {
      _logger.error('Failed to put data in memory cache: $key', error: e);
    }
  }

  /// Get data from SQLite cache
  Future<T?> _getFromSqliteCache<T>(String key) async {
    // This is a placeholder - actual implementation would depend on data type
    // and would query the appropriate SQLite table
    return null;
  }

  /// Put data in SQLite cache
  Future<void> _putInSqliteCache<T>(String key, T data, Duration ttl) async {
    // This is a placeholder - actual implementation would depend on data type
    // and would insert/update the appropriate SQLite table
  }

  /// Evict memory cache items if needed
  void _evictMemoryCacheIfNeeded(int newItemSize) {
    // Evict by size
    while (_currentMemoryBytes + newItemSize > _maxMemoryCacheBytes && _memoryCache.isNotEmpty) {
      _evictLeastRecentlyUsed();
    }
    
    // Evict by count
    while (_memoryCache.length >= _maxMemoryCacheSize) {
      _evictLeastRecentlyUsed();
    }
  }

  /// Evict least recently used item from memory cache
  void _evictLeastRecentlyUsed() {
    if (_memoryCache.isEmpty) return;
    
    String? oldestKey;
    DateTime? oldestTime;
    
    for (final entry in _memoryCache.entries) {
      if (oldestTime == null || entry.value.expiresAt.isBefore(oldestTime)) {
        oldestTime = entry.value.expiresAt;
        oldestKey = entry.key;
      }
    }
    
    if (oldestKey != null) {
      final removed = _memoryCache.remove(oldestKey);
      if (removed != null) {
        _currentMemoryBytes -= removed.sizeBytes;
      }
    }
  }

  /// Clean up expired memory cache entries
  void _cleanupExpiredMemoryCache() {
    final now = DateTime.now();
    final expiredKeys = <String>[];
    
    for (final entry in _memoryCache.entries) {
      if (entry.value.expiresAt.isBefore(now)) {
        expiredKeys.add(entry.key);
      }
    }
    
    for (final key in expiredKeys) {
      final removed = _memoryCache.remove(key);
      if (removed != null) {
        _currentMemoryBytes -= removed.sizeBytes;
      }
    }
    
    if (expiredKeys.isNotEmpty) {
      _logger.debug('Cleaned up ${expiredKeys.length} expired memory cache entries');
    }
  }

  /// Serialize data for storage
  String _serializeData<T>(T data) {
    try {
      return jsonEncode(data);
    } catch (e) {
      // Fallback to toString for non-serializable objects
      return data.toString();
    }
  }

  /// Put data directly into cache
  Future<void> put<T>({
    required String key,
    required T data,
    Duration? memoryTtl,
    Duration? hiveTtl,
    Duration? sqliteTtl,
    bool useMemoryCache = true,
    bool useHiveCache = true,
    bool useSqliteCache = true,
  }) async {
    memoryTtl ??= _defaultMemoryTtl;
    hiveTtl ??= _defaultHiveTtl;
    sqliteTtl ??= _defaultSqliteTtl;

    if (useMemoryCache) {
      _putInMemoryCache(key, data, memoryTtl);
    }
    if (useHiveCache) {
      await _offlineStorage.putCache(key, data, ttl: hiveTtl);
    }
    if (useSqliteCache) {
      await _putInSqliteCache(key, data, sqliteTtl);
    }
  }

  /// Remove data from all cache layers
  Future<void> remove(String key) async {
    _memoryCache.remove(key);
    // Remove from Hive and SQLite would be implemented here
  }

  /// Clear all cache layers
  Future<void> clear() async {
    _memoryCache.clear();
    _currentMemoryBytes = 0;
    await _offlineStorage.clearCache();
  }

  /// Get cache statistics
  Map<String, dynamic> getStats() {
    return {
      'memoryItems': _memoryCache.length,
      'memoryBytes': _currentMemoryBytes,
      'maxMemoryItems': _maxMemoryCacheSize,
      'maxMemoryBytes': _maxMemoryCacheBytes,
      'isOnline': _offlineStorage.isOnline,
    };
  }

  /// Dispose resources
  Future<void> dispose() async {
    await _offlineStorage.dispose();
  }
}

/// Cache entry for memory storage
class _CacheEntry {
  final dynamic data;
  final DateTime expiresAt;
  final int sizeBytes;

  _CacheEntry({
    required this.data,
    required this.expiresAt,
    required this.sizeBytes,
  });

  bool get isExpired => DateTime.now().isAfter(expiresAt);
}
