'use client'

import { memo, Suspense } from 'react'
import { LoadingSpinner } from '@/components/ui/loading-spinner'

interface PageWrapperProps {
  children: React.ReactNode
  title?: string
  className?: string
}

function PageWrapperComponent({ children, title, className = '' }: PageWrapperProps) {
  return (
    <div className={`instant-load ${className}`}>
      {title && (
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-foreground">{title}</h1>
        </div>
      )}
      <Suspense fallback={
        <div className="flex items-center justify-center py-4">
          <LoadingSpinner size="sm" />
        </div>
      }>
        {children}
      </Suspense>
    </div>
  )
}

// Memoize to prevent unnecessary re-renders
export const PageWrapper = memo(PageWrapperComponent)
