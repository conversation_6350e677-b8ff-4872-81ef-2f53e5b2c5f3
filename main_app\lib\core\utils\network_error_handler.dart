import 'dart:async';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'error_handler.dart';
import 'app_logger.dart';

/// A utility class for handling network-related errors.
class NetworkErrorHandler {
  static final AppLogger _logger = AppLogger('NetworkErrorHandler');

  /// Handle HTTP errors and convert them to user-friendly messages
  static String handleHttpError(http.Response response) {
    _logger.error('HTTP Error: ${response.statusCode} - ${response.reasonPhrase}');
    
    switch (response.statusCode) {
      case 400:
        return 'Invalid request. Please try again.';
      case 401:
        return 'You are not authorized. Please log in again.';
      case 403:
        return 'Access denied. You don\'t have permission to access this content.';
      case 404:
        return 'Content not found. Please try again later.';
      case 429:
        return 'Too many requests. Please try again later.';
      case 500:
      case 501:
      case 502:
      case 503:
        return 'Server error. Please try again later.';
      default:
        return 'An unexpected error occurred. Please try again later.';
    }
  }

  /// Execute a network request with built-in error handling and retries
  static Future<T> executeWithRetry<T>({
    required Future<T> Function() request,
    required Function(Object error) onError,
    int maxRetries = 3,
    Duration retryDelay = const Duration(seconds: 2),
  }) async {
    int attempts = 0;
    
    while (attempts < maxRetries) {
      try {
        return await request();
      } catch (e) {
        attempts++;
        _logger.warning('Network request failed (attempt $attempts/$maxRetries): $e');
        
        final bool shouldRetry = _shouldRetry(e);
        
        if (shouldRetry && attempts < maxRetries) {
          await Future.delayed(retryDelay * attempts);
          continue;
        }
        
        // Report the error after all retries have failed
        final StackTrace stackTrace = StackTrace.current;
        ErrorHandler.reportHandledException(
          e,
          stackTrace,
          context: 'Network request after $attempts retries',
          severity: ErrorSeverity.medium,
        );
        
        // Let the caller handle the error
        onError(e);
        rethrow;
      }
    }
    
    // This should not be reached as the loop will either return a value or throw an exception
    throw Exception('Maximum retry attempts reached');
  }

  /// Determine if we should retry a failed request based on the error type
  static bool _shouldRetry(Object error) {
    // Retry for common temporary network issues
    if (error is SocketException) {
      return true;
    }
    
    if (error is TimeoutException) {
      return true;
    }
    
    if (error is http.ClientException) {
      // Some client exceptions might be temporary
      return true;
    }
    
    // If it's an HTTP exception, only retry on server errors (5xx) and rate limiting (429)
    if (error is HttpException) {
      final message = error.toString().toLowerCase();
      return message.contains('500') || 
             message.contains('502') || 
             message.contains('503') || 
             message.contains('504') ||
             message.contains('429');
    }
    
    // Don't retry for other types of errors
    return false;
  }
} 