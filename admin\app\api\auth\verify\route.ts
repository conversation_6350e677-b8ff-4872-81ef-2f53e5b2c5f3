import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const { idToken } = await request.json()

    if (!idToken) {
      return NextResponse.json(
        { error: 'ID token is required' },
        { status: 400 }
      )
    }

    // Handle special case for initialization check
    if (idToken === 'check-persisted') {
      return NextResponse.json(
        { error: 'No valid token provided' },
        { status: 401 }
      )
    }

    // For the admin panel, we'll verify the token by checking if it's valid
    // and return the appropriate user data
    try {
      // Basic token validation - check if it looks like a JWT
      const tokenParts = idToken.split('.')
      if (tokenParts.length !== 3) {
        throw new Error('Invalid token format')
      }

      // Additional validation - check if token is not empty or just whitespace
      if (!idToken.trim() || idToken.length < 10) {
        throw new Error('Token too short or empty')
      }

      // For now, we'll accept any valid-looking token and return admin user data
      // In a production environment, you would verify this with Firebase Admin SDK
      return NextResponse.json({
        success: true,
        user: {
          uid: 'bMNaL2YKHVYhkMgLtPKVu39slfC2',
          email: '<EMAIL>',
          displayName: 'Drive-On Admin',
          photoURL: null,
          permissions: [
            'all',
            'users.read',
            'users.write',
            'users.delete',
            'drivers.read',
            'drivers.write',
            'drivers.delete',
            'drivers.verify',
            'queries.read',
            'queries.write',
            'queries.delete',
            'chats.read',
            'chats.write',
            'chats.moderate',
            'analytics.read',
            'settings.read',
            'settings.write',
            'system.admin'
          ],
          role: 'super_admin',
          lastLoginAt: new Date()
        }
      })

    } catch (tokenError) {
      throw new Error('Invalid token')
    }

  } catch (error: any) {
    console.error('Token verification error:', error)

    return NextResponse.json(
      {
        error: 'Invalid token or insufficient permissions',
        message: error.message
      },
      { status: 401 }
    )
  }
}
