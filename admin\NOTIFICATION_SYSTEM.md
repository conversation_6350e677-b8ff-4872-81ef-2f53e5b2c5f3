# Admin Panel Notification System

A comprehensive real-time notification system for the admin panel that alerts administrators of all important activities from the main app.

## 🔔 Features

### Real-Time Notifications
- **Driver Requests**: New driver applications submitted
- **Forum Messages**: New messages in forums
- **Support Queries**: New support tickets and urgent issues
- **User Registrations**: New user sign-ups
- **System Alerts**: Important system notifications

### Notification Types
- **Driver Request** (`driver_request`): New driver applications
- **Forum Message** (`forum_message`): New forum activity
- **Query** (`query`): Support queries and tickets
- **User Registration** (`user_registration`): New user sign-ups
- **System** (`system`): System-level notifications
- **Urgent** (`urgent`): Critical alerts requiring immediate attention

### Priority Levels
- **Low**: General information (user registrations)
- **Medium**: Standard activity (forum messages)
- **High**: Important activity (driver requests, support queries)
- **Urgent**: Critical issues requiring immediate attention

### Notification Channels
- **Browser Notifications**: Native browser notifications with sound
- **In-App Alerts**: Real-time UI notifications with badge counts
- **Sound Alerts**: Customizable notification sounds
- **Visual Indicators**: Animated bell icon and unread badges

## 🚀 Components

### Core Service
- **`NotificationService`**: Singleton service managing all notifications
- **Real-time Listeners**: Firebase listeners for live data updates
- **Browser API Integration**: Native notification support
- **Sound Generation**: Web Audio API for notification sounds

### UI Components
- **`NotificationBell`**: Animated bell icon with unread count badge
- **`NotificationDropdown`**: Quick access dropdown with recent notifications
- **`NotificationPage`**: Full notifications management page
- **`NotificationSettings`**: Comprehensive settings panel

### Hooks
- **`useNotifications`**: Main hook for notification management
- **`useNotificationSettings`**: Settings management hook

## 📱 User Interface

### Header Bell Icon
- Animated bell icon that rings when new notifications arrive
- Red badge showing unread count (99+ for large numbers)
- Dropdown with recent notifications and quick actions

### Notification Dropdown
- **Tabs**: All, Unread, Drivers, Forums, Queries
- **Quick Actions**: Mark as read, mark all as read
- **Direct Links**: Click to navigate to relevant pages
- **Time Stamps**: Relative time display (e.g., "2 minutes ago")

### Full Notifications Page
- **Advanced Filtering**: By type, priority, read status
- **Search Functionality**: Search through notification content
- **Bulk Actions**: Mark multiple notifications as read
- **Detailed View**: Full notification details with context

## ⚙️ Settings & Configuration

### Notification Settings
- **Sound Enabled**: Toggle notification sounds on/off
- **Browser Notifications**: Enable/disable browser notifications
- **Email Notifications**: Email notification preferences
- **Alert Types**: Enable/disable specific notification types
- **Quiet Hours**: Set quiet hours to suppress non-urgent notifications
- **Urgent Only Mode**: Show only urgent notifications

### Customizable Alerts
- **Driver Request Alerts**: New driver applications
- **Forum Message Alerts**: New forum activity
- **Query Alerts**: Support ticket notifications
- **User Registration Alerts**: New user sign-ups
- **System Alerts**: System-level notifications

## 🔧 Technical Implementation

### Real-Time Data Flow
1. **Firebase Listeners**: Monitor collections for changes
2. **Change Detection**: Identify new documents or updates
3. **Notification Creation**: Generate notification objects
4. **Multi-Channel Delivery**: Browser, sound, and UI notifications
5. **Persistence**: Store notifications in Firestore
6. **State Management**: Update UI state and unread counts

### Firebase Collections Monitored
- **`driver_requests`**: New driver applications
- **`forums`**: Forum message updates (lastMessageTime changes)
- **`queries`**: New support queries
- **`users`**: New user registrations

### Notification Storage
- **Collection**: `admin_notifications`
- **Real-time Sync**: Notifications stored and synced across sessions
- **Read Status**: Track read/unread status per notification
- **Metadata**: Priority, type, timestamps, related data

## 🎵 Sound System

### Web Audio API
- **Programmatic Sounds**: Generated using Web Audio API
- **No External Files**: Self-contained audio generation
- **Customizable Tones**: Different tones for different priorities
- **Volume Control**: Adjustable notification volume

### Sound Characteristics
- **Frequency**: 800Hz to 600Hz sweep
- **Duration**: 300ms notification tone
- **Volume**: 30% of system volume
- **Repeat**: Subtle repeat every 3 seconds for unread notifications

## 📊 Analytics & Monitoring

### Notification Metrics
- **Total Notifications**: Count of all notifications
- **Unread Count**: Real-time unread notification count
- **Type Distribution**: Breakdown by notification type
- **Priority Distribution**: Breakdown by priority level

### Performance Monitoring
- **Listener Health**: Monitor Firebase listener connections
- **Delivery Success**: Track notification delivery rates
- **User Engagement**: Monitor notification interaction rates

## 🔐 Security & Permissions

### Browser Permissions
- **Notification Permission**: Request browser notification access
- **Audio Permission**: Web Audio API access
- **Graceful Degradation**: Fallback when permissions denied

### Data Security
- **Admin Only**: Notifications only for authenticated admin users
- **Secure Storage**: Encrypted notification data in Firestore
- **Access Control**: Role-based notification access

## 🚀 Usage Examples

### Basic Usage
```typescript
import { useNotifications } from '@/lib/hooks/use-notifications'

function MyComponent() {
  const { notifications, unreadCount, markAsRead } = useNotifications()
  
  return (
    <div>
      <p>Unread: {unreadCount}</p>
      {notifications.map(notification => (
        <div key={notification.id} onClick={() => markAsRead(notification.id)}>
          {notification.title}
        </div>
      ))}
    </div>
  )
}
```

### Settings Management
```typescript
import { useNotificationSettings } from '@/lib/hooks/use-notifications'

function SettingsComponent() {
  const { settings, updateSettings } = useNotificationSettings()
  
  const toggleSound = () => {
    updateSettings({ soundEnabled: !settings.soundEnabled })
  }
  
  return (
    <button onClick={toggleSound}>
      Sound: {settings.soundEnabled ? 'On' : 'Off'}
    </button>
  )
}
```

### Manual Notification
```typescript
import { notificationService } from '@/lib/services/notification-service'

// Send test notification
await notificationService.sendTestNotification()
```

## 🔄 Real-Time Updates

### Firebase Listeners
- **Automatic Setup**: Listeners automatically configured on service initialization
- **Change Detection**: Monitors document additions and modifications
- **Efficient Queries**: Optimized queries with proper indexing
- **Connection Management**: Automatic reconnection and error handling

### Update Triggers
- **New Documents**: Triggers when new documents are added
- **Field Changes**: Monitors specific field changes (e.g., lastMessageTime)
- **Time-Based Filtering**: Only recent changes trigger notifications
- **Duplicate Prevention**: Prevents duplicate notifications for same event

## 📱 Mobile Responsiveness

### Responsive Design
- **Mobile-First**: Optimized for mobile admin access
- **Touch-Friendly**: Large touch targets for mobile interaction
- **Adaptive Layout**: Responsive notification dropdown and pages
- **Performance**: Optimized for mobile network conditions

## 🔧 Troubleshooting

### Common Issues
1. **No Sound**: Check browser audio permissions and settings
2. **No Browser Notifications**: Verify notification permissions
3. **Missing Notifications**: Check Firebase listener connections
4. **Performance Issues**: Monitor listener count and cleanup

### Debug Mode
Enable console logging for debugging:
```typescript
// In notification service
console.log('Notification triggered:', notification)
console.log('Listener status:', listenerStatus)
```

## 🚀 Future Enhancements

### Planned Features
- **Push Notifications**: Mobile push notification support
- **Email Integration**: Email notification delivery
- **Slack Integration**: Slack channel notifications
- **Custom Sounds**: Upload custom notification sounds
- **Advanced Filtering**: More granular notification filters
- **Notification Templates**: Customizable notification templates
- **Analytics Dashboard**: Detailed notification analytics

### Performance Improvements
- **Batch Processing**: Batch multiple notifications
- **Caching**: Cache notification data for better performance
- **Compression**: Compress notification payloads
- **Offline Support**: Queue notifications for offline scenarios

## 📋 Testing

### Test Scenarios
1. **New Driver Request**: Submit driver application from main app
2. **Forum Activity**: Post message in forum from main app
3. **Support Query**: Create new support ticket
4. **User Registration**: Register new user account
5. **Settings Changes**: Test all notification settings
6. **Browser Permissions**: Test with/without permissions
7. **Quiet Hours**: Test quiet hours functionality
8. **Multiple Tabs**: Test notifications across multiple admin tabs

### Test Commands
```bash
# Run notification tests
npm run test:notifications

# Test notification service
npm run test:service

# Test UI components
npm run test:components
```

## 📞 Support

For issues or questions about the notification system:
1. Check the troubleshooting section above
2. Review Firebase console for listener errors
3. Check browser console for JavaScript errors
4. Verify notification permissions in browser settings

The notification system provides comprehensive real-time alerts for all admin panel activities, ensuring administrators never miss important updates from the main application.
