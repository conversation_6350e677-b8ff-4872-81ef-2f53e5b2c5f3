@echo off
REM Firebase App Distribution deployment script (Windows)
REM This script builds and distributes Android app to testers

echo 🚀 Starting Android deployment to Firebase App Distribution...

REM Check if Firebase CLI is installed
firebase --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Firebase CLI is not installed. Please install it first:
    echo npm install -g firebase-tools
    pause
    exit /b 1
)

REM Check if Flutter is installed
flutter --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Flutter is not installed. Please install Flutter first.
    pause
    exit /b 1
)

REM Get Flutter dependencies
echo 📦 Getting Flutter dependencies...
flutter pub get
if %errorlevel% neq 0 (
    echo ❌ Failed to get Flutter dependencies
    pause
    exit /b 1
)

REM Clean and build
echo 🧹 Cleaning previous builds...
flutter clean
flutter pub get

REM Build APK
echo 📱 Building APK for distribution...
flutter build apk --release
if %errorlevel% neq 0 (
    echo ❌ APK build failed. Cannot proceed with distribution.
    pause
    exit /b 1
)

REM Check if APK was built successfully
if not exist "build\app\outputs\flutter-apk\app-release.apk" (
    echo ❌ APK build failed. Cannot proceed with distribution.
    pause
    exit /b 1
)

echo 📊 APK built successfully

REM Deploy to Firebase App Distribution
echo 🚀 Deploying to Firebase App Distribution...

REM Create release notes
set "current_date=%date% %time%"
set "release_notes=🚀 Drive-On Main App - Release Build\n\n📅 Build Date: %current_date%\n📱 Platform: Android\n\n🔥 What's New:\n- Latest features and improvements\n- Bug fixes and performance optimizations\n- Enhanced user experience\n\n📲 Installation:\n1. Download and install the APK\n2. Enable 'Install from unknown sources' if prompted\n3. Open the app and enjoy!\n\n⚠️ Note: This is a test build for internal testing."

firebase appdistribution:distribute build\app\outputs\flutter-apk\app-release.apk --app 1:206767723448:android:6a8e1d9c3c8992992d754d --groups "testers" --release-notes "%release_notes%"

if %errorlevel% equ 0 (
    echo ✅ Successfully deployed to Firebase App Distribution!
    echo.
    echo 📱 Your app has been distributed to testers
    echo 🔗 Check Firebase Console: https://console.firebase.google.com/project/drive-on-b2af8/appdistribution
    echo.
    echo 📧 Testers will receive an email notification
    echo 📲 They can download the app from the Firebase App Distribution portal
) else (
    echo ❌ Deployment failed. Please check your Firebase configuration.
    pause
    exit /b 1
)

pause
