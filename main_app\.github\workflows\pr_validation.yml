name: Pull Request Validation

on:
  pull_request:
    branches: [ main, develop ]
    paths-ignore:
      - '**.md'
      - 'docs/**'
      - '.gitignore'

jobs:
  analyze:
    name: Analyze Code
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      
      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.16.x'
          channel: 'stable'
          cache: true
      
      - name: Install dependencies
        run: flutter pub get
      
      - name: Verify formatting
        run: dart format --output=none --set-exit-if-changed .
      
      - name: Analyze project source
        run: flutter analyze --no-fatal-infos

  unit_test:
    name: Unit and Widget Tests
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      
      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.16.x'
          channel: 'stable'
          cache: true
      
      - name: Install dependencies
        run: flutter pub get
      
      - name: Create .env files
        run: |
          cp .env.example .env.development
          cp .env.example .env.staging
          cp .env.example .env.production
      
      - name: Run unit and widget tests
        run: flutter test --coverage
      
      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage/lcov.info
          fail_ci_if_error: false

  integration_test:
    name: Integration Tests
    runs-on: macos-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      
      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.16.x'
          channel: 'stable'
          cache: true
      
      - name: Install dependencies
        run: flutter pub get
      
      - name: Create .env files
        run: |
          cp .env.example .env.development
          cp .env.example .env.staging
          cp .env.example .env.production
      
      - name: Setup iOS simulator
        run: |
          UDID=$(xcrun simctl list devices | grep "iPhone 14" | grep "available" | sort | head -1 | awk -F'[()]' '{print $2}')
          xcrun simctl boot "$UDID"
          echo "Running on simulator with UDID: $UDID"
      
      - name: Run integration tests
        run: flutter test integration_test/app_test.dart -d iPhone

  android_build:
    name: Build Android APK
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      
      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.16.x'
          channel: 'stable'
          cache: true
      
      - name: Install dependencies
        run: flutter pub get
      
      - name: Create .env files
        run: |
          cp .env.example .env.development
          cp .env.example .env.staging
          cp .env.example .env.production
      
      - name: Setup Java
        uses: actions/setup-java@v3
        with:
          distribution: 'zulu'
          java-version: '17'
      
      - name: Build APK (Development)
        run: flutter build apk --debug --dart-define=ENVIRONMENT=development
      
      - name: Upload APK artifact
        uses: actions/upload-artifact@v3
        with:
          name: debug-apk
          path: build/app/outputs/flutter-apk/app-debug.apk 