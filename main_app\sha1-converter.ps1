# PowerShell script to convert SHA-1 fingerprint to Firebase format

Write-Host "🔑 SHA-1 Fingerprint Converter for Firebase" -ForegroundColor Yellow
Write-Host "=============================================" -ForegroundColor Yellow
Write-Host ""

# Original SHA-1 from keytool
$originalSHA1 = "85:C3:07:8A:88:35:3D:1D:15:88:76:93:84:F8:D7:17:41:A9:4B:A3"

Write-Host "📋 Original SHA-1 (from keytool):" -ForegroundColor Cyan
Write-Host $originalSHA1
Write-Host ""

# Remove colons and convert to lowercase
$firebaseSHA1 = $originalSHA1.Replace(":", "").ToLower()

Write-Host "🔥 Firebase Format (for copy-paste):" -ForegroundColor Green
Write-Host $firebaseSHA1
Write-Host ""

# Also show SHA-256 for completeness
$originalSHA256 = "3A:F8:D8:2A:98:99:71:9C:4D:EC:F0:1A:9F:B8:9A:D9:9E:FF:B0:00:8C:A0:2C:64:C5:E9:1B:5E:30:F7:BC:5B"
$firebaseSHA256 = $originalSHA256.Replace(":", "").ToLower()

Write-Host "📋 SHA-256 (also useful for Firebase):" -ForegroundColor Cyan
Write-Host $firebaseSHA256
Write-Host ""

Write-Host "📋 COPY THESE TO FIREBASE CONSOLE:" -ForegroundColor Yellow
Write-Host "SHA-1:  $firebaseSHA1" -ForegroundColor White
Write-Host "SHA-256: $firebaseSHA256" -ForegroundColor White
Write-Host ""

Write-Host "🌐 Firebase Console URL:" -ForegroundColor Cyan
Write-Host "https://console.firebase.google.com/project/drive-on-b2af8/settings/general/" -ForegroundColor Blue
Write-Host ""

# Copy to clipboard if possible
try {
    $firebaseSHA1 | Set-Clipboard
    Write-Host "✅ SHA-1 copied to clipboard!" -ForegroundColor Green
} catch {
    Write-Host "⚠️  Could not copy to clipboard, please copy manually" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Press any key to continue..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
