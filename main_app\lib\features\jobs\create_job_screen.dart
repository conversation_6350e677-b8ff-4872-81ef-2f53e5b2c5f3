import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../core/core.dart';
import '../../core/security/input_validator.dart';
import '../../core/services/email_uniqueness_service.dart';
import 'models/job_model.dart';

class CreateJobScreen extends StatefulWidget {
  const CreateJobScreen({super.key});

  @override
  State<CreateJobScreen> createState() => _CreateJobScreenState();
}

class _CreateJobScreenState extends State<CreateJobScreen> {
  final _formKey = GlobalKey<FormState>();
  final _dutyHoursController = TextEditingController();
  final _emailController = TextEditingController();

  String? _title;
  String? _type = 'InDrive'; // Default value
  String? _employmentType = 'Full-time'; // Default value
  String? _salary;
  String? _city;
  String? _benefits = 'Not provided'; // Default value
  String? _userEmail;

  bool _isLoading = false;
  bool _isLoadingEmail = true;

  @override
  void initState() {
    super.initState();
    _fetchUserEmail();
  }

  @override
  void dispose() {
    _dutyHoursController.dispose();
    _emailController.dispose();
    super.dispose();
  }

  Future<void> _fetchUserEmail() async {
    try {
      final email = await EmailUniquenessService.instance.getCurrentUserEmail();
      if (mounted) {
        setState(() {
          _userEmail = email;
          _emailController.text = email ?? 'Unable to fetch email';
          _isLoadingEmail = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _emailController.text = 'Error fetching email';
          _isLoadingEmail = false;
        });
      }
    }
  }

  Future<void> _submitJob() async {
    if (_formKey.currentState!.validate()) {
      _formKey.currentState!.save();

      setState(() {
        _isLoading = true;
      });

      try {
        final user = FirebaseAuth.instance.currentUser;
        if (user == null) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('You must be logged in to post a job')),
          );
          return;
        }

        // Check email uniqueness before proceeding using the fetched email
        if (_userEmail == null) {
          if (mounted) {
            _showErrorDialog(
              'Email Error',
              'Unable to verify your email address. Please ensure you are logged in.',
            );
          }
          return;
        }

        final hasExistingJob = await EmailUniquenessService.instance.hasExistingJobRequest(_userEmail!);
        if (hasExistingJob) {
          if (mounted) {
            _showErrorDialog(
              'Job Posting Not Allowed',
              'You have already posted a job with this email address. Each email can only be used to post one job.',
            );
          }
          return;
        }

        // Get user name from Firestore
        final userDoc = await FirebaseFirestore.instance
            .collection('users')
            .doc(user.uid)
            .get();

        final String posterName = userDoc.exists
            ? (userDoc.data()?['name'] ?? 'Anonymous')
            : 'Anonymous';

        // Use the already fetched email
        if (_userEmail == null) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Unable to verify your email address. Please ensure you are logged in.')),
            );
          }
          return;
        }

        // Create job object
        final job = JobModel(
          jobId: '', // This will be assigned by Firestore
          posterId: user.uid,
          posterName: posterName,
          title: _title!,
          type: _type!,
          employmentType: _employmentType!,
          salary: _salary!,
          city: _city!,
          benefits: _benefits!,
          dutyHours: _dutyHoursController.text
              .split(',')
              .map((e) => e.trim())
              .where((e) => e.isNotEmpty)
              .toList(),
          createdAt: DateTime.now(),
        );

        // Create job data with email for uniqueness constraint
        final jobData = job.toMap();
        jobData['posterEmail'] = _userEmail; // Add email for uniqueness constraint
        jobData['status'] = 'active'; // Set default status as active

        // Save to Firestore
        await FirebaseFirestore.instance.collection('jobs').add(jobData);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Job posted successfully!')),
          );
          Navigator.pop(context);
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error posting job: $e')),
          );
        }
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }

  Future<void> _showErrorDialog(String title, String message) async {
    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        final isDarkMode = Theme.of(context).brightness == Brightness.dark;

        return AlertDialog(
          backgroundColor: isDarkMode ? AppColors.darkSurface : Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              const Icon(
                Icons.error_outline,
                color: AppColors.error,
                size: 28,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: isDarkMode ? AppColors.darkText : AppColors.lightText,
                  ),
                ),
              ),
            ],
          ),
          content: Text(
            message,
            style: TextStyle(
              fontSize: 16,
              color: isDarkMode ? AppColors.darkTextSecondary : AppColors.lightTextSecondary,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              style: TextButton.styleFrom(
                foregroundColor: AppColors.primaryYellow,
              ),
              child: const Text(
                'OK',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Post a Job'),
        backgroundColor: AppColors.primaryYellow,
        foregroundColor: Colors.black,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Email Field (Read-only)
                    TextFormField(
                      controller: _emailController,
                      decoration: InputDecoration(
                        labelText: 'Your Email (Auto-fetched)',
                        prefixIcon: const Icon(Icons.email),
                        border: const OutlineInputBorder(),
                        suffixIcon: _isLoadingEmail
                            ? const SizedBox(
                                width: 20,
                                height: 20,
                                child: Padding(
                                  padding: EdgeInsets.all(12.0),
                                  child: CircularProgressIndicator(strokeWidth: 2),
                                ),
                              )
                            : const Icon(Icons.lock, color: Colors.grey),
                        helperText: 'This email will be used for your job post. One job per email allowed.',
                        helperMaxLines: 2,
                      ),
                      enabled: false, // Make it read-only
                      style: TextStyle(
                        color: _isLoadingEmail ? Colors.grey : Colors.black87,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 20),

                    // Job Title
                    TextFormField(
                      decoration: const InputDecoration(
                        labelText: 'Job Title',
                        prefixIcon: Icon(Icons.work),
                        border: OutlineInputBorder(),
                      ),
                      validator: (value) => InputValidator.validateRequired(value, 'job title'),
                      onSaved: (value) => _title = value,
                    ),
                    const SizedBox(height: 16),

                    // Job Type
                    DropdownButtonFormField<String>(
                      decoration: const InputDecoration(
                        labelText: 'Job Type',
                        prefixIcon: Icon(Icons.category),
                        border: OutlineInputBorder(),
                      ),
                      value: _type,
                      items: ['InDrive', 'Household', 'Company']
                          .map((type) => DropdownMenuItem(
                                value: type,
                                child: Text(type),
                              ))
                          .toList(),
                      onChanged: (value) {
                        setState(() {
                          _type = value;
                        });
                      },
                      validator: (value) => InputValidator.validateRequired(value, 'job type'),
                    ),
                    const SizedBox(height: 16),

                    // Employment Type
                    DropdownButtonFormField<String>(
                      decoration: const InputDecoration(
                        labelText: 'Employment Type',
                        prefixIcon: Icon(Icons.schedule),
                        border: OutlineInputBorder(),
                      ),
                      value: _employmentType,
                      items: ['Full-time', 'Part-time']
                          .map((type) => DropdownMenuItem(
                                value: type,
                                child: Text(type),
                              ))
                          .toList(),
                      onChanged: (value) {
                        setState(() {
                          _employmentType = value;
                        });
                      },
                      validator: (value) => InputValidator.validateRequired(value, 'employment type'),
                    ),
                    const SizedBox(height: 16),

                    // Salary
                    TextFormField(
                      decoration: const InputDecoration(
                        labelText: 'Salary (PKR)',
                        prefixIcon: Icon(Icons.attach_money),
                        border: OutlineInputBorder(),
                        hintText: 'e.g., 25,000 or 20,000-30,000',
                      ),
                      keyboardType: TextInputType.text,
                      validator: (value) => InputValidator.validateRequired(value, 'salary'),
                      onSaved: (value) => _salary = value,
                    ),
                    const SizedBox(height: 16),

                    // City
                    TextFormField(
                      decoration: const InputDecoration(
                        labelText: 'City',
                        prefixIcon: Icon(Icons.location_city),
                        border: OutlineInputBorder(),
                      ),
                      validator: (value) => InputValidator.validateRequired(value, 'city'),
                      onSaved: (value) => _city = value,
                    ),
                    const SizedBox(height: 16),

                    // Benefits
                    DropdownButtonFormField<String>(
                      decoration: const InputDecoration(
                        labelText: 'Residency & Food Benefits',
                        prefixIcon: Icon(Icons.home),
                        border: OutlineInputBorder(),
                      ),
                      value: _benefits,
                      items: [
                        'Provided',
                        'Food Only',
                        'Residency Only',
                        'Not provided',
                        'On your own'
                      ]
                          .map((benefit) => DropdownMenuItem(
                                value: benefit,
                                child: Text(benefit),
                              ))
                          .toList(),
                      onChanged: (value) {
                        setState(() {
                          _benefits = value;
                        });
                      },
                      validator: (value) => InputValidator.validateRequired(value, 'benefits option'),
                    ),
                    const SizedBox(height: 16),

                    // Duty Hours
                    TextFormField(
                      controller: _dutyHoursController,
                      decoration: const InputDecoration(
                        labelText: 'Duty Hours (comma separated)',
                        prefixIcon: Icon(Icons.access_time),
                        border: OutlineInputBorder(),
                        hintText: 'e.g., 9AM-5PM, 10AM-6PM, Weekends',
                      ),
                      validator: (value) => InputValidator.validateRequired(value, 'duty hours'),
                      maxLines: 2,
                    ),
                    const SizedBox(height: 24),

                    // Submit Button
                    SizedBox(
                      width: double.infinity,
                      height: 50,
                      child: ElevatedButton(
                        onPressed: _submitJob,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.primaryYellow,
                          foregroundColor: Colors.black,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: const Text(
                          'Post Job',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
    );
  }
}