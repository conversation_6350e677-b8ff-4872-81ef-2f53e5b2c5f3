buildscript {
    ext.kotlin_version = '1.9.25'
    repositories {
        google()
        mavenCentral()
        maven {
            url 'https://plugins.gradle.org/m2/'
        }
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:8.7.3'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath 'com.google.gms:google-services:4.4.2'
        classpath 'com.google.firebase:firebase-crashlytics-gradle:3.0.2'
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
        jcenter()
        maven {
            url 'https://plugins.gradle.org/m2/'
        }
    }
}

// Force all subprojects to use the same NDK version and compileSdkVersion
subprojects {
    afterEvaluate { project ->
        if (project.hasProperty('android')) {
            project.android {
                ndkVersion = "26.3.11579264"
                // Force all subprojects to use compileSdkVersion 35 to fix lStar attribute issue
                compileSdkVersion 35
            }
        }
    }
}

rootProject.buildDir = '../build'
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    project.evaluationDependsOn(':app')
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
