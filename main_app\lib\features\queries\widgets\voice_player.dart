import 'package:flutter/material.dart';
import 'package:just_audio/just_audio.dart';
import 'dart:math';
import 'dart:async';

class VoicePlayer extends StatefulWidget {
  final String audioUrl;
  final int duration;
  final bool isDarkMode;
  final bool isMe;

  const VoicePlayer({
    super.key,
    required this.audioUrl,
    required this.duration,
    required this.isDarkMode,
    required this.isMe,
  });

  @override
  State<VoicePlayer> createState() => _VoicePlayerState();
}

class _VoicePlayerState extends State<VoicePlayer> {
  final AudioPlayer _audioPlayer = AudioPlayer();
  bool _isPlaying = false;
  bool _isLoading = false;
  Duration _currentPosition = Duration.zero;
  Duration _totalDuration = Duration.zero;

  @override
  void initState() {
    super.initState();
    _initAudio();
  }

  Future<void> _initAudio() async {
    try {
      setState(() {
        _isLoading = true;
      });

      // Setup duration change stream
      _audioPlayer.positionStream.listen((position) {
        if (mounted) {
          setState(() {
            _currentPosition = position;
          });
        }
      });

      // Setup playback state stream
      _audioPlayer.playerStateStream.listen((playerState) {
        if (mounted) {
          setState(() {
            _isPlaying = playerState.playing;
            if (playerState.processingState == ProcessingState.completed) {
              _isPlaying = false;
              _currentPosition = Duration.zero;
              _audioPlayer.seek(Duration.zero);
            }
          });
        }
      });

      // Setup duration stream
      _audioPlayer.durationStream.listen((duration) {
        if (mounted && duration != null) {
          setState(() {
            _totalDuration = duration;
          });
        }
      });

      // Set initial duration if we have it
      if (widget.duration > 0) {
        _totalDuration = Duration(milliseconds: widget.duration);
      }

      // Set audio source
      await _audioPlayer.setUrl(widget.audioUrl);
      
      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading audio: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _togglePlay() async {
    if (_isPlaying) {
      await _audioPlayer.pause();
    } else {
      await _audioPlayer.play();
    }
  }

  void _seekTo(double value) {
    final newPosition = Duration(milliseconds: value.toInt());
    _audioPlayer.seek(newPosition);
  }

  Stream<PositionData> get _positionDataStream {
    return Stream.periodic(const Duration(milliseconds: 100), (_) {
      return PositionData(
        position: _currentPosition,
        bufferedPosition: Duration.zero,
        duration: _totalDuration,
      );
    });
  }

  @override
  void dispose() {
    _audioPlayer.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final primaryColor = widget.isMe ? Colors.black : Colors.white;
    final backgroundColor = widget.isDarkMode 
      ? (widget.isMe ? Colors.grey[800] : Colors.grey[700]) 
      : (widget.isMe ? Colors.grey[300] : Colors.grey[200]);

    return Container(
      height: 48,
      padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 4),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(24),
      ),
      child: _isLoading
          ? Center(
              child: SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  color: primaryColor,
                ),
              ),
            )
          : Row(
              children: [
                // Play/Pause button
                Material(
                  color: Colors.transparent,
                  child: InkWell(
                    borderRadius: BorderRadius.circular(20),
                    onTap: _togglePlay,
                    child: Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: primaryColor.withOpacity(0.1),
                      ),
                      child: Icon(
                        _isPlaying ? Icons.pause : Icons.play_arrow,
                        color: primaryColor,
                      ),
                    ),
                  ),
                ),
                
                // Progress slider
                Expanded(
                  child: StreamBuilder<PositionData>(
                    stream: _positionDataStream,
                    builder: (context, snapshot) {
                      final positionData = snapshot.data ?? 
                          PositionData(
                            position: _currentPosition,
                            bufferedPosition: Duration.zero,
                            duration: _totalDuration,
                          );
                      
                      return SliderTheme(
                        data: SliderThemeData(
                          trackHeight: 4,
                          thumbShape: const RoundSliderThumbShape(
                            enabledThumbRadius: 6,
                          ),
                          overlayShape: const RoundSliderOverlayShape(
                            overlayRadius: 14,
                          ),
                          activeTrackColor: primaryColor,
                          inactiveTrackColor: primaryColor.withOpacity(0.2),
                          thumbColor: primaryColor,
                          overlayColor: primaryColor.withOpacity(0.2),
                        ),
                        child: Slider(
                          min: 0.0,
                          max: positionData.duration.inMilliseconds.toDouble(),
                          value: min(
                            positionData.position.inMilliseconds.toDouble(),
                            positionData.duration.inMilliseconds.toDouble(),
                          ),
                          onChanged: (value) {
                            _seekTo(value);
                          },
                        ),
                      );
                    },
                  ),
                ),
                
                // Duration text
                Padding(
                  padding: const EdgeInsets.only(right: 8.0),
                  child: Text(
                    _formatDuration(_isPlaying ? _currentPosition : _totalDuration),
                    style: TextStyle(
                      fontSize: 12,
                      color: primaryColor.withOpacity(0.8),
                    ),
                  ),
                ),
              ],
            ),
    );
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }
}

class PositionData {
  final Duration position;
  final Duration bufferedPosition;
  final Duration duration;

  PositionData({
    required this.position,
    required this.bufferedPosition,
    required this.duration,
  });
} 