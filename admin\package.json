{"name": "drive-on-admin-panel", "version": "1.0.0", "description": "Comprehensive admin panel for Drive-On ride-hailing application", "private": true, "scripts": {"dev": "next dev -p 3001", "build": "next build", "build:hosting": "next build && next export", "start": "next start -p 3001", "lint": "next lint", "type-check": "tsc --noEmit", "format": "prettier --write .", "format:check": "prettier --check .", "setup-admin": "node scripts/setup-admin.js", "setup": "npm install && npm run setup-admin", "dev:fast": "next dev -p 3001 --turbo", "build:analyze": "ANALYZE=true next build", "start:prod": "NODE_ENV=production next start -p 3001", "perf:check": "node scripts/performance-check.js", "deploy": "npm run build && firebase deploy", "deploy:hosting": "npm run build && firebase deploy --only hosting", "deploy:staging": "npm run build && firebase deploy --only hosting:staging", "deploy:prod": "npm run build && firebase deploy --only hosting:production", "firebase:login": "firebase login", "firebase:init": "firebase init", "firebase:serve": "npm run build && firebase serve", "pre-deploy": "node scripts/pre-deploy-check.js", "deploy:check": "npm run pre-deploy && npm run deploy", "setup:deployment": "node scripts/setup-deployment.js", "test:login": "node scripts/test-login.js"}, "dependencies": {"@firebase/auth": "^1.10.6", "@firebase/firestore": "^4.7.16", "@firebase/storage": "^0.13.12", "@hookform/resolvers": "^3.3.2", "@next/swc-win32-x64-msvc": "^15.1.8", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.1.4", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-toggle": "^1.0.3", "@radix-ui/react-tooltip": "^1.0.7", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "@tanstack/react-table": "^8.11.2", "@types/multer": "^1.4.11", "@types/node": "^20.17.51", "@types/nodemailer": "^6.4.14", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^2.30.0", "embla-carousel-react": "^8.0.0-rc19", "firebase": "^10.14.1", "firebase-admin": "^12.0.0", "framer-motion": "^10.18.0", "lucide-react": "^0.294.0", "multer": "^1.4.5-lts.1", "next": "^15.3.3", "next-themes": "^0.4.6", "nodemailer": "^6.9.7", "postcss": "^8.5.3", "quill": "^1.3.7", "react": "^18.3.1", "react-day-picker": "^8.10.0", "react-dom": "^18.3.1", "react-dropzone": "^14.2.3", "react-hook-form": "^7.48.2", "react-quill": "^2.0.0", "react-resizable-panels": "^0.0.55", "recharts": "^2.15.3", "sharp": "^0.33.1", "sonner": "^1.3.1", "tailwind-merge": "^2.6.0", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "typescript": "^5.8.3", "vaul": "^0.8.0", "zod": "^3.22.4", "zustand": "^4.4.7"}, "devDependencies": {"@next/bundle-analyzer": "^15.3.3", "@types/react-beautiful-dnd": "^13.1.8", "eslint": "^8.56.0", "eslint-config-next": "^14.0.4", "firebase-tools": "^13.0.0", "prettier": "^3.1.1", "prettier-plugin-tailwindcss": "^0.5.9"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}