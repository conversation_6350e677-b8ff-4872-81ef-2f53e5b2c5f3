import { useState, useEffect, useCallback, useRef } from 'react'
import {
  collection,
  query,
  onSnapshot,
  orderBy,
  limit,
  where,
  startAfter,
  QueryDocumentSnapshot,
  DocumentData,
  Unsubscribe,
  getDocs
} from 'firebase/firestore'
import { db } from '@/lib/firebase/config'

interface UseOptimizedDataOptions {
  collectionName: string
  pageSize?: number
  orderByField?: string
  orderDirection?: 'asc' | 'desc'
  whereConditions?: Array<{
    field: string
    operator: any
    value: any
  }>
  enableRealtime?: boolean
  cacheKey?: string
}

interface OptimizedDataResult<T> {
  data: T[]
  isLoading: boolean
  error: string | null
  hasMore: boolean
  loadMore: () => Promise<void>
  refresh: () => Promise<void>
  totalCount: number
}

// Simple in-memory cache
const dataCache = new Map<string, {
  data: any[]
  timestamp: number
  totalCount: number
}>()

const CACHE_DURATION = 5 * 60 * 1000 // 5 minutes

export function useOptimizedData<T = DocumentData>(
  options: UseOptimizedDataOptions
): OptimizedDataResult<T> {
  const {
    collectionName,
    pageSize = 20,
    orderByField = 'createdAt',
    orderDirection = 'desc',
    whereConditions = [],
    enableRealtime = true,
    cacheKey
  } = options

  const [data, setData] = useState<T[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [hasMore, setHasMore] = useState(true)
  const [totalCount, setTotalCount] = useState(0)
  const [lastDoc, setLastDoc] = useState<QueryDocumentSnapshot<DocumentData> | null>(null)

  const unsubscribeRef = useRef<Unsubscribe | null>(null)
  const isInitialLoad = useRef(true)

  // Generate cache key
  const getCacheKey = useCallback(() => {
    if (cacheKey) return cacheKey
    const conditions = whereConditions.map(c => `${c.field}:${c.operator}:${c.value}`).join('|')
    return `${collectionName}:${orderByField}:${orderDirection}:${conditions}`
  }, [collectionName, orderByField, orderDirection, whereConditions, cacheKey])

  // Check cache
  const getCachedData = useCallback(() => {
    const key = getCacheKey()
    const cached = dataCache.get(key)
    if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
      return cached
    }
    return null
  }, [getCacheKey])

  // Set cache
  const setCachedData = useCallback((newData: T[], count: number) => {
    const key = getCacheKey()
    dataCache.set(key, {
      data: newData,
      timestamp: Date.now(),
      totalCount: count
    })
  }, [getCacheKey])

  // Build query
  const buildQuery = useCallback((startAfterDoc?: QueryDocumentSnapshot<DocumentData>) => {
    let q = query(
      collection(db, collectionName),
      orderBy(orderByField, orderDirection),
      limit(pageSize)
    )

    // Add where conditions
    whereConditions.forEach(condition => {
      q = query(q, where(condition.field, condition.operator, condition.value))
    })

    // Add pagination
    if (startAfterDoc) {
      q = query(q, startAfter(startAfterDoc))
    }

    return q
  }, [collectionName, orderByField, orderDirection, pageSize, whereConditions])

  // Load initial data
  const loadInitialData = useCallback(async () => {
    try {
      setIsLoading(true)
      setError(null)

      // Check cache first
      const cached = getCachedData()
      if (cached && isInitialLoad.current) {
        setData(cached.data)
        setTotalCount(cached.totalCount)
        setIsLoading(false)
        isInitialLoad.current = false
        return
      }

      const q = buildQuery()

      if (enableRealtime) {
        // Set up real-time listener
        unsubscribeRef.current = onSnapshot(
          q,
          (snapshot) => {
            const newData = snapshot.docs.map(doc => ({
              id: doc.id,
              ...doc.data()
            })) as T[]

            setData(newData)
            setTotalCount(snapshot.size)
            setLastDoc(snapshot.docs[snapshot.docs.length - 1] || null)
            setHasMore(snapshot.docs.length === pageSize)
            setIsLoading(false)

            // Cache the data
            setCachedData(newData, snapshot.size)
            isInitialLoad.current = false
          },
          (err) => {
            console.error('Error in real-time listener:', err)
            setError('Failed to load data')
            setIsLoading(false)
          }
        )
      } else {
        // One-time fetch
        const snapshot = await getDocs(q)
        const newData = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        })) as T[]

        setData(newData)
        setTotalCount(snapshot.size)
        setLastDoc(snapshot.docs[snapshot.docs.length - 1] || null)
        setHasMore(snapshot.docs.length === pageSize)
        setIsLoading(false)

        // Cache the data
        setCachedData(newData, snapshot.size)
        isInitialLoad.current = false
      }
    } catch (err) {
      console.error('Error loading initial data:', err)
      setError('Failed to load data')
      setIsLoading(false)
    }
  }, [buildQuery, enableRealtime, getCachedData, setCachedData, pageSize])

  // Load more data (pagination)
  const loadMore = useCallback(async () => {
    if (!hasMore || !lastDoc || isLoading) return

    try {
      setIsLoading(true)
      const q = buildQuery(lastDoc)
      const snapshot = await getDocs(q)

      const newData = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as T[]

      setData(prev => [...prev, ...newData])
      setLastDoc(snapshot.docs[snapshot.docs.length - 1] || null)
      setHasMore(snapshot.docs.length === pageSize)
      setTotalCount(prev => prev + snapshot.size)
      setIsLoading(false)
    } catch (err) {
      console.error('Error loading more data:', err)
      setError('Failed to load more data')
      setIsLoading(false)
    }
  }, [hasMore, lastDoc, isLoading, buildQuery, pageSize])

  // Refresh data
  const refresh = useCallback(async () => {
    // Clear cache
    const key = getCacheKey()
    dataCache.delete(key)

    // Reset state
    setData([])
    setLastDoc(null)
    setHasMore(true)
    setTotalCount(0)
    isInitialLoad.current = true

    // Reload data
    await loadInitialData()
  }, [getCacheKey, loadInitialData])

  // Initialize
  useEffect(() => {
    loadInitialData()

    return () => {
      if (unsubscribeRef.current) {
        unsubscribeRef.current()
        unsubscribeRef.current = null
      }
    }
  }, [loadInitialData])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (unsubscribeRef.current) {
        unsubscribeRef.current()
      }
    }
  }, [])

  return {
    data,
    isLoading,
    error,
    hasMore,
    loadMore,
    refresh,
    totalCount
  }
}

// Clear cache utility
export const clearDataCache = (key?: string) => {
  if (key) {
    dataCache.delete(key)
  } else {
    dataCache.clear()
  }
}
