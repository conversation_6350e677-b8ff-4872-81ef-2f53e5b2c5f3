@echo off
echo ===================================
echo Drive-On App Size Optimization
echo ===================================
echo.
echo This script will build optimized APKs for release

echo.
echo 1. Running flutter clean...
call flutter clean

echo.
echo 2. Getting dependencies...
call flutter pub get

echo.
echo 3. Building optimized APK for arm64-v8a (most modern devices)...
call flutter build apk --release --split-per-abi --target-platform=android-arm64 --obfuscate --split-debug-info=build/debug-info

echo.
echo 4. Building optimized APK for armeabi-v7a (older devices)...
call flutter build apk --release --split-per-abi --target-platform=android-arm --obfuscate --split-debug-info=build/debug-info

echo.
echo APKs have been created at:
echo - build\app\outputs\flutter-apk\app-arm64-v8a-release.apk (for modern devices)
echo - build\app\outputs\flutter-apk\app-armeabi-v7a-release.apk (for older devices)
echo.
echo Also building App Bundle for Play Store submission...
call flutter build appbundle --release --obfuscate --split-debug-info=build/debug-info

echo.
echo App Bundle created at:
echo - build\app\outputs\bundle\release\app-release.aab
echo.
echo Optimization complete! 