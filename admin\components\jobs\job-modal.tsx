'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import {
  X,
  Briefcase,
  MapPin,
  DollarSign,
  Clock,
  Users,
  Phone,
  Mail,
  MessageSquare,
  Calendar,
  Building2,
  Star
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { formatDate, formatCurrency } from '@/lib/utils'

interface Job {
  id: string
  title: string
  company: string
  category: 'InDrive' | 'Household' | 'Company' | 'Part-time'
  description: string
  requirements: string[]
  salary: {
    min: number
    max: number
    type: 'hourly' | 'daily' | 'monthly'
  }
  location: {
    city: string
    area: string
    coordinates?: { lat: number; lng: number }
  }
  contactInfo: {
    phone: string
    email: string
    whatsapp?: string
  }
  status: 'active' | 'inactive' | 'expired' | 'filled'
  isApproved: boolean
  isFeatured: boolean
  applicationsCount: number
  viewsCount: number
  createdAt: Date
  expiresAt: Date
  approvedAt?: Date
  approvedBy?: string
  postedBy: string
}

interface JobModalProps {
  job: Job | null
  mode: 'view' | 'edit' | 'create'
  isOpen: boolean
  onClose: () => void
  onSave: (jobData: Partial<Job>) => Promise<void>
}

export function JobModal({ job, mode, isOpen, onClose, onSave }: JobModalProps) {
  const [formData, setFormData] = useState({
    title: '',
    company: '',
    category: 'InDrive' as Job['category'],
    description: '',
    requirements: [''],
    salary: {
      min: 0,
      max: 0,
      type: 'monthly' as 'hourly' | 'daily' | 'monthly'
    },
    location: {
      city: '',
      area: ''
    },
    contactInfo: {
      phone: '',
      email: '',
      whatsapp: ''
    },
    status: 'active' as Job['status'],
    isFeatured: false,
    expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days from now
  })
  const [isLoading, setIsLoading] = useState(false)

  useEffect(() => {
    if (job && (mode === 'view' || mode === 'edit')) {
      setFormData({
        title: job.title || '',
        company: job.company || '',
        category: job.category || 'InDrive',
        description: job.description || '',
        requirements: job.requirements || [''],
        salary: job.salary || { min: 0, max: 0, type: 'monthly' },
        location: job.location || { city: '', area: '' },
        contactInfo: {
          phone: job.contactInfo?.phone || '',
          email: job.contactInfo?.email || '',
          whatsapp: job.contactInfo?.whatsapp || ''
        },
        status: job.status || 'active',
        isFeatured: job.isFeatured || false,
        expiresAt: job.expiresAt || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
      })
    } else if (mode === 'create') {
      setFormData({
        title: '',
        company: '',
        category: 'InDrive',
        description: '',
        requirements: [''],
        salary: { min: 0, max: 0, type: 'monthly' },
        location: { city: '', area: '' },
        contactInfo: { phone: '', email: '', whatsapp: '' },
        status: 'active',
        isFeatured: false,
        expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
      })
    }
  }, [job, mode])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      await onSave(formData)
    } catch (error) {
      console.error('Error saving job:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleNestedInputChange = (parent: string, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [parent]: {
        ...(prev[parent as keyof typeof prev] as any),
        [field]: value
      }
    }))
  }

  const handleRequirementChange = (index: number, value: string) => {
    const newRequirements = [...formData.requirements]
    newRequirements[index] = value
    setFormData(prev => ({ ...prev, requirements: newRequirements }))
  }

  const addRequirement = () => {
    setFormData(prev => ({
      ...prev,
      requirements: [...prev.requirements, '']
    }))
  }

  const removeRequirement = (index: number) => {
    setFormData(prev => ({
      ...prev,
      requirements: prev.requirements.filter((_, i) => i !== index)
    }))
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="absolute inset-0 bg-black/50"
        onClick={onClose}
      />

      {/* Modal */}
      <motion.div
        initial={{ opacity: 0, scale: 0.95, y: 20 }}
        animate={{ opacity: 1, scale: 1, y: 0 }}
        exit={{ opacity: 0, scale: 0.95, y: 20 }}
        className="relative w-full max-w-4xl mx-4 bg-white rounded-lg shadow-xl max-h-[90vh] overflow-y-auto"
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div className="flex items-center space-x-3">
            <h2 className="text-xl font-semibold text-gray-900">
              {mode === 'create' ? 'Create New Job' :
               mode === 'edit' ? 'Edit Job' : 'Job Details'}
            </h2>
            {job && (
              <div className="flex space-x-2">
                <Badge variant={job.status === 'active' ? 'success' : 'secondary'}>
                  {job.status}
                </Badge>
                {job.isFeatured && (
                  <Badge variant="warning">
                    <Star className="w-3 h-3 mr-1" />
                    Featured
                  </Badge>
                )}
              </div>
            )}
          </div>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="w-4 h-4" />
          </Button>
        </div>

        {/* Content */}
        <div className="p-6">
          {mode === 'view' ? (
            // View Mode
            <div className="space-y-6">
              {/* Job Header */}
              <div className="flex items-start space-x-4">
                <div className="w-16 h-16 bg-primary-100 rounded-lg flex items-center justify-center">
                  <Briefcase className="w-8 h-8 text-primary-600" />
                </div>
                <div className="flex-1">
                  <h3 className="text-xl font-semibold text-gray-900">{job?.title}</h3>
                  <p className="text-gray-600 flex items-center space-x-2">
                    <Building2 className="w-4 h-4" />
                    <span>{job?.company}</span>
                  </p>
                  <div className="flex items-center space-x-4 mt-2">
                    <div className="flex items-center space-x-1">
                      <MapPin className="w-4 h-4 text-gray-400" />
                      <span className="text-sm">{job?.location.city}, {job?.location.area}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <DollarSign className="w-4 h-4 text-gray-400" />
                      <span className="text-sm">
                        {formatCurrency(job?.salary.min || 0)} - {formatCurrency(job?.salary.max || 0)} / {job?.salary.type}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Job Stats */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card>
                  <CardContent className="p-4 text-center">
                    <Users className="w-8 h-8 text-blue-600 mx-auto mb-2" />
                    <p className="text-2xl font-bold text-gray-900">{job?.applicationsCount || 0}</p>
                    <p className="text-sm text-gray-600">Applications</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4 text-center">
                    <Clock className="w-8 h-8 text-green-600 mx-auto mb-2" />
                    <p className="text-2xl font-bold text-gray-900">{job?.viewsCount || 0}</p>
                    <p className="text-sm text-gray-600">Views</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4 text-center">
                    <Calendar className="w-8 h-8 text-orange-600 mx-auto mb-2" />
                    <p className="text-sm font-medium text-gray-900">
                      {job?.expiresAt ? formatDate(job.expiresAt) : 'No expiry'}
                    </p>
                    <p className="text-sm text-gray-600">Expires</p>
                  </CardContent>
                </Card>
              </div>

              {/* Job Description */}
              <Card>
                <CardHeader>
                  <CardTitle>Job Description</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-700 whitespace-pre-wrap">{job?.description}</p>
                </CardContent>
              </Card>

              {/* Requirements */}
              <Card>
                <CardHeader>
                  <CardTitle>Requirements</CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="list-disc list-inside space-y-2">
                    {job?.requirements?.map((req, index) => (
                      <li key={index} className="text-gray-700">{req}</li>
                    ))}
                  </ul>
                </CardContent>
              </Card>

              {/* Contact Information */}
              <Card>
                <CardHeader>
                  <CardTitle>Contact Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <Phone className="w-4 h-4 text-gray-400" />
                    <span>{job?.contactInfo.phone}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Mail className="w-4 h-4 text-gray-400" />
                    <span>{job?.contactInfo.email}</span>
                  </div>
                  {job?.contactInfo.whatsapp && (
                    <div className="flex items-center space-x-2">
                      <MessageSquare className="w-4 h-4 text-gray-400" />
                      <span>{job?.contactInfo.whatsapp}</span>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          ) : (
            // Edit/Create Mode
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="title">Job Title</Label>
                  <Input
                    id="title"
                    value={formData.title}
                    onChange={(e) => handleInputChange('title', e.target.value)}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="company">Company</Label>
                  <Input
                    id="company"
                    value={formData.company}
                    onChange={(e) => handleInputChange('company', e.target.value)}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="category">Category</Label>
                  <select
                    id="category"
                    value={formData.category}
                    onChange={(e) => handleInputChange('category', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                  >
                    <option value="InDrive">InDrive</option>
                    <option value="Household">Household</option>
                    <option value="Company">Company</option>
                    <option value="Part-time">Part-time</option>
                  </select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="status">Status</Label>
                  <select
                    id="status"
                    value={formData.status}
                    onChange={(e) => handleInputChange('status', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                  >
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                    <option value="filled">Filled</option>
                  </select>
                </div>
              </div>

              {/* Description */}
              <div className="space-y-2">
                <Label htmlFor="description">Job Description</Label>
                <textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                  required
                />
              </div>

              {/* Requirements */}
              <div className="space-y-2">
                <Label>Requirements</Label>
                {formData.requirements.map((req, index) => (
                  <div key={index} className="flex space-x-2">
                    <Input
                      value={req}
                      onChange={(e) => handleRequirementChange(index, e.target.value)}
                      placeholder="Enter requirement"
                      className="flex-1"
                    />
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => removeRequirement(index)}
                      disabled={formData.requirements.length === 1}
                    >
                      Remove
                    </Button>
                  </div>
                ))}
                <Button type="button" variant="outline" onClick={addRequirement}>
                  Add Requirement
                </Button>
              </div>

              {/* Salary */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="salaryMin">Minimum Salary</Label>
                  <Input
                    id="salaryMin"
                    type="number"
                    value={formData.salary.min}
                    onChange={(e) => handleNestedInputChange('salary', 'min', Number(e.target.value))}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="salaryMax">Maximum Salary</Label>
                  <Input
                    id="salaryMax"
                    type="number"
                    value={formData.salary.max}
                    onChange={(e) => handleNestedInputChange('salary', 'max', Number(e.target.value))}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="salaryType">Salary Type</Label>
                  <select
                    id="salaryType"
                    value={formData.salary.type}
                    onChange={(e) => handleNestedInputChange('salary', 'type', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                  >
                    <option value="hourly">Hourly</option>
                    <option value="daily">Daily</option>
                    <option value="monthly">Monthly</option>
                  </select>
                </div>
              </div>

              {/* Location */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="city">City</Label>
                  <Input
                    id="city"
                    value={formData.location.city}
                    onChange={(e) => handleNestedInputChange('location', 'city', e.target.value)}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="area">Area</Label>
                  <Input
                    id="area"
                    value={formData.location.area}
                    onChange={(e) => handleNestedInputChange('location', 'area', e.target.value)}
                    required
                  />
                </div>
              </div>

              {/* Contact Information */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="phone">Phone</Label>
                  <Input
                    id="phone"
                    value={formData.contactInfo.phone}
                    onChange={(e) => handleNestedInputChange('contactInfo', 'phone', e.target.value)}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    value={formData.contactInfo.email}
                    onChange={(e) => handleNestedInputChange('contactInfo', 'email', e.target.value)}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="whatsapp">WhatsApp (Optional)</Label>
                  <Input
                    id="whatsapp"
                    value={formData.contactInfo.whatsapp}
                    onChange={(e) => handleNestedInputChange('contactInfo', 'whatsapp', e.target.value)}
                  />
                </div>
              </div>

              {/* Featured */}
              <div className="flex items-center space-x-2">
                <input
                  id="featured"
                  type="checkbox"
                  checked={formData.isFeatured}
                  onChange={(e) => handleInputChange('isFeatured', e.target.checked)}
                  className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                />
                <Label htmlFor="featured">Mark as Featured Job</Label>
              </div>

              {/* Actions */}
              <div className="flex justify-end space-x-3 pt-6 border-t">
                <Button type="button" variant="outline" onClick={onClose}>
                  Cancel
                </Button>
                <Button type="submit" disabled={isLoading} className="gradient-primary">
                  {isLoading ? 'Saving...' : mode === 'create' ? 'Create Job' : 'Save Changes'}
                </Button>
              </div>
            </form>
          )}
        </div>
      </motion.div>
    </div>
  )
}
