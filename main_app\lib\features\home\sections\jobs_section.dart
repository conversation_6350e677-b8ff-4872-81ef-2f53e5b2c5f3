import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../../../core/core.dart';
import '../../jobs/create_job_screen.dart';
import '../../jobs/job_details_screen.dart';
import '../../jobs/models/job_model.dart';
import '../../jobs/services/user_job_service.dart';
import '../../jobs/widgets/my_job_button.dart';

class JobsSection extends StatefulWidget {
  const JobsSection({super.key});

  @override
  State<JobsSection> createState() => _JobsSectionState();
}

class _JobsSectionState extends State<JobsSection> {
  String _selectedCategory = 'All';
  String _selectedEmploymentType = 'All';
  String _searchQuery = '';

  final TextEditingController _searchController = TextEditingController();
  final AppLogger _logger = AppLogger('JobsSection');

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  // Filter jobs based on selected filters
  Stream<QuerySnapshot> _getFilteredJobs() {
    Query query = FirebaseFirestore.instance.collection('jobs');

    // Apply category filter (if not 'All')
    if (_selectedCategory != 'All') {
      query = query.where('type', isEqualTo: _selectedCategory);
    }

    // Apply employment type filter (if not 'All')
    if (_selectedEmploymentType != 'All') {
      query = query.where('employmentType', isEqualTo: _selectedEmploymentType);
    }

    // Sort by creation date (newest first)
    query = query.orderBy('createdAt', descending: true);

    return query.snapshots();
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with search and chat buttons
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Job Portal',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: isDarkMode ? AppColors.darkText : AppColors.lightText,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        'Find the perfect job opportunity',
                        style: TextStyle(
                          fontSize: 12,
                          color: isDarkMode ? AppColors.darkTextSecondary : AppColors.lightTextSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),

            // Search bar
            Container(
              decoration: BoxDecoration(
                color: isDarkMode ? AppColors.darkSurface : Colors.white,
                borderRadius: BorderRadius.circular(10),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: TextField(
                controller: _searchController,
                onChanged: (value) {
                  setState(() {
                    _searchQuery = value;
                  });
                },
                decoration: InputDecoration(
                  hintText: 'Search jobs...',
                  hintStyle: const TextStyle(fontSize: 14),
                  prefixIcon: Icon(
                    Icons.search,
                    size: 20,
                    color: isDarkMode ? Colors.white54 : Colors.black54,
                  ),
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Job type categories
            Text(
              'Categories',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: isDarkMode ? AppColors.darkText : AppColors.lightText,
              ),
            ),
            const SizedBox(height: 6),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: [
                  _buildCompactCategoryChip(context, 'All', isSelected: _selectedCategory == 'All'),
                  _buildCompactCategoryChip(context, 'InDrive', isSelected: _selectedCategory == 'InDrive'),
                  _buildCompactCategoryChip(context, 'Household', isSelected: _selectedCategory == 'Household'),
                  _buildCompactCategoryChip(context, 'Company', isSelected: _selectedCategory == 'Company'),
                ],
              ),
            ),

            const SizedBox(height: 12),

            // Employment type filter
            Text(
              'Employment Type',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: isDarkMode ? AppColors.darkText : AppColors.lightText,
              ),
            ),
            const SizedBox(height: 6),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: [
                  _buildCompactCategoryChip(context, 'All', isSelected: _selectedEmploymentType == 'All'),
                  _buildCompactCategoryChip(context, 'Full-time', isSelected: _selectedEmploymentType == 'Full-time'),
                  _buildCompactCategoryChip(context, 'Part-time', isSelected: _selectedEmploymentType == 'Part-time'),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // Jobs list
            Expanded(
              child: StreamBuilder<QuerySnapshot>(
                stream: _getFilteredJobs(),
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.waiting) {
                    return const Center(child: CircularProgressIndicator());
                  }

                  if (snapshot.hasError) {
                    return Center(
                      child: Text(
                        'Error loading jobs: ${snapshot.error}',
                        style: TextStyle(
                          color: isDarkMode ? AppColors.darkText : AppColors.lightText,
                        ),
                      ),
                    );
                  }

                  if (!snapshot.hasData || snapshot.data!.docs.isEmpty) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.work_outline,
                            size: 64,
                            color: isDarkMode ? Colors.white38 : Colors.black26,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'No jobs found',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: isDarkMode ? Colors.white70 : Colors.black87,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Try changing your filters or be the first to post a job',
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              fontSize: 14,
                              color: isDarkMode ? Colors.white54 : Colors.black54,
                            ),
                          ),
                        ],
                      ),
                    );
                  }

                  // Filter jobs by search query and status (exclude suspended/deleted jobs)
                  final allJobs = snapshot.data!.docs.where((doc) {
                    final data = doc.data() as Map<String, dynamic>;
                    final status = data['status'] as String?;
                    // Only show active jobs (null for backward compatibility)
                    return status == null || status == 'active';
                  }).toList();

                  final filteredJobs = _searchQuery.isEmpty
                      ? allJobs
                      : allJobs.where((doc) {
                          final data = doc.data() as Map<String, dynamic>;
                          final title = data['title'] as String? ?? '';
                          final city = data['city'] as String? ?? '';
                          return title.toLowerCase().contains(_searchQuery.toLowerCase()) ||
                                city.toLowerCase().contains(_searchQuery.toLowerCase());
                        }).toList();

                  if (filteredJobs.isEmpty) {
                    return Center(
                      child: Text(
                        'No jobs match your search',
                        style: TextStyle(
                          color: isDarkMode ? AppColors.darkText : AppColors.lightText,
                        ),
                      ),
                    );
                  }

                  return ListView.builder(
                    itemCount: filteredJobs.length,
                    cacheExtent: 500, // Cache 500 pixels worth of items
                    physics: const BouncingScrollPhysics(),
                    itemBuilder: (context, index) {
                      final job = JobModel.fromDocument(filteredJobs[index]);
                      // Wrap each item in RepaintBoundary for better performance
                      return RepaintBoundary(
                        child: _buildJobCard(context, job),
                      );
                    },
                  );
                },
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: StreamBuilder<JobModel?>(
        stream: UserJobService.instance.getCurrentUserJobStream(),
        builder: (context, snapshot) {
          // Debug: Log the current state
          _logger.debug('StreamBuilder state: hasData=${snapshot.hasData}, userJob=${snapshot.data?.title}');

          if (snapshot.connectionState == ConnectionState.waiting) {
            // Show loading state
            return const FloatingActionButton(
              onPressed: null,
              backgroundColor: Colors.grey,
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
            );
          }

          final userJob = snapshot.data;
          if (userJob != null) {
            // User has posted a job, show "My Job" button
            _logger.debug('Showing MyJobButton for job: ${userJob.title} with status: ${userJob.status}');
            return MyJobButton(job: userJob);
          } else {
            // User hasn't posted a job, show "Post Job" button
            _logger.debug('Showing Post Job button');
            return FloatingActionButton(
              onPressed: () {
                // Check if user is logged in
                final user = FirebaseAuth.instance.currentUser;
                if (user == null) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('You must be logged in to post a job')),
                  );
                  return;
                }

                Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => const CreateJobScreen()),
                );
              },
              backgroundColor: AppColors.primaryYellow,
              child: const Icon(Icons.add, color: Colors.black),
            );
          }
        },
      ),
    );
  }

  Widget _buildCompactCategoryChip(BuildContext context, String label, {bool isSelected = false}) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return GestureDetector(
      onTap: () {
        setState(() {
          if (label == 'All' || label == 'Full-time' || label == 'Part-time') {
            if (_selectedEmploymentType != label && (label == 'Full-time' || label == 'Part-time')) {
              _selectedEmploymentType = label;
            } else if (label == 'All' && _selectedEmploymentType != 'All') {
              _selectedEmploymentType = 'All';
            }
          } else {
            if (_selectedCategory != label) {
              _selectedCategory = label;
            }
          }
        });
      },
      child: Container(
        margin: const EdgeInsets.only(right: 8),
        child: Chip(
          label: Text(label),
          backgroundColor: isSelected
              ? AppColors.primaryYellow
              : (isDarkMode ? AppColors.darkSurface : Colors.white),
          labelStyle: TextStyle(
            color: isSelected
                ? Colors.black
                : (isDarkMode ? AppColors.darkText : AppColors.lightText),
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
            fontSize: 13,
          ),
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
            side: BorderSide(
              color: isSelected
                  ? Colors.transparent
                  : isDarkMode ? Colors.grey.shade700 : Colors.grey.shade300,
              width: 1,
            ),
          ),
          materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
        ),
      ),
    );
  }



  Widget _buildJobCard(BuildContext context, JobModel job) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    // Get appropriate icon based on job type
    IconData jobTypeIcon;
    switch (job.type) {
      case 'InDrive':
        jobTypeIcon = Icons.drive_eta;
        break;
      case 'Household':
        jobTypeIcon = Icons.home;
        break;
      case 'Company':
        jobTypeIcon = Icons.business;
        break;
      default:
        jobTypeIcon = Icons.work;
    }

    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => JobDetailsScreen(jobId: job.jobId)),
        );
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: 16),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isDarkMode ? AppColors.darkSurface : Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    color: AppColors.primaryYellow.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    jobTypeIcon,
                    color: AppColors.primaryYellow,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        job.title,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: isDarkMode ? AppColors.darkText : AppColors.lightText,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        job.type,
                        style: TextStyle(
                          color: isDarkMode ? AppColors.darkTextSecondary : AppColors.lightTextSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: job.employmentType == 'Full-time'
                        ? AppColors.success.withOpacity(0.2)
                        : AppColors.primaryYellow.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    job.employmentType,
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      color: job.employmentType == 'Full-time'
                          ? AppColors.success
                          : AppColors.primaryYellow,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Icon(
                  Icons.location_on_outlined,
                  size: 16,
                  color: isDarkMode ? AppColors.darkTextSecondary : AppColors.lightTextSecondary,
                ),
                const SizedBox(width: 4),
                Text(
                  job.city,
                  style: TextStyle(
                    color: isDarkMode ? AppColors.darkTextSecondary : AppColors.lightTextSecondary,
                  ),
                ),
                const Spacer(),
                Text(
                  '${job.salary} PKR',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    color: AppColors.success,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Icon(
                  Icons.home_work_outlined,
                  size: 16,
                  color: isDarkMode ? AppColors.darkTextSecondary : AppColors.lightTextSecondary,
                ),
                const SizedBox(width: 4),
                Text(
                  'Benefits: ${job.benefits}',
                  style: TextStyle(
                    color: isDarkMode ? AppColors.darkTextSecondary : AppColors.lightTextSecondary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Icon(
                  Icons.access_time,
                  size: 16,
                  color: isDarkMode ? AppColors.darkTextSecondary : AppColors.lightTextSecondary,
                ),
                const SizedBox(width: 4),
                Expanded(
                  child: Text(
                    'Hours: ${job.dutyHours.join(", ")}',
                    style: TextStyle(
                      color: isDarkMode ? AppColors.darkTextSecondary : AppColors.lightTextSecondary,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}