name: Web Build and Deploy

on:
  push:
    branches:
      - main
      - master
      - develop
    paths:
      - 'main_app/**'
  pull_request:
    branches:
      - main
      - master
    paths:
      - 'main_app/**'
  workflow_dispatch:
    inputs:
      environment:
        description: 'Deployment environment'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production

env:
  FLUTTER_VERSION: '3.19.0'
  NODE_VERSION: '18'

jobs:
  test:
    name: Run Tests
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./main_app
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: ${{ env.FLUTTER_VERSION }}
          channel: 'stable'
          cache: true
          
      - name: Get Flutter dependencies
        run: flutter pub get
        
      - name: Verify formatting
        run: dart format --output=none --set-exit-if-changed .
        
      - name: Analyze project source
        run: flutter analyze --no-fatal-infos
        
      - name: Run unit tests
        run: flutter test --coverage
        
      - name: Upload coverage reports
        uses: codecov/codecov-action@v3
        with:
          file: ./main_app/coverage/lcov.info
          fail_ci_if_error: false

  build_web:
    name: Build Web
    needs: test
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./main_app
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: ${{ env.FLUTTER_VERSION }}
          channel: 'stable'
          cache: true
          
      - name: Get Flutter dependencies
        run: flutter pub get
        
      - name: Determine environment
        id: env
        run: |
          if [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
            echo "environment=${{ github.event.inputs.environment }}" >> $GITHUB_OUTPUT
          elif [[ "${{ github.ref }}" == "refs/heads/main" ]] || [[ "${{ github.ref }}" == "refs/heads/master" ]]; then
            echo "environment=production" >> $GITHUB_OUTPUT
          else
            echo "environment=staging" >> $GITHUB_OUTPUT
          fi
          
      - name: Build web app
        run: |
          flutter build web --release \
            --web-renderer html \
            --dart-define=ENVIRONMENT=${{ steps.env.outputs.environment }} \
            --source-maps \
            --pwa-strategy=offline-first
        
      - name: Upload web build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: web-build-${{ steps.env.outputs.environment }}
          path: main_app/build/web/
          retention-days: 30

  deploy_preview:
    name: Deploy Preview
    needs: build_web
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'
    defaults:
      run:
        working-directory: ./main_app
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Download web build artifacts
        uses: actions/download-artifact@v4
        with:
          name: web-build-staging
          path: main_app/build/web/
          
      - name: Deploy to Firebase Hosting Preview
        uses: FirebaseExtended/action-hosting-deploy@v0
        with:
          repoToken: '${{ secrets.GITHUB_TOKEN }}'
          firebaseServiceAccount: '${{ secrets.FIREBASE_SERVICE_ACCOUNT_DRIVE_ON_B2AF8 }}'
          projectId: drive-on-b2af8
          target: driveon
          entryPoint: main_app

  deploy_staging:
    name: Deploy to Staging
    needs: build_web
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/develop' || (github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'staging')
    defaults:
      run:
        working-directory: ./main_app
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Download web build artifacts
        uses: actions/download-artifact@v4
        with:
          name: web-build-staging
          path: main_app/build/web/
          
      - name: Deploy to Firebase Hosting Staging
        uses: FirebaseExtended/action-hosting-deploy@v0
        with:
          repoToken: '${{ secrets.GITHUB_TOKEN }}'
          firebaseServiceAccount: '${{ secrets.FIREBASE_SERVICE_ACCOUNT_DRIVE_ON_B2AF8 }}'
          projectId: drive-on-b2af8
          target: driveon
          channelId: staging
          entryPoint: main_app

  deploy_production:
    name: Deploy to Production
    needs: build_web
    runs-on: ubuntu-latest
    if: (github.ref == 'refs/heads/main' || github.ref == 'refs/heads/master') || (github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'production')
    defaults:
      run:
        working-directory: ./main_app
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Download web build artifacts
        uses: actions/download-artifact@v4
        with:
          name: web-build-production
          path: main_app/build/web/
          
      - name: Deploy to Firebase Hosting Production
        uses: FirebaseExtended/action-hosting-deploy@v0
        with:
          repoToken: '${{ secrets.GITHUB_TOKEN }}'
          firebaseServiceAccount: '${{ secrets.FIREBASE_SERVICE_ACCOUNT_DRIVE_ON_B2AF8 }}'
          projectId: drive-on-b2af8
          target: driveon
          channelId: live
          entryPoint: main_app
          
      - name: Create GitHub Release
        if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/master'
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: v${{ github.run_number }}
          release_name: Release v${{ github.run_number }}
          body: |
            🚀 Production deployment from commit: ${{ github.sha }}
            
            **Changes:**
            ${{ github.event.head_commit.message }}
            
            **Deployment URLs:**
            - 🌐 Web App: https://drive-on-b2af8.web.app
            - 📱 Firebase Console: https://console.firebase.google.com/project/drive-on-b2af8
          draft: false
          prerelease: false
