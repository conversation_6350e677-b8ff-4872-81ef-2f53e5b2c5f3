import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/video_item.dart';
import '../../../../core/utils/app_logger.dart';

class YouTubeService {
  static const String _apiKey = 'AIzaSyAFDM-JlnS0N1dTdBv-ezlJ0CSJ4peB244';
  static const String _channelId = 'UCBlrOI2QsgIq3rk305SfuHw';
  static const String _baseUrl = 'https://www.googleapis.com/youtube/v3';

  final http.Client _httpClient;
  final AppLogger _logger = AppLogger('YouTubeService');

  YouTubeService({http.Client? httpClient}) 
      : _httpClient = httpClient ?? http.Client();

  /// Fetch videos from the specified channel
  Future<List<VideoItem>> fetchChannelVideos({
    int maxResults = 50,
    String? pageToken,
  }) async {
    try {
      // First, get the channel's uploads playlist ID
      final uploadsPlaylistId = await _getUploadsPlaylistId();
      if (uploadsPlaylistId == null) {
        throw Exception('Could not find uploads playlist for channel');
      }

      // Get videos from the uploads playlist
      final playlistItemsUrl = Uri.parse(
        '$_baseUrl/playlistItems'
        '?part=snippet'
        '&playlistId=$uploadsPlaylistId'
        '&maxResults=$maxResults'
        '&order=date'
        '&key=$_apiKey'
        '${pageToken != null ? '&pageToken=$pageToken' : ''}'
      );

      final playlistResponse = await _httpClient.get(playlistItemsUrl);
      
      if (playlistResponse.statusCode != 200) {
        throw Exception('Failed to fetch playlist items: ${playlistResponse.statusCode}');
      }

      final playlistData = json.decode(playlistResponse.body);
      final items = playlistData['items'] as List<dynamic>? ?? [];

      if (items.isEmpty) {
        return [];
      }

      // Extract video IDs
      final videoIds = items
          .map((item) => item['snippet']?['resourceId']?['videoId'])
          .where((id) => id != null)
          .cast<String>()
          .toList();

      if (videoIds.isEmpty) {
        return [];
      }

      // Get detailed video information
      return await _getVideoDetails(videoIds);
    } catch (e) {
      _logger.error('Error fetching YouTube videos', error: e);
      return [];
    }
  }

  /// Get the uploads playlist ID for the channel
  Future<String?> _getUploadsPlaylistId() async {
    try {
      final channelUrl = Uri.parse(
        '$_baseUrl/channels'
        '?part=contentDetails'
        '&id=$_channelId'
        '&key=$_apiKey'
      );

      final response = await _httpClient.get(channelUrl);
      
      if (response.statusCode != 200) {
        throw Exception('Failed to fetch channel info: ${response.statusCode}');
      }

      final data = json.decode(response.body);
      final items = data['items'] as List<dynamic>? ?? [];
      
      if (items.isEmpty) {
        return null;
      }

      return items[0]['contentDetails']?['relatedPlaylists']?['uploads'];
    } catch (e) {
      _logger.error('Error getting uploads playlist ID', error: e);
      return null;
    }
  }

  /// Get detailed information for specific video IDs
  Future<List<VideoItem>> _getVideoDetails(List<String> videoIds) async {
    try {
      final videoUrl = Uri.parse(
        '$_baseUrl/videos'
        '?part=snippet,statistics,contentDetails'
        '&id=${videoIds.join(',')}'
        '&key=$_apiKey'
      );

      final response = await _httpClient.get(videoUrl);
      
      if (response.statusCode != 200) {
        throw Exception('Failed to fetch video details: ${response.statusCode}');
      }

      final data = json.decode(response.body);
      final items = data['items'] as List<dynamic>? ?? [];

      return items
          .map((item) => VideoItem.fromJson(item))
          .toList();
    } catch (e) {
      _logger.error('Error getting video details', error: e);
      return [];
    }
  }

  /// Search for videos in the channel
  Future<List<VideoItem>> searchChannelVideos({
    required String query,
    int maxResults = 25,
  }) async {
    try {
      final searchUrl = Uri.parse(
        '$_baseUrl/search'
        '?part=snippet'
        '&channelId=$_channelId'
        '&q=${Uri.encodeComponent(query)}'
        '&type=video'
        '&order=relevance'
        '&maxResults=$maxResults'
        '&key=$_apiKey'
      );

      final response = await _httpClient.get(searchUrl);
      
      if (response.statusCode != 200) {
        throw Exception('Failed to search videos: ${response.statusCode}');
      }

      final data = json.decode(response.body);
      final items = data['items'] as List<dynamic>? ?? [];

      if (items.isEmpty) {
        return [];
      }

      // Extract video IDs from search results
      final videoIds = items
          .map((item) => item['id']?['videoId'])
          .where((id) => id != null)
          .cast<String>()
          .toList();

      if (videoIds.isEmpty) {
        return [];
      }

      // Get detailed video information
      return await _getVideoDetails(videoIds);
    } catch (e) {
      _logger.error('Error searching YouTube videos', error: e);
      return [];
    }
  }

  void dispose() {
    _httpClient.close();
  }
}
