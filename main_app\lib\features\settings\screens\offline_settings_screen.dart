import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import '../../../core/services/offline_initialization_service.dart';
import '../../../core/services/network_state_manager.dart';
import '../../../core/widgets/offline_status_widget.dart';
import '../../../core/theme/colors.dart';
import '../../../core/utils/app_logger.dart';

/// Screen for managing offline storage settings
/// 
/// Features:
/// - View offline storage statistics
/// - Manual sync controls
/// - Clear offline data
/// - Network status monitoring
class OfflineSettingsScreen extends StatefulWidget {
  const OfflineSettingsScreen({Key? key}) : super(key: key);

  @override
  State<OfflineSettingsScreen> createState() => _OfflineSettingsScreenState();
}

class _OfflineSettingsScreenState extends State<OfflineSettingsScreen> {
  final AppLogger _logger = AppLogger('OfflineSettingsScreen');
  final OfflineInitializationService _offlineService = OfflineInitializationService();
  final NetworkStateManager _networkManager = GetIt.instance<NetworkStateManager>();

  Map<String, dynamic> _offlineStats = {};
  bool _isLoading = true;
  bool _isSyncing = false;
  bool _isClearing = false;

  @override
  void initState() {
    super.initState();
    _loadOfflineStats();
    _listenToNetworkEvents();
  }

  void _listenToNetworkEvents() {
    _networkManager.networkEventsStream.listen((event) {
      if (mounted) {
        switch (event) {
          case NetworkEvent.syncStarted:
            setState(() => _isSyncing = true);
            break;
          case NetworkEvent.syncCompleted:
          case NetworkEvent.syncFailed:
            setState(() => _isSyncing = false);
            _loadOfflineStats(); // Refresh stats after sync
            break;
          default:
            break;
        }
      }
    });
  }

  Future<void> _loadOfflineStats() async {
    try {
      final stats = await _offlineService.getOfflineStats();
      if (mounted) {
        setState(() {
          _offlineStats = stats;
          _isLoading = false;
        });
      }
    } catch (e) {
      _logger.error('Error loading offline stats', error: e);
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: isDarkMode ? AppColors.darkBackground : AppColors.lightBackground,
      appBar: AppBar(
        title: const Text(
          'Offline Storage',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: isDarkMode ? AppColors.darkSurface : AppColors.lightSurface,
        foregroundColor: isDarkMode ? AppColors.darkText : AppColors.lightText,
        elevation: 0,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _loadOfflineStats,
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Network Status
                    const OfflineStatusWidget(showSyncButton: true),
                    
                    const SizedBox(height: 24),
                    
                    // Storage Statistics
                    _buildStorageStats(isDarkMode),
                    
                    const SizedBox(height: 24),
                    
                    // Actions
                    _buildActions(isDarkMode),
                    
                    const SizedBox(height: 24),
                    
                    // Network Statistics
                    _buildNetworkStats(isDarkMode),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildStorageStats(bool isDarkMode) {
    return Card(
      color: isDarkMode ? AppColors.darkSurface : AppColors.lightSurface,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Storage Statistics',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: isDarkMode ? AppColors.darkText : AppColors.lightText,
              ),
            ),
            const SizedBox(height: 16),
            
            if (_offlineStats.containsKey('news'))
              _buildStatItem(
                'News Articles',
                '${_offlineStats['news']['articles'] ?? 0} articles',
                Icons.article,
                isDarkMode,
              ),
            
            if (_offlineStats.containsKey('jobs'))
              _buildStatItem(
                'Job Listings',
                '${_offlineStats['jobs']['jobs'] ?? 0} jobs',
                Icons.work,
                isDarkMode,
              ),
            
            if (_offlineStats.containsKey('drivers'))
              _buildStatItem(
                'Driver Profiles',
                '${_offlineStats['drivers']['drivers'] ?? 0} drivers',
                Icons.person,
                isDarkMode,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String title, String value, IconData icon, bool isDarkMode) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Icon(
            icon,
            color: AppColors.primaryYellow,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: isDarkMode ? AppColors.darkText : AppColors.lightText,
                  ),
                ),
                Text(
                  value,
                  style: TextStyle(
                    fontSize: 12,
                    color: isDarkMode ? AppColors.darkTextSecondary : AppColors.lightTextSecondary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActions(bool isDarkMode) {
    return Card(
      color: isDarkMode ? AppColors.darkSurface : AppColors.lightSurface,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Actions',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: isDarkMode ? AppColors.darkText : AppColors.lightText,
              ),
            ),
            const SizedBox(height: 16),
            
            // Force Sync Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _networkManager.isOnline && !_isSyncing ? _forceSync : null,
                icon: _isSyncing
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Icon(Icons.sync),
                label: Text(_isSyncing ? 'Syncing...' : 'Force Sync'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primaryYellow,
                  foregroundColor: Colors.black,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
            
            const SizedBox(height: 12),
            
            // Clear Data Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isClearing ? null : _showClearDataDialog,
                icon: _isClearing
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Icon(Icons.delete_sweep),
                label: Text(_isClearing ? 'Clearing...' : 'Clear Offline Data'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNetworkStats(bool isDarkMode) {
    final networkStats = _offlineStats['network'] as Map<String, dynamic>? ?? {};
    
    return Card(
      color: isDarkMode ? AppColors.darkSurface : AppColors.lightSurface,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Network Status',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: isDarkMode ? AppColors.darkText : AppColors.lightText,
              ),
            ),
            const SizedBox(height: 16),
            
            _buildStatItem(
              'Connection Status',
              networkStats['isOnline'] == true ? 'Online' : 'Offline',
              networkStats['isOnline'] == true ? Icons.wifi : Icons.wifi_off,
              isDarkMode,
            ),
            
            _buildStatItem(
              'Pending Operations',
              '${networkStats['syncQueueLength'] ?? 0} items',
              Icons.queue,
              isDarkMode,
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _forceSync() async {
    setState(() => _isSyncing = true);
    
    try {
      await _offlineService.forceSyncAll();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Sync completed successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Sync failed: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isSyncing = false);
      }
    }
  }

  void _showClearDataDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear Offline Data'),
        content: const Text(
          'This will remove all cached data. The app will need to download data again when online. Are you sure?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _clearOfflineData();
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Clear'),
          ),
        ],
      ),
    );
  }

  Future<void> _clearOfflineData() async {
    setState(() => _isClearing = true);
    
    try {
      await _offlineService.clearAllOfflineData();
      await _loadOfflineStats(); // Refresh stats
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Offline data cleared successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to clear data: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isClearing = false);
      }
    }
  }
}
