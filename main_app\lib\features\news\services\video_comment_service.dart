import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../data/models/video_comment.dart';

class VideoCommentService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  /// Get comments for a specific video
  Stream<List<VideoComment>> getVideoComments(String videoId) {
    return _firestore
        .collection('video_comments')
        .where('videoId', isEqualTo: videoId)
        .snapshots()
        .map((snapshot) {
          final comments = snapshot.docs
              .map((doc) => VideoComment.fromFirestore(doc))
              .where((comment) => comment.parentCommentId == null) // Filter top-level comments in memory
              .toList();

          // Sort in memory to avoid index requirement
          comments.sort((a, b) => b.createdAt.compareTo(a.createdAt)); // Descending order
          return comments;
        });
  }

  /// Get replies for a specific comment
  Stream<List<VideoComment>> getCommentReplies(String parentCommentId) {
    return _firestore
        .collection('video_comments')
        .where('parentCommentId', isEqualTo: parentCommentId)
        .snapshots()
        .map((snapshot) {
          final comments = snapshot.docs
              .map((doc) => VideoComment.fromFirestore(doc))
              .toList();

          // Sort in memory to avoid index requirement
          comments.sort((a, b) => a.createdAt.compareTo(b.createdAt));
          return comments;
        });
  }

  /// Add a new comment
  Future<void> addComment({
    required String videoId,
    required String comment,
    String? parentCommentId,
    String? replyToUserId,
    String? replyToUserName,
  }) async {
    final user = _auth.currentUser;
    if (user == null) throw Exception('User must be logged in to comment');

    final newComment = VideoComment(
      id: '', // Will be set by Firestore
      videoId: videoId,
      userId: user.uid,
      userName: user.displayName ?? 'Anonymous',
      userEmail: user.email ?? '',
      comment: comment,
      createdAt: DateTime.now(),
      likes: [],
      dislikes: [],
      parentCommentId: parentCommentId,
      replyToUserId: replyToUserId,
      replyToUserName: replyToUserName,
      isEdited: false,
    );

    await _firestore.collection('video_comments').add(newComment.toFirestore());
  }

  /// Edit a comment
  Future<void> editComment(String commentId, String newComment) async {
    final user = _auth.currentUser;
    if (user == null) throw Exception('User must be logged in');

    // Check if user owns the comment
    final commentDoc = await _firestore.collection('video_comments').doc(commentId).get();
    if (!commentDoc.exists) throw Exception('Comment not found');

    final commentData = VideoComment.fromFirestore(commentDoc);
    if (commentData.userId != user.uid) {
      throw Exception('You can only edit your own comments');
    }

    await _firestore.collection('video_comments').doc(commentId).update({
      'comment': newComment,
      'updatedAt': Timestamp.fromDate(DateTime.now()),
      'isEdited': true,
    });
  }

  /// Delete a comment
  Future<void> deleteComment(String commentId) async {
    final user = _auth.currentUser;
    if (user == null) throw Exception('User must be logged in');

    // Check if user owns the comment
    final commentDoc = await _firestore.collection('video_comments').doc(commentId).get();
    if (!commentDoc.exists) throw Exception('Comment not found');

    final commentData = VideoComment.fromFirestore(commentDoc);
    if (commentData.userId != user.uid) {
      throw Exception('You can only delete your own comments');
    }

    // Delete the comment and all its replies
    final batch = _firestore.batch();

    // Delete the main comment
    batch.delete(_firestore.collection('video_comments').doc(commentId));

    // Delete all replies
    final replies = await _firestore
        .collection('video_comments')
        .where('parentCommentId', isEqualTo: commentId)
        .get();

    for (final reply in replies.docs) {
      batch.delete(reply.reference);
    }

    await batch.commit();
  }

  /// Like a comment
  Future<void> likeComment(String commentId) async {
    final user = _auth.currentUser;
    if (user == null) throw Exception('User must be logged in');

    final commentRef = _firestore.collection('video_comments').doc(commentId);

    await _firestore.runTransaction((transaction) async {
      final commentDoc = await transaction.get(commentRef);
      if (!commentDoc.exists) throw Exception('Comment not found');

      final comment = VideoComment.fromFirestore(commentDoc);
      final likes = List<String>.from(comment.likes);
      final dislikes = List<String>.from(comment.dislikes);

      // Remove from dislikes if present
      dislikes.remove(user.uid);

      // Toggle like
      if (likes.contains(user.uid)) {
        likes.remove(user.uid);
      } else {
        likes.add(user.uid);
      }

      transaction.update(commentRef, {
        'likes': likes,
        'dislikes': dislikes,
      });
    });
  }

  /// Dislike a comment
  Future<void> dislikeComment(String commentId) async {
    final user = _auth.currentUser;
    if (user == null) throw Exception('User must be logged in');

    final commentRef = _firestore.collection('video_comments').doc(commentId);

    await _firestore.runTransaction((transaction) async {
      final commentDoc = await transaction.get(commentRef);
      if (!commentDoc.exists) throw Exception('Comment not found');

      final comment = VideoComment.fromFirestore(commentDoc);
      final likes = List<String>.from(comment.likes);
      final dislikes = List<String>.from(comment.dislikes);

      // Remove from likes if present
      likes.remove(user.uid);

      // Toggle dislike
      if (dislikes.contains(user.uid)) {
        dislikes.remove(user.uid);
      } else {
        dislikes.add(user.uid);
      }

      transaction.update(commentRef, {
        'likes': likes,
        'dislikes': dislikes,
      });
    });
  }

  /// Get comment count for a video
  Future<int> getCommentCount(String videoId) async {
    final snapshot = await _firestore
        .collection('video_comments')
        .where('videoId', isEqualTo: videoId)
        .get();

    return snapshot.docs.length;
  }
}
