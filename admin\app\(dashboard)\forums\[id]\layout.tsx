// Generate static params for static export
export async function generateStaticParams() {
  return [
    { id: 'general' },
    { id: 'help' },
    { id: 'announcements' },
    { id: 'support' },
    { id: 'feedback' },
    { id: 'zWKZhBNOlYbiYcBXhswD' },
    { id: 'sample-forum-1' },
    { id: 'sample-forum-2' },
    { id: 'sample-forum-3' },
    { id: 'sample-forum-4' },
    { id: 'sample-forum-5' },
    { id: 'technical-support' },
    { id: 'bug-reports' },
    { id: 'feature-requests' },
    { id: 'community' },
  ]
}

export default function ForumLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return <>{children}</>
}
