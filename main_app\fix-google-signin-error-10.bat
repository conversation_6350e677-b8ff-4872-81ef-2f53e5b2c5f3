@echo off
echo ========================================
echo GOOGLE SIGN-IN ERROR 10 FIX
echo ========================================
echo.

echo 🔍 DIAGNOSING GOOGLE SIGN-IN ERROR 10...
echo.

echo Error 10 typically means:
echo 1. SHA-1 fingerprint mismatch
echo 2. Client ID configuration issue
echo 3. Firebase configuration not propagated
echo 4. App cache needs clearing
echo.

echo 🛠️ APPLYING FIXES...
echo.

echo Step 1: Clearing Flutter cache...
flutter clean
if %errorlevel% neq 0 (
    echo ❌ Failed to clean Flutter cache
    pause
    exit /b 1
)

echo Step 2: Clearing app data on device...
adb shell pm clear com.driver.drive_on.debug
if %errorlevel% neq 0 (
    echo ⚠️ Could not clear app data (device may not be connected)
)

echo Step 3: Clearing Google Play Services cache...
adb shell pm clear com.google.android.gms
if %errorlevel% neq 0 (
    echo ⚠️ Could not clear Google Play Services cache (device may not be connected)
)

echo Step 4: Getting dependencies...
flutter pub get
if %errorlevel% neq 0 (
    echo ❌ Failed to get dependencies
    pause
    exit /b 1
)

echo Step 5: Building debug APK with updated configuration...
flutter build apk --debug
if %errorlevel% neq 0 (
    echo ❌ Failed to build debug APK
    pause
    exit /b 1
)

echo.
echo ✅ FIXES APPLIED SUCCESSFULLY!
echo.

echo 📋 NEXT STEPS:
echo 1. Install the updated APK: flutter install
echo 2. Test Google Sign-In functionality
echo 3. If still failing, check Firebase Console configuration
echo.

echo 🔑 FIREBASE CONFIGURATION CHECK:
echo.
echo Go to: https://console.firebase.google.com/project/drive-on-b2af8/settings/general/
echo.
echo Ensure these SHA-1 fingerprints are added:
echo.
echo DEBUG (current):
echo 9A:8E:90:68:D4:97:C6:E2:5A:EF:B0:51:75:BC:CB:B5:CC:BD:55:DC
echo.
echo PRODUCTION:
echo 85:C3:07:8A:88:35:3D:1D:15:88:76:93:84:F8:D7:17:41:A9:4B:A3
echo.

echo 📱 TESTING INSTRUCTIONS:
echo 1. Open the app
echo 2. Try Google Sign-In
echo 3. If error persists, wait 5-10 minutes for Firebase propagation
echo 4. Clear app data and try again
echo.

echo Press any key to install the updated APK...
pause >nul

flutter install
if %errorlevel% neq 0 (
    echo ❌ Failed to install APK
    echo Please install manually or check device connection
    pause
    exit /b 1
)

echo.
echo 🎉 INSTALLATION COMPLETE!
echo.
echo The app should now work with Google Sign-In.
echo If you still get Error 10, please:
echo 1. Wait 10 minutes for Firebase configuration to propagate
echo 2. Clear app data: Settings > Apps > Drive-On > Storage > Clear Data
echo 3. Try Google Sign-In again
echo.

echo Press any key to exit...
pause >nul
