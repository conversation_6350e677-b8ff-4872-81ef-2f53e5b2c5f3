import 'package:cloud_firestore/cloud_firestore.dart';

/// Notification model for the main app
class NotificationModel {
  final String id;
  final String title;
  final String body;
  final String type; // 'message', 'verification', 'system', 'basic'
  final String? senderId;
  final String? senderName;
  final String? roomId;
  final String? imageUrl;
  final Map<String, dynamic>? data;
  final DateTime createdAt;
  final bool isRead;
  final String userId; // The user this notification belongs to

  NotificationModel({
    required this.id,
    required this.title,
    required this.body,
    required this.type,
    this.senderId,
    this.senderName,
    this.roomId,
    this.imageUrl,
    this.data,
    required this.createdAt,
    required this.isRead,
    required this.userId,
  });

  /// Create from Firestore document
  factory NotificationModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    
    return NotificationModel(
      id: doc.id,
      title: data['title'] ?? '',
      body: data['body'] ?? '',
      type: data['type'] ?? 'basic',
      senderId: data['senderId'],
      senderName: data['senderName'],
      roomId: data['roomId'],
      imageUrl: data['imageUrl'],
      data: data['data'] as Map<String, dynamic>?,
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      isRead: data['isRead'] ?? false,
      userId: data['userId'] ?? '',
    );
  }

  /// Convert to Firestore document
  Map<String, dynamic> toFirestore() {
    return {
      'title': title,
      'body': body,
      'type': type,
      'senderId': senderId,
      'senderName': senderName,
      'roomId': roomId,
      'imageUrl': imageUrl,
      'data': data,
      'createdAt': Timestamp.fromDate(createdAt),
      'isRead': isRead,
      'userId': userId,
    };
  }

  /// Create a copy with updated fields
  NotificationModel copyWith({
    String? id,
    String? title,
    String? body,
    String? type,
    String? senderId,
    String? senderName,
    String? roomId,
    String? imageUrl,
    Map<String, dynamic>? data,
    DateTime? createdAt,
    bool? isRead,
    String? userId,
  }) {
    return NotificationModel(
      id: id ?? this.id,
      title: title ?? this.title,
      body: body ?? this.body,
      type: type ?? this.type,
      senderId: senderId ?? this.senderId,
      senderName: senderName ?? this.senderName,
      roomId: roomId ?? this.roomId,
      imageUrl: imageUrl ?? this.imageUrl,
      data: data ?? this.data,
      createdAt: createdAt ?? this.createdAt,
      isRead: isRead ?? this.isRead,
      userId: userId ?? this.userId,
    );
  }

  /// Get notification icon based on type
  String get iconPath {
    switch (type) {
      case 'message':
        return 'assets/icons/message.png';
      case 'verification':
        return 'assets/icons/verification.png';
      case 'system':
        return 'assets/icons/system.png';
      default:
        return 'assets/icons/notification.png';
    }
  }

  /// Get notification color based on type
  String get colorHex {
    switch (type) {
      case 'message':
        return '#2196F3'; // Blue
      case 'verification':
        return '#4CAF50'; // Green
      case 'system':
        return '#FF9800'; // Orange
      default:
        return '#9E9E9E'; // Grey
    }
  }

  /// Get formatted time ago string
  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inDays > 7) {
      return '${createdAt.day}/${createdAt.month}/${createdAt.year}';
    } else if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  /// Check if notification is recent (within 24 hours)
  bool get isRecent {
    final now = DateTime.now();
    final difference = now.difference(createdAt);
    return difference.inHours < 24;
  }

  @override
  String toString() {
    return 'NotificationModel(id: $id, title: $title, type: $type, isRead: $isRead, createdAt: $createdAt)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is NotificationModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
