import { initializeApp, getApps, type FirebaseApp } from 'firebase/app'
import { getAuth, type Auth } from 'firebase/auth'
import { getFirestore, type Firestore } from 'firebase/firestore'
import { getStorage, type FirebaseStorage } from 'firebase/storage'

// Firebase configuration for Drive-On project
const firebaseConfig = {
  apiKey: "AIzaSyBQehlAZFI-WngAMwzblEu7JBdMcCr-P80",
  authDomain: "drive-on-b2af8.firebaseapp.com",
  projectId: "drive-on-b2af8",
  storageBucket: "drive-on-b2af8.firebasestorage.app",
  messagingSenderId: "206767723448",
  appId: "1:206767723448:android:6a8e1d9c3c8992992d754d",
  databaseURL: "https://drive-on-b2af8-default-rtdb.firebaseio.com"
}

// Initialize Firebase
let app: FirebaseApp

if (getApps().length === 0) {
  app = initializeApp(firebaseConfig)
} else {
  app = getApps()[0]
}

const auth = getAuth(app)
const db = getFirestore(app)
const storage = getStorage(app)

export { app, auth, db, storage }

// Firebase Admin SDK configuration (server-side only)
export const adminConfig = {
  projectId: "drive-on-b2af8",
  databaseURL: "https://drive-on-b2af8-default-rtdb.firebaseio.com",
  serviceAccountPath: "./serviceAccountKey.json"
}
