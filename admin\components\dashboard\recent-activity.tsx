'use client'

import { motion } from 'framer-motion'
import { useRouter } from 'next/navigation'
import {
  UserPlus,
  Car,
  Building2,
  Briefcase,
  MessageSquare,
  CheckCircle,
  XCircle,
  Clock,
  ExternalLink
} from 'lucide-react'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { formatRelativeTime } from '@/lib/utils'
import { useNotifications } from '@/lib/hooks/use-notifications'

// Map notification types to activity types and navigation paths
const getActivityInfo = (notificationType: string) => {
  switch (notificationType) {
    case 'driver_request':
      return {
        type: 'driver_applied',
        icon: Car,
        path: '/drivers',
        color: 'text-green-600',
        bgColor: 'bg-green-100'
      }
    case 'partner_request':
      return {
        type: 'partner_request',
        icon: Building2,
        path: '/partners',
        color: 'text-purple-600',
        bgColor: 'bg-purple-100'
      }
    case 'forum_message':
      return {
        type: 'forum_message',
        icon: MessageSquare,
        path: '/forums',
        color: 'text-blue-600',
        bgColor: 'bg-blue-100'
      }
    case 'query':
      return {
        type: 'query_received',
        icon: MessageSquare,
        path: '/queries',
        color: 'text-orange-600',
        bgColor: 'bg-orange-100'
      }
    case 'user_registration':
      return {
        type: 'user_registered',
        icon: UserPlus,
        path: '/users',
        color: 'text-blue-600',
        bgColor: 'bg-blue-100'
      }
    default:
      return {
        type: 'system',
        icon: Clock,
        path: '/dashboard',
        color: 'text-gray-600',
        bgColor: 'bg-gray-100'
      }
  }
}

const getStatusIcon = (priority: string) => {
  switch (priority) {
    case 'high':
    case 'urgent':
      return XCircle
    case 'medium':
      return Clock
    case 'low':
      return CheckCircle
    default:
      return Clock
  }
}

const getStatusVariant = (priority: string) => {
  switch (priority) {
    case 'urgent':
      return 'error'
    case 'high':
      return 'warning'
    case 'medium':
      return 'info'
    case 'low':
      return 'success'
    default:
      return 'info'
  }
}

export function RecentActivity() {
  const router = useRouter()
  const { notifications, markAsRead, isLoading } = useNotifications()

  // Get recent notifications (last 24 hours) and limit to 6
  const recentNotifications = notifications
    .filter(notification => {
      const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000)
      return notification.createdAt > twentyFourHoursAgo
    })
    .slice(0, 6)

  const handleNotificationClick = async (notification: any) => {
    // Mark as read
    await markAsRead(notification.id)

    // Navigate to relevant section
    const activityInfo = getActivityInfo(notification.type)

    // Build specific navigation path based on notification data
    let targetPath = activityInfo.path

    // If there's a specific action URL, use that
    if (notification.actionUrl) {
      targetPath = notification.actionUrl
    } else if (notification.relatedId && notification.relatedCollection) {
      // Build specific path for related items
      switch (notification.type) {
        case 'query':
          targetPath = `/queries/${notification.relatedId}/chat`
          break
        case 'forum_message':
          targetPath = `/forums/${notification.relatedId}`
          break
        case 'driver_request':
          targetPath = `/drivers?highlight=${notification.relatedId}`
          break
        case 'partner_request':
          targetPath = `/partners?highlight=${notification.relatedId}`
          break
        default:
          // Use general path
          break
      }
    }

    router.push(targetPath)
  }

  const handleViewAllActivities = () => {
    router.push('/notifications')
  }
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, x: -20 },
    visible: {
      opacity: 1,
      x: 0,
      transition: {
        duration: 0.3
      }
    }
  }

  if (isLoading) {
    return (
      <div className="text-center py-8">
        <div className="animate-spin w-8 h-8 border-2 border-primary-600 border-t-transparent rounded-full mx-auto mb-4"></div>
        <p className="text-gray-500">Loading recent activities...</p>
      </div>
    )
  }

  if (recentNotifications.length === 0) {
    return (
      <div className="text-center py-8">
        <Clock className="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <p className="text-gray-500">No recent activities</p>
        <p className="text-sm text-gray-400">New notifications will appear here</p>
      </div>
    )
  }

  return (
    <motion.div
      className="space-y-4"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {recentNotifications.map((notification) => {
        const activityInfo = getActivityInfo(notification.type)
        const ActivityIcon = activityInfo.icon
        const StatusIcon = getStatusIcon(notification.priority)

        return (
          <motion.div
            key={notification.id}
            variants={itemVariants}
            className="flex items-start space-x-4 p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200 cursor-pointer group"
            onClick={() => handleNotificationClick(notification)}
          >
            <div className="flex-shrink-0">
              <div className={`w-10 h-10 ${activityInfo.bgColor} rounded-full flex items-center justify-center`}>
                <ActivityIcon className={`w-5 h-5 ${activityInfo.color}`} />
              </div>
            </div>

            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between">
                <p className="text-sm font-medium text-gray-900 truncate">
                  {notification.title}
                </p>
                <div className="flex items-center space-x-2">
                  <Badge
                    variant={getStatusVariant(notification.priority)}
                    className="text-xs"
                  >
                    <StatusIcon className="w-3 h-3 mr-1" />
                    {notification.priority}
                  </Badge>
                  <span className="text-xs text-gray-500">
                    {formatRelativeTime(notification.createdAt)}
                  </span>
                  <ExternalLink className="w-3 h-3 text-gray-400 opacity-0 group-hover:opacity-100 transition-opacity" />
                </div>
              </div>
              <p className="text-sm text-gray-600 mt-1">
                {notification.message}
              </p>
              {!notification.isRead && (
                <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
              )}
            </div>
          </motion.div>
        )
      })}

      <div className="text-center pt-4">
        <Button
          variant="ghost"
          onClick={handleViewAllActivities}
          className="text-sm text-primary-600 hover:text-primary-700 font-medium"
        >
          View all activities →
        </Button>
      </div>
    </motion.div>
  )
}
