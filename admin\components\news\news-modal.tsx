'use client'

import { useState, useEffect, useRef } from 'react'
import { motion } from 'framer-motion'
import {
  X,
  Upload,
  Image,
  FileText,
  CheckCircle,
  AlertCircle
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { formatDate } from '@/lib/utils'
import { ref, uploadBytes, getDownloadURL } from 'firebase/storage'
import { storage } from '@/lib/firebase/config'

interface NewsArticle {
  id: string
  title: string
  content: string
  excerpt: string
  category: 'Car Prices' | 'Industry News' | 'Fuel Prices' | 'Taxes & Duties' | 'Road Safety' | 'Traffic Updates' | 'Accident Reports' | 'General'
  status: 'draft' | 'published' | 'archived'
  featuredImage?: string
  author: string
  tags: string[]
  publishedAt?: Date
  createdAt: Date
  updatedAt: Date
  viewsCount: number
  likesCount: number
  isFeatured: boolean
  isBreaking: boolean
  seoTitle?: string
  seoDescription?: string
  // Main app compatible fields
  description: string
  url: string
  source: string
  imageUrl: string
  views: number
  pinned: boolean
  adminComment?: string
  reactionCounts: { [key: string]: number }
}

interface NewsModalProps {
  article: NewsArticle | null
  mode: 'view' | 'edit' | 'create'
  isOpen: boolean
  onClose: () => void
  onSave: (articleData: Partial<NewsArticle>) => Promise<void>
}

export function NewsModal({ article, mode, isOpen, onClose, onSave }: NewsModalProps) {
  const [formData, setFormData] = useState({
    title: '',
    content: '',
    featuredImage: ''
  })
  const [selectedImage, setSelectedImage] = useState<File | null>(null)
  const [imagePreview, setImagePreview] = useState<string>('')
  const [isLoading, setIsLoading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const fileInputRef = useRef<HTMLInputElement>(null)

  useEffect(() => {
    if (article && (mode === 'view' || mode === 'edit')) {
      setFormData({
        title: article.title || '',
        content: article.content || '',
        featuredImage: article.featuredImage || ''
      })
      setImagePreview(article.featuredImage || '')
    } else if (mode === 'create') {
      setFormData({
        title: '',
        content: '',
        featuredImage: ''
      })
      setImagePreview('')
      setSelectedImage(null)
    }
  }, [article, mode])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setUploadProgress(0)

    try {
      let imageUrl = formData.featuredImage

      // Upload image if a new one is selected
      if (selectedImage) {
        const fileName = `news_${Date.now()}_${selectedImage.name}`
        const imageRef = ref(storage, `news/images/${fileName}`)

        setUploadProgress(25)
        await uploadBytes(imageRef, selectedImage)
        setUploadProgress(75)
        imageUrl = await getDownloadURL(imageRef)
        setUploadProgress(100)
      }

      const submitData = {
        title: formData.title,
        content: formData.content,
        description: formData.content, // Use content as description for main app compatibility
        featuredImage: imageUrl,
        imageUrl: imageUrl, // For main app compatibility
        category: 'General' as NewsArticle['category'],
        status: 'published' as NewsArticle['status'],
        author: 'Admin',
        source: 'Admin',
        url: '',
        tags: [],
        excerpt: formData.content.substring(0, 150) + '...',
        isFeatured: false,
        isBreaking: false,
        views: 0,
        viewsCount: 0,
        likesCount: 0,
        pinned: false,
        reactionCounts: {}
      }

      await onSave(submitData)
    } catch (error) {
      console.error('Error saving article:', error)
    } finally {
      setIsLoading(false)
      setUploadProgress(0)
    }
  }

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleImageSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      // Validate file type
      if (!file.type.startsWith('image/')) {
        alert('Please select an image file')
        return
      }

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        alert('Image size should be less than 5MB')
        return
      }

      setSelectedImage(file)

      // Create preview
      const reader = new FileReader()
      reader.onload = (e) => {
        setImagePreview(e.target?.result as string)
      }
      reader.readAsDataURL(file)
    }
  }

  const removeImage = () => {
    setSelectedImage(null)
    setImagePreview('')
    setFormData(prev => ({ ...prev, featuredImage: '' }))
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="absolute inset-0 bg-black/50"
        onClick={onClose}
      />

      {/* Modal */}
      <motion.div
        initial={{ opacity: 0, scale: 0.95, y: 20 }}
        animate={{ opacity: 1, scale: 1, y: 0 }}
        exit={{ opacity: 0, scale: 0.95, y: 20 }}
        className="relative w-full max-w-4xl mx-4 bg-white rounded-lg shadow-xl max-h-[90vh] overflow-y-auto"
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div className="flex items-center space-x-3">
            <FileText className="w-6 h-6 text-primary" />
            <h2 className="text-xl font-semibold text-gray-900">
              {mode === 'create' ? 'Create New Article' :
               mode === 'edit' ? 'Edit Article' : 'Article Details'}
            </h2>
          </div>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="w-4 h-4" />
          </Button>
        </div>

        {/* Content */}
        <div className="p-6">
          {mode === 'view' ? (
            // View Mode - Simple article display
            <div className="space-y-6">
              {article?.featuredImage && (
                <div className="w-full h-64 bg-gray-200 rounded-lg overflow-hidden">
                  <img
                    src={article.featuredImage}
                    alt={article.title}
                    className="w-full h-full object-cover"
                  />
                </div>
              )}

              <div>
                <h1 className="text-2xl font-bold text-gray-900 mb-4">{article?.title}</h1>
                <div className="prose max-w-none">
                  <p className="text-gray-700 whitespace-pre-wrap">{article?.content}</p>
                </div>
              </div>
            </div>
          ) : (
            // Edit/Create Mode - Simplified Form
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Title */}
              <div className="space-y-2">
                <Label htmlFor="title">Article Title</Label>
                <Input
                  id="title"
                  value={formData.title}
                  onChange={(e) => handleInputChange('title', e.target.value)}
                  placeholder="Enter article title..."
                  required
                />
              </div>

              {/* Description/Content */}
              <div className="space-y-2">
                <Label htmlFor="content">Description</Label>
                <textarea
                  id="content"
                  value={formData.content}
                  onChange={(e) => handleInputChange('content', e.target.value)}
                  rows={8}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                  placeholder="Write your article content here..."
                  required
                />
              </div>

              {/* Image Upload */}
              <div className="space-y-2">
                <Label>Article Image</Label>
                <div className="space-y-4">
                  {/* Image Preview */}
                  {imagePreview && (
                    <div className="relative w-full h-48 bg-gray-100 rounded-lg overflow-hidden">
                      <img
                        src={imagePreview}
                        alt="Preview"
                        className="w-full h-full object-cover"
                      />
                      <button
                        type="button"
                        onClick={removeImage}
                        className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors"
                      >
                        <X className="w-4 h-4" />
                      </button>
                    </div>
                  )}

                  {/* Upload Button */}
                  <div className="flex items-center justify-center w-full">
                    <label
                      htmlFor="image-upload"
                      className="flex flex-col items-center justify-center w-full h-32 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100 transition-colors"
                    >
                      <div className="flex flex-col items-center justify-center pt-5 pb-6">
                        <Upload className="w-8 h-8 mb-2 text-gray-400" />
                        <p className="mb-2 text-sm text-gray-500">
                          <span className="font-semibold">Click to upload</span> or drag and drop
                        </p>
                        <p className="text-xs text-gray-500">PNG, JPG, GIF up to 5MB</p>
                      </div>
                      <input
                        id="image-upload"
                        ref={fileInputRef}
                        type="file"
                        accept="image/*"
                        onChange={handleImageSelect}
                        className="hidden"
                      />
                    </label>
                  </div>

                  {/* Upload Progress */}
                  {uploadProgress > 0 && uploadProgress < 100 && (
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-primary h-2 rounded-full transition-all duration-300"
                        style={{ width: `${uploadProgress}%` }}
                      />
                    </div>
                  )}
                </div>
              </div>

              {/* Actions */}
              <div className="flex justify-end space-x-3 pt-6 border-t">
                <Button type="button" variant="outline" onClick={onClose}>
                  Cancel
                </Button>
                <Button type="submit" disabled={isLoading} className="bg-primary hover:bg-primary/90 text-white">
                  {isLoading ? (
                    <div className="flex items-center space-x-2">
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                      <span>
                        {uploadProgress > 0 && uploadProgress < 100
                          ? `Uploading... ${uploadProgress}%`
                          : 'Creating Article...'
                        }
                      </span>
                    </div>
                  ) : (
                    <div className="flex items-center space-x-2">
                      <CheckCircle className="w-4 h-4" />
                      <span>{mode === 'create' ? 'Create Article' : 'Save Changes'}</span>
                    </div>
                  )}
                </Button>
              </div>
            </form>
          )}
        </div>
      </motion.div>
    </div>
  )
}
