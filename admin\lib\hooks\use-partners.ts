import { useState, useEffect } from 'react'
import { collection, query, orderBy, onSnapshot, doc, updateDoc, deleteDoc, addDoc, Timestamp } from 'firebase/firestore'
import { db } from '@/lib/firebase/config'

interface Partner {
  id: string
  companyName: string
  email: string
  phone: string
  address: string
  description: string
  status: 'pending' | 'approved' | 'rejected'
  createdAt: Date
  approvedAt?: Date
  approvedBy?: string
  rejectionReason?: string
  welcomeEmailSent?: boolean
}

export function usePartners() {
  const [partners, setPartners] = useState<Partner[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const partnersQuery = query(
      collection(db, 'partners'),
      orderBy('createdAt', 'desc')
    )

    const unsubscribe = onSnapshot(
      partnersQuery,
      (snapshot) => {
        const partnersData = snapshot.docs.map(doc => {
          const data = doc.data()
          return {
            id: doc.id,
            companyName: data.companyName || '',
            email: data.email || '',
            phone: data.phone || '',
            address: data.address || '',
            description: data.description || '',
            status: data.status || 'pending',
            createdAt: data.createdAt?.toDate() || new Date(),
            approvedAt: data.approvedAt?.toDate(),
            approvedBy: data.approvedBy,
            rejectionReason: data.rejectionReason,
            welcomeEmailSent: data.welcomeEmailSent || false,
          } as Partner
        })

        setPartners(partnersData)
        setIsLoading(false)
        setError(null)
      },
      (error) => {
        console.error('Error fetching partners:', error)
        setError('Failed to fetch partners')
        setIsLoading(false)
      }
    )

    return () => unsubscribe()
  }, [])

  const updatePartner = async (partnerId: string, updates: Partial<Partner>) => {
    try {
      const partnerRef = doc(db, 'partners', partnerId)
      const updateData: any = { ...updates }

      // Remove undefined values to prevent Firestore errors
      Object.keys(updateData).forEach(key => {
        if (updateData[key] === undefined) {
          delete updateData[key]
        }
      })

      // Convert dates to Firestore timestamps
      if (updateData.createdAt instanceof Date) {
        updateData.createdAt = Timestamp.fromDate(updateData.createdAt)
      }
      if (updateData.approvedAt instanceof Date) {
        updateData.approvedAt = Timestamp.fromDate(updateData.approvedAt)
      }

      updateData.updatedAt = Timestamp.now()

      await updateDoc(partnerRef, updateData)
    } catch (error) {
      console.error('Error updating partner:', error)
      throw error
    }
  }

  const deletePartner = async (partnerId: string) => {
    try {
      const partnerRef = doc(db, 'partners', partnerId)
      await deleteDoc(partnerRef)
    } catch (error) {
      console.error('Error deleting partner:', error)
      throw error
    }
  }

  const createPartner = async (partnerData: Partial<Partner>) => {
    try {
      const newPartnerData = {
        companyName: partnerData.companyName || '',
        email: partnerData.email || '',
        phone: partnerData.phone || '',
        address: partnerData.address || '',
        description: partnerData.description || '',
        status: 'pending' as const,
        createdAt: Timestamp.now(),
        welcomeEmailSent: false
      }

      await addDoc(collection(db, 'partners'), newPartnerData)
    } catch (error) {
      console.error('Error creating partner:', error)
      throw error
    }
  }

  const approvePartner = async (partnerId: string) => {
    try {
      const partnerRef = doc(db, 'partners', partnerId)
      await updateDoc(partnerRef, {
        status: 'approved',
        approvedAt: Timestamp.now(),
        approvedBy: 'Admin', // TODO: Get actual admin name
        updatedAt: Timestamp.now()
      })
    } catch (error) {
      console.error('Error approving partner:', error)
      throw error
    }
  }

  const rejectPartner = async (partnerId: string, reason: string) => {
    try {
      const partnerRef = doc(db, 'partners', partnerId)
      await updateDoc(partnerRef, {
        status: 'rejected',
        rejectionReason: reason,
        rejectedAt: Timestamp.now(),
        rejectedBy: 'Admin', // TODO: Get actual admin name
        updatedAt: Timestamp.now()
      })
    } catch (error) {
      console.error('Error rejecting partner:', error)
      throw error
    }
  }

  return {
    partners,
    isLoading,
    error,
    updatePartner,
    deletePartner,
    createPartner,
    approvePartner,
    rejectPartner,
  }
}
