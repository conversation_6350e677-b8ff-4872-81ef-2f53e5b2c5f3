import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../../data/models/video_comment.dart';
import '../../services/video_comment_service.dart';
import '../../../../core/theme/colors.dart';

class VideoCommentWidget extends StatefulWidget {
  final VideoComment comment;
  final bool isDarkMode;
  final VoidCallback? onReply;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;

  const VideoCommentWidget({
    super.key,
    required this.comment,
    required this.isDarkMode,
    this.onReply,
    this.onEdit,
    this.onDelete,
  });

  @override
  State<VideoCommentWidget> createState() => _VideoCommentWidgetState();
}

class _VideoCommentWidgetState extends State<VideoCommentWidget> {
  final VideoCommentService _commentService = VideoCommentService();
  final User? _currentUser = FirebaseAuth.instance.currentUser;
  bool _showReplies = false;
  bool _isLiking = false;
  bool _isDisliking = false;

  @override
  Widget build(BuildContext context) {
    final isOwner = _currentUser?.uid == widget.comment.userId;

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Main comment
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // User avatar
              CircleAvatar(
                radius: 18,
                backgroundColor: AppColors.primaryYellow,
                child: Text(
                  widget.comment.userName.isNotEmpty
                      ? widget.comment.userName[0].toUpperCase()
                      : 'U',
                  style: const TextStyle(
                    color: Colors.black,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),

              const SizedBox(width: 12),

              // Comment content
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // User name and time
                    Row(
                      children: [
                        Text(
                          widget.comment.userName,
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            color: widget.isDarkMode
                                ? AppColors.darkText
                                : AppColors.lightText,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Text(
                          widget.comment.timeAgo,
                          style: TextStyle(
                            fontSize: 12,
                            color: widget.isDarkMode
                                ? AppColors.darkTextSecondary
                                : AppColors.lightTextSecondary,
                          ),
                        ),
                        if (widget.comment.isEdited) ...[
                          const SizedBox(width: 4),
                          Text(
                            '(edited)',
                            style: TextStyle(
                              fontSize: 12,
                              fontStyle: FontStyle.italic,
                              color: widget.isDarkMode
                                  ? AppColors.darkTextSecondary
                                  : AppColors.lightTextSecondary,
                            ),
                          ),
                        ],
                      ],
                    ),

                    const SizedBox(height: 4),

                    // Reply indicator for nested replies
                    if (widget.comment.replyToUserName != null) ...[
                      Container(
                        margin: const EdgeInsets.only(bottom: 8),
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: widget.isDarkMode
                              ? AppColors.darkBackground.withOpacity(0.5)
                              : Colors.grey.shade100,
                          borderRadius: BorderRadius.circular(4),
                          border: const Border(
                            left: BorderSide(
                              color: AppColors.primaryYellow,
                              width: 3,
                            ),
                          ),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.reply,
                              size: 12,
                              color: widget.isDarkMode
                                  ? AppColors.darkTextSecondary
                                  : AppColors.lightTextSecondary,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              'Replying to ${widget.comment.replyToUserName}',
                              style: TextStyle(
                                fontSize: 11,
                                fontStyle: FontStyle.italic,
                                color: widget.isDarkMode
                                    ? AppColors.darkTextSecondary
                                    : AppColors.lightTextSecondary,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],

                    // Comment text
                    Text(
                      widget.comment.comment,
                      style: TextStyle(
                        color: widget.isDarkMode
                            ? AppColors.darkText
                            : AppColors.lightText,
                        height: 1.4,
                      ),
                    ),

                    const SizedBox(height: 8),

                    // Action buttons
                    Row(
                      children: [
                        // Like button
                        GestureDetector(
                          onTap: _isLiking ? null : _handleLike,
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                widget.comment.isLikedBy(_currentUser?.uid ?? '')
                                    ? Icons.thumb_up
                                    : Icons.thumb_up_outlined,
                                size: 16,
                                color: widget.comment.isLikedBy(_currentUser?.uid ?? '')
                                    ? AppColors.primaryYellow
                                    : (widget.isDarkMode
                                        ? AppColors.darkTextSecondary
                                        : AppColors.lightTextSecondary),
                              ),
                              const SizedBox(width: 4),
                              Text(
                                widget.comment.likeCount.toString(),
                                style: TextStyle(
                                  fontSize: 12,
                                  color: widget.isDarkMode
                                      ? AppColors.darkTextSecondary
                                      : AppColors.lightTextSecondary,
                                ),
                              ),
                            ],
                          ),
                        ),

                        const SizedBox(width: 16),

                        // Dislike button
                        GestureDetector(
                          onTap: _isDisliking ? null : _handleDislike,
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                widget.comment.isDislikedBy(_currentUser?.uid ?? '')
                                    ? Icons.thumb_down
                                    : Icons.thumb_down_outlined,
                                size: 16,
                                color: widget.comment.isDislikedBy(_currentUser?.uid ?? '')
                                    ? Colors.red
                                    : (widget.isDarkMode
                                        ? AppColors.darkTextSecondary
                                        : AppColors.lightTextSecondary),
                              ),
                              const SizedBox(width: 4),
                              Text(
                                widget.comment.dislikeCount.toString(),
                                style: TextStyle(
                                  fontSize: 12,
                                  color: widget.isDarkMode
                                      ? AppColors.darkTextSecondary
                                      : AppColors.lightTextSecondary,
                                ),
                              ),
                            ],
                          ),
                        ),

                        const SizedBox(width: 16),

                        // Reply button - show for all comments, not just top-level
                        if (widget.onReply != null)
                          GestureDetector(
                            onTap: widget.onReply,
                            child: Text(
                              'Reply',
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                                color: widget.isDarkMode
                                    ? AppColors.darkTextSecondary
                                    : AppColors.lightTextSecondary,
                              ),
                            ),
                          ),

                        const Spacer(),

                        // More options for owner
                        if (isOwner)
                          PopupMenuButton<String>(
                            icon: Icon(
                              Icons.more_vert,
                              size: 16,
                              color: widget.isDarkMode
                                  ? AppColors.darkTextSecondary
                                  : AppColors.lightTextSecondary,
                            ),
                            onSelected: (value) {
                              switch (value) {
                                case 'edit':
                                  widget.onEdit?.call();
                                  break;
                                case 'delete':
                                  widget.onDelete?.call();
                                  break;
                              }
                            },
                            itemBuilder: (context) => [
                              const PopupMenuItem(
                                value: 'edit',
                                child: Text('Edit'),
                              ),
                              const PopupMenuItem(
                                value: 'delete',
                                child: Text('Delete'),
                              ),
                            ],
                          ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),

          // Replies section - show for all comments that have replies
          const SizedBox(height: 8),
          StreamBuilder<List<VideoComment>>(
            stream: _commentService.getCommentReplies(widget.comment.id),
            builder: (context, snapshot) {
              if (!snapshot.hasData || snapshot.data!.isEmpty) {
                return const SizedBox.shrink();
              }

              final replies = snapshot.data!;

              return Column(
                children: [
                  // Show/hide replies button
                  GestureDetector(
                    onTap: () {
                      setState(() {
                        _showReplies = !_showReplies;
                      });
                    },
                    child: Padding(
                      padding: const EdgeInsets.only(left: 42),
                      child: Row(
                        children: [
                          Icon(
                            _showReplies ? Icons.expand_less : Icons.expand_more,
                            size: 16,
                            color: AppColors.primaryYellow,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '${replies.length} ${replies.length == 1 ? 'reply' : 'replies'}',
                            style: const TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                              color: AppColors.primaryYellow,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  // Replies list
                  if (_showReplies) ...[
                    const SizedBox(height: 8),
                    Padding(
                      padding: const EdgeInsets.only(left: 42),
                      child: Column(
                        children: replies.map((reply) => VideoCommentWidget(
                          comment: reply,
                          isDarkMode: widget.isDarkMode,
                          onReply: widget.onReply, // Pass the reply callback to nested replies
                          onEdit: widget.onEdit,
                          onDelete: widget.onDelete,
                        )).toList(),
                      ),
                    ),
                  ],
                ],
              );
            },
          ),
        ],
      ),
    );
  }

  Future<void> _handleLike() async {
    if (_currentUser == null) return;

    setState(() {
      _isLiking = true;
    });

    try {
      await _commentService.likeComment(widget.comment.id);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLiking = false;
        });
      }
    }
  }

  Future<void> _handleDislike() async {
    if (_currentUser == null) return;

    setState(() {
      _isDisliking = true;
    });

    try {
      await _commentService.dislikeComment(widget.comment.id);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isDisliking = false;
        });
      }
    }
  }
}
