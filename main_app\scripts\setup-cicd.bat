@echo off
REM CI/CD Pipeline Setup Script for Drive-On Main App
REM This script helps set up the CI/CD pipeline and required configurations

echo 🚀 Drive-On CI/CD Pipeline Setup
echo ================================
echo.

REM Check prerequisites
echo 📋 Checking prerequisites...

REM Check Flutter
flutter --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Flutter is not installed. Please install Flutter first.
    echo    Download from: https://flutter.dev/docs/get-started/install
    exit /b 1
) else (
    echo ✅ Flutter is installed
)

REM Check Firebase CLI
firebase --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Firebase CLI is not installed. Installing now...
    npm install -g firebase-tools
    if %errorlevel% neq 0 (
        echo ❌ Failed to install Firebase CLI. Please install manually:
        echo    npm install -g firebase-tools
        exit /b 1
    )
) else (
    echo ✅ Firebase CLI is installed
)

REM Check Git
git --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Git is not installed. Please install Git first.
    echo    Download from: https://git-scm.com/downloads
    exit /b 1
) else (
    echo ✅ Git is installed
)

echo.
echo 🔧 Setting up project configuration...

REM Create environment files if they don't exist
if not exist ".env.development" (
    echo 📝 Creating .env.development...
    copy ".env.example" ".env.development"
    echo    Please edit .env.development with your development configuration
)

if not exist ".env.staging" (
    echo 📝 Creating .env.staging...
    copy ".env.example" ".env.staging"
    echo    Please edit .env.staging with your staging configuration
)

if not exist ".env.production" (
    echo 📝 Creating .env.production...
    copy ".env.example" ".env.production"
    echo    Please edit .env.production with your production configuration
)

REM Check Firebase project
echo.
echo 🔥 Checking Firebase configuration...
if exist ".firebaserc" (
    echo ✅ Firebase project is configured
    type .firebaserc
) else (
    echo ❌ Firebase project not configured. Setting up...
    firebase use drive-on-b2af8
    if %errorlevel% neq 0 (
        echo ❌ Failed to set Firebase project. Please run:
        echo    firebase login
        echo    firebase use drive-on-b2af8
    )
)

REM Install Flutter dependencies
echo.
echo 📦 Installing Flutter dependencies...
flutter pub get
if %errorlevel% neq 0 (
    echo ❌ Failed to install Flutter dependencies
    exit /b 1
)

REM Run Flutter doctor
echo.
echo 🩺 Running Flutter doctor...
flutter doctor

echo.
echo 📋 CI/CD Pipeline Setup Checklist:
echo.
echo ✅ Prerequisites installed
echo ✅ Environment files created
echo ✅ Firebase project configured
echo ✅ Flutter dependencies installed
echo.
echo 🔧 Manual Setup Required:
echo.
echo 1. GitHub Repository Setup:
echo    - Create GitHub repository for your project
echo    - Push your code to the repository
echo.
echo 2. GitHub Secrets Configuration:
echo    Add the following secrets to your GitHub repository:
echo    - FIREBASE_SERVICE_ACCOUNT_DRIVE_ON_B2AF8
echo    - FIREBASE_APP_ID
echo    - CODECOV_TOKEN (optional)
echo.
echo 3. Firebase Service Account:
echo    Generate a service account key:
echo    firebase serviceaccount:generate-key service-account-key.json --project drive-on-b2af8
echo.
echo 4. Firebase App Distribution:
echo    - Enable App Distribution in Firebase Console
echo    - Create tester groups: staging-testers, production-testers
echo    - Get your Firebase App ID from Project Settings
echo.
echo 5. Environment Configuration:
echo    - Edit .env.development with development settings
echo    - Edit .env.staging with staging settings  
echo    - Edit .env.production with production settings
echo.
echo 📚 Documentation:
echo    - CI/CD Pipeline: CI_CD_PIPELINE.md
echo    - Deployment Guide: DEPLOYMENT.md
echo    - Firebase Setup: FIREBASE_HOSTING_SETUP.md
echo.
echo 🚀 Quick Commands:
echo    - Build optimized: scripts\build-optimized.bat
echo    - Deploy web: scripts\deploy.bat
echo    - Deploy Android: scripts\deploy-android.bat
echo.
echo 🎉 Setup completed! Your CI/CD pipeline is ready to use.
echo.
echo Next steps:
echo 1. Configure GitHub secrets
echo 2. Edit environment files
echo 3. Push code to trigger first deployment
echo.
pause
