import 'package:flutter/material.dart';

/// A custom scaffold widget with standardized app bar and layout
class AppScaffold extends StatelessWidget {
  /// The title displayed in the app bar
  final String title;
  
  /// The main content of the scaffold
  final Widget body;
  
  /// Optional actions for the app bar
  final List<Widget>? actions;
  
  /// Optional floating action button
  final Widget? floatingActionButton;
  
  /// Optional drawer widget
  final Widget? drawer;
  
  /// Optional bottom navigation bar
  final Widget? bottomNavigationBar;
  
  /// Creates an AppScaffold with standard Drive-On styling
  const AppScaffold({
    Key? key,
    required this.title,
    required this.body,
    this.actions,
    this.floatingActionButton,
    this.drawer,
    this.bottomNavigationBar,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(title),
        actions: actions,
        elevation: 2,
      ),
      body: body,
      floatingActionButton: floatingActionButton,
      drawer: drawer,
      bottomNavigationBar: bottomNavigationBar,
    );
  }
} 