'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  X, 
  MessageSquare, 
  User, 
  Calendar,
  Eye,
  MessageCircle,
  Pin,
  Lock,
  Flag,
  Tag
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { formatDate, formatRelativeTime } from '@/lib/utils'

interface ForumPost {
  id: string
  title: string
  content: string
  category: 'general' | 'help' | 'feedback' | 'announcements' | 'off-topic'
  author: {
    id: string
    name: string
    avatar?: string
  }
  status: 'active' | 'locked' | 'archived' | 'flagged'
  isPinned: boolean
  isSticky: boolean
  repliesCount: number
  viewsCount: number
  lastReplyAt?: Date
  lastReplyBy?: {
    id: string
    name: string
  }
  createdAt: Date
  updatedAt: Date
  tags: string[]
  moderatedBy?: string
  moderationReason?: string
}

interface ForumModalProps {
  post: ForumPost | null
  mode: 'view' | 'edit' | 'create'
  isOpen: boolean
  onClose: () => void
  onSave: (postData: Partial<ForumPost>) => Promise<void>
}

export function ForumModal({ post, mode, isOpen, onClose, onSave }: ForumModalProps) {
  const [formData, setFormData] = useState({
    title: '',
    content: '',
    category: 'general' as ForumPost['category'],
    status: 'active' as ForumPost['status'],
    isPinned: false,
    isSticky: false,
    tags: ['']
  })
  const [isLoading, setIsLoading] = useState(false)

  useEffect(() => {
    if (post && (mode === 'view' || mode === 'edit')) {
      setFormData({
        title: post.title || '',
        content: post.content || '',
        category: post.category || 'general',
        status: post.status || 'active',
        isPinned: post.isPinned || false,
        isSticky: post.isSticky || false,
        tags: post.tags?.length ? post.tags : ['']
      })
    } else if (mode === 'create') {
      setFormData({
        title: '',
        content: '',
        category: 'general',
        status: 'active',
        isPinned: false,
        isSticky: false,
        tags: ['']
      })
    }
  }, [post, mode])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      const submitData = {
        ...formData,
        tags: formData.tags.filter(tag => tag.trim() !== ''),
        author: mode === 'create' ? { id: 'admin', name: 'Admin' } : post?.author
      }
      await onSave(submitData)
    } catch (error) {
      console.error('Error saving post:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleTagChange = (index: number, value: string) => {
    const newTags = [...formData.tags]
    newTags[index] = value
    setFormData(prev => ({ ...prev, tags: newTags }))
  }

  const addTag = () => {
    setFormData(prev => ({
      ...prev,
      tags: [...prev.tags, '']
    }))
  }

  const removeTag = (index: number) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter((_, i) => i !== index)
    }))
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'success'
      case 'locked':
        return 'warning'
      case 'archived':
        return 'secondary'
      case 'flagged':
        return 'error'
      default:
        return 'secondary'
    }
  }

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'general':
        return 'bg-blue-100 text-blue-800'
      case 'help':
        return 'bg-green-100 text-green-800'
      case 'feedback':
        return 'bg-purple-100 text-purple-800'
      case 'announcements':
        return 'bg-orange-100 text-orange-800'
      case 'off-topic':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="absolute inset-0 bg-black/50"
        onClick={onClose}
      />

      {/* Modal */}
      <motion.div
        initial={{ opacity: 0, scale: 0.95, y: 20 }}
        animate={{ opacity: 1, scale: 1, y: 0 }}
        exit={{ opacity: 0, scale: 0.95, y: 20 }}
        className="relative w-full max-w-4xl mx-4 bg-white rounded-lg shadow-xl max-h-[90vh] overflow-y-auto"
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div className="flex items-center space-x-3">
            <h2 className="text-xl font-semibold text-gray-900">
              {mode === 'create' ? 'Create New Post' : 
               mode === 'edit' ? 'Edit Post' : 'Post Details'}
            </h2>
            {post && (
              <div className="flex space-x-2">
                <Badge variant={getStatusColor(post.status) as any}>
                  {post.status}
                </Badge>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getCategoryColor(post.category)}`}>
                  {post.category}
                </span>
                {post.isPinned && (
                  <Badge variant="warning">
                    <Pin className="w-3 h-3 mr-1" />
                    Pinned
                  </Badge>
                )}
                {post.isSticky && (
                  <Badge variant="info">Sticky</Badge>
                )}
              </div>
            )}
          </div>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="w-4 h-4" />
          </Button>
        </div>

        {/* Content */}
        <div className="p-6">
          {mode === 'view' ? (
            // View Mode
            <div className="space-y-6">
              {/* Post Header */}
              <div className="space-y-4">
                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center">
                    {post?.author.avatar ? (
                      <img src={post.author.avatar} alt={post.author.name} className="w-12 h-12 rounded-full" />
                    ) : (
                      <User className="w-6 h-6 text-primary-600" />
                    )}
                  </div>
                  <div className="flex-1">
                    <h1 className="text-xl font-bold text-gray-900 mb-2">{post?.title}</h1>
                    <div className="flex items-center space-x-4 text-sm text-gray-500">
                      <div className="flex items-center space-x-1">
                        <User className="w-4 h-4" />
                        <span>{post?.author.name}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Calendar className="w-4 h-4" />
                        <span>{post?.createdAt ? formatDate(post.createdAt) : ''}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Eye className="w-4 h-4" />
                        <span>{post?.viewsCount || 0} views</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <MessageCircle className="w-4 h-4" />
                        <span>{post?.repliesCount || 0} replies</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Tags */}
              {post?.tags && post.tags.length > 0 && (
                <div className="flex items-center space-x-2">
                  <Tag className="w-4 h-4 text-gray-400" />
                  <div className="flex flex-wrap gap-2">
                    {post.tags.map((tag, index) => (
                      <Badge key={index} variant="secondary">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {/* Content */}
              <Card>
                <CardHeader>
                  <CardTitle>Post Content</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="prose max-w-none">
                    <div className="whitespace-pre-wrap text-gray-700">
                      {post?.content}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Moderation Info */}
              {(post?.moderatedBy || post?.moderationReason) && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm text-orange-600">Moderation Information</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    {post.moderatedBy && (
                      <div>
                        <Label className="text-sm font-medium">Moderated By</Label>
                        <p className="text-sm text-gray-700">{post.moderatedBy}</p>
                      </div>
                    )}
                    {post.moderationReason && (
                      <div>
                        <Label className="text-sm font-medium">Reason</Label>
                        <p className="text-sm text-gray-700">{post.moderationReason}</p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              )}

              {/* Last Reply Info */}
              {post?.lastReplyAt && post?.lastReplyBy && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">Last Reply</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center space-x-2 text-sm text-gray-600">
                      <span>by {post.lastReplyBy.name}</span>
                      <span>•</span>
                      <span>{formatRelativeTime(post.lastReplyAt)}</span>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          ) : (
            // Edit/Create Mode
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="title">Title</Label>
                  <Input
                    id="title"
                    value={formData.title}
                    onChange={(e) => handleInputChange('title', e.target.value)}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="category">Category</Label>
                  <select
                    id="category"
                    value={formData.category}
                    onChange={(e) => handleInputChange('category', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                  >
                    <option value="general">General</option>
                    <option value="help">Help</option>
                    <option value="feedback">Feedback</option>
                    <option value="announcements">Announcements</option>
                    <option value="off-topic">Off Topic</option>
                  </select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="status">Status</Label>
                  <select
                    id="status"
                    value={formData.status}
                    onChange={(e) => handleInputChange('status', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                  >
                    <option value="active">Active</option>
                    <option value="locked">Locked</option>
                    <option value="archived">Archived</option>
                    <option value="flagged">Flagged</option>
                  </select>
                </div>
              </div>

              {/* Content */}
              <div className="space-y-2">
                <Label htmlFor="content">Content</Label>
                <textarea
                  id="content"
                  value={formData.content}
                  onChange={(e) => handleInputChange('content', e.target.value)}
                  rows={8}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                  placeholder="Post content..."
                  required
                />
              </div>

              {/* Tags */}
              <div className="space-y-2">
                <Label>Tags</Label>
                {formData.tags.map((tag, index) => (
                  <div key={index} className="flex space-x-2">
                    <Input
                      value={tag}
                      onChange={(e) => handleTagChange(index, e.target.value)}
                      placeholder="Enter tag"
                      className="flex-1"
                    />
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => removeTag(index)}
                      disabled={formData.tags.length === 1}
                    >
                      Remove
                    </Button>
                  </div>
                ))}
                <Button type="button" variant="outline" onClick={addTag}>
                  Add Tag
                </Button>
              </div>

              {/* Options */}
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <input
                    id="pinned"
                    type="checkbox"
                    checked={formData.isPinned}
                    onChange={(e) => handleInputChange('isPinned', e.target.checked)}
                    className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                  />
                  <Label htmlFor="pinned">Pin this post</Label>
                </div>
                
                <div className="flex items-center space-x-2">
                  <input
                    id="sticky"
                    type="checkbox"
                    checked={formData.isSticky}
                    onChange={(e) => handleInputChange('isSticky', e.target.checked)}
                    className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                  />
                  <Label htmlFor="sticky">Make sticky</Label>
                </div>
              </div>

              {/* Actions */}
              <div className="flex justify-end space-x-3 pt-6 border-t">
                <Button type="button" variant="outline" onClick={onClose}>
                  Cancel
                </Button>
                <Button type="submit" disabled={isLoading} className="gradient-primary">
                  {isLoading ? 'Saving...' : mode === 'create' ? 'Create Post' : 'Save Changes'}
                </Button>
              </div>
            </form>
          )}
        </div>
      </motion.div>
    </div>
  )
}
