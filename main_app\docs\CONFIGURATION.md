# Drive-On Configuration System

This document describes the configuration management system used in the Drive-On application. The system allows for environment-specific settings and feature flags to be easily managed across development, staging, and production environments.

## Table of Contents

1. [Overview](#overview)
2. [Environment Setup](#environment-setup)
3. [Configuration Files](#configuration-files)
4. [Core Components](#core-components)
5. [Usage Examples](#usage-examples)
6. [Building for Different Environments](#building-for-different-environments)
7. [Adding New Configuration Values](#adding-new-configuration-values)
8. [Handling Sensitive Configuration](#handling-sensitive-configuration)

## Overview

Drive-On uses a multi-environment configuration system that supports:
- Environment-specific settings (development, staging, production)
- Feature flags for enabling/disabling features per environment
- API endpoint configuration
- Secure handling of sensitive configuration data

## Environment Setup

The application supports three environments:

1. **Development**: Used during local development
2. **Staging**: Used for testing before production release
3. **Production**: Used for the released application

## Configuration Files

The configuration system uses `.env` files to store environment-specific values:

- `.env.development` - Development environment settings
- `.env.staging` - Staging environment settings
- `.env.production` - Production environment settings
- `.env.example` - Example template with placeholders (safe to commit)

**Important**: The actual `.env.*` files containing real values should NOT be committed to version control. They should be listed in `.gitignore`.

## Core Components

### AppConfig

`AppConfig` is the main class for accessing configuration values:

```dart
// Access configuration values
final apiBaseUrl = AppConfig.instance.apiBaseUrl;
final isDevMode = AppConfig.instance.isDevelopment;

// Access feature flags
if (AppConfig.instance.featureFlags.enableExperimentalFeatures) {
  // Enable experimental feature
}
```

### ConfigInitializer

`ConfigInitializer` handles loading the appropriate configuration based on the current environment:

- It determines the environment from compile-time flags, environment variables, or `.env` files
- It initializes the `AppConfig` instance with the appropriate values
- It's called at application startup before other services are initialized

### ApiConfig

`ApiConfig` provides access to API-related configuration:

```dart
// Get API endpoints
final signInEndpoint = ApiConfig.endpoints.auth.signIn;

// Get full URL for an endpoint
final fullUrl = ApiConfig.endpoints.fullUrl('users/profile');

// Get API timeout values
final timeout = ApiConfig.timeouts.defaultTimeout;
```

### FeatureFlags

`FeatureFlags` manages feature toggles based on the environment:

```dart
// Check if a feature is enabled
if (AppConfig.instance.featureFlags.enableBetaFeatures) {
  // Show beta features
}
```

## Usage Examples

### Reading Configuration Values

```dart
// Get the current environment
final env = AppConfig.instance.environment;

// Check if in development mode
if (AppConfig.instance.isDevelopment) {
  // Development-only code
}

// Get API base URL
final apiUrl = AppConfig.instance.apiBaseUrl;

// Get full version string
final version = AppConfig.instance.fullVersionString;
```

### Using API Configuration

```dart
// Get an API endpoint
final loginEndpoint = ApiConfig.endpoints.auth.signIn;

// Construct a full URL
final fullUrl = ApiConfig.endpoints.fullUrl(loginEndpoint);

// Get default headers
final headers = ApiConfig.auth.defaultHeaders;

// Add auth token to headers
final authHeaders = ApiConfig.auth.addAuthToken(headers, 'user-token');
```

### Using Feature Flags

```dart
// Check if experimental features are enabled
if (AppConfig.instance.featureFlags.enableExperimentalFeatures) {
  // Show experimental UI
}

// Check if analytics should be collected
if (AppConfig.instance.featureFlags.enableAnalytics) {
  // Track analytics event
}
```

## Building for Different Environments

### Flutter CLI

Build the app for a specific environment:

```bash
# Development build
flutter build apk --dart-define=ENVIRONMENT=development

# Staging build
flutter build apk --dart-define=ENVIRONMENT=staging

# Production build
flutter build apk --dart-define=ENVIRONMENT=production
```

### Using FlutterDotEnv

The app will automatically load the appropriate `.env` file based on the environment:

1. Development will load `.env.development`
2. Staging will load `.env.staging`
3. Production will load `.env.production`

If no environment is specified, it defaults to development for debug builds and production for release builds.

## Adding New Configuration Values

1. Add the new value to each `.env.*` file and `.env.example`
2. Update the `AppConfig` class to include the new property
3. Update the `ConfigInitializer._getXXX` method to load the value
4. Use the new configuration value in your code via `AppConfig.instance`

## Handling Sensitive Configuration

Sensitive values like API keys and secrets should:

1. Never be committed to version control (use `.gitignore`)
2. Be stored in `.env.*` files that are excluded from version control
3. For CI/CD, use secure environment variables provided by your CI system
4. Be accessed securely through the configuration system

### Example for CI/CD

Configure your CI/CD pipeline to generate `.env` files during build:

```yaml
# Example GitHub Actions step
- name: Create env file
  run: |
    echo "API_BASE_URL=${{ secrets.API_BASE_URL }}" >> .env.production
    echo "FIREBASE_PROJECT_ID=${{ secrets.FIREBASE_PROJECT_ID }}" >> .env.production
    echo "SENTRY_DSN=${{ secrets.SENTRY_DSN }}" >> .env.production
``` 