const admin = require('firebase-admin');

// Initialize Firebase Admin SDK
try {
  const serviceAccount = require('../serviceAccountKey.json');
  
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
    databaseURL: "https://drive-on-b2af8-default-rtdb.firebaseio.com"
  });
  
  console.log('✅ Firebase Admin initialized');
} catch (error) {
  console.error('❌ Error initializing Firebase Admin:', error.message);
  process.exit(1);
}

async function createTestAdmin() {
  try {
    const email = '<EMAIL>';
    const password = 'Admin123!';
    const displayName = 'Test Admin';

    console.log('🔧 Creating test admin user...');

    // Create user
    let userRecord;
    try {
      userRecord = await admin.auth().createUser({
        email,
        password,
        displayName,
        emailVerified: true
      });
      console.log('✅ User created:', userRecord.uid);
    } catch (error) {
      if (error.code === 'auth/email-already-exists') {
        console.log('📝 User already exists, getting user record...');
        userRecord = await admin.auth().getUserByEmail(email);
        console.log('✅ User found:', userRecord.uid);
      } else {
        throw error;
      }
    }

    // Set admin claims
    await admin.auth().setCustomUserClaims(userRecord.uid, {
      admin: true,
      role: 'admin'
    });
    console.log('✅ Admin claims set');

    // Create admin document in Firestore
    await admin.firestore().collection('admins').doc(userRecord.uid).set({
      email,
      displayName,
      role: 'admin',
      isActive: true,
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      permissions: [
        'read', 
        'write', 
        'delete', 
        'manage_users', 
        'manage_drivers', 
        'manage_partners',
        'manage_jobs',
        'manage_news',
        'manage_forums',
        'manage_queries',
        'view_analytics',
        'manage_settings'
      ],
      lastLoginAt: null,
      createdBy: 'test-script'
    });

    console.log('✅ Admin document created in Firestore');

    console.log('\n🎉 Test admin user created successfully!');
    console.log('📧 Email:', email);
    console.log('🔑 Password:', password);
    console.log('🆔 UID:', userRecord.uid);
    console.log('\n💡 You can now use these credentials to login to the admin panel');

  } catch (error) {
    console.error('❌ Error creating test admin:', error);
  } finally {
    process.exit(0);
  }
}

// Check if Firebase is properly initialized
console.log('🔧 Checking Firebase connection...');
admin.firestore().collection('test').limit(1).get()
  .then(() => {
    console.log('✅ Firebase connection successful\n');
    createTestAdmin();
  })
  .catch((error) => {
    console.error('❌ Firebase connection failed:', error.message);
    console.log('\n💡 Please check your serviceAccountKey.json file and Firebase configuration.');
    process.exit(1);
  });
