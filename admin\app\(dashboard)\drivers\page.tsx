'use client'

import { useState, useEffect } from 'react'
import {
  Car,
  Search,
  Filter,
  Download,
  Eye,
  CheckCircle,
  XCircle,
  Clock,
  FileText,
  Mail,
  Phone,
  Users,
  UserCheck,
} from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { DataTable } from '@/components/ui/data-table'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { DriverModal } from '@/components/drivers/driver-modal'
import { DriverFilters } from '@/components/drivers/driver-filters'
import { LoadingSpinner } from '@/components/ui/loading-spinner'
import { ListSkeleton } from '@/components/ui/skeleton-screen'
import { useDrivers } from '@/lib/hooks/use-drivers'
import { formatDate, formatRelativeTime } from '@/lib/utils'

interface Driver {
  id: string
  name: string
  mobile: string
  city: string
  maritalStatus: string
  education: string
  experience: number
  isVerified: boolean
  status: 'pending' | 'verified' | 'rejected'
  documents: {
    cnic?: string
    license?: string
    photo?: string
  }
  documentUrls?: { [key: string]: string }
  createdAt: Date
  verifiedAt?: Date
  verifiedBy?: string
  rejectionReason?: string
  submittedBy?: string
  submittedAt?: Date
}

interface DriverRequest {
  id: string
  name: string
  mobile: string
  city: string
  maritalStatus: string
  education: string
  experience: number
  documentUrls: { [key: string]: string }
  status: 'pending'
  isVerified: false
  documentsVerified: false
  createdAt: Date
  submittedBy: string
  submittedAt: Date
}

export default function DriversPage() {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedDriver, setSelectedDriver] = useState<Driver | DriverRequest | null>(null)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [activeTab, setActiveTab] = useState('pending')
  const [filters, setFilters] = useState({
    status: 'all',
    city: 'all',
    experience: 'all'
  })

  const {
    drivers,
    driverRequests,
    isLoading,
    error,
    updateDriver,
    verifyDriver,
    rejectDriver,
    approveDriverRequest,
    rejectDriverRequest
  } = useDrivers()

  const filteredDrivers = drivers?.filter(driver => {
    const matchesSearch = driver.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         driver.mobile.includes(searchQuery) ||
                         driver.city.toLowerCase().includes(searchQuery.toLowerCase())

    const matchesStatus = filters.status === 'all' || driver.status === filters.status
    const matchesCity = filters.city === 'all' || driver.city === filters.city

    return matchesSearch && matchesStatus && matchesCity
  }) || []

  const filteredDriverRequests = driverRequests?.filter(request => {
    const matchesSearch = request.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         request.mobile.includes(searchQuery) ||
                         request.city.toLowerCase().includes(searchQuery.toLowerCase())

    const matchesCity = filters.city === 'all' || request.city === filters.city

    return matchesSearch && matchesCity
  }) || []

  const handleViewDriver = (driver: Driver | DriverRequest) => {
    setSelectedDriver(driver)
    setIsModalOpen(true)
  }

  const handleVerifyDriver = async (driver: Driver) => {
    try {
      await verifyDriver(driver.id)
    } catch (error) {
      console.error('Error verifying driver:', error)
    }
  }

  const handleRejectDriver = async (driver: Driver, reason: string) => {
    try {
      await rejectDriver(driver.id, reason)
    } catch (error) {
      console.error('Error rejecting driver:', error)
    }
  }

  const handleApproveRequest = async (request: DriverRequest, comments?: string) => {
    try {
      await approveDriverRequest(request.id, comments)
    } catch (error) {
      console.error('Error approving driver request:', error)
    }
  }

  const handleRejectRequest = async (request: DriverRequest, reason: string) => {
    try {
      await rejectDriverRequest(request.id, reason)
    } catch (error) {
      console.error('Error rejecting driver request:', error)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'verified':
        return 'success'
      case 'rejected':
        return 'error'
      case 'pending':
        return 'warning'
      default:
        return 'secondary'
    }
  }

  // Columns for pending driver requests
  const requestColumns = [
    {
      key: 'driver',
      label: 'Driver',
      render: (request: DriverRequest) => (
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center">
            <Clock className="w-5 h-5 text-yellow-600" />
          </div>
          <div>
            <p className="font-medium text-gray-900">{request.name}</p>
            <p className="text-sm text-gray-500">{request.mobile}</p>
          </div>
        </div>
      )
    },
    {
      key: 'city',
      label: 'City',
      render: (request: DriverRequest) => request.city
    },
    {
      key: 'experience',
      label: 'Experience',
      render: (request: DriverRequest) => `${request.experience} years`
    },
    {
      key: 'education',
      label: 'Education',
      render: (request: DriverRequest) => request.education
    },
    {
      key: 'documents',
      label: 'Documents',
      render: (request: DriverRequest) => {
        const docCount = Object.keys(request.documentUrls || {}).length
        const requiredDocs = 6 // Driver License, ID Front, ID Back, Electricity Bill, Police Verification, Passport Photo
        const totalDocs = Object.keys(request.documentUrls || {}).length
        return (
          <div className="flex items-center space-x-1">
            <FileText className="w-4 h-4 text-gray-400" />
            <span className="text-sm">{totalDocs}/{requiredDocs}</span>
            {totalDocs >= requiredDocs && (
              <Badge variant="success" className="text-xs ml-1">Complete</Badge>
            )}
          </div>
        )
      }
    },
    {
      key: 'appliedDate',
      label: 'Applied',
      render: (request: DriverRequest) => formatRelativeTime(request.createdAt)
    },
    {
      key: 'actions',
      label: 'Actions',
      render: (request: DriverRequest) => (
        <div className="flex items-center space-x-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleViewDriver(request)}
          >
            <Eye className="w-4 h-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleApproveRequest(request)}
            className="text-green-600 hover:text-green-700"
          >
            <CheckCircle className="w-4 h-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleRejectRequest(request, 'Documents not valid')}
            className="text-red-600 hover:text-red-700"
          >
            <XCircle className="w-4 h-4" />
          </Button>
        </div>
      )
    }
  ]

  // Columns for approved drivers
  const driverColumns = [
    {
      key: 'driver',
      label: 'Driver',
      render: (driver: Driver) => (
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
            <UserCheck className="w-5 h-5 text-green-600" />
          </div>
          <div>
            <p className="font-medium text-gray-900">{driver.name}</p>
            <p className="text-sm text-gray-500">{driver.mobile}</p>
          </div>
        </div>
      )
    },
    {
      key: 'city',
      label: 'City',
      render: (driver: Driver) => driver.city
    },
    {
      key: 'experience',
      label: 'Experience',
      render: (driver: Driver) => `${driver.experience} years`
    },
    {
      key: 'education',
      label: 'Education',
      render: (driver: Driver) => driver.education
    },
    {
      key: 'status',
      label: 'Status',
      render: (driver: Driver) => (
        <Badge variant={getStatusColor(driver.status) as any}>
          {driver.status}
        </Badge>
      )
    },
    {
      key: 'verifiedDate',
      label: 'Verified',
      render: (driver: Driver) => driver.verifiedAt ? formatRelativeTime(driver.verifiedAt) : '-'
    },
    {
      key: 'actions',
      label: 'Actions',
      render: (driver: Driver) => (
        <div className="flex items-center space-x-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleViewDriver(driver)}
          >
            <Eye className="w-4 h-4" />
          </Button>
          {driver.status === 'verified' && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleRejectDriver(driver, 'Verification revoked')}
              className="text-red-600 hover:text-red-700"
            >
              <XCircle className="w-4 h-4" />
            </Button>
          )}
        </div>
      )
    }
  ]

  // Show skeleton immediately if no cached data
  if (isLoading && (!drivers || drivers.length === 0) && (!driverRequests || driverRequests.length === 0)) {
    return <ListSkeleton items={6} />
  }

  return (
    <div className="space-y-6 instant-load">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Driver Management</h1>
          <p className="text-gray-600">Review and verify driver applications</p>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Applications</p>
                <p className="text-2xl font-bold text-gray-900">
                  {(driverRequests?.length || 0) + (drivers?.length || 0)}
                </p>
              </div>
              <Car className="w-8 h-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Pending Review</p>
                <p className="text-2xl font-bold text-gray-900">
                  {driverRequests?.length || 0}
                </p>
              </div>
              <Clock className="w-8 h-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Verified Drivers</p>
                <p className="text-2xl font-bold text-gray-900">
                  {filteredDrivers.filter(d => d.status === 'verified').length}
                </p>
              </div>
              <CheckCircle className="w-8 h-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Rejected</p>
                <p className="text-2xl font-bold text-gray-900">
                  {filteredDrivers.filter(d => d.status === 'rejected').length}
                </p>
              </div>
              <XCircle className="w-8 h-8 text-red-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Driver Management Tabs */}
      <Card>
        <CardHeader>
          <CardTitle>Driver Management</CardTitle>
          <CardDescription>
            Review pending applications and manage verified drivers
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="pending" className="flex items-center gap-2">
                <Clock className="w-4 h-4" />
                Pending Requests ({driverRequests?.length || 0})
              </TabsTrigger>
              <TabsTrigger value="approved" className="flex items-center gap-2">
                <Users className="w-4 h-4" />
                Approved Drivers ({drivers?.length || 0})
              </TabsTrigger>
            </TabsList>

            {/* Search and Filters */}
            <div className="flex flex-col sm:flex-row gap-4 my-6">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    placeholder="Search drivers by name, mobile, or city..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              <DriverFilters filters={filters} onFiltersChange={setFilters} />

              <Button variant="outline">
                <Download className="w-4 h-4 mr-2" />
                Export
              </Button>
            </div>

            <TabsContent value="pending" className="space-y-4">
              <div className="rounded-md border">
                <DataTable
                  data={filteredDriverRequests}
                  columns={requestColumns}
                  searchQuery={searchQuery}
                  isLoading={isLoading}
                />
              </div>
            </TabsContent>

            <TabsContent value="approved" className="space-y-4">
              <div className="rounded-md border">
                <DataTable
                  data={filteredDrivers}
                  columns={driverColumns}
                  searchQuery={searchQuery}
                  isLoading={isLoading}
                />
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Driver Modal */}
      <DriverModal
        driver={selectedDriver}
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onVerify={handleVerifyDriver}
        onReject={handleRejectDriver}
        onApprove={handleApproveRequest}
        onRejectRequest={handleRejectRequest}
      />
    </div>
  )
}
