'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import {
  Settings as SettingsIcon,
  User,
  Shield,
  Bell,
  Palette,
  Database,
  Mail,
  Globe,
  Key,
  Monitor,
  Smartphone,
  Save,
  RefreshCw,
  Download,
  Upload,
} from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { ProfileSettings } from '@/components/settings/profile-settings'
import { SecuritySettings } from '@/components/settings/security-settings'
import { NotificationSettings } from '@/components/settings/notification-settings'
import { ThemeSettings } from '@/components/settings/theme-settings'
import { DatabaseSettings } from '@/components/settings/database-settings'
import { EmailSettings } from '@/components/settings/email-settings'
import { SystemSettings } from '@/components/settings/system-settings'
import { ApiSettings } from '@/components/settings/api-settings'
import { useSettings } from '@/lib/hooks/use-settings'
import { useTheme } from '@/lib/hooks/use-theme'

export default function SettingsPage() {
  const [activeTab, setActiveTab] = useState('profile')
  const [isSaving, setIsSaving] = useState(false)
  const { settings, updateSettings, resetSettings, exportSettings, importSettings } = useSettings()
  const { theme } = useTheme()

  const handleSaveAll = async () => {
    setIsSaving(true)
    try {
      // Save all settings
      await updateSettings(settings)
      // Show success message
    } catch (error) {
      console.error('Error saving settings:', error)
      // Show error message
    } finally {
      setIsSaving(false)
    }
  }

  const handleExportSettings = () => {
    exportSettings()
  }

  const handleImportSettings = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      importSettings(file)
    }
  }

  const settingsTabs = [
    {
      id: 'profile',
      label: 'Profile',
      icon: <User className="w-4 h-4" />,
      description: 'Manage your profile information'
    },
    {
      id: 'security',
      label: 'Security',
      icon: <Shield className="w-4 h-4" />,
      description: 'Security and authentication settings'
    },
    {
      id: 'notifications',
      label: 'Notifications',
      icon: <Bell className="w-4 h-4" />,
      description: 'Configure notification preferences'
    },
    {
      id: 'theme',
      label: 'Appearance',
      icon: <Palette className="w-4 h-4" />,
      description: 'Customize theme and appearance'
    },
    {
      id: 'database',
      label: 'Database',
      icon: <Database className="w-4 h-4" />,
      description: 'Database configuration and backup'
    },
    {
      id: 'email',
      label: 'Email',
      icon: <Mail className="w-4 h-4" />,
      description: 'Email service configuration'
    },
    {
      id: 'system',
      label: 'System',
      icon: <Monitor className="w-4 h-4" />,
      description: 'System and performance settings'
    },
    {
      id: 'api',
      label: 'API & Keys',
      icon: <Key className="w-4 h-4" />,
      description: 'API keys and integrations'
    }
  ]

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Settings</h1>
          <p className="text-gray-600 dark:text-gray-400">
            Manage your admin panel preferences and configurations
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <input
            type="file"
            accept=".json"
            onChange={handleImportSettings}
            className="hidden"
            id="import-settings"
          />
          <label htmlFor="import-settings">
            <Button variant="outline" className="cursor-pointer" asChild>
              <span>
                <Upload className="w-4 h-4 mr-2" />
                Import
              </span>
            </Button>
          </label>
          <Button variant="outline" onClick={handleExportSettings}>
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
          <Button 
            onClick={handleSaveAll} 
            disabled={isSaving}
            className="gradient-primary"
          >
            {isSaving ? (
              <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <Save className="w-4 h-4 mr-2" />
            )}
            {isSaving ? 'Saving...' : 'Save All'}
          </Button>
        </div>
      </div>

      {/* Settings Content */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Settings Navigation */}
        <Card className="lg:col-span-1">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <SettingsIcon className="w-5 h-5" />
              <span>Settings</span>
            </CardTitle>
            <CardDescription>
              Configure your admin panel
            </CardDescription>
          </CardHeader>
          <CardContent className="p-0">
            <nav className="space-y-1">
              {settingsTabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors ${
                    activeTab === tab.id 
                      ? 'bg-primary-50 dark:bg-primary-900/20 text-primary-600 dark:text-primary-400 border-r-2 border-primary-600' 
                      : 'text-gray-700 dark:text-gray-300'
                  }`}
                >
                  {tab.icon}
                  <div className="flex-1 min-w-0">
                    <p className="font-medium">{tab.label}</p>
                    <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
                      {tab.description}
                    </p>
                  </div>
                </button>
              ))}
            </nav>
          </CardContent>
        </Card>

        {/* Settings Content */}
        <div className="lg:col-span-3">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsContent value="profile" className="mt-0">
              <ProfileSettings />
            </TabsContent>

            <TabsContent value="security" className="mt-0">
              <SecuritySettings />
            </TabsContent>

            <TabsContent value="notifications" className="mt-0">
              <NotificationSettings />
            </TabsContent>

            <TabsContent value="theme" className="mt-0">
              <ThemeSettings />
            </TabsContent>

            <TabsContent value="database" className="mt-0">
              <DatabaseSettings />
            </TabsContent>

            <TabsContent value="email" className="mt-0">
              <EmailSettings />
            </TabsContent>

            <TabsContent value="system" className="mt-0">
              <SystemSettings />
            </TabsContent>

            <TabsContent value="api" className="mt-0">
              <ApiSettings />
            </TabsContent>
          </Tabs>
        </div>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>
            Common administrative tasks and shortcuts
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button variant="outline" className="h-auto p-4 flex flex-col items-center space-y-2">
              <Database className="w-6 h-6" />
              <span className="font-medium">Backup Database</span>
              <span className="text-xs text-gray-500 dark:text-gray-400">Create system backup</span>
            </Button>
            
            <Button variant="outline" className="h-auto p-4 flex flex-col items-center space-y-2">
              <RefreshCw className="w-6 h-6" />
              <span className="font-medium">Clear Cache</span>
              <span className="text-xs text-gray-500 dark:text-gray-400">Refresh system cache</span>
            </Button>
            
            <Button variant="outline" className="h-auto p-4 flex flex-col items-center space-y-2">
              <Globe className="w-6 h-6" />
              <span className="font-medium">System Status</span>
              <Badge variant="success" className="text-xs">
                All Systems Operational
              </Badge>
            </Button>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )
}
