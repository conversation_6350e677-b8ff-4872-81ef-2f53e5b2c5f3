default_platform(:ios)

platform :ios do
  desc "Build the iOS application"
  lane :build do
    setup_ci if ENV['CI']
    
    # Update app identifier, version, etc. if needed
    update_info_plist(
      xcodeproj: "Runner.xcodeproj",
      plist_path: "Runner/Info.plist",
      display_name: "Drive-On"
    )
    
    # Build the app
    gym(
      workspace: "Runner.xcworkspace",
      scheme: "Runner",
      export_method: "app-store",
      clean: true,
      output_directory: "build",
      output_name: "drive-on.ipa"
    )
  end
  
  desc "Upload to TestFlight"
  lane :upload_to_testflight do
    # Upload to TestFlight
    pilot(
      skip_waiting_for_build_processing: true,
      skip_submission: true,
      distribute_external: false,
      ipa: "build/drive-on.ipa"
    )
  end
  
  desc "Deploy a new version to the App Store"
  lane :deploy do
    # Upload to App Store
    deliver(
      submit_for_review: false,
      automatic_release: false,
      force: true,
      skip_metadata: true,
      skip_screenshots: true,
      ipa: "build/drive-on.ipa"
    )
  end
end 