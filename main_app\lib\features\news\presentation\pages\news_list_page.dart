import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../data/models/news_article.dart';
import '../../data/repositories/news_repository.dart';
import '../widgets/article_card.dart';
import '../widgets/trending_carousel.dart';
import '../../../../core/utils/app_logger.dart';

import 'article_detail_page.dart';
import 'video_list_page.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../../services/news_service_provider.dart';

class NewsListPage extends StatefulWidget {
  const NewsListPage({
    super.key,
  });

  @override
  State<NewsListPage> createState() => _NewsListPageState();
}

class _NewsListPageState extends State<NewsListPage> {
  final List<String> _categories = [
    'Videos',
    'All',
    'Car Prices',
    'Industry News',
    'Fuel Prices',
    'Taxes & Duties',
    'Road Safety',
    'Traffic Updates',
    'Accident Reports',
  ];

  late final NewsRepository _newsRepository;
  final AppLogger _logger = AppLogger('NewsListPage');
  String _selectedCategory = 'Videos';
  List<NewsArticle> _articles = [];
  List<NewsArticle> _trendingArticles = [];
  bool _isLoading = true;
  bool _isLoadingMore = false;
  bool _hasMore = true;
  DocumentSnapshot? _lastDocument;
  final ScrollController _scrollController = ScrollController();
  Map<String, dynamic>? _refreshStatus;

  @override
  void initState() {
    super.initState();
    _initializeServices();

    _scrollController.addListener(() {
      if (_scrollController.position.pixels >=
          _scrollController.position.maxScrollExtent - 200) {
        _loadMoreData();
      }
    });
  }

  Future<void> _initializeServices() async {
    try {
      _logger.info('Starting service initialization...');

      // Always try to initialize NewsServiceProvider - it's safe to call multiple times
      _logger.debug('Initializing NewsServiceProvider...');
      await NewsServiceProvider.initialize();
      _logger.info('NewsServiceProvider initialized successfully');

      // Get the repository instance
      _newsRepository = NewsServiceProvider.newsRepository;
      _logger.debug('Got repository instance');

      // Load initial data
      _logger.debug('Loading initial data...');
      await _loadInitialData();
      _logger.info('Initial data loaded');

      // Load refresh status
      _logger.debug('Loading refresh status...');
      await _loadRefreshStatus();
      _logger.info('Refresh status loaded');

    } catch (e) {
      _logger.error('Error initializing news services', error: e);

      // Fallback to basic repository
      _logger.info('Using fallback repository...');
      _newsRepository = NewsRepository();
      await _loadInitialData();

      // Set a basic refresh status to avoid null errors
      if (mounted) {
        setState(() {
          _refreshStatus = {
            'timeUntilNextRefresh': const Duration(minutes: 50),
            'remainingApiRequests': 100,
            'isRefreshing': false,
            'lastRefresh': null,
            'nextRefresh': DateTime.now().add(const Duration(minutes: 50)),
            'refreshInterval': const Duration(minutes: 50),
          };
        });
      }
      _logger.info('Fallback setup complete');
    }
  }

  Future<void> _loadRefreshStatus() async {
    try {
      // Check if services are initialized before trying to get status
      if (!NewsServiceProvider.isInitialized) {
        _logger.warning('NewsServiceProvider not initialized, skipping refresh status');
        return;
      }

      final status = await NewsServiceProvider.getRefreshStatus();
      if (mounted) {
        setState(() {
          _refreshStatus = status;
        });
      }
    } catch (e) {
      _logger.error('Error loading refresh status', error: e);
      // Set a fallback status to prevent UI errors
      if (mounted) {
        setState(() {
          _refreshStatus = {
            'timeUntilNextRefresh': const Duration(minutes: 50),
            'remainingApiRequests': 100,
            'isRefreshing': false,
            'lastRefresh': null,
            'nextRefresh': DateTime.now().add(const Duration(minutes: 50)),
            'refreshInterval': const Duration(minutes: 50),
          };
        });
      }
    }
  }

  Future<void> _loadInitialData() async {
    setState(() {
      _isLoading = true;
      _lastDocument = null;
    });

    try {
      // Skip loading news data if Videos category is selected
      if (_selectedCategory == 'Videos') {
        setState(() {
          _trendingArticles = [];
          _articles = [];
          _isLoading = false;
          _hasMore = false;
        });
        return;
      }

      // Load trending news
      final trendingArticles = await _newsRepository.getTrendingArticles();

      // Load feed based on category
      final String? categoryFilter =
          _selectedCategory == 'All' ? null : _selectedCategory;

      final articles = await _newsRepository.getNewsFeed(
        category: categoryFilter,
      );

      // Only set lastDocument if it's a DocumentSnapshot (not for mock data)
      if (articles.isNotEmpty && articles.last is DocumentSnapshot) {
        _lastDocument = articles.last as DocumentSnapshot?;
      }

      setState(() {
        _trendingArticles = trendingArticles;
        _articles = articles;
        _isLoading = false;
        _hasMore = articles.length >= 15; // Assuming 15 is the page size
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        // Set empty lists to avoid null errors
        _trendingArticles = [];
        _articles = [];
      });

      String errorMessage = 'Failed to load news. Please try again.';

      // Provide more specific error messages based on the error type
      if (e is FirebaseException) {
        if (e.code == 'permission-denied') {
          errorMessage = 'You don\'t have permission to access this content.';
        } else if (e.code == 'unavailable') {
          errorMessage = 'The service is currently unavailable. Please check your internet connection.';
        } else if (e.message?.contains('requires an index') ?? false) {
          errorMessage = 'Database configuration issue. Please contact support.';

          // Set empty lists when indexes are missing
          setState(() {
            _trendingArticles = [];
            _articles = [];
            _hasMore = false;
          });
          return; // Don't show error message if we're handling the error
        }
      }

      _showErrorSnackBar(errorMessage);
    }
  }

  Future<void> _loadMoreData() async {
    if (_isLoadingMore || !_hasMore || _selectedCategory == 'Videos') return;

    setState(() {
      _isLoadingMore = true;
    });

    try {
      final String? categoryFilter =
          _selectedCategory == 'All' ? null : _selectedCategory;

      final articles = await _newsRepository.getNewsFeed(
        category: categoryFilter,
        lastDocument: _lastDocument,
      );

      // Only set lastDocument if it's a DocumentSnapshot (not for mock data)
      if (articles.isNotEmpty && articles.last is DocumentSnapshot) {
        _lastDocument = articles.last as DocumentSnapshot?;
      }

      setState(() {
        _articles.addAll(articles);
        _isLoadingMore = false;
        _hasMore = articles.length >= 15; // Assuming 15 is the page size
      });
    } catch (e) {
      setState(() {
        _isLoadingMore = false;
        _hasMore = false; // Prevent further load more attempts
      });

      String errorMessage = 'Failed to load more news. Please try again.';

      // Provide more specific error messages based on the error type
      if (e is FirebaseException) {
        if (e.code == 'permission-denied') {
          errorMessage = 'You don\'t have permission to access this content.';
        } else if (e.code == 'unavailable') {
          errorMessage = 'The service is currently unavailable. Please check your internet connection.';
        } else if (e.message?.contains('requires an index') ?? false) {
          // Don't show error for index issues, just stop loading more
          return;
        }
      }

      _showErrorSnackBar(errorMessage);
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  Future<void> _onRefresh() async {
    await _loadInitialData();
    await _loadRefreshStatus();
  }

  Widget _buildRefreshStatusBanner() {
    // Show a simple banner if refresh status is not available
    if (_refreshStatus == null) {
      return Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        color: Colors.blue.withOpacity(0.1),
        child: Row(
          children: [
            const Expanded(
              child: Text(
                '🤖 AI-Enhanced News • Powered by DeepSeek R1 Zero',
                style: TextStyle(
                  color: Colors.blue,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            GestureDetector(
              onTap: _forceRefresh,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.blue,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Text(
                  'Refresh',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ],
        ),
      );
    }

    final timeUntilNext = _refreshStatus!['timeUntilNextRefresh'] as Duration;
    final remainingRequests = _refreshStatus!['remainingApiRequests'] as int;
    final isRefreshing = _refreshStatus!['isRefreshing'] as bool;

    String statusText;
    Color statusColor;

    if (isRefreshing) {
      statusText = '🔄 Refreshing news...';
      statusColor = Colors.blue;
    } else if (timeUntilNext.inMinutes <= 0) {
      statusText = '⏰ News refresh overdue';
      statusColor = Colors.orange;
    } else {
      final minutes = timeUntilNext.inMinutes;
      statusText = '⏱️ Next refresh in ${minutes}m • $remainingRequests API calls left';
      statusColor = Colors.green;
    }

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      color: statusColor.withOpacity(0.1),
      child: Row(
        children: [
          Expanded(
            child: Text(
              statusText,
              style: TextStyle(
                color: statusColor,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          if (!isRefreshing)
            GestureDetector(
              onTap: _forceRefresh,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: statusColor,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Text(
                  'Refresh Now',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Future<void> _forceRefresh() async {
    try {
      // Check if services are initialized
      if (!NewsServiceProvider.isInitialized) {
        _logger.warning('NewsServiceProvider not initialized, initializing now...');
        await NewsServiceProvider.initialize();
      }

      await NewsServiceProvider.forceRefreshNews();
      await _loadRefreshStatus();
      await _loadInitialData();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('News refreshed successfully!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      _logger.error('Error in force refresh', error: e);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Refresh failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final user = FirebaseAuth.instance.currentUser;
    final bool isAdmin = user != null &&
        (user.email == '<EMAIL>' || user.uid == 'ADMIN_UID');
    // Replace with your admin check logic

    return Scaffold(
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header
                  Text(
                    'Automotive News',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).brightness == Brightness.dark
                          ? Colors.white
                          : Colors.black,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    'Stay updated with latest automotive news',
                    style: TextStyle(
                      fontSize: 12,
                      color: Theme.of(context).brightness == Brightness.dark
                          ? Colors.white70
                          : Colors.black54,
                    ),
                  ),
                  const SizedBox(height: 12),

                  // Refresh status banner (only show for non-video categories)
                  if (_selectedCategory != 'Videos')
                    _buildRefreshStatusBanner(),

                  // Categories
                  Text(
                    'Categories',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).brightness == Brightness.dark
                          ? Colors.white
                          : Colors.black,
                    ),
                  ),
                  const SizedBox(height: 6),
                  SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Row(
                      children: _categories.map((category) {
                        return _buildCompactCategoryChip(
                          context,
                          category,
                          isSelected: _selectedCategory == category,
                        );
                      }).toList(),
                    ),
                  ),
                  const SizedBox(height: 12),

                // Content based on selected category
                Expanded(
                  child: _selectedCategory == 'Videos'
                      ? VideoListPage(
                          isDarkMode: Theme.of(context).brightness == Brightness.dark,
                        )
                      : RefreshIndicator(
                          onRefresh: _onRefresh,
                          child: CustomScrollView(
                            controller: _scrollController,
                            physics: const AlwaysScrollableScrollPhysics(),
                            slivers: [
                              // Trending section
                              if (_trendingArticles.isNotEmpty)
                                SliverToBoxAdapter(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Padding(
                                        padding: const EdgeInsets.only(
                                          left: 16, top: 16, bottom: 8),
                                        child: Row(
                                          children: [
                                            Icon(Icons.trending_up,
                                              color: Theme.of(context).primaryColor),
                                            const SizedBox(width: 8),
                                            Text(
                                              'TRENDING',
                                              style: TextStyle(
                                                fontWeight: FontWeight.bold,
                                                color: Theme.of(context).primaryColor,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      SizedBox(
                                        height: 220,
                                        child: TrendingCarousel(
                                          articles: _trendingArticles,
                                          onArticleTap: (article) =>
                                            Navigator.push(
                                              context,
                                              MaterialPageRoute(
                                                builder: (context) =>
                                                  ArticleDetailPage(article: article),
                                              ),
                                            ),
                                        ),
                                      ),
                                      const Divider(),
                                    ],
                                  ),
                                ),

                              // News feed
                              SliverPadding(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 16, vertical: 8),
                                sliver: SliverList(
                                  delegate: SliverChildBuilderDelegate(
                                    (context, index) {
                                      if (index >= _articles.length) {
                                        if (_isLoadingMore) {
                                          return const Center(
                                            child: Padding(
                                              padding: EdgeInsets.all(16.0),
                                              child: CircularProgressIndicator(),
                                            ),
                                          );
                                        } else if (!_hasMore) {
                                          return const Center(
                                            child: Padding(
                                              padding: EdgeInsets.all(16.0),
                                              child: Text('No more articles'),
                                            ),
                                          );
                                        }
                                        return null;
                                      }

                                      final article = _articles[index];
                                      return ArticleCard(
                                        article: article,
                                        isAdmin: isAdmin,
                                        onTap: () => Navigator.push(
                                          context,
                                          MaterialPageRoute(
                                            builder: (context) =>
                                              ArticleDetailPage(article: article),
                                          ),
                                        ),
                                        onPinToggle: isAdmin
                                            ? (article) async {
                                                await _newsRepository.togglePinArticle(
                                                  article.id,
                                                  !article.pinned,
                                                );
                                                _loadInitialData();
                                              }
                                            : null,
                                      );
                                    },
                                    childCount: _hasMore
                                        ? _articles.length + 1
                                        : _articles.length,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                ),
              ],
            ),
          ),
    );
  }

  Widget _buildCompactCategoryChip(BuildContext context, String label, {bool isSelected = false}) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedCategory = label;
        });
        _loadInitialData();
      },
      child: Container(
        margin: const EdgeInsets.only(right: 8),
        child: Chip(
          label: Text(label),
          backgroundColor: isSelected
              ? Theme.of(context).primaryColor
              : (isDarkMode ? Colors.grey[800] : Colors.white),
          labelStyle: TextStyle(
            color: isSelected
                ? Colors.white
                : (isDarkMode ? Colors.white : Colors.black),
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
            fontSize: 13,
          ),
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
            side: BorderSide(
              color: isSelected
                  ? Colors.transparent
                  : isDarkMode ? Colors.grey.shade700 : Colors.grey.shade300,
              width: 1,
            ),
          ),
          materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
        ),
      ),
    );
  }
}