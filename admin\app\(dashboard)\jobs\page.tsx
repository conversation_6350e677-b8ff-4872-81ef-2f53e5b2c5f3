'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import {
  Briefcase,
  Search,
  Filter,
  Download,
  Plus,
  Eye,
  Edit,
  Trash2,
  CheckCircle,
  XCircle,
  Clock,
  MapPin,
  DollarSign,
  Users,
} from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { DataTable } from '@/components/ui/data-table'
import { JobModal } from '@/components/jobs/job-modal'
import { JobFilters } from '@/components/jobs/job-filters'
import { LoadingSpinner } from '@/components/ui/loading-spinner'
import { useJobs } from '@/lib/hooks/use-jobs'
import { formatDate, formatRelativeTime, formatCurrency } from '@/lib/utils'

interface Job {
  id: string
  title: string
  company: string
  category: 'InDrive' | 'Household' | 'Company' | 'Part-time'
  description: string
  requirements: string[]
  salary: {
    min: number
    max: number
    type: 'hourly' | 'daily' | 'monthly'
  }
  location: {
    city: string
    area: string
    coordinates?: { lat: number; lng: number }
  }
  contactInfo: {
    phone: string
    email: string
    whatsapp?: string
  }
  status: 'active' | 'inactive' | 'expired' | 'filled'
  isApproved: boolean
  isFeatured: boolean
  applicationsCount: number
  viewsCount: number
  createdAt: Date
  expiresAt: Date
  approvedAt?: Date
  approvedBy?: string
  postedBy: string
}

export default function JobsPage() {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedJob, setSelectedJob] = useState<Job | null>(null)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [modalMode, setModalMode] = useState<'view' | 'edit' | 'create'>('view')
  const [filters, setFilters] = useState({
    category: 'all',
    status: 'all',
    city: 'all',
    salaryRange: 'all'
  })

  const { jobs, isLoading, error, updateJob, deleteJob, createJob, approveJob } = useJobs()

  const filteredJobs = jobs?.filter(job => {
    const matchesSearch = job.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         job.company.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         job.location.city.toLowerCase().includes(searchQuery.toLowerCase())

    const matchesCategory = filters.category === 'all' || job.category === filters.category
    const matchesStatus = filters.status === 'all' || job.status === filters.status
    const matchesCity = filters.city === 'all' || job.location.city === filters.city

    return matchesSearch && matchesCategory && matchesStatus && matchesCity
  }) || []

  const handleCreateJob = () => {
    setSelectedJob(null)
    setModalMode('create')
    setIsModalOpen(true)
  }

  const handleViewJob = (job: Job) => {
    setSelectedJob(job)
    setModalMode('view')
    setIsModalOpen(true)
  }

  const handleEditJob = (job: Job) => {
    setSelectedJob(job)
    setModalMode('edit')
    setIsModalOpen(true)
  }

  const handleDeleteJob = async (job: Job) => {
    if (window.confirm(`Are you sure you want to delete "${job.title}"?`)) {
      try {
        await deleteJob(job.id)
      } catch (error) {
        console.error('Error deleting job:', error)
      }
    }
  }

  const handleApproveJob = async (job: Job) => {
    try {
      await approveJob(job.id)
    } catch (error) {
      console.error('Error approving job:', error)
    }
  }

  const handleToggleStatus = async (job: Job) => {
    try {
      const newStatus = job.status === 'active' ? 'inactive' : 'active'
      await updateJob(job.id, { status: newStatus })
    } catch (error) {
      console.error('Error updating job status:', error)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'success'
      case 'inactive':
        return 'secondary'
      case 'expired':
        return 'warning'
      case 'filled':
        return 'info'
      default:
        return 'secondary'
    }
  }

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'InDrive':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
      case 'Household':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
      case 'Company':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300'
      case 'Part-time':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300'
      default:
        return 'bg-muted text-muted-foreground'
    }
  }

  const columns = [
    {
      key: 'job',
      label: 'Job',
      render: (job: Job) => (
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
            <Briefcase className="w-5 h-5 text-primary-600" />
          </div>
          <div>
            <p className="font-medium text-gray-900">{job.title}</p>
            <p className="text-sm text-gray-500">{job.company}</p>
          </div>
        </div>
      )
    },
    {
      key: 'category',
      label: 'Category',
      render: (job: Job) => (
        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getCategoryColor(job.category)}`}>
          {job.category}
        </span>
      )
    },
    {
      key: 'location',
      label: 'Location',
      render: (job: Job) => (
        <div className="flex items-center space-x-1">
          <MapPin className="w-4 h-4 text-gray-400" />
          <span className="text-sm">{job.location.city}</span>
        </div>
      )
    },
    {
      key: 'salary',
      label: 'Salary',
      render: (job: Job) => (
        <div className="flex items-center space-x-1">
          <DollarSign className="w-4 h-4 text-gray-400" />
          <span className="text-sm">
            {formatCurrency(job.salary.min)} - {formatCurrency(job.salary.max)}
          </span>
        </div>
      )
    },
    {
      key: 'applications',
      label: 'Applications',
      render: (job: Job) => (
        <div className="flex items-center space-x-1">
          <Users className="w-4 h-4 text-gray-400" />
          <span className="text-sm">{job.applicationsCount}</span>
        </div>
      )
    },
    {
      key: 'status',
      label: 'Status',
      render: (job: Job) => (
        <div className="space-y-1">
          <Badge variant={getStatusColor(job.status) as any}>
            {job.status}
          </Badge>
          {!job.isApproved && (
            <Badge variant="warning" className="text-xs">
              Pending Approval
            </Badge>
          )}
        </div>
      )
    },
    {
      key: 'posted',
      label: 'Posted',
      render: (job: Job) => formatRelativeTime(job.createdAt)
    },
    {
      key: 'actions',
      label: 'Actions',
      render: (job: Job) => (
        <div className="flex items-center space-x-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleViewJob(job)}
          >
            <Eye className="w-4 h-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleEditJob(job)}
          >
            <Edit className="w-4 h-4" />
          </Button>
          {!job.isApproved && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleApproveJob(job)}
              className="text-green-600 hover:text-green-700"
            >
              <CheckCircle className="w-4 h-4" />
            </Button>
          )}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleToggleStatus(job)}
            className={job.status === 'active' ? 'text-red-600 hover:text-red-700' : 'text-green-600 hover:text-green-700'}
          >
            {job.status === 'active' ? <XCircle className="w-4 h-4" /> : <CheckCircle className="w-4 h-4" />}
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleDeleteJob(job)}
            className="text-red-600 hover:text-red-700"
          >
            <Trash2 className="w-4 h-4" />
          </Button>
        </div>
      )
    }
  ]

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Job Management</h1>
          <p className="text-gray-600">Manage job postings and applications</p>
        </div>
        <Button onClick={handleCreateJob} className="gradient-primary">
          <Plus className="w-4 h-4 mr-2" />
          Post Job
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Jobs</p>
                <p className="text-2xl font-bold text-gray-900">{jobs?.length || 0}</p>
              </div>
              <Briefcase className="w-8 h-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Active Jobs</p>
                <p className="text-2xl font-bold text-gray-900">
                  {filteredJobs.filter(j => j.status === 'active').length}
                </p>
              </div>
              <CheckCircle className="w-8 h-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Pending Approval</p>
                <p className="text-2xl font-bold text-gray-900">
                  {filteredJobs.filter(j => !j.isApproved).length}
                </p>
              </div>
              <Clock className="w-8 h-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Applications</p>
                <p className="text-2xl font-bold text-gray-900">
                  {filteredJobs.reduce((sum, job) => sum + job.applicationsCount, 0)}
                </p>
              </div>
              <Users className="w-8 h-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <CardTitle>Job Listings</CardTitle>
          <CardDescription>
            Manage job postings and moderate applications
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search jobs by title, company, or location..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <JobFilters filters={filters} onFiltersChange={setFilters} />

            <Button variant="outline">
              <Download className="w-4 h-4 mr-2" />
              Export
            </Button>
          </div>

          <DataTable
            data={filteredJobs}
            columns={columns}
            searchQuery={searchQuery}
            isLoading={isLoading}
          />
        </CardContent>
      </Card>

      {/* Job Modal */}
      <JobModal
        job={selectedJob}
        mode={modalMode}
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onSave={async (jobData) => {
          try {
            if (modalMode === 'create') {
              await createJob(jobData)
            } else if (modalMode === 'edit' && selectedJob) {
              await updateJob(selectedJob.id, jobData)
            }
            setIsModalOpen(false)
          } catch (error) {
            console.error('Error saving job:', error)
          }
        }}
      />
    </motion.div>
  )
}
