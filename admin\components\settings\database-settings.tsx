'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import {
  Database,
  Download,
  Upload,
  Shield,
  Clock,
  HardDrive,
  Zap,
  AlertTriangle,
  CheckCircle,
  Save,
  RefreshCw,
  Play,
  Pause,
} from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { useSettings } from '@/lib/hooks/use-settings'

export function DatabaseSettings() {
  const { settings, updateDatabaseSettings, testDatabaseConnection } = useSettings()
  const [isLoading, setIsLoading] = useState(false)
  const [formData, setFormData] = useState(settings.database)
  const [backupProgress, setBackupProgress] = useState(0)
  const [isBackingUp, setIsBackingUp] = useState(false)
  const [connectionStatus, setConnectionStatus] = useState<'testing' | 'success' | 'error' | null>(null)

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleSave = async () => {
    setIsLoading(true)
    try {
      await updateDatabaseSettings(formData)
      // Show success message
    } catch (error) {
      console.error('Error saving database settings:', error)
      // Show error message
    } finally {
      setIsLoading(false)
    }
  }

  const testConnection = async () => {
    setConnectionStatus('testing')
    try {
      const result = await testDatabaseConnection()
      setConnectionStatus(result.success ? 'success' : 'error')
      setTimeout(() => setConnectionStatus(null), 3000)
    } catch (error) {
      setConnectionStatus('error')
      setTimeout(() => setConnectionStatus(null), 3000)
    }
  }

  const startBackup = async () => {
    setIsBackingUp(true)
    setBackupProgress(0)

    // Simulate backup progress
    const interval = setInterval(() => {
      setBackupProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval)
          setIsBackingUp(false)
          return 100
        }
        return prev + 10
      })
    }, 500)
  }

  const backupFrequencyOptions = [
    { value: 'hourly', label: 'Every Hour' },
    { value: 'daily', label: 'Daily' },
    { value: 'weekly', label: 'Weekly' },
    { value: 'monthly', label: 'Monthly' },
  ]

  return (
    <div className="space-y-6">
      {/* Database Connection */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Database className="w-5 h-5" />
            <span>Database Connection</span>
          </CardTitle>
          <CardDescription>
            Monitor and test your database connection
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-center justify-between p-4 border rounded-lg dark:border-gray-700">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center">
                <Database className="w-6 h-6 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <h3 className="font-medium text-gray-900 dark:text-white">Firebase Firestore</h3>
                <p className="text-sm text-gray-500 dark:text-gray-400">Primary database connection</p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <Badge variant="success">Connected</Badge>
              <Button
                size="sm"
                variant="outline"
                onClick={testConnection}
                disabled={connectionStatus === 'testing'}
              >
                {connectionStatus === 'testing' ? (
                  <RefreshCw className="w-4 h-4 animate-spin" />
                ) : connectionStatus === 'success' ? (
                  <CheckCircle className="w-4 h-4 text-green-600" />
                ) : connectionStatus === 'error' ? (
                  <AlertTriangle className="w-4 h-4 text-red-600" />
                ) : (
                  'Test Connection'
                )}
              </Button>
            </div>
          </div>

          {connectionStatus && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              className={`p-3 rounded-lg ${
                connectionStatus === 'success'
                  ? 'bg-green-50 dark:bg-green-900/20 text-green-800 dark:text-green-200'
                  : 'bg-red-50 dark:bg-red-900/20 text-red-800 dark:text-red-200'
              }`}
            >
              {connectionStatus === 'success'
                ? '✅ Database connection successful'
                : '❌ Database connection failed'}
            </motion.div>
          )}
        </CardContent>
      </Card>

      {/* Backup Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Download className="w-5 h-5" />
            <span>Backup Configuration</span>
          </CardTitle>
          <CardDescription>
            Configure automatic backups and retention policies
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label>Automatic Backups</Label>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Enable scheduled database backups
              </p>
            </div>
            <Switch
              checked={formData.autoBackup}
              onCheckedChange={(checked) => handleInputChange('autoBackup', checked)}
            />
          </div>

          {formData.autoBackup && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              className="space-y-4"
            >
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="backupFrequency">Backup Frequency</Label>
                  <select
                    id="backupFrequency"
                    value={formData.backupFrequency}
                    onChange={(e) => handleInputChange('backupFrequency', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"
                  >
                    {backupFrequencyOptions.map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="retentionPeriod">Retention Period (days)</Label>
                  <Input
                    id="retentionPeriod"
                    type="number"
                    value={formData.retentionPeriod}
                    onChange={(e) => handleInputChange('retentionPeriod', parseInt(e.target.value))}
                    min="1"
                    max="365"
                  />
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <Label>Compression</Label>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Compress backups to save storage space
                    </p>
                  </div>
                  <Switch
                    checked={formData.compressionEnabled}
                    onCheckedChange={(checked) => handleInputChange('compressionEnabled', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <Label>Encryption</Label>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Encrypt backup files for security
                    </p>
                  </div>
                  <Switch
                    checked={formData.encryptionEnabled}
                    onCheckedChange={(checked) => handleInputChange('encryptionEnabled', checked)}
                  />
                </div>
              </div>
            </motion.div>
          )}

          {/* Manual Backup */}
          <div className="border-t pt-6 dark:border-gray-700">
            <div className="flex items-center justify-between mb-4">
              <div>
                <h3 className="font-medium text-gray-900 dark:text-white">Manual Backup</h3>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Create an immediate backup of your database
                </p>
              </div>
              <Button
                onClick={startBackup}
                disabled={isBackingUp}
                className="gradient-primary"
              >
                {isBackingUp ? (
                  <Pause className="w-4 h-4 mr-2" />
                ) : (
                  <Play className="w-4 h-4 mr-2" />
                )}
                {isBackingUp ? 'Backing Up...' : 'Start Backup'}
              </Button>
            </div>

            {isBackingUp && (
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Backup Progress</span>
                  <span>{backupProgress}%</span>
                </div>
                <Progress value={backupProgress} className="w-full" />
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Performance Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Zap className="w-5 h-5" />
            <span>Performance Settings</span>
          </CardTitle>
          <CardDescription>
            Optimize database performance and replication
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label>Database Replication</Label>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Enable read replicas for better performance
              </p>
            </div>
            <Switch
              checked={formData.replicationEnabled}
              onCheckedChange={(checked) => handleInputChange('replicationEnabled', checked)}
            />
          </div>

          {formData.replicationEnabled && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800"
            >
              <div className="flex items-center space-x-2 mb-2">
                <Zap className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                <span className="font-medium text-blue-800 dark:text-blue-200">
                  Replication Enabled
                </span>
              </div>
              <p className="text-sm text-blue-700 dark:text-blue-300">
                Read operations will be distributed across multiple replicas for improved performance.
              </p>
            </motion.div>
          )}
        </CardContent>
      </Card>

      {/* Storage Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <HardDrive className="w-5 h-5" />
            <span>Storage Information</span>
          </CardTitle>
          <CardDescription>
            Current database usage and storage metrics
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <div className="w-12 h-12 bg-blue-100 dark:bg-blue-800 rounded-full flex items-center justify-center mx-auto mb-3">
                <Database className="w-6 h-6 text-blue-600 dark:text-blue-400" />
              </div>
              <h3 className="font-medium text-gray-900 dark:text-white">Database Size</h3>
              <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">2.4 GB</p>
              <p className="text-sm text-gray-500 dark:text-gray-400">Total storage used</p>
            </div>

            <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
              <div className="w-12 h-12 bg-green-100 dark:bg-green-800 rounded-full flex items-center justify-center mx-auto mb-3">
                <Download className="w-6 h-6 text-green-600 dark:text-green-400" />
              </div>
              <h3 className="font-medium text-gray-900 dark:text-white">Backup Size</h3>
              <p className="text-2xl font-bold text-green-600 dark:text-green-400">1.8 GB</p>
              <p className="text-sm text-gray-500 dark:text-gray-400">Latest backup</p>
            </div>

            <div className="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
              <div className="w-12 h-12 bg-purple-100 dark:bg-purple-800 rounded-full flex items-center justify-center mx-auto mb-3">
                <Clock className="w-6 h-6 text-purple-600 dark:text-purple-400" />
              </div>
              <h3 className="font-medium text-gray-900 dark:text-white">Last Backup</h3>
              <p className="text-2xl font-bold text-purple-600 dark:text-purple-400">2h ago</p>
              <p className="text-sm text-gray-500 dark:text-gray-400">Automatic backup</p>
            </div>
          </div>

          <div className="mt-6 space-y-3">
            <div className="flex justify-between text-sm">
              <span>Storage Usage</span>
              <span>2.4 GB / 10 GB</span>
            </div>
            <Progress value={24} className="w-full" />
            <p className="text-xs text-gray-500 dark:text-gray-400">
              You&apos;re using 24% of your allocated storage
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Save Button */}
      <div className="flex justify-end space-x-3">
        <Button
          variant="outline"
          onClick={() => setFormData(settings.database)}
        >
          Reset Changes
        </Button>
        <Button
          onClick={handleSave}
          disabled={isLoading}
          className="gradient-primary"
        >
          {isLoading ? (
            <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
          ) : (
            <Save className="w-4 h-4 mr-2" />
          )}
          {isLoading ? 'Saving...' : 'Save Changes'}
        </Button>
      </div>
    </div>
  )
}
