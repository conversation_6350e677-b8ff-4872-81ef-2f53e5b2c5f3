import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:sqflite/sqflite.dart';
import '../models/driver.dart';
import '../../../core/services/enhanced_cache_service.dart';
import '../../../core/services/offline_storage_service.dart';
import '../../../core/utils/app_logger.dart';

/// Offline-first repository for drivers data
/// 
/// Features:
/// - Offline-first data access
/// - Automatic synchronization
/// - Conflict resolution
/// - Background sync
class OfflineDriversRepository {
  static final OfflineDriversRepository _instance = OfflineDriversRepository._internal();
  factory OfflineDriversRepository() => _instance;
  OfflineDriversRepository._internal();

  static const String _tag = 'OfflineDriversRepository';
  static final AppLogger _logger = AppLogger(_tag);

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final EnhancedCacheService _enhancedCache = EnhancedCacheService();
  final OfflineStorageService _offlineStorage = OfflineStorageService();

  // Collection reference
  CollectionReference<Map<String, dynamic>> get _driversCollection =>
      _firestore.collection('drivers');

  /// Initialize the repository
  Future<void> initialize() async {
    await _enhancedCache.initialize();
  }

  /// Get drivers with offline-first approach
  Future<List<Driver>> getDrivers({
    int limit = 20,
    String? city,
    String? status,
  }) async {
    final cacheKey = 'drivers_${city ?? 'all'}_${status ?? 'all'}_$limit';
    
    return await _enhancedCache.get<List<Driver>>(
      key: cacheKey,
      fetcher: () => _fetchDriversFromFirestore(
        limit: limit,
        city: city,
        status: status,
      ),
      memoryTtl: const Duration(minutes: 15),
      hiveTtl: const Duration(hours: 3),
      sqliteTtl: const Duration(days: 2),
    ) ?? [];
  }

  /// Get drivers with offline storage fallback
  Future<List<Driver>> getDriversOfflineFirst({
    int limit = 20,
    String? city,
    String? status,
  }) async {
    try {
      // First try to get from offline storage
      final offlineDrivers = await _getDriversFromOfflineStorage(
        limit: limit,
        city: city,
        status: status,
      );
      
      // If we have offline data and we're offline, return it
      if (offlineDrivers.isNotEmpty && !_offlineStorage.isOnline) {
        _logger.info('Returning ${offlineDrivers.length} drivers from offline storage');
        return offlineDrivers;
      }
      
      // If we're online, try to fetch fresh data
      if (_offlineStorage.isOnline) {
        try {
          final freshDrivers = await _fetchDriversFromFirestore(
            limit: limit,
            city: city,
            status: status,
          );
          if (freshDrivers.isNotEmpty) {
            // Save to offline storage
            await _saveDriversToOfflineStorage(freshDrivers);
            return freshDrivers;
          }
        } catch (e) {
          _logger.warning('Failed to fetch fresh drivers, falling back to offline data', error: e);
        }
      }
      
      // Return offline data as fallback
      return offlineDrivers;
    } catch (e) {
      _logger.error('Error getting drivers', error: e);
      return [];
    }
  }

  /// Fetch drivers from Firestore
  Future<List<Driver>> _fetchDriversFromFirestore({
    int limit = 20,
    String? city,
    String? status,
  }) async {
    try {
      Query<Map<String, dynamic>> query = _driversCollection
          .orderBy('createdAt', descending: true);

      if (status != null && status.isNotEmpty && status != 'All') {
        query = query.where('status', isEqualTo: status);
      }

      if (city != null && city.isNotEmpty && city != 'All') {
        query = query.where('city', isEqualTo: city);
      }

      query = query.limit(limit);

      final QuerySnapshot<Map<String, dynamic>> snapshot = await query.get();
      return snapshot.docs.map((doc) => Driver.fromMap(doc.data(), doc.id)).toList();
    } catch (e) {
      _logger.error('Error fetching drivers from Firestore', error: e);
      return [];
    }
  }

  /// Get drivers from offline storage (SQLite)
  Future<List<Driver>> _getDriversFromOfflineStorage({
    int limit = 20,
    String? city,
    String? status,
  }) async {
    try {
      final db = _offlineStorage.database;
      if (db == null) return [];

      String whereClause = '';
      List<dynamic> whereArgs = [];

      if (status != null && status.isNotEmpty && status != 'All') {
        whereClause = 'status = ?';
        whereArgs.add(status);
      }

      if (city != null && city.isNotEmpty && city != 'All') {
        if (whereClause.isNotEmpty) {
          whereClause += ' AND city = ?';
        } else {
          whereClause = 'city = ?';
        }
        whereArgs.add(city);
      }

      final List<Map<String, dynamic>> maps = await db.query(
        'drivers',
        where: whereClause.isEmpty ? null : whereClause,
        whereArgs: whereArgs.isEmpty ? null : whereArgs,
        orderBy: 'created_at DESC',
        limit: limit,
      );

      return maps.map((map) => _driverFromMap(map)).toList();
    } catch (e) {
      _logger.error('Error getting drivers from offline storage', error: e);
      return [];
    }
  }

  /// Save drivers to offline storage (SQLite)
  Future<void> _saveDriversToOfflineStorage(List<Driver> drivers) async {
    try {
      final db = _offlineStorage.database;
      if (db == null) return;

      final batch = db.batch();

      for (final driver in drivers) {
        batch.insert(
          'drivers',
          _driverToMap(driver),
          conflictAlgorithm: ConflictAlgorithm.replace,
        );
      }

      await batch.commit();
      _logger.debug('Saved ${drivers.length} drivers to offline storage');
    } catch (e) {
      _logger.error('Error saving drivers to offline storage', error: e);
    }
  }

  /// Convert Driver to Map for SQLite storage
  Map<String, dynamic> _driverToMap(Driver driver) {
    return {
      'id': driver.id,
      'name': driver.name,
      'mobile': driver.mobile,
      'city': driver.city,
      'marital_status': driver.maritalStatus,
      'education': driver.education,
      'experience': driver.experience,
      'is_verified': driver.isVerified ? 1 : 0,
      'status': driver.status,
      'user_email': driver.userEmail,
      'created_at': driver.createdAt?.millisecondsSinceEpoch,
      'updated_at': DateTime.now().millisecondsSinceEpoch,
      'sync_status': 'synced',
    };
  }

  /// Convert Map from SQLite to Driver
  Driver _driverFromMap(Map<String, dynamic> map) {
    return Driver(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      mobile: map['mobile'] ?? '',
      city: map['city'] ?? '',
      maritalStatus: map['marital_status'] ?? '',
      education: map['education'] ?? '',
      experience: map['experience'] ?? 0,
      isVerified: (map['is_verified'] ?? 0) == 1,
      status: map['status'] ?? 'pending',
      userEmail: map['user_email'],
      createdAt: map['created_at'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['created_at'])
          : null,
      documents: {}, // Documents would need separate handling
    );
  }

  /// Get driver cities from offline storage
  Future<List<String>> getDriverCities() async {
    try {
      final db = _offlineStorage.database;
      if (db == null) return [];

      final List<Map<String, dynamic>> maps = await db.rawQuery(
        'SELECT DISTINCT city FROM drivers WHERE city IS NOT NULL AND city != ""'
      );

      return maps.map((map) => map['city'] as String).toList();
    } catch (e) {
      _logger.error('Error getting driver cities from offline storage', error: e);
      return [];
    }
  }

  /// Get verified drivers count
  Future<int> getVerifiedDriversCount() async {
    try {
      final db = _offlineStorage.database;
      if (db == null) return 0;

      final count = Sqflite.firstIntValue(
        await db.rawQuery('SELECT COUNT(*) FROM drivers WHERE is_verified = 1')
      ) ?? 0;

      return count;
    } catch (e) {
      _logger.error('Error getting verified drivers count', error: e);
      return 0;
    }
  }

  /// Clear offline drivers data
  Future<void> clearOfflineData() async {
    try {
      final db = _offlineStorage.database;
      if (db != null) {
        await db.delete('drivers');
      }
      _logger.info('Cleared offline drivers data');
    } catch (e) {
      _logger.error('Error clearing offline drivers data', error: e);
    }
  }

  /// Get offline data statistics
  Future<Map<String, dynamic>> getOfflineStats() async {
    try {
      final db = _offlineStorage.database;
      if (db == null) return {'drivers': 0, 'cities': 0, 'verified': 0};

      final driverCount = Sqflite.firstIntValue(
        await db.rawQuery('SELECT COUNT(*) FROM drivers')
      ) ?? 0;

      final cityCount = Sqflite.firstIntValue(
        await db.rawQuery('SELECT COUNT(DISTINCT city) FROM drivers WHERE city IS NOT NULL')
      ) ?? 0;

      final verifiedCount = Sqflite.firstIntValue(
        await db.rawQuery('SELECT COUNT(*) FROM drivers WHERE is_verified = 1')
      ) ?? 0;

      return {
        'drivers': driverCount,
        'cities': cityCount,
        'verified': verifiedCount,
        'cacheStats': _enhancedCache.getStats(),
      };
    } catch (e) {
      _logger.error('Error getting offline stats', error: e);
      return {'drivers': 0, 'cities': 0, 'verified': 0};
    }
  }
}
