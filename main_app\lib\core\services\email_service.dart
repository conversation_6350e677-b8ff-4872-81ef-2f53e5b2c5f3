import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import '../utils/app_logger.dart';

class EmailService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final String _apiKey = 'YOUR_EMAIL_API_KEY'; // Replace with your actual API key
  final String _apiUrl = 'YOUR_EMAIL_API_URL'; // Replace with your actual API URL

  /// Sends an email to the specified recipient
  Future<bool> sendEmail({
    required String to,
    required String subject,
    required String body,
  }) async {
    try {
      final response = await http.post(
        Uri.parse(_apiUrl),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $_apiKey',
        },
        body: jsonEncode({
          'to': to,
          'subject': subject,
          'body': body,
        }),
      );

      if (response.statusCode == 200) {
        // Track the email in Firestore
        await _firestore.collection('emailHistory').add({
          'to': to,
          'subject': subject,
          'body': body,
          'sentAt': FieldValue.serverTimestamp(),
          'status': 'sent',
        });

        return true;
      } else {
        Log.e('Failed to send email: ${response.statusCode}',
            tag: 'EmailService');
        return false;
      }
    } catch (e) {
      Log.e('Error sending email', tag: 'EmailService', error: e);
      return false;
    }
  }

  /// Saves an email template to Firestore
  Future<bool> saveEmailTemplate({
    required String name,
    required String subject,
    required String body,
  }) async {
    try {
      await _firestore.collection('emailTemplates').add({
        'name': name,
        'subject': subject,
        'body': body,
        'createdAt': FieldValue.serverTimestamp(),
      });
      return true;
    } catch (e) {
      Log.e('Error saving email template', tag: 'EmailService', error: e);
      return false;
    }
  }

  /// Retrieves an email template by ID
  Future<Map<String, dynamic>?> getEmailTemplate(String templateId) async {
    try {
      final doc = await _firestore
          .collection('emailTemplates')
          .doc(templateId)
          .get();
      return doc.data();
    } catch (e) {
      Log.e('Error getting email template', tag: 'EmailService', error: e);
      return null;
    }
  }

  /// Retrieves all email templates
  Future<List<Map<String, dynamic>>> getAllEmailTemplates() async {
    try {
      final snapshot = await _firestore
          .collection('emailTemplates')
          .orderBy('createdAt', descending: true)
          .get();
      return snapshot.docs.map((doc) => doc.data()).toList();
    } catch (e) {
      Log.e('Error getting email templates', tag: 'EmailService', error: e);
      return [];
    }
  }
} 