import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/foundation.dart';
import '../utils/error_handler.dart';
import '../utils/app_logger.dart';
import 'firebase_options.dart';
import 'storage_service.dart';

class FirebaseService {
  static final FirebaseAuth _auth = FirebaseAuth.instance;
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseStorage _storage = FirebaseStorage.instance;
  static final AppLogger _logger = AppLogger('FirebaseService');
  static FirebaseApp? _app;

  // Getters
  static FirebaseAuth get auth => _auth;
  static FirebaseFirestore get firestore => _firestore;
  static FirebaseStorage get storage => _storage;
  static FirebaseApp get app => _app ?? Firebase.app();

  /// Initialize Firebase
  static Future<void> initializeFirebase({String? projectId}) async {
    try {
      // Log the start of Firebase initialization
      ErrorHandler.addLog('Starting Firebase initialization');

      if (projectId != null) {
        _logger.info('Initializing Firebase with project ID: $projectId');
        ErrorHandler.setCustomKey('firebase_project_id', projectId);
      }

      // Check if Firebase is already initialized
      if (Firebase.apps.isEmpty) {
        _app = await Firebase.initializeApp(
          options: DefaultFirebaseOptions.currentPlatform,
        );
        _logger.info('Firebase Core initialized');
      } else {
        _app = Firebase.app();
        _logger.info('Firebase Core was already initialized');
      }

      // Set up Firestore settings for better performance
      _firestore.settings = const Settings(
        persistenceEnabled: true,
        cacheSizeBytes: 50 * 1024 * 1024, // 50MB instead of unlimited
      );

      // Setup default forums in background (non-blocking)
      _setupDefaultForumsInBackground();

      if (kDebugMode) {
        _logger.info('Firebase initialized successfully');
      }

      // Add success log
      ErrorHandler.addLog('Firebase initialized successfully');
    } catch (e) {
      final stackTrace = StackTrace.current;
      // Report this as a high severity error since Firebase is critical
      ErrorHandler.reportHandledException(
        e,
        stackTrace,
        context: 'Firebase initialization',
        severity: ErrorSeverity.high,
      );

      if (kDebugMode) {
        _logger.error('Error initializing Firebase: $e');
        _logger.error('Stack trace: $stackTrace');
      }
      rethrow;
    }
  }

  /// Check if user is logged in
  static bool isUserLoggedIn() {
    return _auth.currentUser != null;
  }

  /// Get current user
  static User? getCurrentUser() {
    return _auth.currentUser;
  }

  /// Sign in with email and password
  static Future<UserCredential> signInWithEmailPassword(
      String email, String password) async {
    try {
      ErrorHandler.addLog('Attempting sign in for user: $email');
      return await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );
    } on FirebaseAuthException catch (e) {
      // Log and handle auth-specific errors with user-friendly messages
      final stackTrace = StackTrace.current;
      ErrorHandler.reportHandledException(
        e,
        stackTrace,
        context: 'Email sign in',
        severity: ErrorSeverity.medium,
      );

      if (kDebugMode) {
        _logger.error('Firebase Auth error signing in: ${e.code} - ${e.message}');
      }

      // Add custom keys for better crash reports
      ErrorHandler.setCustomKey('auth_error_code', e.code);
      ErrorHandler.setCustomKey('auth_error_email', email);

      // Rethrow with the original error to be handled by UI layer
      throw FirebaseAuthException(
        code: e.code,
        message: ErrorHandler.handleAuthError(e),
      );
    } catch (e) {
      // Handle general errors
      final stackTrace = StackTrace.current;
      ErrorHandler.reportHandledException(
        e,
        stackTrace,
        context: 'Email sign in - general error',
        severity: ErrorSeverity.medium,
      );

      if (kDebugMode) {
        _logger.error('Error signing in: $e');
      }
      rethrow;
    }
  }

  /// Create user with email and password
  static Future<UserCredential> createUserWithEmailPassword(
      String email, String password) async {
    try {
      ErrorHandler.addLog('Attempting to create new user: $email');
      return await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );
    } on FirebaseAuthException catch (e) {
      // Log and handle auth-specific errors
      final stackTrace = StackTrace.current;
      ErrorHandler.reportHandledException(
        e,
        stackTrace,
        context: 'Create user with email',
        severity: ErrorSeverity.medium,
      );

      if (kDebugMode) {
        _logger.error('Firebase Auth error creating user: ${e.code} - ${e.message}');
      }

      // Add custom keys for better crash reports
      ErrorHandler.setCustomKey('auth_error_code', e.code);
      ErrorHandler.setCustomKey('auth_error_email', email);

      // Rethrow with the original error to be handled by UI layer
      throw FirebaseAuthException(
        code: e.code,
        message: ErrorHandler.handleAuthError(e),
      );
    } catch (e) {
      // Handle general errors
      final stackTrace = StackTrace.current;
      ErrorHandler.reportHandledException(
        e,
        stackTrace,
        context: 'Create user - general error',
        severity: ErrorSeverity.medium,
      );

      if (kDebugMode) {
        _logger.error('Error creating user: $e');
      }
      rethrow;
    }
  }

  /// Sign out
  static Future<void> signOut() async {
    try {
      await _auth.signOut();
      ErrorHandler.addLog('User signed out successfully');
    } catch (e) {
      final stackTrace = StackTrace.current;
      ErrorHandler.reportHandledException(
        e,
        stackTrace,
        context: 'User sign out',
        severity: ErrorSeverity.low, // Sign out issues are usually low severity
      );

      if (kDebugMode) {
        _logger.error('Error signing out: $e');
      }
      rethrow;
    }
  }

  /// Get document from Firestore with error handling
  static Future<DocumentSnapshot<Map<String, dynamic>>> getDocument(
    String collection,
    String documentId,
  ) async {
    try {
      return await _firestore.collection(collection).doc(documentId).get();
    } catch (e) {
      final stackTrace = StackTrace.current;
      ErrorHandler.reportHandledException(
        e,
        stackTrace,
        context: 'Firestore get document: $collection/$documentId',
        severity: ErrorSeverity.medium,
      );

      if (kDebugMode) {
        _logger.error('Error getting document: $e');
      }
      rethrow;
    }
  }

  /// Upload file to Firebase Storage with error handling and progress tracking
  static Future<String> uploadFile(
    String storagePath,
    Uint8List fileData,
    void Function(double progress)? onProgress,
  ) async {
    try {
      ErrorHandler.addLog('Starting file upload to: $storagePath');

      // Use StorageService for uploads to ensure correct bucket is used
      return await StorageService.uploadBytes(
        fileData,
        storagePath,
        onProgress: onProgress
      );
    } catch (e) {
      final stackTrace = StackTrace.current;
      ErrorHandler.reportHandledException(
        e,
        stackTrace,
        context: 'Storage file upload: $storagePath',
        severity: ErrorSeverity.medium,
      );

      if (kDebugMode) {
        _logger.error('Error uploading file: $e');
      }
      rethrow;
    }
  }

  /// Setup default forums in background to avoid blocking startup
  static void _setupDefaultForumsInBackground() {
    // Run in background after a delay
    Future.delayed(const Duration(seconds: 2), () async {
      try {
        // Check if forums collection exists and create default forums if needed
        final forumsSnapshot = await _firestore.collection('forums').limit(1).get();
        if (forumsSnapshot.docs.isEmpty) {
          _logger.info('Setting up default forums in background');
          // Create a simple default forum without heavy dependencies
          await _firestore.collection('forums').add({
            'title': 'General Discussion',
            'description': 'General discussion about driving and vehicles',
            'createdAt': FieldValue.serverTimestamp(),
            'creatorId': 'system',
            'creatorName': 'System',
            'participants': <String>[],
            'participantNames': <String, String>{},
            'lastMessage': '',
            'lastMessageTime': FieldValue.serverTimestamp(),
            'lastSenderId': '',
            'messageCount': 0,
          });
          _logger.info('Default forums setup completed in background');
        }
      } catch (e) {
        _logger.warning('Failed to setup default forums in background: $e');
        // This is non-critical, so we don't rethrow
      }
    });
  }
}