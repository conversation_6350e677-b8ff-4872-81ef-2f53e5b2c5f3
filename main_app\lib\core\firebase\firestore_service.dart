import 'package:cloud_firestore/cloud_firestore.dart';
import '../utils/log_util.dart';
import 'firebase_service.dart';

/// A service class for common Firestore operations
class FirestoreService {
  static final FirebaseFirestore _firestore = FirebaseService.firestore;

  /// Create a document in a collection
  static Future<DocumentReference> createDocument(
      String collection, Map<String, dynamic> data) async {
    try {
      Log.d('Creating document in $collection', tag: 'FirestoreService');
      final docRef = await _firestore.collection(collection).add(data);
      Log.i('Document created successfully in $collection with ID: ${docRef.id}', tag: 'FirestoreService');
      return docRef;
    } catch (e) {
      Log.e('Error creating document in $collection', tag: 'FirestoreService', error: e);
      rethrow;
    }
  }

  /// Set a document with a specific ID
  static Future<void> setDocument(
      String collection, String documentId, Map<String, dynamic> data, {bool merge = true}) async {
    try {
      Log.d('Setting document $documentId in $collection', tag: 'FirestoreService');
      await _firestore.collection(collection).doc(documentId).set(data, SetOptions(merge: merge));
      Log.i('Document set successfully: $collection/$documentId', tag: 'FirestoreService');
    } catch (e) {
      Log.e('Error setting document $documentId in $collection', tag: 'FirestoreService', error: e);
      rethrow;
    }
  }

  /// Update fields in a document
  static Future<void> updateDocument(
      String collection, String documentId, Map<String, dynamic> data) async {
    try {
      Log.d('Updating document $documentId in $collection', tag: 'FirestoreService');
      await _firestore.collection(collection).doc(documentId).update(data);
      Log.i('Document updated successfully: $collection/$documentId', tag: 'FirestoreService');
    } catch (e) {
      Log.e('Error updating document $documentId in $collection', tag: 'FirestoreService', error: e);
      rethrow;
    }
  }

  /// Delete a document
  static Future<void> deleteDocument(
      String collection, String documentId) async {
    try {
      Log.d('Deleting document $documentId from $collection', tag: 'FirestoreService');
      await _firestore.collection(collection).doc(documentId).delete();
      Log.i('Document deleted successfully: $collection/$documentId', tag: 'FirestoreService');
    } catch (e) {
      Log.e('Error deleting document $documentId from $collection', tag: 'FirestoreService', error: e);
      rethrow;
    }
  }

  /// Get a document by ID
  static Future<DocumentSnapshot> getDocument(
      String collection, String documentId) async {
    try {
      Log.d('Fetching document $documentId from $collection', tag: 'FirestoreService');
      final docSnapshot = await _firestore.collection(collection).doc(documentId).get();
      return docSnapshot;
    } catch (e) {
      Log.e('Error getting document $documentId from $collection', tag: 'FirestoreService', error: e);
      rethrow;
    }
  }

  /// Get all documents in a collection
  static Future<QuerySnapshot> getCollection(String collection) async {
    try {
      Log.d('Fetching all documents from $collection', tag: 'FirestoreService');
      final querySnapshot = await _firestore.collection(collection).get();
      Log.i('Fetched ${querySnapshot.docs.length} documents from $collection', tag: 'FirestoreService');
      return querySnapshot;
    } catch (e) {
      Log.e('Error getting collection $collection', tag: 'FirestoreService', error: e);
      rethrow;
    }
  }

  /// Get a stream of a document
  static Stream<DocumentSnapshot> streamDocument(
      String collection, String documentId) {
    Log.d('Setting up stream for document $documentId in $collection', tag: 'FirestoreService');
    return _firestore.collection(collection).doc(documentId).snapshots();
  }

  /// Get a stream of a collection
  static Stream<QuerySnapshot> streamCollection(String collection) {
    Log.d('Setting up stream for collection $collection', tag: 'FirestoreService');
    return _firestore.collection(collection).snapshots();
  }

  /// Query documents in a collection with optimized query building
  static Future<QuerySnapshot> queryDocuments(String collection, {
    List<List<dynamic>> whereConditions = const [],
    String? orderBy,
    bool descending = false,
    int? limit,
  }) async {
    try {
      Log.d('Querying documents in $collection', tag: 'FirestoreService');

      Query query = _firestore.collection(collection);

      // Apply where conditions efficiently (only one where call per condition)
      for (final condition in whereConditions) {
        if (condition.length == 3) {
          final field = condition[0] as String;
          final operator = condition[1] as String;
          final value = condition[2];

          switch (operator) {
            case '==':
              query = query.where(field, isEqualTo: value);
              break;
            case '>':
              query = query.where(field, isGreaterThan: value);
              break;
            case '>=':
              query = query.where(field, isGreaterThanOrEqualTo: value);
              break;
            case '<':
              query = query.where(field, isLessThan: value);
              break;
            case '<=':
              query = query.where(field, isLessThanOrEqualTo: value);
              break;
            case 'array-contains':
              query = query.where(field, arrayContains: value);
              break;
            case 'array-contains-any':
              query = query.where(field, arrayContainsAny: value);
              break;
            case 'in':
              query = query.where(field, whereIn: value);
              break;
            case 'not-in':
              query = query.where(field, whereNotIn: value);
              break;
            default:
              Log.w('Unknown operator: $operator', tag: 'FirestoreService');
          }
        }
      }

      // Apply ordering
      if (orderBy != null) {
        query = query.orderBy(orderBy, descending: descending);
      }

      // Apply limit (default to 50 if not specified to prevent large queries)
      final effectiveLimit = limit ?? 50;
      query = query.limit(effectiveLimit);

      final querySnapshot = await query.get();
      Log.i('Query returned ${querySnapshot.docs.length} documents from $collection', tag: 'FirestoreService');
      return querySnapshot;
    } catch (e) {
      Log.e('Error querying documents in $collection', tag: 'FirestoreService', error: e);
      rethrow; // Use rethrow instead of throw e
    }
  }

  /// Query documents with pagination support (maintains backward compatibility)
  static Future<QuerySnapshot> queryDocumentsPaginated(String collection, {
    List<List<dynamic>> whereConditions = const [],
    String? orderBy,
    bool descending = false,
    int limit = 20,
    DocumentSnapshot? startAfter,
  }) async {
    try {
      Log.d('Querying paginated documents in $collection', tag: 'FirestoreService');

      Query query = _firestore.collection(collection);

      // Apply where conditions efficiently
      for (final condition in whereConditions) {
        if (condition.length == 3) {
          final field = condition[0] as String;
          final operator = condition[1] as String;
          final value = condition[2];

          switch (operator) {
            case '==':
              query = query.where(field, isEqualTo: value);
              break;
            case '>':
              query = query.where(field, isGreaterThan: value);
              break;
            case '>=':
              query = query.where(field, isGreaterThanOrEqualTo: value);
              break;
            case '<':
              query = query.where(field, isLessThan: value);
              break;
            case '<=':
              query = query.where(field, isLessThanOrEqualTo: value);
              break;
            case 'array-contains':
              query = query.where(field, arrayContains: value);
              break;
            case 'array-contains-any':
              query = query.where(field, arrayContainsAny: value);
              break;
            case 'in':
              query = query.where(field, whereIn: value);
              break;
            case 'not-in':
              query = query.where(field, whereNotIn: value);
              break;
          }
        }
      }

      // Apply ordering (required for pagination)
      if (orderBy != null) {
        query = query.orderBy(orderBy, descending: descending);
      } else {
        // Default ordering by document ID for consistent pagination
        query = query.orderBy(FieldPath.documentId, descending: descending);
      }

      // Apply pagination
      if (startAfter != null) {
        query = query.startAfterDocument(startAfter);
      }

      query = query.limit(limit);

      final querySnapshot = await query.get();
      Log.i('Paginated query returned ${querySnapshot.docs.length} documents from $collection', tag: 'FirestoreService');
      return querySnapshot;
    } catch (e) {
      Log.e('Error querying paginated documents in $collection', tag: 'FirestoreService', error: e);
      rethrow;
    }
  }
}

/// Helper class for query filters
class QueryFilter {
  final String field;
  final dynamic isEqualTo;
  final dynamic isGreaterThan;
  final dynamic isGreaterThanOrEqualTo;
  final dynamic isLessThan;
  final dynamic isLessThanOrEqualTo;
  final dynamic arrayContains;

  QueryFilter({
    required this.field,
    this.isEqualTo,
    this.isGreaterThan,
    this.isGreaterThanOrEqualTo,
    this.isLessThan,
    this.isLessThanOrEqualTo,
    this.arrayContains,
  });
}