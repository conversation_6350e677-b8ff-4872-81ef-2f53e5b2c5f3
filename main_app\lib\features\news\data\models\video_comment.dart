import 'package:cloud_firestore/cloud_firestore.dart';

class VideoComment {
  final String id;
  final String videoId;
  final String userId;
  final String userName;
  final String userEmail;
  final String comment;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final List<String> likes;
  final List<String> dislikes;
  final String? parentCommentId; // For replies - points to root comment
  final String? replyToUserId; // For nested replies - who we're replying to
  final String? replyToUserName; // For nested replies - display name
  final bool isEdited;

  VideoComment({
    required this.id,
    required this.videoId,
    required this.userId,
    required this.userName,
    required this.userEmail,
    required this.comment,
    required this.createdAt,
    this.updatedAt,
    required this.likes,
    required this.dislikes,
    this.parentCommentId,
    this.replyToUserId,
    this.replyToUserName,
    required this.isEdited,
  });

  factory VideoComment.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return VideoComment(
      id: doc.id,
      videoId: data['videoId'] ?? '',
      userId: data['userId'] ?? '',
      userName: data['userName'] ?? '',
      userEmail: data['userEmail'] ?? '',
      comment: data['comment'] ?? '',
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (data['updatedAt'] as Timestamp?)?.toDate(),
      likes: List<String>.from(data['likes'] ?? []),
      dislikes: List<String>.from(data['dislikes'] ?? []),
      parentCommentId: data['parentCommentId'],
      replyToUserId: data['replyToUserId'],
      replyToUserName: data['replyToUserName'],
      isEdited: data['isEdited'] ?? false,
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'videoId': videoId,
      'userId': userId,
      'userName': userName,
      'userEmail': userEmail,
      'comment': comment,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
      'likes': likes,
      'dislikes': dislikes,
      'parentCommentId': parentCommentId,
      'replyToUserId': replyToUserId,
      'replyToUserName': replyToUserName,
      'isEdited': isEdited,
    };
  }

  VideoComment copyWith({
    String? id,
    String? videoId,
    String? userId,
    String? userName,
    String? userEmail,
    String? comment,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<String>? likes,
    List<String>? dislikes,
    String? parentCommentId,
    String? replyToUserId,
    String? replyToUserName,
    bool? isEdited,
  }) {
    return VideoComment(
      id: id ?? this.id,
      videoId: videoId ?? this.videoId,
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      userEmail: userEmail ?? this.userEmail,
      comment: comment ?? this.comment,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      likes: likes ?? this.likes,
      dislikes: dislikes ?? this.dislikes,
      parentCommentId: parentCommentId ?? this.parentCommentId,
      replyToUserId: replyToUserId ?? this.replyToUserId,
      replyToUserName: replyToUserName ?? this.replyToUserName,
      isEdited: isEdited ?? this.isEdited,
    );
  }

  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inDays > 365) {
      final years = (difference.inDays / 365).floor();
      return '$years year${years > 1 ? 's' : ''} ago';
    } else if (difference.inDays > 30) {
      final months = (difference.inDays / 30).floor();
      return '$months month${months > 1 ? 's' : ''} ago';
    } else if (difference.inDays > 0) {
      return '${difference.inDays} day${difference.inDays > 1 ? 's' : ''} ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hour${difference.inHours > 1 ? 's' : ''} ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} minute${difference.inMinutes > 1 ? 's' : ''} ago';
    } else {
      return 'Just now';
    }
  }

  int get likeCount => likes.length;
  int get dislikeCount => dislikes.length;

  bool isLikedBy(String userId) => likes.contains(userId);
  bool isDislikedBy(String userId) => dislikes.contains(userId);
}
