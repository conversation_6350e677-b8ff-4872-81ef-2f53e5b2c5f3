# Drive-On Notification System

## Overview

The Drive-On main app features a comprehensive notification system that provides real-time alerts for messages, verification status updates, and system notifications. The system uses Firebase Cloud Messaging (FCM) for push notifications and local notifications for immediate alerts.

## Features

### 🔔 Notification Types

1. **Message Notifications**
   - New messages in forums
   - New messages in queries/support tickets
   - Admin replies to user queries
   - Real-time message alerts

2. **Verification Notifications**
   - Driver application status updates
   - Document verification results
   - License verification confirmations
   - Approval/rejection notifications

3. **System Notifications**
   - App updates available
   - Maintenance announcements
   - Welcome messages for new users
   - Critical system alerts

### 🎵 Sound & Vibration

- **System Default Ring**: Uses device's default notification sound
- **Vibration Support**: Configurable vibration patterns
- **Sound Control**: Users can enable/disable notification sounds
- **Channel-based Audio**: Different notification types use appropriate channels

### 📱 Platform Support

- **Android**: Full notification channel support with custom importance levels
- **iOS**: Native notification support with sound and badge management
- **Background Processing**: Handles notifications when app is closed
- **Foreground Alerts**: Shows notifications even when app is active

## Architecture

### Core Components

1. **NotificationService** (`core/firebase/notification_service.dart`)
   - Core FCM integration
   - Local notification management
   - Channel configuration
   - Permission handling

2. **NotificationManager** (`core/services/notification_manager.dart`)
   - Central coordination service
   - User authentication integration
   - High-level notification methods

3. **MessageNotificationService** (`core/services/message_notification_service.dart`)
   - Real-time message monitoring
   - Forum and query message detection
   - Automatic notification triggering

4. **VerificationNotificationService** (`core/services/verification_notification_service.dart`)
   - Driver verification monitoring
   - Document status tracking
   - Verification result notifications

### Notification Channels

#### Android Channels
- **Main Channel**: General notifications
- **Message Channel**: Chat, forum, and query messages
- **Verification Channel**: Driver and document verification
- **System Channel**: App updates and system alerts
- **Chat Channel**: Direct chat messages
- **News Channel**: News and announcements

#### iOS Categories
- **Default**: Standard notifications with sound and badge
- **Critical**: Urgent system notifications
- **Message**: Message-specific handling

## Implementation

### Initialization

The notification system is automatically initialized when the app starts:

```dart
// In main.dart
await NotificationManager.instance.initialize();
```

### Sending Notifications

#### Message Notifications
```dart
await NotificationManager.instance.sendMessageNotification(
  title: 'New Message',
  body: 'You have a new message in the forum',
  messageType: 'forum',
  senderId: 'user123',
  senderName: 'John Doe',
  roomId: 'forum_room_1',
);
```

#### Verification Notifications
```dart
await NotificationManager.instance.sendVerificationNotification(
  title: '✅ Driver Application Approved!',
  body: 'Congratulations! Your driver application has been approved.',
  verificationType: 'driver',
  status: 'approved',
);
```

#### System Notifications
```dart
await NotificationManager.instance.sendSystemNotification(
  title: '🔄 App Update Available',
  body: 'A new version is available. Update now!',
  systemType: 'update',
);
```

### Real-time Monitoring

The system automatically monitors:

1. **Forum Messages**: Listens for new messages in active forums
2. **Query Messages**: Monitors user's support queries for admin replies
3. **Driver Verification**: Tracks driver application status changes
4. **Document Verification**: Monitors document approval/rejection

### Permission Management

```dart
// Check permissions
bool hasPermission = await NotificationManager.instance.areNotificationsEnabled();

// Request permissions
bool granted = await NotificationManager.instance.requestNotificationPermissions();
```

## User Interface

### Notification Preferences Screen

Located at `features/settings/notification_preferences_screen.dart`, this screen allows users to:

- Enable/disable notification categories
- Control sound and vibration settings
- Test different notification types
- Manage FCM topic subscriptions

### Test Notifications

The preferences screen includes test buttons for:
- **Basic**: Standard test notification
- **Message**: Sample message notification
- **Verification**: Sample verification notification
- **System**: Sample system notification

## Configuration

### Firebase Setup

1. **FCM Configuration**: Configured in `firebase_options.dart`
2. **Background Handler**: Set up in `main.dart`
3. **Channel Creation**: Automatic on app initialization

### Notification Channels

```dart
// Message notifications
AndroidNotificationChannel(
  'message_channel',
  'Message Notifications',
  description: 'New messages in chats, forums, and queries',
  importance: Importance.high,
  enableVibration: true,
  playSound: true,
);
```

## Testing

### Manual Testing

1. Open the app and navigate to Settings → Notification Preferences
2. Ensure notification permissions are granted
3. Use the test buttons to verify each notification type
4. Check that notifications appear with proper sound and vibration

### Real-time Testing

1. **Forum Messages**: Post a message in a forum from another account
2. **Query Messages**: Have an admin reply to a user query
3. **Verification**: Update driver verification status in admin panel

### Background Testing

1. Close the app completely
2. Trigger a notification from the admin panel
3. Verify the notification appears and opens the app when tapped

## Troubleshooting

### Common Issues

1. **No Notifications Received**
   - Check notification permissions in device settings
   - Verify FCM token is being generated
   - Ensure Firebase project is properly configured

2. **No Sound/Vibration**
   - Check device notification settings
   - Verify channel configuration
   - Test with different notification types

3. **Background Notifications Not Working**
   - Check battery optimization settings
   - Verify background app refresh is enabled
   - Test FCM background message handler

### Debug Information

Enable debug logging to see notification flow:

```dart
final logger = AppLogger('NotificationDebug');
logger.debug('Notification sent: $title');
```

## Future Enhancements

- **Rich Notifications**: Support for images and action buttons
- **Notification History**: Local storage of notification history
- **Smart Scheduling**: Quiet hours and intelligent timing
- **Personalization**: User-specific notification preferences
- **Analytics**: Notification engagement tracking

## Dependencies

- `firebase_messaging`: FCM integration
- `flutter_local_notifications`: Local notification support
- `shared_preferences`: Settings persistence
- `permission_handler`: Permission management

## Security

- **Token Management**: Secure FCM token handling
- **User Privacy**: No sensitive data in notification payloads
- **Permission Respect**: Honors user notification preferences
- **Data Encryption**: Secure communication with Firebase
