org.gradle.jvmargs=-Xmx4G -Dfile.encoding=UTF-8
android.useAndroidX=true
android.enableJetifier=true
org.gradle.java.home=C:\\Program Files\\Java\\jdk-17
android.defaults.buildfeatures.buildconfig=true
kotlin.stdlib.default.dependency=false
kotlin.incremental=false
kotlin.incremental.js=false
kotlin.incremental.useClasspathSnapshot=false

# Fix Gradle lock issues
org.gradle.daemon=false
org.gradle.parallel=false
org.gradle.configureondemand=false
org.gradle.caching=false

# Proxy settings commented out
# systemProp.http.proxyHost=proxy.company.com
# systemProp.http.proxyPort=8080
# systemProp.https.proxyHost=proxy.company.com
# systemProp.https.proxyPort=8080
# systemProp.https.protocols=TLSv1.2,TLSv1.3
# systemProp.https.cipherSuites=TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256,TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256
