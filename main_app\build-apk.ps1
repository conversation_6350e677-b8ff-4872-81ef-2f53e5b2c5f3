# PowerShell script to build Drive-On APK with comprehensive error handling

Write-Host "🚀 Building Drive-On Release APK" -ForegroundColor Green
Write-Host "================================" -ForegroundColor Green

# Set Android SDK environment variables
$env:ANDROID_HOME = "C:\Users\<USER>\AppData\Local\Android\Sdk"
$env:ANDROID_SDK_ROOT = "C:\Users\<USER>\AppData\Local\Android\Sdk"
$env:PATH = "$env:ANDROID_HOME\platform-tools;$env:ANDROID_HOME\build-tools\35.0.1;$env:PATH"

Write-Host "📍 Android SDK Path: $env:ANDROID_HOME" -ForegroundColor Yellow

# Check if Android SDK exists
if (Test-Path $env:ANDROID_HOME) {
    Write-Host "✅ Android SDK found!" -ForegroundColor Green
} else {
    Write-Host "❌ Android SDK not found at: $env:ANDROID_HOME" -ForegroundColor Red
    Write-Host "Please check the path and try again." -ForegroundColor Red
    exit 1
}

# Check Flutter installation
Write-Host "🔍 Checking Flutter installation..." -ForegroundColor Yellow
try {
    $flutterVersion = flutter --version 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Flutter found!" -ForegroundColor Green
    } else {
        Write-Host "❌ Flutter not found in PATH" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "❌ Flutter not found in PATH" -ForegroundColor Red
    exit 1
}

# Clean previous build
Write-Host "🧹 Cleaning previous build..." -ForegroundColor Yellow
flutter clean
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Flutter clean failed" -ForegroundColor Red
    exit 1
}

# Clean Android build cache
Write-Host "🧹 Cleaning Android build cache..." -ForegroundColor Yellow
if (Test-Path "android\.gradle") {
    Remove-Item -Recurse -Force "android\.gradle"
    Write-Host "✅ Android .gradle cache cleared" -ForegroundColor Green
}

# Get dependencies with upgrade
Write-Host "📦 Upgrading dependencies..." -ForegroundColor Yellow
flutter pub upgrade
if ($LASTEXITCODE -ne 0) {
    Write-Host "⚠️ Pub upgrade had issues, trying pub get..." -ForegroundColor Yellow
    flutter pub get
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Flutter pub get failed" -ForegroundColor Red
        exit 1
    }
}

Write-Host "✅ Dependencies updated!" -ForegroundColor Green

# Run Flutter doctor to check for issues
Write-Host "🩺 Running Flutter doctor..." -ForegroundColor Yellow
flutter doctor --android-licenses 2>&1 | Out-Null

# Build release APK with verbose output
Write-Host "🔨 Building release APK..." -ForegroundColor Yellow
Write-Host "This may take several minutes..." -ForegroundColor Yellow
flutter build apk --release --dart-define=ENVIRONMENT=production --verbose
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ APK build failed" -ForegroundColor Red
    Write-Host "Trying alternative build approach..." -ForegroundColor Yellow

    # Try building without obfuscation
    flutter build apk --release --dart-define=ENVIRONMENT=production --no-obfuscate
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Alternative build also failed" -ForegroundColor Red
        exit 1
    }
}

Write-Host ""
Write-Host "🎉 APK BUILD SUCCESSFUL!" -ForegroundColor Green
Write-Host "========================" -ForegroundColor Green
Write-Host ""
Write-Host "📱 Your APK is ready at:" -ForegroundColor Yellow
Write-Host "build\app\outputs\flutter-apk\app-release.apk" -ForegroundColor Cyan
Write-Host ""
Write-Host "🎯 Features in your updated app:" -ForegroundColor Yellow
Write-Host "✅ Google-only login screen" -ForegroundColor Green
Write-Host "✅ Persistent authentication" -ForegroundColor Green
Write-Host "✅ All animations preserved" -ForegroundColor Green
Write-Host "✅ Production optimized" -ForegroundColor Green
Write-Host ""
