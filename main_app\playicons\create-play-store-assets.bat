@echo off
echo ========================================
echo Drive-On Play Store Assets Creator
echo ========================================
echo.

echo 📱 Current Assets Status:
echo.

REM Check for app icon
if exist "app-icon-original.png" (
    echo ✅ App Icon Source: app-icon-original.png ^(192x192^)
) else (
    echo ❌ App Icon Source: MISSING
)

if exist "app-icon-512x512.png" (
    echo ✅ Play Store App Icon: app-icon-512x512.png ^(512x512^)
) else (
    echo ❌ Play Store App Icon: MISSING - Need to create 512x512 version
)

if exist "feature-graphic-1024x500.png" (
    echo ✅ Feature Graphic: feature-graphic-1024x500.png ^(1024x500^)
) else (
    echo ❌ Feature Graphic: MISSING - Need to create 1024x500 banner
)

echo.
echo 🛠️  REQUIRED ACTIONS:
echo.

echo 1. CREATE 512x512 APP ICON:
echo    📁 Source: app-icon-original.png ^(current: 192x192^)
echo    🎯 Target: app-icon-512x512.png ^(need: 512x512^)
echo    🔗 Online Tool: https://www.iloveimg.com/resize-image/resize-png
echo    📋 Steps:
echo       - Upload app-icon-original.png
echo       - Resize to 512x512 pixels
echo       - Download and save as app-icon-512x512.png
echo.

echo 2. CREATE 1024x500 FEATURE GRAPHIC:
echo    🎯 Target: feature-graphic-1024x500.png ^(need: 1024x500^)
echo    🔗 Design Tool: https://www.canva.com
echo    📋 Steps:
echo       - Search for "Google Play Feature Graphic" template
echo       - Use 1024x500 dimensions
echo       - Include: Drive-On logo, app name, tagline
echo       - Colors: Yellow ^(#FFDD00^), White, Dark Blue
echo       - Text: "Drive-On - Pakistan's Premier Driver Employment Platform"
echo.

echo 3. OPTIONAL - SCREENSHOTS:
echo    📱 Take 2-8 screenshots of your app showing:
echo       - Registration screen
echo       - Job listings
echo       - Driver verification
echo       - Forum/community features
echo    📐 Size: 1080x1920 or 1440x2560 ^(phone screenshots^)
echo.

echo 🎨 DESIGN GUIDELINES:
echo.
echo App Icon ^(512x512^):
echo - Keep simple and recognizable
echo - High contrast colors
echo - No text ^(except very short^)
echo - Test at small sizes
echo.
echo Feature Graphic ^(1024x500^):
echo - Landscape orientation
echo - Include app name prominently
echo - Show key benefits
echo - Use brand colors
echo - Readable text
echo.

echo 📋 BRAND COLORS FOR DRIVE-ON:
echo - Primary Yellow: #FFDD00 or #FFC107
echo - Background White: #FFFFFF  
echo - Text Dark: #333333
echo - Accent Blue: #2196F3
echo.

echo 🔗 HELPFUL TOOLS:
echo.
echo Resize Images:
echo - https://www.iloveimg.com/resize-image/resize-png
echo - https://imageresizer.com/
echo.
echo Create Feature Graphic:
echo - https://www.canva.com ^(has Play Store templates^)
echo - https://www.figma.com ^(free design tool^)
echo - https://www.photopea.com ^(free Photoshop alternative^)
echo.

echo 📁 SAVE FILES AS:
echo - app-icon-512x512.png ^(512x512 app icon^)
echo - feature-graphic-1024x500.png ^(1024x500 banner^)
echo - screenshot-1.png, screenshot-2.png, etc.
echo.

echo ✅ WHEN COMPLETE:
echo Your playicons folder should contain:
echo - app-icon-512x512.png ^(for Play Store app icon^)
echo - feature-graphic-1024x500.png ^(for Play Store banner^)
echo - screenshot-1.png to screenshot-8.png ^(optional^)
echo.

echo 🚀 These files are ready to upload to Google Play Console!
echo.

echo Opening helpful links in your browser...
echo.

REM Open helpful links
start https://www.iloveimg.com/resize-image/resize-png
timeout /t 2 /nobreak >nul
start https://www.canva.com/create/google-play-feature-graphics/

echo.
echo Press any key to exit...
pause >nul
