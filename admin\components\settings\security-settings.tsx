'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import {
  Shield,
  Key,
  Smartphone,
  Clock,
  AlertTriangle,
  Eye,
  EyeOff,
  Plus,
  Trash2,
  Save,
  RefreshCw,
} from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import { useSettings } from '@/lib/hooks/use-settings'

export function SecuritySettings() {
  const { settings, updateSecuritySettings } = useSettings()
  const [isLoading, setIsLoading] = useState(false)
  const [formData, setFormData] = useState(settings.security)
  const [showPasswords, setShowPasswords] = useState(false)
  const [newIpAddress, setNewIpAddress] = useState('')

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleSave = async () => {
    setIsLoading(true)
    try {
      await updateSecuritySettings(formData)
      // Show success message
    } catch (error) {
      console.error('Error saving security settings:', error)
      // Show error message
    } finally {
      setIsLoading(false)
    }
  }

  const addIpAddress = () => {
    if (newIpAddress && !formData.ipWhitelist.includes(newIpAddress)) {
      handleInputChange('ipWhitelist', [...formData.ipWhitelist, newIpAddress])
      setNewIpAddress('')
    }
  }

  const removeIpAddress = (ip: string) => {
    handleInputChange('ipWhitelist', formData.ipWhitelist.filter(item => item !== ip))
  }

  const generateApiKey = () => {
    // Generate a random API key
    const key = Array.from(crypto.getRandomValues(new Uint8Array(32)))
      .map(b => b.toString(16).padStart(2, '0'))
      .join('')
    return key
  }

  return (
    <div className="space-y-6">
      {/* Two-Factor Authentication */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Smartphone className="w-5 h-5" />
            <span>Two-Factor Authentication</span>
          </CardTitle>
          <CardDescription>
            Add an extra layer of security to your account
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label>Enable Two-Factor Authentication</Label>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Require a verification code from your phone when signing in
              </p>
            </div>
            <Switch
              checked={formData.twoFactorEnabled}
              onCheckedChange={(checked) => handleInputChange('twoFactorEnabled', checked)}
            />
          </div>

          {formData.twoFactorEnabled && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800"
            >
              <div className="flex items-center space-x-2 mb-2">
                <Shield className="w-4 h-4 text-green-600 dark:text-green-400" />
                <span className="font-medium text-green-800 dark:text-green-200">
                  Two-Factor Authentication Enabled
                </span>
              </div>
              <p className="text-sm text-green-700 dark:text-green-300">
                Your account is protected with 2FA. You&apos;ll need your authenticator app to sign in.
              </p>
              <div className="mt-3 flex space-x-2">
                <Button size="sm" variant="outline">
                  View Recovery Codes
                </Button>
                <Button size="sm" variant="outline">
                  Regenerate Codes
                </Button>
              </div>
            </motion.div>
          )}
        </CardContent>
      </Card>

      {/* Session Management */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Clock className="w-5 h-5" />
            <span>Session Management</span>
          </CardTitle>
          <CardDescription>
            Configure session timeouts and security policies
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="sessionTimeout">Session Timeout (minutes)</Label>
              <Input
                id="sessionTimeout"
                type="number"
                value={formData.sessionTimeout}
                onChange={(e) => handleInputChange('sessionTimeout', parseInt(e.target.value))}
                min="5"
                max="480"
              />
              <p className="text-xs text-gray-500 dark:text-gray-400">
                Automatically log out after this period of inactivity
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="passwordExpiry">Password Expiry (days)</Label>
              <Input
                id="passwordExpiry"
                type="number"
                value={formData.passwordExpiry}
                onChange={(e) => handleInputChange('passwordExpiry', parseInt(e.target.value))}
                min="30"
                max="365"
              />
              <p className="text-xs text-gray-500 dark:text-gray-400">
                Force password change after this many days
              </p>
            </div>
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label>Login Notifications</Label>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Get notified when someone signs into your account
              </p>
            </div>
            <Switch
              checked={formData.loginNotifications}
              onCheckedChange={(checked) => handleInputChange('loginNotifications', checked)}
            />
          </div>
        </CardContent>
      </Card>

      {/* IP Whitelist */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Shield className="w-5 h-5" />
            <span>IP Address Whitelist</span>
          </CardTitle>
          <CardDescription>
            Restrict access to specific IP addresses for enhanced security
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex space-x-2">
            <Input
              placeholder="Enter IP address (e.g., ***********)"
              value={newIpAddress}
              onChange={(e) => setNewIpAddress(e.target.value)}
              className="flex-1"
            />
            <Button onClick={addIpAddress} disabled={!newIpAddress}>
              <Plus className="w-4 h-4 mr-2" />
              Add
            </Button>
          </div>

          {formData.ipWhitelist.length > 0 ? (
            <div className="space-y-2">
              <Label>Whitelisted IP Addresses</Label>
              <div className="space-y-2">
                {formData.ipWhitelist.map((ip, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg"
                  >
                    <span className="font-mono text-sm">{ip}</span>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => removeIpAddress(ip)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          ) : (
            <div className="text-center py-6 text-gray-500 dark:text-gray-400">
              <Shield className="w-8 h-8 mx-auto mb-2 opacity-50" />
              <p>No IP addresses whitelisted</p>
              <p className="text-sm">Add IP addresses to restrict access</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* API Security */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Key className="w-5 h-5" />
            <span>API Security</span>
          </CardTitle>
          <CardDescription>
            Manage API keys and access controls
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-2">
            <Label htmlFor="apiKeyRotation">API Key Rotation (days)</Label>
            <Input
              id="apiKeyRotation"
              type="number"
              value={formData.apiKeyRotation}
              onChange={(e) => handleInputChange('apiKeyRotation', parseInt(e.target.value))}
              min="7"
              max="365"
            />
            <p className="text-xs text-gray-500 dark:text-gray-400">
              Automatically rotate API keys every specified number of days
            </p>
          </div>

          <div className="p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800">
            <div className="flex items-center space-x-2 mb-2">
              <AlertTriangle className="w-4 h-4 text-yellow-600 dark:text-yellow-400" />
              <span className="font-medium text-yellow-800 dark:text-yellow-200">
                Security Recommendation
              </span>
            </div>
            <p className="text-sm text-yellow-700 dark:text-yellow-300">
              Regular API key rotation helps prevent unauthorized access. Consider enabling automatic rotation.
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Security Status */}
      <Card>
        <CardHeader>
          <CardTitle>Security Status</CardTitle>
          <CardDescription>
            Overview of your current security configuration
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
              <div className="w-12 h-12 bg-green-100 dark:bg-green-800 rounded-full flex items-center justify-center mx-auto mb-3">
                <Shield className="w-6 h-6 text-green-600 dark:text-green-400" />
              </div>
              <h3 className="font-medium text-gray-900 dark:text-white">2FA Status</h3>
              <Badge variant={formData.twoFactorEnabled ? 'success' : 'error'}>
                {formData.twoFactorEnabled ? 'Enabled' : 'Disabled'}
              </Badge>
            </div>

            <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <div className="w-12 h-12 bg-blue-100 dark:bg-blue-800 rounded-full flex items-center justify-center mx-auto mb-3">
                <Clock className="w-6 h-6 text-blue-600 dark:text-blue-400" />
              </div>
              <h3 className="font-medium text-gray-900 dark:text-white">Session Timeout</h3>
              <Badge variant="secondary">
                {formData.sessionTimeout}m
              </Badge>
            </div>

            <div className="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
              <div className="w-12 h-12 bg-purple-100 dark:bg-purple-800 rounded-full flex items-center justify-center mx-auto mb-3">
                <Key className="w-6 h-6 text-purple-600 dark:text-purple-400" />
              </div>
              <h3 className="font-medium text-gray-900 dark:text-white">IP Whitelist</h3>
              <Badge variant="secondary">
                {formData.ipWhitelist.length} IPs
              </Badge>
            </div>

            <div className="text-center p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
              <div className="w-12 h-12 bg-orange-100 dark:bg-orange-800 rounded-full flex items-center justify-center mx-auto mb-3">
                <AlertTriangle className="w-6 h-6 text-orange-600 dark:text-orange-400" />
              </div>
              <h3 className="font-medium text-gray-900 dark:text-white">Notifications</h3>
              <Badge variant={formData.loginNotifications ? 'success' : 'warning'}>
                {formData.loginNotifications ? 'On' : 'Off'}
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Save Button */}
      <div className="flex justify-end space-x-3">
        <Button
          variant="outline"
          onClick={() => setFormData(settings.security)}
        >
          Reset Changes
        </Button>
        <Button
          onClick={handleSave}
          disabled={isLoading}
          className="gradient-primary"
        >
          {isLoading ? (
            <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
          ) : (
            <Save className="w-4 h-4 mr-2" />
          )}
          {isLoading ? 'Saving...' : 'Save Changes'}
        </Button>
      </div>
    </div>
  )
}
