'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  Mail,
  Phone,
  Copy,
  CheckCircle,
  Building2,
  User,
  X,
} from 'lucide-react'

interface Partner {
  id: string
  companyName: string
  email: string
  phone: string
  address: string
  description: string
  status: 'pending' | 'approved' | 'rejected'
  createdAt: Date
  approvedAt?: Date
  approvedBy?: string
  rejectionReason?: string
  welcomeEmailSent?: boolean
}

interface ContactInfoModalProps {
  partner: Partner | null
  type: 'email' | 'phone'
  isOpen: boolean
  onClose: () => void
}

export function ContactInfoModal({ partner, type, isOpen, onClose }: ContactInfoModalProps) {
  const [copiedField, setCopiedField] = useState<string | null>(null)

  const copyToClipboard = async (text: string, field: string) => {
    try {
      await navigator.clipboard.writeText(text)
      setCopiedField(field)
      setTimeout(() => setCopiedField(null), 2000)
    } catch (error) {
      console.error('Failed to copy:', error)
    }
  }

  if (!partner || !isOpen) return null

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="absolute inset-0 bg-black/50"
        onClick={onClose}
      />

      {/* Modal */}
      <motion.div
        initial={{ opacity: 0, scale: 0.95, y: 20 }}
        animate={{ opacity: 1, scale: 1, y: 0 }}
        exit={{ opacity: 0, scale: 0.95, y: 20 }}
        className="relative w-full max-w-md mx-4 bg-white rounded-lg shadow-xl"
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div className="flex items-center space-x-2">
            {type === 'email' ? (
              <Mail className="w-5 h-5 text-blue-600" />
            ) : (
              <Phone className="w-5 h-5 text-green-600" />
            )}
            <h2 className="text-lg font-semibold text-gray-900">Partner Contact Information</h2>
          </div>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="w-4 h-4" />
          </Button>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Description */}
          <p className="text-sm text-gray-600 mb-4">
            Partner&apos;s contact information for reaching out about their partnership request.
          </p>

          <div className="space-y-4">
          {/* Partner Info */}
          <Card>
            <CardContent className="p-4">
              <div className="flex items-start space-x-3">
                <div className="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
                  <Building2 className="w-5 h-5 text-primary-600" />
                </div>
                <div className="flex-1">
                  <h3 className="font-medium text-gray-900">{partner.companyName}</h3>
                  <p className="text-sm text-gray-500">{partner.email}</p>
                  <p className="text-sm text-gray-500">{partner.phone}</p>
                  <Badge variant="outline" className="mt-1">
                    {partner.status}
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Partner Contact Info */}
          <Card>
            <CardContent className="p-4">
              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <User className="w-4 h-4 text-gray-500" />
                  <span className="text-sm font-medium text-gray-700">Partner Contact</span>
                </div>

                {type === 'email' ? (
                  <div className="space-y-2">
                    <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                      <div className="flex items-center space-x-2">
                        <Mail className="w-4 h-4 text-blue-600" />
                        <span className="font-mono text-sm">{partner.email}</span>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => copyToClipboard(partner.email, 'email')}
                        className="h-8 w-8 p-0"
                      >
                        {copiedField === 'email' ? (
                          <CheckCircle className="w-4 h-4 text-green-600" />
                        ) : (
                          <Copy className="w-4 h-4" />
                        )}
                      </Button>
                    </div>
                    <p className="text-xs text-gray-600">
                      Click the copy button to copy the partner&apos;s email address.
                    </p>
                  </div>
                ) : (
                  <div className="space-y-2">
                    <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                      <div className="flex items-center space-x-2">
                        <Phone className="w-4 h-4 text-green-600" />
                        <span className="font-mono text-sm">{partner.phone}</span>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => copyToClipboard(partner.phone, 'phone')}
                        className="h-8 w-8 p-0"
                      >
                        {copiedField === 'phone' ? (
                          <CheckCircle className="w-4 h-4 text-green-600" />
                        ) : (
                          <Copy className="w-4 h-4" />
                        )}
                      </Button>
                    </div>
                    <p className="text-xs text-gray-600">
                      Click the copy button to copy the partner&apos;s phone number.
                    </p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Instructions */}
          <Card>
            <CardContent className="p-4">
              <h4 className="text-sm font-medium text-gray-700 mb-2">Instructions:</h4>
              <ul className="text-xs text-gray-600 space-y-1">
                <li>• Use this contact information to reach out to the partner</li>
                <li>• Mention their partnership request in your communication</li>
                <li>• Be professional and provide clear next steps</li>
                <li>• Keep a record of your communication for follow-up</li>
              </ul>
            </CardContent>
          </Card>
          </div>

          <div className="flex justify-end space-x-2 pt-4">
            <Button variant="outline" onClick={onClose}>
              Close
            </Button>
          </div>
        </div>
      </motion.div>
    </div>
  )
}
