import 'dart:async';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../data/services/youtube_service.dart';
import '../data/models/video_item.dart';
import '../../../core/theme/colors.dart';
import '../../../core/utils/app_logger.dart';

class YouTubeNotificationService {
  static const String _lastVideoIdKey = 'last_youtube_video_id';
  static const String _lastCheckTimeKey = 'last_youtube_check_time';
  static const int _checkIntervalMinutes = 30; // Check every 30 minutes

  final YouTubeService _youtubeService = YouTubeService();
  final FlutterLocalNotificationsPlugin _notifications = FlutterLocalNotificationsPlugin();
  final AppLogger _logger = AppLogger('YouTubeNotificationService');
  Timer? _periodicTimer;

  static final YouTubeNotificationService _instance = YouTubeNotificationService._internal();
  factory YouTubeNotificationService() => _instance;
  YouTubeNotificationService._internal();

  /// Initialize the notification service
  Future<void> initialize() async {
    await _initializeNotifications();
    await _startPeriodicCheck();
  }

  /// Initialize local notifications
  Future<void> _initializeNotifications() async {
    const androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _notifications.initialize(
      initSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );
  }

  /// Handle notification tap
  void _onNotificationTapped(NotificationResponse response) {
    // Handle navigation to video when notification is tapped
    // You can implement navigation logic here
    _logger.debug('Notification tapped: ${response.payload}');
  }

  /// Start periodic check for new videos
  Future<void> _startPeriodicCheck() async {
    // Check immediately on start
    await _checkForNewVideos();

    // Set up periodic timer
    _periodicTimer = Timer.periodic(
      const Duration(minutes: _checkIntervalMinutes),
      (_) => _checkForNewVideos(),
    );
  }

  /// Check for new videos and send notifications
  Future<void> _checkForNewVideos() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastVideoId = prefs.getString(_lastVideoIdKey);
      final lastCheckTime = prefs.getInt(_lastCheckTimeKey) ?? 0;

      // Don't check too frequently
      final now = DateTime.now().millisecondsSinceEpoch;
      if (now - lastCheckTime < const Duration(minutes: _checkIntervalMinutes).inMilliseconds) {
        return;
      }

      // Fetch latest videos
      final videos = await _youtubeService.fetchChannelVideos(maxResults: 5);

      if (videos.isNotEmpty) {
        final latestVideo = videos.first;

        // Check if this is a new video
        if (lastVideoId != latestVideo.id) {
          // Check if the video is actually new (published in last 24 hours)
          final videoAge = DateTime.now().difference(latestVideo.publishedAt);
          if (videoAge.inHours <= 24) {
            await _sendNewVideoNotification(latestVideo);
          }

          // Update stored video ID
          await prefs.setString(_lastVideoIdKey, latestVideo.id);
        }
      }

      // Update last check time
      await prefs.setInt(_lastCheckTimeKey, now);

    } catch (e) {
      _logger.error('Error checking for new videos', error: e);
    }
  }

  /// Send notification for new video
  Future<void> _sendNewVideoNotification(VideoItem video) async {
    const androidDetails = AndroidNotificationDetails(
      'youtube_videos',
      'YouTube Videos',
      channelDescription: 'Notifications for new YouTube videos',
      importance: Importance.high,
      priority: Priority.high,
      icon: '@mipmap/ic_launcher',
      color: AppColors.primaryYellow, // App's yellow color
      playSound: true,
      enableVibration: true,
    );

    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    const notificationDetails = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _notifications.show(
      video.id.hashCode, // Use video ID hash as notification ID
      '🎥 New Video Available!',
      video.title,
      notificationDetails,
      payload: video.id, // Pass video ID for navigation
    );
  }

  /// Manually check for new videos (for pull-to-refresh)
  Future<void> manualCheck() async {
    await _checkForNewVideos();
  }

  /// Stop the notification service
  void dispose() {
    _periodicTimer?.cancel();
    _youtubeService.dispose();
  }

  /// Enable/disable notifications
  Future<void> setNotificationsEnabled(bool enabled) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('youtube_notifications_enabled', enabled);

    if (enabled) {
      await _startPeriodicCheck();
    } else {
      _periodicTimer?.cancel();
    }
  }

  /// Check if notifications are enabled
  Future<bool> areNotificationsEnabled() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool('youtube_notifications_enabled') ?? true;
  }
}
