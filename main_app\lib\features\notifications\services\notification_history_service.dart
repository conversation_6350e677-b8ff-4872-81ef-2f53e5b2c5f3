import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/notification_model.dart';
import '../../../core/utils/app_logger.dart';

/// Service for managing notification history and storage
class NotificationHistoryService {
  static final NotificationHistoryService _instance = NotificationHistoryService._internal();
  static NotificationHistoryService get instance => _instance;
  NotificationHistoryService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final AppLogger _logger = AppLogger('NotificationHistoryService');

  /// Get current user ID
  String? get _currentUserId => _auth.currentUser?.uid;

  /// Save notification to Firestore for history
  Future<void> saveNotification(NotificationModel notification) async {
    try {
      if (_currentUserId == null) {
        _logger.warning('Cannot save notification: user not authenticated');
        return;
      }

      await _firestore
          .collection('user_notifications')
          .doc(notification.id)
          .set(notification.toFirestore());

      _logger.debug('Notification saved to history: ${notification.id}');
    } catch (e, stackTrace) {
      _logger.error('Error saving notification to history',
          error: e, stackTrace: stackTrace);
    }
  }

  /// Get user's notification history stream with real-time updates
  Stream<List<NotificationModel>> getUserNotifications() {
    if (_currentUserId == null) {
      _logger.warning('Cannot get notifications: user not authenticated');
      return Stream.value([]);
    }

    return _firestore
        .collection('user_notifications')
        .where('userId', isEqualTo: _currentUserId)
        .orderBy('createdAt', descending: true)
        .limit(100) // Limit to last 100 notifications
        .snapshots(includeMetadataChanges: true) // Real-time updates with zero delay
        .map((snapshot) {
      return snapshot.docs
          .map((doc) => NotificationModel.fromFirestore(doc))
          .toList();
    });
  }

  /// Get unread notification count with real-time updates
  Stream<int> getUnreadCount() {
    if (_currentUserId == null) {
      return Stream.value(0);
    }

    return _firestore
        .collection('user_notifications')
        .where('userId', isEqualTo: _currentUserId)
        .where('isRead', isEqualTo: false)
        .snapshots(includeMetadataChanges: true) // Real-time updates with zero delay
        .map((snapshot) => snapshot.docs.length);
  }

  /// Mark notification as read
  Future<void> markAsRead(String notificationId) async {
    try {
      await _firestore
          .collection('user_notifications')
          .doc(notificationId)
          .update({'isRead': true});

      _logger.debug('Notification marked as read: $notificationId');
    } catch (e, stackTrace) {
      _logger.error('Error marking notification as read',
          error: e, stackTrace: stackTrace);
    }
  }

  /// Mark all notifications as read
  Future<void> markAllAsRead() async {
    try {
      if (_currentUserId == null) return;

      final batch = _firestore.batch();
      final unreadNotifications = await _firestore
          .collection('user_notifications')
          .where('userId', isEqualTo: _currentUserId)
          .where('isRead', isEqualTo: false)
          .get();

      for (final doc in unreadNotifications.docs) {
        batch.update(doc.reference, {'isRead': true});
      }

      await batch.commit();
      _logger.info('All notifications marked as read for user: $_currentUserId');
    } catch (e, stackTrace) {
      _logger.error('Error marking all notifications as read',
          error: e, stackTrace: stackTrace);
    }
  }

  /// Delete notification
  Future<void> deleteNotification(String notificationId) async {
    try {
      await _firestore
          .collection('user_notifications')
          .doc(notificationId)
          .delete();

      _logger.debug('Notification deleted: $notificationId');
    } catch (e, stackTrace) {
      _logger.error('Error deleting notification',
          error: e, stackTrace: stackTrace);
    }
  }

  /// Clear all notifications for current user
  Future<void> clearAllNotifications() async {
    try {
      if (_currentUserId == null) return;

      final batch = _firestore.batch();
      final userNotifications = await _firestore
          .collection('user_notifications')
          .where('userId', isEqualTo: _currentUserId)
          .get();

      for (final doc in userNotifications.docs) {
        batch.delete(doc.reference);
      }

      await batch.commit();
      _logger.info('All notifications cleared for user: $_currentUserId');
    } catch (e, stackTrace) {
      _logger.error('Error clearing all notifications',
          error: e, stackTrace: stackTrace);
    }
  }

  /// Get notifications by type
  Stream<List<NotificationModel>> getNotificationsByType(String type) {
    if (_currentUserId == null) {
      return Stream.value([]);
    }

    return _firestore
        .collection('user_notifications')
        .where('userId', isEqualTo: _currentUserId)
        .where('type', isEqualTo: type)
        .orderBy('createdAt', descending: true)
        .limit(50)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs
          .map((doc) => NotificationModel.fromFirestore(doc))
          .toList();
    });
  }

  /// Get recent notifications (last 24 hours)
  Stream<List<NotificationModel>> getRecentNotifications() {
    if (_currentUserId == null) {
      return Stream.value([]);
    }

    final yesterday = DateTime.now().subtract(const Duration(days: 1));

    return _firestore
        .collection('user_notifications')
        .where('userId', isEqualTo: _currentUserId)
        .where('createdAt', isGreaterThan: Timestamp.fromDate(yesterday))
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs
          .map((doc) => NotificationModel.fromFirestore(doc))
          .toList();
    });
  }

  /// Create and save a new notification
  Future<void> createNotification({
    required String title,
    required String body,
    required String type,
    String? senderId,
    String? senderName,
    String? roomId,
    String? imageUrl,
    Map<String, dynamic>? data,
  }) async {
    try {
      if (_currentUserId == null) {
        _logger.warning('Cannot create notification: user not authenticated');
        return;
      }

      final notificationId = _firestore.collection('user_notifications').doc().id;

      final notification = NotificationModel(
        id: notificationId,
        title: title,
        body: body,
        type: type,
        senderId: senderId,
        senderName: senderName,
        roomId: roomId,
        imageUrl: imageUrl,
        data: data,
        createdAt: DateTime.now(),
        isRead: false,
        userId: _currentUserId!,
      );

      await saveNotification(notification);
      _logger.info('Notification created and saved: $notificationId');
    } catch (e, stackTrace) {
      _logger.error('Error creating notification',
          error: e, stackTrace: stackTrace);
    }
  }

  /// Get notification statistics
  Future<Map<String, int>> getNotificationStats() async {
    try {
      if (_currentUserId == null) {
        return {
          'total': 0,
          'unread': 0,
          'message': 0,
          'verification': 0,
          'system': 0,
          'basic': 0,
        };
      }

      final allNotifications = await _firestore
          .collection('user_notifications')
          .where('userId', isEqualTo: _currentUserId)
          .get();

      final stats = <String, int>{
        'total': allNotifications.docs.length,
        'unread': 0,
        'message': 0,
        'verification': 0,
        'system': 0,
        'basic': 0,
      };

      for (final doc in allNotifications.docs) {
        final data = doc.data();
        final type = data['type'] as String? ?? 'basic';
        final isRead = data['isRead'] as bool? ?? false;

        if (!isRead) stats['unread'] = (stats['unread'] ?? 0) + 1;
        stats[type] = (stats[type] ?? 0) + 1;
      }

      return stats;
    } catch (e, stackTrace) {
      _logger.error('Error getting notification stats',
          error: e, stackTrace: stackTrace);
      return {
        'total': 0,
        'unread': 0,
        'message': 0,
        'verification': 0,
        'system': 0,
        'basic': 0,
      };
    }
  }
}
