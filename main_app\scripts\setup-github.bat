@echo off
REM GitHub Repository Setup Script for Drive-On CI/CD Pipeline
REM Repository: UzairDevelops/Drive_on
REM Email: <EMAIL>

echo 🚀 GitHub Repository Setup for Drive-On CI/CD
echo ===============================================
echo.
echo Repository: UzairDevelops/Drive_on
echo Email: <EMAIL>
echo Firebase Project: drive-on-b2af8
echo.

REM Check if Git is installed
git --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Git is not installed. Please install Git first.
    echo    Download from: https://git-scm.com/downloads
    exit /b 1
)

REM Check if Firebase CLI is installed
firebase --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Firebase CLI is not installed. Installing now...
    npm install -g firebase-tools
    if %errorlevel% neq 0 (
        echo ❌ Failed to install Firebase CLI
        exit /b 1
    )
)

echo 🔐 Step 1: Firebase Service Account Setup
echo ==========================================
echo.

REM Login to Firebase
echo Logging into Firebase...
firebase login --email <EMAIL>
if %errorlevel% neq 0 (
    echo ❌ Firebase login failed. Please try manually:
    echo    firebase login --email <EMAIL>
    pause
)

REM Set Firebase project
echo Setting Firebase project...
firebase use drive-on-b2af8
if %errorlevel% neq 0 (
    echo ❌ Failed to set Firebase project
    pause
)

REM Generate service account key
echo.
echo 🔑 Generating Firebase service account key...
firebase serviceaccount:generate-key service-account-key.json --project drive-on-b2af8
if %errorlevel% neq 0 (
    echo ❌ Failed to generate service account key
    pause
    exit /b 1
)

echo ✅ Service account key generated: service-account-key.json
echo.

REM Display service account key content
echo 📋 Service Account Key Content (copy this to GitHub secrets):
echo ================================================================
type service-account-key.json
echo.
echo ================================================================
echo.

REM Get Firebase App ID
echo 📱 Getting Firebase App ID...
firebase apps:list --project drive-on-b2af8

echo.
echo 🔧 Step 2: GitHub Repository Setup
echo ==================================
echo.

REM Check if this is already a Git repository
if not exist ".git" (
    echo Initializing Git repository...
    git init
    git branch -M main
)

REM Set Git user configuration
echo Setting Git user configuration...
git config user.name "UzairDevelops"
git config user.email "<EMAIL>"

REM Add remote origin if not exists
git remote get-url origin >nul 2>&1
if %errorlevel% neq 0 (
    echo Adding GitHub remote...
    git remote add origin https://github.com/UzairDevelops/Drive_on.git
) else (
    echo ✅ GitHub remote already configured
)

REM Create .gitignore if not exists
if not exist ".gitignore" (
    echo Creating .gitignore...
    echo # Firebase Service Account Keys > .gitignore
    echo service-account-key.json >> .gitignore
    echo *.json >> .gitignore
    echo. >> .gitignore
    echo # Environment Files >> .gitignore
    echo .env.development >> .gitignore
    echo .env.staging >> .gitignore
    echo .env.production >> .gitignore
    echo. >> .gitignore
    echo # Build outputs >> .gitignore
    echo build/ >> .gitignore
    echo .dart_tool/ >> .gitignore
    echo .packages >> .gitignore
    echo .pub-cache/ >> .gitignore
    echo .pub/ >> .gitignore
    echo. >> .gitignore
    echo # IDE >> .gitignore
    echo .vscode/ >> .gitignore
    echo .idea/ >> .gitignore
    echo *.iml >> .gitignore
    echo. >> .gitignore
    echo # OS >> .gitignore
    echo .DS_Store >> .gitignore
    echo Thumbs.db >> .gitignore
)

echo.
echo 📋 Step 3: GitHub Secrets Configuration
echo =======================================
echo.
echo You need to manually add these secrets to your GitHub repository:
echo https://github.com/UzairDevelops/Drive_on/settings/secrets/actions
echo.
echo 🔑 Required Secrets:
echo.
echo 1. FIREBASE_SERVICE_ACCOUNT_DRIVE_ON_B2AF8
echo    Value: Copy the entire JSON content from above
echo.
echo 2. FIREBASE_APP_ID  
echo    Value: 1:************:android:6a8e1d9c3c8992992d754d
echo.
echo 3. CODECOV_TOKEN (Optional)
echo    Get from: https://codecov.io/gh/UzairDevelops/Drive_on
echo.

echo 🚀 Step 4: Initial Commit and Push
echo ===================================
echo.

REM Stage all files
echo Staging files for commit...
git add .

REM Create initial commit
echo Creating initial commit...
git commit -m "🚀 Setup CI/CD pipeline for Drive-On main app

- Added comprehensive GitHub Actions workflows
- Configured Firebase hosting and app distribution  
- Added environment-specific configurations
- Enhanced build and deployment scripts
- Added security scanning and testing
- Implemented multi-environment support (dev/staging/prod)"

REM Push to GitHub
echo.
echo Pushing to GitHub...
set /p push_confirm="Push to GitHub repository? (y/n): "
if /i "%push_confirm%"=="y" (
    git push -u origin main
    if %errorlevel% equ 0 (
        echo ✅ Successfully pushed to GitHub!
    ) else (
        echo ❌ Failed to push to GitHub. You may need to authenticate.
        echo    Try: git push -u origin main
    )
) else (
    echo Skipping push. You can push manually later with:
    echo    git push -u origin main
)

echo.
echo 🎉 Setup Summary
echo ===============
echo.
echo ✅ Firebase service account key generated
echo ✅ Git repository configured  
echo ✅ GitHub remote added
echo ✅ .gitignore created
echo ✅ Initial commit created
echo.
echo 📋 Manual Steps Required:
echo.
echo 1. Add GitHub Secrets:
echo    https://github.com/UzairDevelops/Drive_on/settings/secrets/actions
echo.
echo 2. Configure Firebase App Distribution:
echo    https://console.firebase.google.com/project/drive-on-b2af8/appdistribution
echo    - Create tester groups: staging-testers, production-testers
echo.
echo 3. Update environment files:
echo    - .env.development
echo    - .env.staging  
echo    - .env.production
echo.
echo 4. Monitor first deployment:
echo    https://github.com/UzairDevelops/Drive_on/actions
echo.
echo 🔗 Useful Links:
echo    - Repository: https://github.com/UzairDevelops/Drive_on
echo    - Actions: https://github.com/UzairDevelops/Drive_on/actions
echo    - Firebase Console: https://console.firebase.google.com/project/drive-on-b2af8
echo    - Web App: https://drive-on-b2af8.web.app
echo.
echo 📚 Documentation:
echo    - setup-github-secrets.md
echo    - CI_CD_PIPELINE.md
echo    - DEPLOYMENT.md
echo.

REM Clean up sensitive file
echo 🧹 Cleaning up...
if exist "service-account-key.json" (
    echo Removing service account key file for security...
    del service-account-key.json
)

echo.
echo 🎯 Next Steps:
echo 1. Add the GitHub secrets shown above
echo 2. Push code to trigger first CI/CD run
echo 3. Check GitHub Actions for build status
echo 4. Verify deployments in Firebase Console
echo.
pause
