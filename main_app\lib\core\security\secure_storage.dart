import 'dart:convert';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

/// Handles secure storage of sensitive data using flutter_secure_storage
class SecureStorage {
  static final SecureStorage _instance = SecureStorage._internal();
  
  /// Singleton instance of SecureStorage
  factory SecureStorage() => _instance;
  
  SecureStorage._internal();
  
  /// The underlying secure storage instance
  final FlutterSecureStorage _storage = const FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
      resetOnError: true,
    ),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.first_unlock,
    ),
  );
  
  /// Storage keys
  static const String _tokenKey = 'auth_token';
  static const String _refreshTokenKey = 'refresh_token';
  static const String _userIdKey = 'user_id';
  static const String _userEmailKey = 'user_email';
  
  /// Stores an authentication token securely
  Future<void> storeAuthToken(String token) async {
    await _storage.write(key: _tokenKey, value: token);
  }
  
  /// Retrieves the stored authentication token
  Future<String?> getAuthToken() async {
    return await _storage.read(key: _tokenKey);
  }
  
  /// Stores a refresh token securely
  Future<void> storeRefreshToken(String token) async {
    await _storage.write(key: _refreshTokenKey, value: token);
  }
  
  /// Retrieves the stored refresh token
  Future<String?> getRefreshToken() async {
    return await _storage.read(key: _refreshTokenKey);
  }
  
  /// Stores the user ID securely
  Future<void> storeUserId(String userId) async {
    await _storage.write(key: _userIdKey, value: userId);
  }
  
  /// Retrieves the stored user ID
  Future<String?> getUserId() async {
    return await _storage.read(key: _userIdKey);
  }
  
  /// Stores the user email securely
  Future<void> storeUserEmail(String email) async {
    await _storage.write(key: _userEmailKey, value: email);
  }
  
  /// Retrieves the stored user email
  Future<String?> getUserEmail() async {
    return await _storage.read(key: _userEmailKey);
  }
  
  /// Stores a custom key-value pair securely
  Future<void> storeValue(String key, String value) async {
    await _storage.write(key: key, value: value);
  }
  
  /// Retrieves a stored value for the given key
  Future<String?> getValue(String key) async {
    return await _storage.read(key: key);
  }
  
  /// Stores a map of data securely as JSON
  Future<void> storeMap(String key, Map<String, dynamic> map) async {
    final String jsonString = jsonEncode(map);
    await _storage.write(key: key, value: jsonString);
  }
  
  /// Retrieves and parses a stored map
  Future<Map<String, dynamic>?> getMap(String key) async {
    final String? jsonString = await _storage.read(key: key);
    if (jsonString == null) return null;
    
    try {
      return jsonDecode(jsonString) as Map<String, dynamic>;
    } catch (e) {
      return null;
    }
  }
  
  /// Deletes the stored authentication token
  Future<void> deleteAuthToken() async {
    await _storage.delete(key: _tokenKey);
  }
  
  /// Deletes the stored refresh token
  Future<void> deleteRefreshToken() async {
    await _storage.delete(key: _refreshTokenKey);
  }
  
  /// Deletes a value with the specified key
  Future<void> deleteValue(String key) async {
    await _storage.delete(key: key);
  }
  
  /// Clears all authentication related data
  Future<void> clearAuthData() async {
    await _storage.delete(key: _tokenKey);
    await _storage.delete(key: _refreshTokenKey);
    await _storage.delete(key: _userIdKey);
    await _storage.delete(key: _userEmailKey);
  }
  
  /// Clears all stored data
  Future<void> clearAll() async {
    await _storage.deleteAll();
  }
} 