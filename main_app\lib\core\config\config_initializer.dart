import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:flutter/services.dart';
import 'package:package_info_plus/package_info_plus.dart';

import 'app_config.dart';

/// Class responsible for initializing application configuration
class ConfigInitializer {
  /// Private constructor to prevent instantiation
  ConfigInitializer._();
  
  /// Determines current environment based on compilation flags or environment variables
  static Future<Environment> _determineEnvironment() async {
    // Check for environment variable or compilation flag
    const envName = String.fromEnvironment(
      'ENVIRONMENT',
      defaultValue: kDebugMode ? 'development' : 'production',
    );
    
    // If using dotenv, try to get from there as well (fallback)
    if (await _canLoadDotEnv()) {
      await dotenv.load();
      final dotenvValue = dotenv.env['ENVIRONMENT'];
      if (dotenvValue != null && dotenvValue.isNotEmpty) {
        return _parseEnvironment(dotenvValue);
      }
    }
    
    return _parseEnvironment(envName);
  }
  
  /// Parse environment string to Environment enum
  static Environment _parseEnvironment(String envName) {
    switch (envName.toLowerCase()) {
      case 'dev':
      case 'development':
        return Environment.development;
      case 'staging':
      case 'test':
        return Environment.staging;
      case 'prod':
      case 'production':
        return Environment.production;
      default:
        // Default to production for safety
        return Environment.production;
    }
  }
  
  /// Check if we can load .env files
  static Future<bool> _canLoadDotEnv() async {
    try {
      // Check for .env file existence
      if (kIsWeb) {
        // For web, we'll need a different approach
        return await rootBundle.loadString('assets/.env').then((_) => true).catchError((_) => false);
      } else {
        return await File('.env').exists() || await File('assets/.env').exists();
      }
    } catch (e) {
      return false;
    }
  }
  
  /// Initialize the app configuration
  static Future<void> initialize() async {
    // Determine environment
    final environment = await _determineEnvironment();
    
    // Get package info for version tracking
    final packageInfo = await PackageInfo.fromPlatform();
    
    // Load environment variables if dotenv is available
    if (await _canLoadDotEnv()) {
      await dotenv.load();
    }
    
    // Create and initialize AppConfig
    AppConfig(
      environment: environment,
      apiBaseUrl: _getApiBaseUrl(environment),
      appVersion: packageInfo.version,
      buildNumber: packageInfo.buildNumber,
      enableVerboseLogging: environment != Environment.production,
      sentryDsn: _getSentryDsn(),
      firebaseProjectId: _getFirebaseProjectId(environment),
    );
    
    debugPrint('App initialized with environment: ${environment.toString().split('.').last}');
  }
  
  /// Get API base URL for the given environment
  static String _getApiBaseUrl(Environment environment) {
    // First check for compile-time env vars
    const fromEnv = String.fromEnvironment('API_BASE_URL');
    if (fromEnv.isNotEmpty) return fromEnv;
    
    // Then check dotenv if available
    if (dotenv.isInitialized && dotenv.env['API_BASE_URL'] != null) {
      return dotenv.env['API_BASE_URL']!;
    }
    
    // Finally use environment-specific defaults
    switch (environment) {
      case Environment.development:
        return 'https://dev-api.driveon.com/v1';
      case Environment.staging:
        return 'https://staging-api.driveon.com/v1';
      case Environment.production:
        return 'https://api.driveon.com/v1';
    }
  }
  
  /// Get Sentry DSN from environment variables or default value
  static String _getSentryDsn() {
    // First check for compile-time env vars
    const fromEnv = String.fromEnvironment('SENTRY_DSN');
    if (fromEnv.isNotEmpty) return fromEnv;
    
    // Then check dotenv if available
    if (dotenv.isInitialized && dotenv.env['SENTRY_DSN'] != null) {
      return dotenv.env['SENTRY_DSN']!;
    }
    
    // Return empty string if not found
    return '';
  }
  
  /// Get Firebase project ID for the given environment
  static String _getFirebaseProjectId(Environment environment) {
    // First check for compile-time env vars
    const fromEnv = String.fromEnvironment('FIREBASE_PROJECT_ID');
    if (fromEnv.isNotEmpty) return fromEnv;
    
    // Then check dotenv if available
    if (dotenv.isInitialized && dotenv.env['FIREBASE_PROJECT_ID'] != null) {
      return dotenv.env['FIREBASE_PROJECT_ID']!;
    }
    
    // Finally use environment-specific defaults
    switch (environment) {
      case Environment.development:
        return 'drive-on-dev';
      case Environment.staging:
        return 'drive-on-staging';
      case Environment.production:
        return 'drive-on-prod';
    }
  }
} 