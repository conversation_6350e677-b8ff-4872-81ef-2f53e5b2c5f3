import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../../core/utils/app_logger.dart';
import '../../core/firebase/firebase_service.dart';
import '../notifications/screens/notifications_screen.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({Key? key}) : super(key: key);

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  final AppLogger _logger = AppLogger('SettingsScreen');
  User? _currentUser;

  @override
  void initState() {
    super.initState();
    _currentUser = FirebaseService.getCurrentUser();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Settings'),
      ),
      body: ListView(
        children: [
          // Account section
          if (_currentUser != null) ...[
            _buildSectionHeader('Account'),
            _buildSettingItem(
              title: 'Profile',
              icon: Icons.person,
              onTap: () {
                _logger.debug('Navigate to profile settings');
                // Navigate to profile settings
              },
            ),
            _buildSettingItem(
              title: 'Security',
              icon: Icons.security,
              onTap: () {
                _logger.debug('Navigate to security settings');
                // Navigate to security settings
              },
            ),
          ],

          // Notifications section
          _buildSectionHeader('Notifications'),
          _buildSettingItem(
            title: 'Notifications',
            icon: Icons.notifications,
            onTap: () {
              _logger.debug('Navigate to notifications screen');
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const NotificationsScreen(),
                ),
              );
            },
          ),
          _buildSettingItem(
            title: 'Notification Preferences',
            icon: Icons.settings,
            onTap: () {
              _logger.debug('Notification preferences - Coming soon');
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Notification preferences coming soon!'),
                  duration: Duration(seconds: 2),
                ),
              );
            },
          ),

          // Appearance section
          _buildSectionHeader('Appearance'),
          _buildSettingItem(
            title: 'Theme',
            icon: Icons.brightness_6,
            onTap: () {
              _logger.debug('Toggle theme');
              // This will be implemented in the future
            },
          ),

          // Storage section
          _buildSectionHeader('Storage'),
          _buildSettingItem(
            title: 'Clear Cache',
            icon: Icons.cleaning_services,
            onTap: () {
              _logger.debug('Clear cache');
              _showClearCacheDialog();
            },
          ),

          // About section
          _buildSectionHeader('About'),
          _buildSettingItem(
            title: 'App Version',
            icon: Icons.info_outline,
            trailing: const Text('1.0.0', style: TextStyle(color: Colors.grey)),
            onTap: null,
          ),
          _buildSettingItem(
            title: 'Terms of Service',
            icon: Icons.description,
            onTap: () {
              _logger.debug('Navigate to terms of service');
              // Navigate to terms of service
            },
          ),
          _buildSettingItem(
            title: 'Privacy Policy',
            icon: Icons.privacy_tip,
            onTap: () {
              _logger.debug('Navigate to privacy policy');
              // Navigate to privacy policy
            },
          ),

          // Logout option
          if (_currentUser != null) ...[
            const SizedBox(height: 16),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: ElevatedButton(
                onPressed: _showLogoutDialog,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Logout'),
              ),
            ),
          ],

          const SizedBox(height: 32),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 24, 16, 8),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: Colors.blue,
        ),
      ),
    );
  }

  Widget _buildSettingItem({
    required String title,
    required IconData icon,
    required VoidCallback? onTap,
    Widget? trailing,
  }) {
    return ListTile(
      leading: Icon(icon),
      title: Text(title),
      trailing: trailing ?? const Icon(Icons.arrow_forward_ios, size: 16),
      onTap: onTap,
    );
  }

  void _showClearCacheDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear Cache'),
        content: const Text(
          'This will clear all cached data including images and app preferences. '
          'This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              _logger.debug('Cache cleared');
              Navigator.pop(context);

              // Show confirmation snackbar
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Cache cleared successfully'),
                  duration: Duration(seconds: 2),
                ),
              );
            },
            child: const Text('Clear', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _showLogoutDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Logout'),
        content: const Text('Are you sure you want to logout?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              try {
                Navigator.pop(context); // Close dialog
                await FirebaseService.signOut();
                _logger.debug('User logged out successfully');

                // Navigate to login screen or home depending on your app's navigation
              } catch (e) {
                _logger.error('Error logging out', error: e);

                // Show error snackbar
                if (mounted && context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Error logging out. Please try again.'),
                      duration: Duration(seconds: 2),
                    ),
                  );
                }
              }
            },
            child: const Text('Logout', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }
}