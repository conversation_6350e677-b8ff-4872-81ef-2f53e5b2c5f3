# PowerShell Script to Resize App Icon for Google Play Store
# This script resizes your app icon to 512x512 pixels

Write-Host "🎨 Drive-On App Icon Resizer for Google Play Store" -ForegroundColor Yellow
Write-Host "=================================================" -ForegroundColor Yellow
Write-Host ""

# Check if the original icon exists
$originalIcon = "app-icon-original.png"
$outputIcon = "app-icon-512x512.png"

if (-not (Test-Path $originalIcon)) {
    Write-Host "❌ Error: $originalIcon not found!" -ForegroundColor Red
    Write-Host "Please make sure the original icon file exists in this directory." -ForegroundColor Red
    exit 1
}

Write-Host "✅ Found original icon: $originalIcon" -ForegroundColor Green

# Load System.Drawing assembly for image processing
Add-Type -AssemblyName System.Drawing

try {
    # Load the original image
    $originalImage = [System.Drawing.Image]::FromFile((Resolve-Path $originalIcon).Path)
    
    Write-Host "📏 Original size: $($originalImage.Width)x$($originalImage.Height) pixels" -ForegroundColor Cyan
    
    # Create a new bitmap with 512x512 dimensions
    $newImage = New-Object System.Drawing.Bitmap(512, 512)
    
    # Create graphics object for drawing
    $graphics = [System.Drawing.Graphics]::FromImage($newImage)
    
    # Set high quality rendering
    $graphics.InterpolationMode = [System.Drawing.Drawing2D.InterpolationMode]::HighQualityBicubic
    $graphics.SmoothingMode = [System.Drawing.Drawing2D.SmoothingMode]::HighQuality
    $graphics.PixelOffsetMode = [System.Drawing.Drawing2D.PixelOffsetMode]::HighQuality
    $graphics.CompositingQuality = [System.Drawing.Drawing2D.CompositingQuality]::HighQuality
    
    # Draw the resized image
    $graphics.DrawImage($originalImage, 0, 0, 512, 512)
    
    # Save the resized image
    $newImage.Save($outputIcon, [System.Drawing.Imaging.ImageFormat]::Png)
    
    # Clean up
    $graphics.Dispose()
    $newImage.Dispose()
    $originalImage.Dispose()
    
    Write-Host "🎉 Successfully created 512x512 app icon!" -ForegroundColor Green
    Write-Host "📁 Saved as: $outputIcon" -ForegroundColor Green
    
    # Show file info
    $fileInfo = Get-ChildItem $outputIcon
    Write-Host "📊 File size: $([math]::Round($fileInfo.Length / 1KB, 2)) KB" -ForegroundColor Cyan
    
    Write-Host ""
    Write-Host "✅ Your 512x512 app icon is ready for Google Play Store!" -ForegroundColor Yellow
    Write-Host "📋 Next step: Create the 1024x500 feature graphic" -ForegroundColor Yellow
    
} catch {
    Write-Host "❌ Error resizing image: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "💡 Try using an online tool like https://www.iloveimg.com/resize-image/resize-png" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Press any key to continue..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
