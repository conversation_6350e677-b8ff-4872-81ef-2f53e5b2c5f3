import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import '../../../core/utils/app_logger.dart';
import 'auth_repository.dart';

/// Firebase implementation of the auth repository
class FirebaseAuthRepository implements AuthRepository {
  final FirebaseAuth _auth;
  final GoogleSignIn _googleSignIn;
  final AppLogger _logger = AppLogger('FirebaseAuthRepository');

  /// Creates a new Firebase auth repository
  /// 
  /// Accepts optional auth and googleSignIn parameters for testing
  FirebaseAuthRepository({
    FirebaseAuth? auth,
    GoogleSignIn? googleSignIn,
  }) :
    _auth = auth ?? FirebaseAuth.instance,
    _googleSignIn = googleSignIn ?? GoogleSignIn(
      // Explicitly configure for debug/release compatibility
      scopes: ['email', 'profile'],
    );

  @override
  FirebaseAuth get authInstance => _auth;

  @override
  Future<User?> getCurrentUser() async {
    try {
      return _auth.currentUser;
    } catch (e) {
      _logger.error('Error getting current user', error: e);
      return null;
    }
  }

  @override
  Stream<User?> get authStateChanges => _auth.authStateChanges();

  @override
  Future<UserCredential> signInWithEmailPassword(String email, String password) async {
    try {
      _logger.info('Attempting sign in for user: $email');
      return await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );
    } catch (e) {
      _logger.error('Error signing in with email and password', error: e);
      rethrow;
    }
  }

  @override
  Future<UserCredential> createUserWithEmailPassword(String email, String password) async {
    try {
      _logger.info('Attempting to create new user: $email');
      return await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );
    } catch (e) {
      _logger.error('Error creating user with email and password', error: e);
      rethrow;
    }
  }

  @override
  Future<UserCredential?> signInWithGoogle() async {
    try {
      // Initialize Google Sign In
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();
      
      // If user cancels the sign-in process
      if (googleUser == null) {
        return null;
      }
      
      // Obtain the auth details from the request
      final GoogleSignInAuthentication googleAuth = await googleUser.authentication;
      
      // Create a new credential
      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );
      
      // Sign in to Firebase with the credential
      return await _auth.signInWithCredential(credential);
    } catch (e) {
      _logger.error('Error signing in with Google', error: e);
      rethrow;
    }
  }

  @override
  Future<void> signOut() async {
    try {
      await _auth.signOut();
      _logger.info('User signed out successfully');
    } catch (e) {
      _logger.error('Error signing out', error: e);
      rethrow;
    }
  }

  @override
  Future<void> sendPasswordResetEmail(String email) async {
    try {
      _logger.info('Sending password reset email to: $email');
      await _auth.sendPasswordResetEmail(email: email);
      _logger.info('Password reset email sent successfully');
    } catch (e) {
      _logger.error('Error sending password reset email', error: e);
      rethrow;
    }
  }
} 