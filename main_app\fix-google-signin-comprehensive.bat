@echo off
echo ========================================
echo COMPREHENSIVE GOOGLE SIGN-IN FIX
echo ========================================
echo.

echo 🔍 DIAGNOSING GOOGLE SIGN-IN ISSUES...
echo.

echo 📋 CURRENT CONFIGURATION STATUS:
echo.
echo ✅ Production SHA-1: 85:C3:07:8A:88:35:3D:1D:15:88:76:93:84:F8:D7:17:41:A9:4B:A3
echo ✅ Debug SHA-1: 9A:8E:90:68:D4:97:C6:E2:5A:EF:B0:51:75:BC:CB:B5:CC:BD:55:DC
echo ✅ Both fingerprints configured in google-services.json
echo.

echo 🚨 MOST LIKELY CAUSES:
echo 1. Cached authentication data on device
echo 2. Google Play Services cache issues
echo 3. Firebase configuration not fully propagated
echo 4. Device-specific authentication state
echo.

echo 🛠️ APPLYING COMPREHENSIVE FIXES...
echo.

echo Step 1: Cleaning Flutter cache...
flutter clean
if %errorlevel% neq 0 (
    echo ❌ Failed to clean Flutter cache
    pause
    exit /b 1
)

echo Step 2: Getting dependencies...
flutter pub get
if %errorlevel% neq 0 (
    echo ❌ Failed to get dependencies
    pause
    exit /b 1
)

echo Step 3: Building fresh debug APK...
flutter build apk --debug
if %errorlevel% neq 0 (
    echo ❌ Failed to build debug APK
    pause
    exit /b 1
)

echo Step 4: Installing fresh APK...
flutter install
if %errorlevel% neq 0 (
    echo ❌ Failed to install APK
    pause
    exit /b 1
)

echo.
echo ✅ BUILD COMPLETED SUCCESSFULLY!
echo.

echo 📱 MANUAL DEVICE CLEANUP REQUIRED:
echo.
echo Since ADB may not be available, please manually clear these on your device:
echo.
echo 1. CLEAR DRIVE-ON APP DATA:
echo    - Settings ^> Apps ^> Drive-On
echo    - Storage ^> Clear Data ^> Clear Cache
echo.
echo 2. CLEAR GOOGLE PLAY SERVICES:
echo    - Settings ^> Apps ^> Google Play Services
echo    - Storage ^> Clear Cache (NOT Clear Data)
echo.
echo 3. CLEAR GOOGLE SERVICES FRAMEWORK:
echo    - Settings ^> Apps ^> Google Services Framework
echo    - Storage ^> Clear Cache
echo.
echo 4. RESTART DEVICE:
echo    - Power off completely
echo    - Wait 10 seconds
echo    - Power back on
echo.

echo 🔍 FIREBASE CONFIGURATION VERIFICATION:
echo.
echo Go to: https://console.firebase.google.com/project/drive-on-b2af8/settings/general/
echo.
echo Verify these SHA-1 fingerprints are present:
echo.
echo DEBUG SHA-1:
echo 9A:8E:90:68:D4:97:C6:E2:5A:EF:B0:51:75:BC:CB:B5:CC:BD:55:DC
echo.
echo PRODUCTION SHA-1:
echo 85:C3:07:8A:88:35:3D:1D:15:88:76:93:84:F8:D7:17:41:A9:4B:A3
echo.

echo 🧪 TESTING STEPS:
echo.
echo 1. After clearing device cache and restarting:
echo    - Open Drive-On app
echo    - Try Google Sign-In
echo    - Wait 5-10 minutes if it fails initially
echo.
echo 2. If still failing, try:
echo    - Different Google account
echo    - Release build: flutter build apk --release
echo    - Check device date/time is correct
echo.

echo 🔄 ALTERNATIVE TESTING:
echo.
echo If debug build continues to fail, test with release build:
echo.
echo flutter build apk --release
echo flutter install --release
echo.
echo The release build uses production SHA-1 which is also configured.
echo.

echo 📊 TROUBLESHOOTING CHECKLIST:
echo.
echo ✅ Flutter cache cleared
echo ✅ Fresh APK built and installed
echo ⏳ Device cache cleared (manual step)
echo ⏳ Device restarted (manual step)
echo ⏳ Google Sign-In tested
echo ⏳ Firebase configuration verified
echo.

echo 🎯 SUCCESS INDICATORS:
echo.
echo You'll know it's fixed when:
echo - Google Sign-In dialog appears
echo - You can select your Google account
echo - Authentication completes successfully
echo - No "ApiException: 10" errors
echo.

echo 📞 IF STILL FAILING:
echo.
echo 1. Wait 24 hours for Firebase propagation
echo 2. Try different Google account
echo 3. Check device has correct date/time
echo 4. Verify internet connection
echo 5. Contact Firebase support if needed
echo.

echo 🔗 HELPFUL RESOURCES:
echo.
echo Firebase Console: https://console.firebase.google.com/project/drive-on-b2af8/
echo Google Sign-In Docs: https://developers.google.com/identity/sign-in/android
echo SHA-1 Guide: https://developers.google.com/android/guides/client-auth
echo.

pause
