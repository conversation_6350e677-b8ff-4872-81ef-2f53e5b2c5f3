import 'package:flutter/material.dart';
import '../theme/ios_theme.dart';
import '../theme/colors.dart';

class ThemeProvider extends ChangeNotifier {
  ThemeData _themeData = IOSTheme.theme;
  bool _isDarkMode = false;

  ThemeData get theme => _themeData;
  bool get isDarkMode => _isDarkMode;

  void toggleTheme() {
    _isDarkMode = !_isDarkMode;
    _themeData = _isDarkMode ? _createDarkTheme() : IOSTheme.theme;
    notifyListeners();
  }

  // Create a dark version of the iOS theme
  ThemeData _createDarkTheme() {
    return ThemeData.dark().copyWith(
      primaryColor: AppColors.primaryYellow,
      scaffoldBackgroundColor: AppColors.darkBackground, // Pure black background
      cardColor: AppColors.darkSurface, // Dark surface color
      dividerColor: AppColors.darkDivider, // Dark divider
      appBarTheme: const AppBarTheme(
        backgroundColor: AppColors.primaryYellow,
        foregroundColor: AppColors.darkBackground,
        elevation: 0,
        centerTitle: true,
        titleTextStyle: TextStyle(
          color: Colors.white,
          fontSize: 17,
          fontWeight: FontWeight.w600,
          fontFamily: IOSTheme.fontFamily,
        ),
        iconTheme: IconThemeData(
          color: AppColors.darkBackground,
        ),
      ),
      textTheme: const TextTheme(
        bodyLarge: TextStyle(
          color: Colors.white,
          fontSize: 17,
          fontWeight: FontWeight.normal,
          fontFamily: IOSTheme.fontFamily,
        ),
        bodyMedium: TextStyle(
          color: Colors.white,
          fontSize: 15,
          fontWeight: FontWeight.normal,
          fontFamily: IOSTheme.fontFamily,
        ),
        titleLarge: TextStyle(
          color: Colors.white,
          fontSize: 20,
          fontWeight: FontWeight.w600,
          fontFamily: IOSTheme.fontFamily,
        ),
        titleMedium: TextStyle(
          color: Colors.white,
          fontSize: 17,
          fontWeight: FontWeight.w600,
          fontFamily: IOSTheme.fontFamily,
        ),
        labelLarge: TextStyle(
          color: IOSTheme.primaryColor,
          fontSize: 17,
          fontWeight: FontWeight.normal,
          fontFamily: IOSTheme.fontFamily,
        ),
      ),
      bottomNavigationBarTheme: const BottomNavigationBarThemeData(
        backgroundColor: AppColors.darkBackground,
        selectedItemColor: AppColors.primaryYellow,
        unselectedItemColor: AppColors.darkTextSecondary,
        type: BottomNavigationBarType.fixed,
        elevation: 8,
      ),
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: AppColors.darkSurface,
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppColors.darkDivider, width: 0.5),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppColors.darkDivider, width: 0.5),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppColors.primaryYellow, width: 1.0),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: IOSTheme.errorColor, width: 1.0),
        ),
        hintStyle: const TextStyle(
          color: AppColors.darkTextSecondary,
          fontSize: 17,
          fontWeight: FontWeight.normal,
          fontFamily: IOSTheme.fontFamily,
        ),
      ),
      cardTheme: CardTheme(
        elevation: 0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        color: AppColors.darkSurface,
      ),
      listTileTheme: const ListTileThemeData(
        contentPadding: EdgeInsets.symmetric(horizontal: 16),
        minLeadingWidth: 24,
        tileColor: AppColors.darkSurface,
      ),
      dialogTheme: DialogTheme(
        backgroundColor: AppColors.darkSurface,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(14),
        ),
        titleTextStyle: const TextStyle(
          color: Colors.white,
          fontSize: 17,
          fontWeight: FontWeight.w600,
          fontFamily: IOSTheme.fontFamily,
        ),
        contentTextStyle: const TextStyle(
          color: Colors.white,
          fontSize: 13,
          fontWeight: FontWeight.normal,
          fontFamily: IOSTheme.fontFamily,
        ),
      ),
    );
  }
}
