'use client'

import { Filter } from 'lucide-react'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'

interface UserFiltersProps {
  filters: {
    role: string
    status: string
    dateRange: string
  }
  onFiltersChange: (filters: any) => void
}

export function UserFilters({ filters, onFiltersChange }: UserFiltersProps) {
  const updateFilter = (key: string, value: string) => {
    onFiltersChange({
      ...filters,
      [key]: value
    })
  }

  return (
    <div className="flex space-x-2">
      {/* Role Filter */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" size="sm">
            <Filter className="w-4 h-4 mr-2" />
            Role: {filters.role === 'all' ? 'All' : filters.role}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          <DropdownMenuLabel>Filter by Role</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={() => updateFilter('role', 'all')}>
            All Roles
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('role', 'user')}>
            Users
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('role', 'admin')}>
            Admins
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Status Filter */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" size="sm">
            Status: {filters.status === 'all' ? 'All' : filters.status}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          <DropdownMenuLabel>Filter by Status</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={() => updateFilter('status', 'all')}>
            All Status
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('status', 'active')}>
            Active
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('status', 'inactive')}>
            Inactive
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Date Range Filter */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" size="sm">
            Date: {filters.dateRange === 'all' ? 'All time' : filters.dateRange}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          <DropdownMenuLabel>Filter by Date</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={() => updateFilter('dateRange', 'all')}>
            All time
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('dateRange', 'today')}>
            Today
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('dateRange', 'week')}>
            This week
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('dateRange', 'month')}>
            This month
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('dateRange', 'year')}>
            This year
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  )
}
