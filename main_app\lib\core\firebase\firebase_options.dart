import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';

class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      default:
        return android; // Default to Android for other platforms
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: "AIzaSyBQehlAZFI-WngAMwzblEu7JBdMcCr-P80",
    authDomain: "drive-on-b2af8.firebaseapp.com",
    projectId: "drive-on-b2af8",
    storageBucket: "drive-on-b2af8.appspot.com",
    messagingSenderId: "206767723448",
    appId: "1:206767723448:android:6a8e1d9c3c8992992d754d",
    measurementId: "G-measurement-id",
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: "AIzaSyBQehlAZFI-WngAMwzblEu7JBdMcCr-P80",
    appId: "1:206767723448:android:6a8e1d9c3c8992992d754d",
    messagingSenderId: "206767723448",
    projectId: "drive-on-b2af8",
    storageBucket: "drive-on-b2af8.appspot.com",
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: "AIzaSyBQehlAZFI-WngAMwzblEu7JBdMcCr-P80",
    appId: "1:206767723448:ios:36bc43f21837f9142d754d",
    messagingSenderId: "206767723448",
    projectId: "drive-on-b2af8",
    storageBucket: "drive-on-b2af8.appspot.com",
    iosClientId: "206767723448-ipb3s3mf9bkd45nj2cgftq5f7f51u2vb.apps.googleusercontent.com",
  );
}