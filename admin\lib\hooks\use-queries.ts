import { useState, useEffect } from 'react'
import { collection, query, orderBy, onSnapshot, doc, updateDoc, addDoc, Timestamp } from 'firebase/firestore'
import { db } from '@/lib/firebase/config'

interface Query {
  id: string
  subject: string
  description: string
  category: 'technical' | 'billing' | 'account' | 'general' | 'complaint' | 'feature-request'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  status: 'open' | 'in-progress' | 'resolved' | 'closed'
  userId: string
  userName: string
  userEmail: string
  userPhone?: string
  userAvatar?: string
  assignedTo?: {
    id: string
    name: string
  }
  lastMessage: string
  lastMessageTime: Date
  lastSenderId: string
  messageCount: number
  createdAt: Date
  updatedAt: Date
  resolvedAt?: Date
  resolvedBy?: string
  resolutionNote?: string
  satisfactionRating?: number
  tags: string[]
}

interface QueryMessage {
  id: string
  queryId: string
  senderId: string
  senderName: string
  senderAvatar: string
  text: string
  timestamp: Date
  isAdmin: boolean
  attachments?: string[]
  voiceNote?: string
  voiceNoteDuration?: number
  isEdited: boolean
  isPending?: boolean
  isUploading?: boolean
}

export function useQueries() {
  const [queries, setQueries] = useState<Query[]>([])
  const [queryMessages, setQueryMessages] = useState<{ [queryId: string]: QueryMessage[] }>({})
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const queriesQuery = query(
      collection(db, 'queries'),
      orderBy('lastMessageTime', 'desc')
    )

    const unsubscribe = onSnapshot(
      queriesQuery,
      (snapshot) => {
        const queriesData = snapshot.docs.map(doc => {
          const data = doc.data()
          return {
            id: doc.id,
            subject: data.subject || '',
            description: data.description || '',
            category: data.category || 'general',
            priority: data.priority || 'medium',
            status: data.status || 'open',
            userId: data.userId || '',
            userName: data.userName || '',
            userEmail: data.userEmail || '',
            userPhone: data.userPhone,
            userAvatar: data.userAvatar,
            assignedTo: data.assignedTo,
            lastMessage: data.lastMessage || '',
            lastMessageTime: data.lastMessageTime?.toDate() || new Date(),
            lastSenderId: data.lastSenderId || '',
            messageCount: data.messageCount || 0,
            createdAt: data.createdAt?.toDate() || new Date(),
            updatedAt: data.updatedAt?.toDate() || new Date(),
            resolvedAt: data.resolvedAt?.toDate(),
            resolvedBy: data.resolvedBy,
            resolutionNote: data.resolutionNote,
            satisfactionRating: data.satisfactionRating,
            tags: data.tags || [],
          } as Query
        })

        setQueries(queriesData)
        setIsLoading(false)
        setError(null)
      },
      (error) => {
        console.error('Error fetching queries:', error)
        setError('Failed to fetch queries')
        setIsLoading(false)
      }
    )

    return () => unsubscribe()
  }, [])

  // Fetch messages for a specific query
  const fetchQueryMessages = (queryId: string) => {
    console.log('Setting up message listener for query:', queryId) // Debug log

    const messagesQuery = query(
      collection(db, 'queries', queryId, 'messages'),
      orderBy('timestamp', 'asc')
    )

    return onSnapshot(
      messagesQuery,
      (snapshot) => {
        console.log('Messages snapshot received:', snapshot.docs.length, 'messages') // Debug log

        const messagesData = snapshot.docs.map(doc => {
          const data = doc.data()
          console.log('Processing message:', doc.id, data) // Debug log

          return {
            id: doc.id,
            queryId,
            senderId: data.senderId || '',
            senderName: data.senderName || '',
            senderAvatar: data.senderAvatar || '',
            text: data.text || '',
            timestamp: data.timestamp?.toDate() || new Date(),
            isAdmin: data.isAdmin || false,
            attachments: data.attachments || [],
            voiceNote: data.voiceNote,
            voiceNoteDuration: data.voiceNoteDuration,
            isEdited: data.isEdited || false,
            isPending: data.isPending || false,
            isUploading: data.isUploading || false,
          } as QueryMessage
        })

        console.log('Setting messages for query:', queryId, messagesData) // Debug log

        setQueryMessages(prev => ({
          ...prev,
          [queryId]: messagesData
        }))
      },
      (error) => {
        console.error('Error in message listener:', error) // Debug log
      }
    )
  }

  const updateQuery = async (queryId: string, updates: Partial<Query>) => {
    try {
      const queryRef = doc(db, 'queries', queryId)
      const updateData: any = { ...updates }

      // Convert dates to Firestore timestamps
      if (updateData.createdAt instanceof Date) {
        updateData.createdAt = Timestamp.fromDate(updateData.createdAt)
      }
      if (updateData.updatedAt instanceof Date) {
        updateData.updatedAt = Timestamp.fromDate(updateData.updatedAt)
      }
      if (updateData.resolvedAt instanceof Date) {
        updateData.resolvedAt = Timestamp.fromDate(updateData.resolvedAt)
      }
      if (updateData.lastMessageTime instanceof Date) {
        updateData.lastMessageTime = Timestamp.fromDate(updateData.lastMessageTime)
      }

      updateData.updatedAt = Timestamp.now()

      await updateDoc(queryRef, updateData)
    } catch (error) {
      console.error('Error updating query:', error)
      throw error
    }
  }

  const respondToQuery = async (queryId: string, message: string) => {
    try {
      const messageData = {
        senderId: 'admin', // TODO: Get actual admin ID
        senderName: 'Admin', // TODO: Get actual admin name
        senderAvatar: '', // TODO: Get actual admin avatar
        text: message,
        timestamp: Timestamp.now(),
        isAdmin: true,
        attachments: [],
        isEdited: false,
        isPending: false,
        isUploading: false,
      }

      await addDoc(collection(db, 'queries', queryId, 'messages'), messageData)

      // Update query's last message info
      const queryRef = doc(db, 'queries', queryId)
      await updateDoc(queryRef, {
        lastMessage: message,
        lastMessageTime: Timestamp.now(),
        lastSenderId: 'admin',
        status: 'in-progress',
        messageCount: (queries.find(q => q.id === queryId)?.messageCount || 0) + 1,
        updatedAt: Timestamp.now()
      })
    } catch (error) {
      console.error('Error responding to query:', error)
      throw error
    }
  }

  const sendMessage = async (queryId: string, message: Partial<QueryMessage>) => {
    try {
      // Build message data, excluding undefined values
      const messageData: any = {
        queryId: queryId, // Ensure queryId is included
        timestamp: Timestamp.now(),
        isEdited: false,
        isPending: false,
        isUploading: false,
      }

      // Only add fields that have values to avoid undefined values in Firebase
      if (message.senderId) messageData.senderId = message.senderId
      if (message.senderName) messageData.senderName = message.senderName
      if (message.senderAvatar !== undefined) messageData.senderAvatar = message.senderAvatar
      if (message.text !== undefined) messageData.text = message.text
      if (message.isAdmin !== undefined) messageData.isAdmin = message.isAdmin
      if (message.attachments) messageData.attachments = message.attachments
      if (message.voiceNote) messageData.voiceNote = message.voiceNote
      if (message.voiceNoteDuration) messageData.voiceNoteDuration = message.voiceNoteDuration

      console.log('Sending message:', messageData) // Debug log

      await addDoc(collection(db, 'queries', queryId, 'messages'), messageData)

      // Update query's last message info
      const queryRef = doc(db, 'queries', queryId)
      await updateDoc(queryRef, {
        lastMessage: message.text || '',
        lastMessageTime: Timestamp.now(),
        lastSenderId: message.senderId || '',
        messageCount: (queries.find(q => q.id === queryId)?.messageCount || 0) + 1,
        updatedAt: Timestamp.now()
      })

      console.log('Message sent successfully') // Debug log
    } catch (error) {
      console.error('Error sending message:', error)
      throw error
    }
  }

  const resolveQuery = async (queryId: string, resolutionNote: string) => {
    try {
      const queryRef = doc(db, 'queries', queryId)
      await updateDoc(queryRef, {
        status: 'resolved',
        resolvedAt: Timestamp.now(),
        resolvedBy: 'Admin', // TODO: Get actual admin name
        resolutionNote,
        updatedAt: Timestamp.now()
      })
    } catch (error) {
      console.error('Error resolving query:', error)
      throw error
    }
  }

  const assignQuery = async (queryId: string, assigneeId: string) => {
    try {
      const queryRef = doc(db, 'queries', queryId)

      // Mock assignee data - in real app, fetch from users collection
      const assigneeMap: Record<string, { id: string; name: string }> = {
        '1': { id: '1', name: 'John Admin' },
        '2': { id: '2', name: 'Sarah Support' },
        '3': { id: '3', name: 'Mike Manager' },
      }

      const assignee = assigneeMap[assigneeId]
      if (!assignee) throw new Error('Assignee not found')

      await updateDoc(queryRef, {
        assignedTo: assignee,
        status: 'in-progress',
        updatedAt: Timestamp.now()
      })
    } catch (error) {
      console.error('Error assigning query:', error)
      throw error
    }
  }

  return {
    queries,
    queryMessages,
    isLoading,
    error,
    updateQuery,
    respondToQuery,
    sendMessage,
    resolveQuery,
    assignQuery,
    fetchQueryMessages,
  }
}
