import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../core/core.dart';
import '../../core/security/input_validator.dart';
import 'models/job_model.dart';

class EditJobScreen extends StatefulWidget {
  final JobModel job;

  const EditJobScreen({
    Key? key,
    required this.job,
  }) : super(key: key);

  @override
  State<EditJobScreen> createState() => _EditJobScreenState();
}

class _EditJobScreenState extends State<EditJobScreen> {
  final _formKey = GlobalKey<FormState>();
  final _dutyHoursController = TextEditingController();

  bool _isLoading = false;

  // Form fields
  String? _title;
  String? _type;
  String? _employmentType;
  String? _salary;
  String? _city;
  String? _benefits;

  @override
  void initState() {
    super.initState();
    _initializeFields();
  }

  void _initializeFields() {
    _title = widget.job.title;
    _type = widget.job.type;
    _employmentType = widget.job.employmentType;
    _salary = widget.job.salary;
    _city = widget.job.city;
    _benefits = widget.job.benefits;
    _dutyHoursController.text = widget.job.dutyHours.join(', ');
  }

  @override
  void dispose() {
    _dutyHoursController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Edit Job Post'),
        backgroundColor: isDarkMode ? AppColors.darkSurface : Colors.white,
        foregroundColor: isDarkMode ? AppColors.darkText : AppColors.lightText,
        elevation: 0,
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _updateJob,
            child: const Text(
              'Save',
              style: TextStyle(
                color: AppColors.primaryYellow,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildSectionTitle('Job Information'),
                    const SizedBox(height: 16),

                    _buildTextField(
                      label: 'Job Title',
                      initialValue: _title,
                      onSaved: (value) => _title = value,
                      validator: (value) => InputValidator.validateRequired(value, 'job title'),
                    ),

                    const SizedBox(height: 16),

                    _buildDropdownField(
                      label: 'Job Type',
                      value: _type,
                      items: ['InDrive', 'Household', 'Company'],
                      onChanged: (value) => setState(() => _type = value),
                    ),

                    const SizedBox(height: 16),

                    _buildDropdownField(
                      label: 'Employment Type',
                      value: _employmentType,
                      items: ['Full-time', 'Part-time'],
                      onChanged: (value) => setState(() => _employmentType = value),
                    ),

                    const SizedBox(height: 16),

                    _buildTextField(
                      label: 'Salary',
                      initialValue: _salary,
                      onSaved: (value) => _salary = value,
                      validator: (value) => InputValidator.validateRequired(value, 'salary'),
                    ),

                    const SizedBox(height: 16),

                    _buildTextField(
                      label: 'City',
                      initialValue: _city,
                      onSaved: (value) => _city = value,
                      validator: (value) => InputValidator.validateRequired(value, 'city'),
                    ),

                    const SizedBox(height: 16),

                    _buildTextField(
                      label: 'Benefits',
                      initialValue: _benefits,
                      onSaved: (value) => _benefits = value,
                      validator: (value) => InputValidator.validateRequired(value, 'benefits'),
                    ),

                    const SizedBox(height: 16),

                    _buildTextField(
                      label: 'Duty Hours (comma separated)',
                      controller: _dutyHoursController,
                      onSaved: (value) {}, // Not needed since we use controller
                      validator: (value) => InputValidator.validateRequired(value, 'duty hours'),
                    ),

                    const SizedBox(height: 32),

                    SizedBox(
                      width: double.infinity,
                      height: 50,
                      child: ElevatedButton(
                        onPressed: _isLoading ? null : _updateJob,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.primaryYellow,
                          foregroundColor: Colors.black,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(25),
                          ),
                        ),
                        child: _isLoading
                            ? const CircularProgressIndicator(color: Colors.black)
                            : const Text(
                                'Update Job Post',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildSectionTitle(String title) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return Text(
      title,
      style: TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.bold,
        color: isDarkMode ? AppColors.darkText : AppColors.lightText,
      ),
    );
  }

  Widget _buildTextField({
    required String label,
    String? initialValue,
    TextEditingController? controller,
    required FormFieldSetter<String> onSaved,
    required FormFieldValidator<String> validator,
  }) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return TextFormField(
      initialValue: controller == null ? initialValue : null,
      controller: controller,
      decoration: InputDecoration(
        labelText: label,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        filled: true,
        fillColor: isDarkMode ? AppColors.darkSurface : Colors.grey[50],
      ),
      onSaved: onSaved,
      validator: validator,
    );
  }

  Widget _buildDropdownField({
    required String label,
    required String? value,
    required List<String> items,
    required ValueChanged<String?> onChanged,
  }) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return DropdownButtonFormField<String>(
      value: value,
      decoration: InputDecoration(
        labelText: label,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        filled: true,
        fillColor: isDarkMode ? AppColors.darkSurface : Colors.grey[50],
      ),
      items: items.map((item) => DropdownMenuItem(
        value: item,
        child: Text(item),
      )).toList(),
      onChanged: onChanged,
      validator: (value) => InputValidator.validateRequired(value, label.toLowerCase()),
    );
  }

  Future<void> _updateJob() async {
    if (!_formKey.currentState!.validate()) return;

    _formKey.currentState!.save();

    setState(() => _isLoading = true);

    try {
      final updatedJob = widget.job.copyWith(
        title: _title!,
        type: _type!,
        employmentType: _employmentType!,
        salary: _salary!,
        city: _city!,
        benefits: _benefits!,
        dutyHours: _dutyHoursController.text
            .split(',')
            .map((e) => e.trim())
            .where((e) => e.isNotEmpty)
            .toList(),
      );

      await FirebaseFirestore.instance
          .collection('jobs')
          .doc(widget.job.jobId)
          .update(updatedJob.toMap());

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Job updated successfully!')),
        );
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error updating job: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }
}
