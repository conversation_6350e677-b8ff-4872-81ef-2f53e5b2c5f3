# Logging Infrastructure Documentation

## Overview

This document describes the logging infrastructure implemented in the Drive-On application. The system provides a robust and configurable logging mechanism that replaces basic debug prints with structured logging. It enables environment-specific logging configurations and supports remote error reporting via Sentry.

## Key Features

- **Structured Logging**: Organized logs with timestamps, tags, and severity levels
- **Environment-Specific Configuration**: Different logging behaviors in development and production
- **Remote Error Monitoring**: Integration with Sentry for production error tracking
- **Performance Optimized**: Minimal impact on application performance
- **Breadcrumb Tracking**: Record user actions and flow for better debugging
- **Centralized Control**: Unified logging interface across the application

## Components

### 1. AppLogger Class

The main logging engine that provides different logging levels and handles both local and remote logging.

Key features:
- Singleton pattern to avoid creating multiple instances
- Environment-specific configuration
- Integration with Sentry for remote error tracking
- Support for bread crumbs to track user flows

### 2. Log Utility

A simplified interface for logging throughout the application. This utility class provides static methods that make it easy to replace all `print()` statements with proper logging calls.

Available methods:
- `Log.d()` - Debug level logs (dev only)
- `Log.i()` - Information level logs
- `Log.w()` - Warning level logs (sent to Sentry in prod)
- `Log.e()` - Error level logs (sent to Sentry in prod)
- `Log.wtf()` - Critical error logs (sent to Sentry in prod)
- `Log.breadcrumb()` - Track user actions and application flow

### 3. Sentry Integration

For production builds, errors are automatically reported to Sentry for monitoring and analysis.

Features:
- Automatic error capturing
- User context tracking
- Breadcrumbs for tracing the sequence of events leading to an error
- Custom tags and metadata

## Usage Guidelines

### Basic Logging

Replace all `print()` statements with the appropriate logging method:

```dart
// Before
print('User logged in: $userId');

// After
Log.i('User logged in: $userId', tag: 'Authentication');
```

### Error Logging

For error conditions, include the error object and stack trace:

```dart
try {
  // Some operation
} catch (e, stackTrace) {
  Log.e('Operation failed', error: e, stackTrace: stackTrace, tag: 'MyFeature');
}
```

### Tracking User Actions

To track user flows and actions for debugging:

```dart
// When user taps a button
Log.breadcrumb('User tapped login button', 
  category: 'user.interaction',
  data: {'screen': 'LoginScreen'}
);
```

### Advanced Usage with AppLogger

For more specific logging needs, you can create a logger instance for a specific component:

```dart
class MyService {
  final AppLogger _logger = AppLogger('MyService');
  
  void doSomething() {
    _logger.info('Operation started');
    // ...
    _logger.debug('Technical details: $details');
  }
}
```

## Log Levels Guide

Choose the appropriate log level based on the information being logged:

- **Verbose**: Highly detailed tracing (rarely used, development only)
- **Debug**: Information useful for development and debugging
- **Info**: General information about app operation
- **Warning**: Potential issues that don't interrupt operation
- **Error**: Errors that prevent a function from working
- **WTF**: Critical errors that may crash the app

## Environment-Specific Behavior

- **Development Mode**:
  - All log levels are displayed
  - Colored console output with method traces
  - Logs are not sent to Sentry
  - Detailed information for debugging

- **Production Mode**:
  - Only warnings and errors are logged
  - Minimal information is logged locally
  - Errors and warnings are sent to Sentry
  - Optimized for performance

## Best Practices

1. **Be Specific**: Use meaningful message text and appropriate tags
2. **Choose Proper Levels**: Don't log everything as errors
3. **Sensitive Data**: Never log passwords, tokens, or personal information
4. **Use Tags**: Add tags to make filtering easier
5. **Include Context**: Add relevant data to help understand the context
6. **Performance**: Avoid excessive logging in critical paths

## Sentry DSN Configuration

For security reasons, the Sentry DSN should be configured via environment variables and not hard-coded:

```bash
# Local development with Sentry disabled
flutter run

# Production build with Sentry enabled
flutter build apk --dart-define=SENTRY_DSN=https://your-sentry-dsn
```

## Migrating from Print Statements

When replacing `print()` statements, consider the following guidelines:

1. For simple debugging: `Log.d('message')`
2. For informative messages: `Log.i('message')`
3. For warning conditions: `Log.w('message')`
4. For errors: `Log.e('message', error: e)`
5. For debugging only prints: Use `Log.debug('message')` which only appears in debug builds

## Viewing Logs

- **Development**: Logs appear in the console with colors and formatting
- **Production**: Critical errors appear in Sentry dashboard
- **Device Logs**: For on-device logs, consider adding a UI to display recent logs 