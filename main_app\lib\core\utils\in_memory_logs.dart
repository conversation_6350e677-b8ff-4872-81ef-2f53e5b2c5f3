import 'dart:convert';
import 'dart:io';
import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';

/// Log level enumeration for categorizing logs by severity
///
/// These levels are used to visually distinguish different types of logs
/// and to filter logs by importance.
enum LogLevel {
  /// Verbose logging for detailed tracing (lowest severity)
  verbose,

  /// Debug information useful during development
  debug,

  /// General information about normal application flow
  info,

  /// Warnings that don't interrupt execution but need attention
  warning,

  /// Errors that affect functionality but don't crash the app
  error,

  /// Critical failures that require immediate attention ("What a Terrible Failure")
  wtf,
}

/// Extension to get string and color representation of log levels
extension LogLevelExtension on LogLevel {
  /// Returns a human-readable name for this log level
  String get name {
    switch (this) {
      case LogLevel.verbose: return 'VERBOSE';
      case LogLevel.debug: return 'DEBUG';
      case LogLevel.info: return 'INFO';
      case LogLevel.warning: return 'WARNING';
      case LogLevel.error: return 'ERROR';
      case LogLevel.wtf: return 'WTF';
    }
  }

  /// Returns a numeric value for ordering and comparing log levels
  int get value {
    switch (this) {
      case LogLevel.verbose: return 0;
      case LogLevel.debug: return 1;
      case LogLevel.info: return 2;
      case LogLevel.warning: return 3;
      case LogLevel.error: return 4;
      case LogLevel.wtf: return 5;
    }
  }
}

/// A log entry representing a single log record in the application
///
/// Contains all metadata needed to understand the context of a log event
/// including timestamp, message, severity level, and error details.
class LogEntry {
  /// When the log was created
  final DateTime timestamp;

  /// The main log message
  final String message;

  /// Severity level of the log
  final LogLevel level;

  /// Optional category or module tag for filtering
  final String tag;

  /// Optional error object that triggered this log
  final Object? error;

  /// Optional stack trace for error debugging
  final StackTrace? stackTrace;

  /// Creates a new log entry with the specified properties
  ///
  /// The [timestamp] defaults to the current time if not specified.
  LogEntry({
    DateTime? timestamp,
    required this.message,
    required this.level,
    required this.tag,
    this.error,
    this.stackTrace,
  }) : timestamp = timestamp ?? DateTime.now();

  /// Creates a log entry from a JSON map
  ///
  /// Used when deserializing logs from storage
  factory LogEntry.fromJson(Map<String, dynamic> json) {
    return LogEntry(
      timestamp: DateTime.parse(json['timestamp'] as String),
      message: json['message'] as String,
      level: LogLevel.values.firstWhere(
        (e) => e.toString() == json['level'],
        orElse: () => LogLevel.info,
      ),
      tag: json['tag'] as String,
      error: json['error'] != null ? json['error'] as String : null,
      // Stack trace isn't restored from JSON
    );
  }

  /// Converts this log entry to a JSON map for serialization
  ///
  /// Used when persisting logs to storage
  Map<String, dynamic> toJson() {
    return {
      'timestamp': timestamp.toIso8601String(),
      'message': message,
      'level': level.toString(),
      'tag': tag,
      'error': error?.toString(),
      // Stack trace isn't included in JSON
    };
  }

  /// Returns the color associated with this log level for UI display
  ///
  /// Each severity level has a distinct color for visual differentiation
  Color get levelColor {
    switch (level) {
      case LogLevel.verbose:
        return Colors.grey;
      case LogLevel.debug:
        return Colors.blue;
      case LogLevel.info:
        return Colors.green;
      case LogLevel.warning:
        return Colors.orange;
      case LogLevel.error:
        return Colors.red;
      case LogLevel.wtf:
        return Colors.purple;
    }
  }

  /// Returns a short string representation of the log level
  ///
  /// Used for compact display in the log viewer UI
  String get levelString {
    switch (level) {
      case LogLevel.verbose:
        return 'V';
      case LogLevel.debug:
        return 'D';
      case LogLevel.info:
        return 'I';
      case LogLevel.warning:
        return 'W';
      case LogLevel.error:
        return 'E';
      case LogLevel.wtf:
        return 'F'; // Fatal
    }
  }

  /// Returns a formatted timestamp string for display
  ///
  /// Format: 'HH:mm:ss.mmm'
  String get formattedTime {
    return '${timestamp.hour.toString().padLeft(2, '0')}:'
        '${timestamp.minute.toString().padLeft(2, '0')}:'
        '${timestamp.second.toString().padLeft(2, '0')}.'
        '${timestamp.millisecond.toString().padLeft(3, '0')}';
  }

  /// Returns a formatted date string for display
  ///
  /// Format: 'yyyy-MM-dd'
  String get formattedDate {
    return '${timestamp.year}-'
        '${timestamp.month.toString().padLeft(2, '0')}-'
        '${timestamp.day.toString().padLeft(2, '0')}';
  }

  /// Returns a string representation of this log entry
  @override
  String toString() {
    final buffer = StringBuffer();
    buffer.write('$formattedDate $formattedTime [$levelString] [$tag] $message');

    if (error != null) {
      buffer.write('\nError: $error');
    }

    if (stackTrace != null) {
      buffer.write('\nStack trace: $stackTrace');
    }

    return buffer.toString();
  }
}

/// In-memory storage for application logs with persistence capabilities
///
/// This class maintains a circular buffer of log messages in memory for efficient
/// access in the app's log viewer. It also provides functionality to:
/// - Store logs with different severity levels
/// - Filter logs by level, tag, or text
/// - Export logs to a file for sharing
/// - Persist logs between app restarts
/// - Notify listeners when new logs are added
///
/// The implementation uses a singleton pattern to ensure a single storage instance
/// is shared throughout the application.
class InMemoryLogs extends ChangeNotifier {
  // Singleton instance
  static final InMemoryLogs _instance = InMemoryLogs._internal();

  /// Access to the singleton instance
  static InMemoryLogs get instance => _instance;

  /// Private constructor for singleton pattern
  InMemoryLogs._internal();

  /// Maximum number of log entries to keep in memory (circular buffer)
  final int _maxEntries = 1000;

  /// The list of log entries stored in memory
  final List<LogEntry> _entries = [];

  /// Whether logs are also persisted to disk storage
  bool _persistToDisk = true;

  /// ValueNotifier to notify listeners when logs change
  final ValueNotifier<List<LogEntry>> logsNotifier = ValueNotifier<List<LogEntry>>([]);

  /// Directory where logs are stored on disk
  String? _logsDirectory;

  /// Base name for log files
  final String _logFileName = 'app_logs';

  /// Maximum number of log files to keep in rotation
  final int _maxLogFiles = 5;

  /// Maximum size of a log file before rotation in bytes (5MB)
  final int _maxLogSizeBytes = 5 * 1024 * 1024;

  /// Initialize the in-memory logs system
  ///
  /// Sets up disk persistence if enabled and loads existing logs
  Future<void> init() async {
    if (_persistToDisk) {
      await _initLogDirectory();
      await _loadLogsFromDisk();
    }
  }

  /// Enable or disable persisting logs to disk storage
  ///
  /// When enabled, logs will be saved to files and loaded on restart
  void setPersistToDisk(bool enabled) {
    _persistToDisk = enabled;
    notifyListeners();
  }

  /// Add a new log entry to storage
  ///
  /// Parameters:
  /// - [message] The message to log
  /// - [level] The severity level
  /// - [tag] Category or module tag
  /// - [error] Optional error object
  /// - [stackTrace] Optional stack trace
  void addLog(
    String message,
    LogLevel level,
    String tag, {
    Object? error,
    StackTrace? stackTrace,
  }) {
    final entry = LogEntry(
      message: message,
      level: level,
      tag: tag,
      error: error,
      stackTrace: stackTrace,
    );

    // Keep logs under the maximum size by removing oldest entries
    if (_entries.length >= _maxEntries) {
      _entries.removeAt(0);
    }

    _entries.add(entry);

    // Write to disk if persistence is enabled
    if (_persistToDisk) {
      _writeLogToDisk(entry);
    }

    notifyListeners();
  }

  /// Clear all logs from memory
  ///
  /// This will not remove logs from disk unless [removePersisted] is true
  Future<void> clearLogs({bool removePersisted = false}) async {
    _entries.clear();

    if (removePersisted && _persistToDisk) {
      await _clearPersistedLogs();
    }

    notifyListeners();
  }

  /// Get all log entries currently stored in memory
  ///
  /// Returns an unmodifiable list to prevent external modification
  List<LogEntry> get entries => List.unmodifiable(_entries);

  /// Get filtered log entries by level, tag, and/or text content
  ///
  /// Parameters:
  /// - [level] Optional level to filter by
  /// - [tag] Optional tag to filter by
  /// - [searchText] Optional text to search in messages
  List<LogEntry> getFilteredLogs({
    LogLevel? level,
    String? tag,
    String? searchText,
  }) {
    return _entries.where((entry) {
      bool matchesLevel = level == null || entry.level == level;
      bool matchesTag = tag == null || tag.isEmpty || entry.tag == tag;
      bool matchesSearch = searchText == null ||
          searchText.isEmpty ||
          entry.message.toLowerCase().contains(searchText.toLowerCase());

      return matchesLevel && matchesTag && matchesSearch;
    }).toList();
  }

  /// Export logs to a file for sharing
  ///
  /// Returns the path to the created file, or null if export failed
  Future<String?> exportLogs() async {
    try {
      final directory = await getTemporaryDirectory();
      final filePath = '${directory.path}/app_logs_export.txt';
      final file = File(filePath);

      final buffer = StringBuffer();

      // Add header
      buffer.writeln('Application Logs Export');
      buffer.writeln('Generated: ${DateTime.now().toIso8601String()}');
      buffer.writeln('Total Entries: ${_entries.length}');
      buffer.writeln('-' * 80);
      buffer.writeln();

      // Add each log entry
      for (final entry in _entries) {
        buffer.writeln('${entry.formattedDate} ${entry.formattedTime}'
            ' [${entry.levelString}] [${entry.tag}] ${entry.message}');

        if (entry.error != null) {
          buffer.writeln('ERROR: ${entry.error}');
        }

        if (entry.stackTrace != null) {
          buffer.writeln('STACK TRACE:');
          buffer.writeln(entry.stackTrace);
        }

        buffer.writeln();
      }

      // Write to file
      await file.writeAsString(buffer.toString());
      return filePath;
    } catch (e) {
      if (kDebugMode) {
        print('Failed to export logs: $e');
      }
      return null;
    }
  }

  /// Share logs via the system share dialog
  ///
  /// This will create a temporary file and share it using the share_plus package
  Future<void> shareLogs() async {
    final filePath = await exportLogs();
    if (filePath != null) {
      await Share.shareXFiles([XFile(filePath)], subject: 'Application Logs');
    }
  }

  /// Share a specific log entry via the system share dialog
  ///
  /// Parameters:
  /// - [entry] The log entry to share
  Future<void> shareLogEntry(LogEntry entry) async {
    final text = '''
Log Entry: ${entry.formattedDate} ${entry.formattedTime}
Level: ${entry.level}
Tag: ${entry.tag}
Message: ${entry.message}
${entry.error != null ? 'Error: ${entry.error}' : ''}
${entry.stackTrace != null ? 'Stack Trace: ${entry.stackTrace}' : ''}
''';

    await Share.share(text, subject: 'Application Log Entry');
  }

  /// Initialize the directory for storing logs
  Future<void> _initLogDirectory() async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      _logsDirectory = '${appDir.path}/logs';

      final dir = Directory(_logsDirectory!);
      if (!await dir.exists()) {
        await dir.create(recursive: true);
      }
    } catch (e) {
      if (kDebugMode) {
        print('Failed to initialize log directory: $e');
      }
    }
  }

  /// Load persisted logs from disk into memory
  Future<void> _loadLogsFromDisk() async {
    if (_logsDirectory == null) return;

    try {
      final dir = Directory(_logsDirectory!);
      if (!await dir.exists()) return;

      final files = await dir.list()
          .where((entity) => entity is File && entity.path.contains(_logFileName))
          .toList();

      // Sort by file name (which includes timestamp) to load in chronological order
      files.sort((a, b) => a.path.compareTo(b.path));

      // Load logs from each file, starting with the oldest
      for (final entity in files) {
        if (entity is File) {
          final content = await entity.readAsString();

          // Skip empty files
          if (content.trim().isEmpty) continue;

          // Parse JSON in background isolate to avoid blocking UI
          final List<dynamic> jsonList = await compute(_parseJsonContent, content);

          for (final json in jsonList) {
            try {
              final entry = LogEntry.fromJson(json);

              // Skip if we've hit the max entries
              if (_entries.length >= _maxEntries) break;

              _entries.add(entry);
            } catch (e) {
              if (kDebugMode) {
                print('Failed to parse log entry: $e');
              }
            }
          }

          // Stop loading more files if we've hit the max entries
          if (_entries.length >= _maxEntries) break;
        }
      }

      notifyListeners();
    } catch (e) {
      if (kDebugMode) {
        print('Failed to load logs from disk: $e');
      }
    }
  }

  /// Write a log entry to the current log file on disk (optimized for performance)
  Future<void> _writeLogToDisk(LogEntry entry) async {
    if (_logsDirectory == null) return;

    // Run file operations in background to avoid blocking UI
    unawaited(compute(_writeLogToDiskIsolate, {
      'entry': entry.toJson(),
      'logsDirectory': _logsDirectory!,
      'logFileName': _logFileName,
      'maxLogSizeBytes': _maxLogSizeBytes,
      'maxLogFiles': _maxLogFiles,
    }));
  }

  /// Static method to run in isolate for file operations
  static Future<void> _writeLogToDiskIsolate(Map<String, dynamic> params) async {
    try {
      final entryJson = params['entry'] as Map<String, dynamic>;
      final logsDirectory = params['logsDirectory'] as String;
      final logFileName = params['logFileName'] as String;
      final maxLogSizeBytes = params['maxLogSizeBytes'] as int;
      final maxLogFiles = params['maxLogFiles'] as int;

      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final filePath = '$logsDirectory/${logFileName}_$timestamp.json';
      final logFile = File(filePath);

      // Check if we need to rotate logs
      if (await logFile.exists()) {
        final size = await logFile.length();
        if (size >= maxLogSizeBytes) {
          await _rotateLogsIsolate(logsDirectory, logFileName, maxLogFiles);
        }
      }

      // Read existing content and append new entry (optimized)
      List<dynamic> jsonList = [];
      if (await logFile.exists()) {
        try {
          final content = await logFile.readAsString();
          if (content.isNotEmpty) {
            jsonList = jsonDecode(content);
          }
        } catch (e) {
          // If parsing fails, start fresh
          jsonList = [];
        }
      }

      jsonList.add(entryJson);
      await logFile.writeAsString(jsonEncode(jsonList));
    } catch (e) {
      // Silent fail in isolate to avoid crashing
      if (kDebugMode) {
        print('Failed to write log to disk in isolate: $e');
      }
    }
  }

  /// Rotate logs in isolate
  static Future<void> _rotateLogsIsolate(String logsDirectory, String logFileName, int maxLogFiles) async {
    try {
      final dir = Directory(logsDirectory);
      if (!await dir.exists()) return;

      final logFiles = await dir
          .list()
          .where((entity) => entity is File && entity.path.contains(logFileName))
          .toList();

      // Sort by file name (which includes timestamp) with oldest first
      logFiles.sort((a, b) => a.path.compareTo(b.path));

      // Remove oldest files if we have too many
      while (logFiles.length >= maxLogFiles) {
        final oldestFile = logFiles.removeAt(0);
        await oldestFile.delete();
      }
    } catch (e) {
      if (kDebugMode) {
        print('Failed to rotate logs in isolate: $e');
      }
    }
  }



  /// Clear all persisted log files from disk
  Future<void> _clearPersistedLogs() async {
    if (_logsDirectory == null) return;

    try {
      final dir = Directory(_logsDirectory!);
      if (!await dir.exists()) return;

      final logFiles = await dir
          .list()
          .where((entity) => entity is File && entity.path.contains(_logFileName))
          .toList();

      for (final file in logFiles) {
        await file.delete();
      }
    } catch (e) {
      if (kDebugMode) {
        print('Failed to clear persisted logs: $e');
      }
    }
  }

  /// Get all logs currently in memory
  List<LogEntry> getLogs() {
    return List.unmodifiable(_entries);
  }

  /// Static method to parse JSON content in isolate
  static List<dynamic> _parseJsonContent(String content) {
    return jsonDecode(content);
  }
}