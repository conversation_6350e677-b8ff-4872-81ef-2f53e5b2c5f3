import 'dart:io';

import 'package:flutter/material.dart';
import '../models/query_message.dart';
import '../models/query_metadata.dart';
import '../services/query_service.dart';
import '../widgets/query_message_bubble.dart';

import 'package:image_picker/image_picker.dart';
import '../../../core/firebase/firebase_permissions_check.dart';

class UserQueryScreen extends StatefulWidget {
  const UserQueryScreen({super.key});

  @override
  State<UserQueryScreen> createState() => _UserQueryScreenState();
}

class _UserQueryScreenState extends State<UserQueryScreen> {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final ImagePicker _imagePicker = ImagePicker();
  final QueryService _queryService = QueryService();

  QueryMetadata? _queryMetadata;
  String? _queryId;
  List<QueryMessage>? _messages;

  bool _isLoading = true;
  bool _isSending = false;
  bool _showAttachmentOptions = false;

  List<File> _selectedImages = [];
  QueryMessage? _editingMessage;

  @override
  void initState() {
    super.initState();
    _initializeQuery();
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _initializeQuery() async {
    try {
      setState(() => _isLoading = true);

      final queryData = await _queryService.getUserQuery();

      setState(() {
        _queryMetadata = queryData;
        _queryId = queryData.id;
        _isLoading = false;
      });

      // Mark messages as read when screen opens
      if (_queryId != null) {
        await _queryService.markAllAsRead(_queryId!);
      }
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _sendMessage() async {
    final text = _messageController.text.trim();

    if (text.isEmpty && _selectedImages.isEmpty) {
      return;
    }

    try {
      setState(() {
        _isSending = true;
        _showAttachmentOptions = false;
      });

      // Check permissions before attempting to upload
      bool hasPermissions = true;
      if (_selectedImages.isNotEmpty) {
        hasPermissions = await FirebasePermissionsCheck.checkRequiredPermissions();
        if (!hasPermissions && mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Storage permissions required. Please grant permissions in settings.'),
              backgroundColor: Colors.red,
              duration: Duration(seconds: 5),
            ),
          );
          setState(() {
            _isSending = false;
          });
          return;
        }
      }

      if (_editingMessage != null) {
        // Editing message
        await _queryService.editMessage(
          messageId: _editingMessage!.id,
          queryId: _queryId!,
          newText: text,
        );

        _messageController.clear();
        setState(() {
          _editingMessage = null;
          _isSending = false;
        });
      } else {
        // Sending new message
        await _queryService.sendMessage(
          queryId: _queryId!,
          text: text,
          images: _selectedImages,
        );

        _messageController.clear();
        setState(() {
          _selectedImages = [];
          _isSending = false;
        });
      }

      // Scroll to bottom after sending
      await Future.delayed(const Duration(milliseconds: 300));
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    } catch (e) {
      setState(() {
        _isSending = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error sending message: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _pickImages() async {
    try {
      final pickedFiles = await _imagePicker.pickMultiImage();

      if (pickedFiles.isNotEmpty) {
        setState(() {
          for (final pickedFile in pickedFiles) {
            _selectedImages.add(File(pickedFile.path));
          }
          _showAttachmentOptions = false;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error picking images: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _takePhoto() async {
    try {
      final pickedFile = await _imagePicker.pickImage(source: ImageSource.camera);

      if (pickedFile != null) {
        setState(() {
          _selectedImages.add(File(pickedFile.path));
          _showAttachmentOptions = false;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error taking photo: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _removeImage(int index) {
    setState(() {
      _selectedImages.removeAt(index);
    });
  }



  void _cancelEditing() {
    setState(() {
      _editingMessage = null;
      _messageController.clear();
    });
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Support Chat'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _initializeQuery,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                // Status indicator (if query is closed)
                if (_queryMetadata != null && !_queryMetadata!.isActive)
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    color: isDarkMode ? Colors.grey[800] : Colors.grey[200],
                    child: Row(
                      children: [
                        Icon(
                          Icons.archive,
                          size: 16,
                          color: isDarkMode ? Colors.grey[400] : Colors.grey[700],
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'This query has been closed by support',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: isDarkMode ? Colors.grey[400] : Colors.grey[700],
                          ),
                        ),
                      ],
                    ),
                  ),

                // Messages area
                Expanded(
                  child: _queryId == null
                      ? const Center(child: Text('Error loading chat'))
                      : StreamBuilder<List<QueryMessage>>(
                          stream: _queryService.getMessagesStream(_queryId!),
                          builder: (context, snapshot) {
                            if (snapshot.connectionState == ConnectionState.waiting && _messages == null) {
                              return const Center(child: CircularProgressIndicator());
                            }

                            if (snapshot.hasError) {
                              return Center(
                                child: Text(
                                  'Error: ${snapshot.error}',
                                  style: const TextStyle(color: Colors.red),
                                ),
                              );
                            }

                            final messages = snapshot.data ?? _messages ?? [];
                            if (messages.isEmpty) {
                              return Center(
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      Icons.question_answer_outlined,
                                      size: 64,
                                      color: Colors.grey[400],
                                    ),
                                    const SizedBox(height: 16),
                                    Text(
                                      'Start a conversation with support',
                                      style: TextStyle(
                                        fontSize: 18,
                                        color: Colors.grey[600],
                                      ),
                                    ),
                                    const SizedBox(height: 8),
                                    Text(
                                      'We\'re here to help with any questions or issues',
                                      style: TextStyle(
                                        fontSize: 14,
                                        color: Colors.grey[500],
                                      ),
                                      textAlign: TextAlign.center,
                                    ),
                                  ],
                                ),
                              );
                            }

                            // Cache messages for smoother UX
                            _messages = messages;

                            // Auto-scroll to bottom on new messages
                            WidgetsBinding.instance.addPostFrameCallback((_) {
                              if (_scrollController.hasClients) {
                                _scrollController.animateTo(
                                  _scrollController.position.maxScrollExtent,
                                  duration: const Duration(milliseconds: 300),
                                  curve: Curves.easeOut,
                                );
                              }
                            });

                            // Build message list
                            return ListView.builder(
                              controller: _scrollController,
                              padding: const EdgeInsets.all(16),
                              itemCount: messages.length,
                              itemBuilder: (context, index) {
                                final message = messages[index];
                                final isMe = message.senderId != 'admin' && message.senderId != 'system';

                                // Show sender name for first message or when sender changes
                                final showSender = index == 0 ||
                                    messages[index - 1].senderId != message.senderId;

                                return Padding(
                                  padding: const EdgeInsets.only(bottom: 8),
                                  child: QueryMessageBubble(
                                    message: message,
                                    isMe: isMe,
                                    showSender: showSender,
                                    onEdit: isMe ? (text) {
                                      if (_queryId != null) {
                                        _queryService.editMessage(
                                          messageId: message.id,
                                          queryId: _queryId!,
                                          newText: text,
                                        );
                                      }
                                    } : null,
                                    onDelete: isMe ? () {
                                      if (_queryId != null) {
                                        _queryService.deleteMessage(
                                          messageId: message.id,
                                          queryId: _queryId!,
                                        );
                                      }
                                    } : null,
                                  ),
                                );
                              },
                            );
                          },
                        ),
                ),

                // Editing indicator
                if (_editingMessage != null)
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    color: isDarkMode ? Colors.grey[800] : Colors.grey[200],
                    child: Row(
                      children: [
                        const Icon(Icons.edit, size: 16),
                        const SizedBox(width: 8),
                        const Expanded(
                          child: Text(
                            'Editing message',
                            style: TextStyle(
                              fontWeight: FontWeight.w500,
                              fontSize: 14,
                            ),
                          ),
                        ),
                        IconButton(
                          icon: const Icon(Icons.close, size: 16),
                          padding: EdgeInsets.zero,
                          constraints: const BoxConstraints(
                            minWidth: 24,
                            minHeight: 24,
                          ),
                          onPressed: _cancelEditing,
                        ),
                      ],
                    ),
                  ),

                // Image preview
                if (_selectedImages.isNotEmpty)
                  Container(
                    height: 100,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    color: isDarkMode ? Colors.grey[800] : Colors.grey[200],
                    child: ListView.builder(
                      scrollDirection: Axis.horizontal,
                      itemCount: _selectedImages.length,
                      itemBuilder: (context, index) {
                        return Container(
                          width: 80,
                          height: 80,
                          margin: const EdgeInsets.only(right: 8),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: isDarkMode ? Colors.grey[700]! : Colors.grey[300]!,
                            ),
                          ),
                          child: Stack(
                            fit: StackFit.expand,
                            children: [
                              ClipRRect(
                                borderRadius: BorderRadius.circular(7),
                                child: Image.file(
                                  _selectedImages[index],
                                  fit: BoxFit.cover,
                                ),
                              ),
                              Positioned(
                                top: 0,
                                right: 0,
                                child: GestureDetector(
                                  onTap: () => _removeImage(index),
                                  child: Container(
                                    decoration: BoxDecoration(
                                      color: Colors.black.withOpacity(0.5),
                                      borderRadius: const BorderRadius.only(
                                        bottomLeft: Radius.circular(8),
                                        topRight: Radius.circular(7),
                                      ),
                                    ),
                                    child: const Icon(
                                      Icons.close,
                                      size: 16,
                                      color: Colors.white,
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        );
                      },
                    ),
                  ),



                // Message input
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: isDarkMode ? Colors.grey[900] : Colors.white,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.05),
                        blurRadius: 10,
                        offset: const Offset(0, -3),
                      ),
                    ],
                  ),
                  child: Row(
                    children: [
                      IconButton(
                        icon: const Icon(Icons.add),
                        onPressed: () {
                          setState(() {
                            _showAttachmentOptions = !_showAttachmentOptions;
                          });
                        },
                      ),
                      Expanded(
                        child: TextField(
                          controller: _messageController,
                          decoration: InputDecoration(
                            hintText: 'Type a message...',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(24),
                              borderSide: BorderSide.none,
                            ),
                            filled: true,
                            fillColor: isDarkMode
                                ? Colors.grey[800]
                                : Colors.grey[100],
                            contentPadding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 8,
                            ),
                          ),
                          minLines: 1,
                          maxLines: 5,
                          textCapitalization: TextCapitalization.sentences,
                          enabled: _queryMetadata?.isActive ?? true,
                        ),
                      ),
                      if (_queryMetadata?.isActive ?? true)
                        IconButton(
                          icon: const Icon(Icons.send),
                          onPressed: _isSending ? null : _sendMessage,
                        ),
                    ],
                  ),
                ),

                // Attachment options
                if (_showAttachmentOptions && (_queryMetadata?.isActive ?? true))
                  Container(
                    color: isDarkMode ? Colors.grey[900] : Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        _buildAttachmentOption(
                          icon: Icons.image,
                          label: 'Gallery',
                          onTap: _pickImages,
                        ),
                        _buildAttachmentOption(
                          icon: Icons.camera_alt,
                          label: 'Camera',
                          onTap: _takePhoto,
                        ),
                      ],
                    ),
                  ),
              ],
            ),
    );
  }

  Widget _buildAttachmentOption({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(25),
              ),
              child: Icon(
                icon,
                color: Theme.of(context).primaryColor,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: isDarkMode ? Colors.grey[400] : Colors.grey[800],
              ),
            ),
          ],
        ),
      ),
    );
  }
}