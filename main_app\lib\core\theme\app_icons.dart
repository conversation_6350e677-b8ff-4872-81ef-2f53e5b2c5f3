import 'package:flutter/material.dart';

class AppIcons {
  // Navigation Icons
  static const IconData home = Icons.home_rounded;
  static const IconData back = Icons.arrow_back_rounded;
  static const IconData forward = Icons.arrow_forward_rounded;
  static const IconData menu = Icons.menu_rounded;
  static const IconData close = Icons.close_rounded;
  
  // Action Icons
  static const IconData add = Icons.add_rounded;
  static const IconData edit = Icons.edit_rounded;
  static const IconData delete = Icons.delete_rounded;
  static const IconData save = Icons.save_rounded;
  static const IconData share = Icons.share_rounded;
  static const IconData search = Icons.search_rounded;
  static const IconData filter = Icons.filter_list_rounded;
  static const IconData sort = Icons.sort_rounded;
  static const IconData more = Icons.more_vert_rounded;
  
  // Status Icons
  static const IconData success = Icons.check_circle_rounded;
  static const IconData warning = Icons.warning_rounded;
  static const IconData error = Icons.error_rounded;
  static const IconData info = Icons.info_rounded;
  
  // User Icons
  static const IconData person = Icons.person_rounded;
  static const IconData people = Icons.people_rounded;
  static const IconData account = Icons.account_circle_rounded;
  static const IconData settings = Icons.settings_rounded;
  
  // Content Icons
  static const IconData favorite = Icons.favorite_rounded;
  static const IconData favoriteBorder = Icons.favorite_border_rounded;
  static const IconData star = Icons.star_rounded;
  static const IconData starBorder = Icons.star_border_rounded;
  static const IconData calendar = Icons.calendar_today_rounded;
  static const IconData notification = Icons.notifications_rounded;
  static const IconData notificationOff = Icons.notifications_off_rounded;
  
  // Communication Icons
  static const IconData email = Icons.email_rounded;
  static const IconData phone = Icons.phone_rounded;
  static const IconData chat = Icons.chat_rounded;
  static const IconData message = Icons.message_rounded;
  
  // Media Icons
  static const IconData photo = Icons.photo_rounded;
  static const IconData camera = Icons.camera_alt_rounded;
  static const IconData video = Icons.videocam_rounded;
  static const IconData music = Icons.music_note_rounded;
  static const IconData play = Icons.play_arrow_rounded;
  static const IconData pause = Icons.pause_rounded;
  
  // Transport Icons
  static const IconData car = Icons.directions_car_rounded;
  static const IconData bus = Icons.directions_bus_rounded;
  static const IconData train = Icons.directions_transit_rounded;
  static const IconData bike = Icons.directions_bike_rounded;
  static const IconData walk = Icons.directions_walk_rounded;
  
  // Theme Icons
  static const IconData lightMode = Icons.wb_sunny_rounded;
  static const IconData darkMode = Icons.nights_stay_rounded;
  
  // Payment Icons
  static const IconData creditCard = Icons.credit_card_rounded;
  static const IconData payment = Icons.payment_rounded;
  static const IconData wallet = Icons.account_balance_wallet_rounded;
  
  // Location Icons
  static const IconData location = Icons.location_on_rounded;
  static const IconData map = Icons.map_rounded;
  static const IconData navigation = Icons.navigation_rounded;
  static const IconData nearMe = Icons.near_me_rounded;
  
  // Misc Icons
  static const IconData checkmark = Icons.check_rounded;
  static const IconData link = Icons.link_rounded;
  static const IconData attach = Icons.attach_file_rounded;
  static const IconData download = Icons.download_rounded;
  static const IconData upload = Icons.upload_rounded;
  static const IconData refresh = Icons.refresh_rounded;
} 