import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../../../core/core.dart';
import '../../drivers/models/driver.dart';
import '../../drivers/screens/driver_details_screen.dart';
import '../../drivers/screens/unified_driver_registration_screen.dart';
import '../../drivers/widgets/driver_avatar.dart';
import '../../drivers/services/user_driver_service.dart';

// Create a logger for this section
final _logger = AppLogger('DriversSection');

class DriversSection extends StatefulWidget {
  const DriversSection({super.key});

  @override
  State<DriversSection> createState() => _DriversSectionState();
}

class _DriversSectionState extends State<DriversSection> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  // Add a key to force rebuild of the StreamBuilder
  Key _streamKey = UniqueKey();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _refreshDriversList() {
    setState(() {
      _streamKey = UniqueKey();
    });
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Drivers',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: isDarkMode ? AppColors.darkText : AppColors.lightText,
                ),
              ),
              // Dynamic button based on user's driver status
              StreamBuilder<Driver?>(
                stream: UserDriverService.instance.getCurrentUserDriverStream(),
                builder: (context, snapshot) {
                  _logger.debug('StreamBuilder: connectionState=${snapshot.connectionState}, hasData=${snapshot.hasData}, hasError=${snapshot.hasError}');
                  if (snapshot.hasError) {
                    _logger.error('StreamBuilder error', error: snapshot.error);
                  }

                  final userDriver = snapshot.data;
                  _logger.debug('StreamBuilder: userDriver=${userDriver?.name}, status=${userDriver?.status}');

                  if (snapshot.connectionState == ConnectionState.waiting) {
                    return ElevatedButton.icon(
                      onPressed: null,
                      icon: const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      ),
                      label: const Text('Loading...'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.grey,
                        foregroundColor: Colors.white,
                      ),
                    );
                  }

                  if (userDriver != null) {
                    // User has an approved driver profile, show "My Driver Post" button
                    _logger.debug('Showing My Driver Post button for ${userDriver.name}');
                    return ElevatedButton.icon(
                      onPressed: () => _showDriverOptions(context, userDriver),
                      icon: const Icon(Icons.person_pin),
                      label: const Text('My Driver Post'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primaryYellow,
                        foregroundColor: Colors.black,
                      ),
                    );
                  } else {
                    // User hasn't registered as driver, show "Register as Driver" button
                    _logger.debug('Showing Register as Driver button');
                    return ElevatedButton.icon(
                      onPressed: () {
                        // Check if user is logged in
                        final user = FirebaseAuth.instance.currentUser;
                        if (user == null) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(content: Text('You must be logged in to register as a driver')),
                          );
                          return;
                        }

                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => UnifiedDriverRegistrationScreen(
                              onRegistrationComplete: _refreshDriversList,
                            ),
                          ),
                        );
                      },
                      icon: const Icon(Icons.add),
                      label: const Text('Register as Driver'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primaryYellow,
                        foregroundColor: Colors.black,
                      ),
                    );
                  }
                },
              ),
            ],
          ),

          const SizedBox(height: 12),
          Text(
            'Find and connect with professional drivers',
            style: TextStyle(
              fontSize: 12,
              color: isDarkMode ? AppColors.darkTextSecondary : AppColors.lightTextSecondary,
            ),
          ),
          const SizedBox(height: 16),

          // Search bar
          Container(
            decoration: BoxDecoration(
              color: isDarkMode ? AppColors.darkSurface : Colors.white,
              borderRadius: BorderRadius.circular(10),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search drivers...',
                hintStyle: const TextStyle(fontSize: 14),
                prefixIcon: Icon(
                  Icons.search,
                  size: 20,
                  color: isDarkMode ? Colors.white54 : Colors.black54,
                ),
                suffixIcon: _searchQuery.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        _searchController.clear();
                        setState(() {
                          _searchQuery = '';
                        });
                      },
                    )
                  : null,
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(vertical: 12),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
          ),

          const SizedBox(height: 16),

          // Drivers List
          Expanded(
            child: StreamBuilder<QuerySnapshot>(
              key: _streamKey,
              stream: FirebaseFirestore.instance
                  .collection('drivers')
                  .orderBy('createdAt', descending: true)
                  .snapshots(),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Center(
                    child: CircularProgressIndicator(
                      color: AppColors.primaryYellow,
                    ),
                  );
                }

                if (snapshot.hasError) {
                  if (kDebugMode) {
                    print('Error fetching drivers: ${snapshot.error}');
                  }
                  return const Center(
                    child: Text('Error loading drivers. Please try again.'),
                  );
                }

                if (!snapshot.hasData || snapshot.data!.docs.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.people_outline,
                          size: 64,
                          color: isDarkMode ? Colors.white38 : Colors.black38,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'No drivers available',
                          style: TextStyle(
                            fontSize: 18,
                            color: isDarkMode ? Colors.white38 : Colors.black38,
                          ),
                        ),
                        const SizedBox(height: 24),
                        ElevatedButton.icon(
                          onPressed: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => UnifiedDriverRegistrationScreen(
                                  onRegistrationComplete: _refreshDriversList,
                                ),
                              ),
                            );
                          },
                          icon: const Icon(Icons.add),
                          label: const Text('Register as Driver'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.primaryYellow,
                            foregroundColor: Colors.black,
                            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                }

                // Filter drivers by search query and status (exclude suspended drivers)
                final allDrivers = snapshot.data!.docs.where((doc) {
                  final data = doc.data() as Map<String, dynamic>;
                  final status = data['status'] as String?;
                  // Only show verified drivers (null for backward compatibility)
                  return status == null || status == 'verified';
                }).map((doc) {
                  return Driver.fromMap(
                    doc.data() as Map<String, dynamic>,
                    doc.id,
                  );
                }).toList();

                final filteredDrivers = _searchQuery.isEmpty
                    ? allDrivers
                    : allDrivers.where((driver) =>
                        driver.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
                        driver.city.toLowerCase().contains(_searchQuery.toLowerCase())
                      ).toList();

                if (filteredDrivers.isEmpty) {
                  return Center(
                    child: Text(
                      'No drivers match your search',
                      style: TextStyle(
                        fontSize: 16,
                        color: isDarkMode ? Colors.white38 : Colors.black38,
                      ),
                    ),
                  );
                }

                return ListView.builder(
                  physics: const BouncingScrollPhysics(),
                  itemCount: filteredDrivers.length,
                  cacheExtent: 500, // Cache 500 pixels worth of items
                  itemBuilder: (context, index) {
                    final driver = filteredDrivers[index];
                    // Wrap each item in RepaintBoundary for better performance
                    return RepaintBoundary(
                      child: _buildDriverCard(context, driver),
                    );
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  void _showDriverOptions(BuildContext context, Driver driver) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: isDarkMode ? AppColors.darkSurface : AppColors.lightSurface,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const SizedBox(height: 12),
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: isDarkMode ? AppColors.darkTextSecondary : Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 20),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Text(
                'Manage Driver Profile',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: isDarkMode ? AppColors.darkText : AppColors.lightText,
                ),
              ),
            ),
            const SizedBox(height: 20),

            // Get real-time status for options
            StreamBuilder<DocumentSnapshot>(
              stream: FirebaseFirestore.instance
                  .collection('drivers')
                  .doc(driver.id)
                  .snapshots(),
              builder: (context, snapshot) {
                String? currentStatus;
                if (snapshot.hasData && snapshot.data!.exists) {
                  final data = snapshot.data!.data() as Map<String, dynamic>?;
                  currentStatus = data?['status'] as String?;
                }

                return Column(
                  children: [
                    _buildOptionTile(
                      context,
                      icon: Icons.visibility,
                      title: 'View Profile',
                      onTap: () {
                        Navigator.pop(context);
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => DriverDetailsScreen(driver: driver),
                          ),
                        );
                      },
                    ),
                    const Divider(),
                    // Show different options based on real-time driver status
                    // Only show suspend/go live options if driver is verified (admin approved)
                    if (currentStatus == 'verified' || currentStatus == 'suspended') ...[
                      if (currentStatus == 'suspended') ...[
                        _buildOptionTile(
                          context,
                          icon: Icons.play_circle,
                          title: 'Go Live',
                          subtitle: 'Make profile visible to users again',
                          onTap: () {
                            Navigator.pop(context);
                            _goLiveDriver(context, driver);
                          },
                        ),
                      ] else ...[
                        _buildOptionTile(
                          context,
                          icon: Icons.pause_circle,
                          title: 'Suspend Profile',
                          subtitle: 'Temporarily hide from users',
                          onTap: () {
                            Navigator.pop(context);
                            _suspendDriver(context, driver);
                          },
                        ),
                      ],
                    ],
                    _buildOptionTile(
                      context,
                      icon: Icons.delete,
                      title: 'Delete Profile',
                      subtitle: 'Permanently remove driver profile',
                      isDestructive: true,
                      onTap: () {
                        Navigator.pop(context);
                        _deleteDriver(context, driver);
                      },
                    ),
                    const SizedBox(height: 10),
                  ],
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOptionTile(
    BuildContext context, {
    required IconData icon,
    required String title,
    String? subtitle,
    required VoidCallback onTap,
    bool isDestructive = false,
  }) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return ListTile(
      leading: Icon(
        icon,
        color: isDestructive ? Colors.red : AppColors.primaryYellow,
      ),
      title: Text(
        title,
        style: TextStyle(
          color: isDestructive
              ? Colors.red
              : (isDarkMode ? AppColors.darkText : AppColors.lightText),
          fontWeight: FontWeight.w500,
        ),
      ),
      subtitle: subtitle != null
          ? Text(
              subtitle,
              style: TextStyle(
                color: isDarkMode ? AppColors.darkTextSecondary : AppColors.lightTextSecondary,
                fontSize: 12,
              ),
            )
          : null,
      onTap: onTap,
    );
  }

  void _goLiveDriver(BuildContext context, Driver driver) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Go Live'),
          content: const Text(
            'Are you sure you want to make your driver profile visible to users again?',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () async {
                Navigator.pop(context);
                await _performGoLiveDriver(context, driver);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
              ),
              child: const Text('Go Live'),
            ),
          ],
        );
      },
    );
  }

  Future<void> _performGoLiveDriver(BuildContext context, Driver driver) async {
    try {
      await UserDriverService.instance.reactivateDriver(driver.id);

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Driver profile is now live and visible to users'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error reactivating driver profile: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _suspendDriver(BuildContext context, Driver driver) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Suspend Profile'),
          content: const Text(
            'Are you sure you want to temporarily hide your driver profile from users?',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () async {
                Navigator.pop(context);
                await _performSuspendDriver(context, driver);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
              ),
              child: const Text('Suspend'),
            ),
          ],
        );
      },
    );
  }

  Future<void> _performSuspendDriver(BuildContext context, Driver driver) async {
    try {
      await UserDriverService.instance.suspendDriver(driver.id);

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Driver profile has been suspended'),
            backgroundColor: Colors.orange,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error suspending driver profile: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _deleteDriver(BuildContext context, Driver driver) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Delete Profile'),
          content: const Text(
            'Are you sure you want to permanently delete your driver profile? This action cannot be undone.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () async {
                Navigator.pop(context);
                await _performDeleteDriver(context, driver);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: const Text('Delete'),
            ),
          ],
        );
      },
    );
  }

  Future<void> _performDeleteDriver(BuildContext context, Driver driver) async {
    try {
      await UserDriverService.instance.deleteDriver(driver.id);

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Driver profile has been deleted'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error deleting driver profile: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Widget _buildDriverCard(BuildContext context, Driver driver) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => DriverDetailsScreen(driver: driver),
          ),
        );
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: 16),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isDarkMode ? AppColors.darkSurface : AppColors.lightSurface,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: isDarkMode
                  ? Colors.black.withOpacity(0.3)
                  : Colors.black.withOpacity(0.05),
              blurRadius: isDarkMode ? 8 : 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            DriverAvatar(
              profilePictureUrl: driver.profilePictureUrl,
              driverName: driver.name,
              radius: 30,
              heroTag: 'driver-avatar-${driver.id}',
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          driver.name,
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: isDarkMode ? AppColors.darkText : AppColors.lightText,
                          ),
                        ),
                      ),
                      if (driver.isVerified)
                        const Icon(
                          Icons.verified,
                          color: AppColors.success,
                          size: 18,
                        ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${driver.city} • ${driver.experience} years exp.',
                    style: TextStyle(
                      color: isDarkMode ? AppColors.darkTextSecondary : AppColors.lightTextSecondary,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    driver.education,
                    style: TextStyle(
                      color: isDarkMode ? AppColors.darkTextSecondary : AppColors.lightTextSecondary,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => DriverDetailsScreen(driver: driver),
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primaryYellow,
                foregroundColor: Colors.black,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              ),
              child: const Text('Details'),
            ),
          ],
        ),
      ),
    );
  }
}