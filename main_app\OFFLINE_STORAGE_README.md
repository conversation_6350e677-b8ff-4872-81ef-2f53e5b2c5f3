# Offline Storage Implementation

This document describes the comprehensive offline storage system implemented for the Drive-On app.

## Overview

The offline storage system provides a robust, multi-layered caching and data persistence solution that ensures the app works seamlessly even when the device is offline. The system automatically syncs data when connectivity is restored and provides intelligent cache management.

## Architecture

### Core Components

1. **OfflineStorageService** - Main service combining Hive and SQLite
2. **EnhancedCacheService** - Multi-layer caching (Memory → Hive → SQLite → Network)
3. **NetworkStateManager** - Connectivity monitoring and sync management
4. **OfflineInitializationService** - Centralized initialization and management

### Data Flow

```
Network Request → Memory Cache → Hive Cache → SQLite Cache → Firebase/API
                     ↓              ↓           ↓
                 Fast Access    Medium Speed   Persistent Storage
```

## Features

### Multi-Layer Caching
- **Memory Cache**: Ultra-fast access for frequently used data (15-30 minutes TTL)
- **Hive Cache**: Fast key-value storage for medium-term caching (2-6 hours TTL)
- **SQLite Cache**: Structured persistent storage for long-term offline access (1-7 days TTL)

### Offline-First Approach
- Data is served from cache first, then updated from network
- Automatic fallback to cached data when offline
- Intelligent cache invalidation and refresh strategies

### Network State Management
- Real-time connectivity monitoring
- Automatic sync when connection is restored
- Queue for offline operations with retry logic

### Background Synchronization
- Periodic sync of all data types
- Conflict resolution for data updates
- Efficient batch operations

## Implementation Details

### Database Schema

#### News Articles Table
```sql
CREATE TABLE news_articles (
  id TEXT PRIMARY KEY,
  title TEXT NOT NULL,
  description TEXT,
  url TEXT,
  source TEXT,
  published_at INTEGER,
  image_url TEXT,
  views INTEGER DEFAULT 0,
  pinned INTEGER DEFAULT 0,
  category TEXT,
  content TEXT,
  created_at INTEGER,
  updated_at INTEGER,
  sync_status TEXT DEFAULT 'synced'
);
```

#### Jobs Table
```sql
CREATE TABLE jobs (
  id TEXT PRIMARY KEY,
  title TEXT NOT NULL,
  description TEXT,
  company TEXT,
  location TEXT,
  salary_range TEXT,
  employment_type TEXT,
  category TEXT,
  poster_email TEXT,
  status TEXT DEFAULT 'active',
  created_at INTEGER,
  updated_at INTEGER,
  sync_status TEXT DEFAULT 'synced'
);
```

#### Drivers Table
```sql
CREATE TABLE drivers (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  mobile TEXT,
  city TEXT,
  marital_status TEXT,
  education TEXT,
  experience INTEGER,
  is_verified INTEGER DEFAULT 0,
  status TEXT DEFAULT 'pending',
  user_email TEXT,
  created_at INTEGER,
  updated_at INTEGER,
  sync_status TEXT DEFAULT 'synced'
);
```

### Cache Configuration

```dart
// Memory cache limits
static const int _maxMemoryCacheSize = 200; // items
static const int _maxMemoryCacheBytes = 20 * 1024 * 1024; // 20MB

// TTL settings
static const Duration _defaultMemoryTtl = Duration(minutes: 15);
static const Duration _defaultHiveTtl = Duration(hours: 6);
static const Duration _defaultSqliteTtl = Duration(days: 7);
```

## Usage Examples

### Basic Data Fetching with Offline Support

```dart
// Get news articles with offline-first approach
final newsRepo = NewsRepository();
final articles = await newsRepo.getNewsFeedOfflineFirst(
  limit: 20,
  category: 'automotive',
);
```

### Manual Cache Management

```dart
// Force refresh from network
final freshArticles = await newsRepo.getNewsFeed(
  limit: 20,
  forceRefresh: true,
);

// Clear offline data
await newsRepo.clearOfflineData();
```

### Network State Monitoring

```dart
final networkManager = GetIt.instance<NetworkStateManager>();

// Listen to connectivity changes
networkManager.connectivityStream.listen((isOnline) {
  if (isOnline) {
    print('Device is online');
  } else {
    print('Device is offline');
  }
});

// Listen to sync events
networkManager.networkEventsStream.listen((event) {
  switch (event) {
    case NetworkEvent.syncCompleted:
      print('Data sync completed');
      break;
    case NetworkEvent.syncFailed:
      print('Data sync failed');
      break;
  }
});
```

## UI Components

### Offline Status Widget

```dart
// Compact status indicator
OfflineStatusWidget(compact: true)

// Full status with sync controls
OfflineStatusWidget(showSyncButton: true)
```

### Offline Settings Screen

Access comprehensive offline storage management:
- View storage statistics
- Manual sync controls
- Clear offline data
- Network status monitoring

## Performance Optimizations

### Memory Management
- Automatic cache eviction based on size and age
- LRU (Least Recently Used) eviction strategy
- Configurable memory limits

### Database Optimizations
- Indexed queries for fast lookups
- Batch operations for bulk data
- Connection pooling and reuse

### Network Efficiency
- Intelligent sync scheduling
- Differential updates where possible
- Compression for large data transfers

## Monitoring and Debugging

### Statistics and Metrics

```dart
// Get comprehensive offline statistics
final stats = await OfflineInitializationService().getOfflineStats();
print('News articles cached: ${stats['news']['articles']}');
print('Jobs cached: ${stats['jobs']['jobs']}');
print('Network status: ${stats['network']['isOnline']}');
```

### Logging

The system provides detailed logging for:
- Cache hits and misses
- Sync operations
- Network state changes
- Error conditions

## Error Handling

### Graceful Degradation
- App continues to work with cached data when offline
- Automatic retry mechanisms for failed operations
- User-friendly error messages

### Conflict Resolution
- Last-write-wins strategy for simple conflicts
- Custom resolution logic for complex scenarios
- Backup and restore capabilities

## Configuration

### Environment-Specific Settings

```dart
// Development
static const Duration _syncInterval = Duration(minutes: 1);

// Production
static const Duration _syncInterval = Duration(minutes: 5);
```

### Cache Size Limits

Adjust based on device capabilities:
- Low-end devices: Smaller cache sizes
- High-end devices: Larger cache sizes
- Automatic detection and adjustment

## Best Practices

1. **Always use offline-first methods** for data fetching
2. **Monitor cache sizes** to prevent excessive storage usage
3. **Handle network state changes** gracefully in UI
4. **Provide user feedback** for sync operations
5. **Test offline scenarios** thoroughly

## Troubleshooting

### Common Issues

1. **High memory usage**: Reduce cache sizes or TTL values
2. **Slow sync**: Check network conditions and batch sizes
3. **Data inconsistency**: Clear cache and force full sync
4. **Storage errors**: Check device storage space

### Debug Commands

```dart
// Clear all offline data
await OfflineInitializationService().clearAllOfflineData();

// Force sync all data
await OfflineInitializationService().forceSyncAll();

// Get detailed statistics
final stats = await OfflineInitializationService().getOfflineStats();
```

## Future Enhancements

1. **Intelligent prefetching** based on user behavior
2. **Compression** for cached data
3. **Encryption** for sensitive cached data
4. **Cross-device sync** capabilities
5. **Advanced conflict resolution** strategies

## Dependencies

- `sqflite`: SQLite database
- `hive`: Fast key-value storage
- `connectivity_plus`: Network state monitoring
- `shared_preferences`: Simple key-value storage

## Migration Guide

When updating the offline storage system:

1. **Backup existing data** before migration
2. **Update database schema** incrementally
3. **Test migration** on development devices first
4. **Provide fallback** mechanisms for failed migrations
