'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { motion } from 'framer-motion'
import {
  HelpCircle,
  Search,
  Filter,
  Download,
  Plus,
  Eye,
  MessageSquare,
  MessageCircle,
  CheckCircle,
  XCircle,
  Clock,
  User,
  Mail,
  Phone,
  Calendar,
  AlertTriangle,
} from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { DataTable } from '@/components/ui/data-table'
import { QueryModal, ModalQuery } from '@/components/queries/query-modal'
import { QueryFilters } from '@/components/queries/query-filters'
import { LoadingSpinner } from '@/components/ui/loading-spinner'

import { useQueries } from '@/lib/hooks/use-queries'
import { formatDate, formatRelativeTime } from '@/lib/utils'

interface Query {
  id: string
  subject: string
  description: string
  category: 'technical' | 'billing' | 'account' | 'general' | 'complaint' | 'feature-request'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  status: 'open' | 'in-progress' | 'resolved' | 'closed'
  userId: string
  userName: string
  userEmail: string
  userPhone?: string
  userAvatar?: string
  assignedTo?: {
    id: string
    name: string
  }
  lastMessage: string
  lastMessageTime: Date
  lastSenderId: string
  messageCount: number
  createdAt: Date
  updatedAt: Date
  resolvedAt?: Date
  resolvedBy?: string
  resolutionNote?: string
  satisfactionRating?: number
  tags: string[]
}

export default function QueriesPage() {
  const router = useRouter()
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedQuery, setSelectedQuery] = useState<ModalQuery | null>(null)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [modalMode, setModalMode] = useState<'view' | 'respond'>('view')

  const [filters, setFilters] = useState({
    category: 'all',
    status: 'all',
    priority: 'all',
    assignedTo: 'all'
  })

  const {
    queries,
    queryMessages,
    isLoading,
    error,
    updateQuery,
    respondToQuery,
    sendMessage,
    resolveQuery,
    assignQuery,
    fetchQueryMessages
  } = useQueries()

  const filteredQueries = queries?.filter(query => {
    const matchesSearch = query.subject.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         query.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         query.userName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         query.userEmail.toLowerCase().includes(searchQuery.toLowerCase())

    const matchesCategory = filters.category === 'all' || query.category === filters.category
    const matchesStatus = filters.status === 'all' || query.status === filters.status
    const matchesPriority = filters.priority === 'all' || query.priority === filters.priority
    const matchesAssignedTo = filters.assignedTo === 'all' ||
                             (filters.assignedTo === 'unassigned' && !query.assignedTo) ||
                             (query.assignedTo?.name === filters.assignedTo)

    return matchesSearch && matchesCategory && matchesStatus && matchesPriority && matchesAssignedTo
  }) || []

  // Transform query data to match modal interface
  const transformQueryForModal = (query: Query): ModalQuery => {
    return {
      ...query,
      user: {
        id: query.userId,
        name: query.userName,
        email: query.userEmail,
        phone: query.userPhone,
        avatar: query.userAvatar
      },
      responses: [], // Will be populated when modal loads
      attachments: [] // Will be populated when modal loads
    }
  }

  const handleViewQuery = (query: Query) => {
    setSelectedQuery(transformQueryForModal(query))
    setModalMode('view')
    setIsModalOpen(true)
  }

  const handleRespondToQuery = (query: Query) => {
    setSelectedQuery(transformQueryForModal(query))
    setModalMode('respond')
    setIsModalOpen(true)
  }

  const handleOpenChat = (query: Query) => {
    router.push(`/queries/${query.id}/chat`)
  }

  const handleResolveQuery = async (query: Query, resolutionNote: string) => {
    try {
      await resolveQuery(query.id, resolutionNote)
    } catch (error) {
      console.error('Error resolving query:', error)
    }
  }





  const handleAssignQuery = async (query: Query, assigneeId: string) => {
    try {
      await assignQuery(query.id, assigneeId)
    } catch (error) {
      console.error('Error assigning query:', error)
    }
  }

  const handleUpdateStatus = async (query: Query, newStatus: Query['status']) => {
    try {
      await updateQuery(query.id, { status: newStatus })
    } catch (error) {
      console.error('Error updating query status:', error)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open':
        return 'error'
      case 'in-progress':
        return 'warning'
      case 'resolved':
        return 'success'
      case 'closed':
        return 'secondary'
      default:
        return 'secondary'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'low':
        return 'bg-green-100 text-green-800'
      case 'medium':
        return 'bg-yellow-100 text-yellow-800'
      case 'high':
        return 'bg-orange-100 text-orange-800'
      case 'urgent':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'technical':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
      case 'billing':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
      case 'account':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300'
      case 'general':
        return 'bg-muted text-muted-foreground'
      case 'complaint':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
      case 'feature-request':
        return 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-300'
      default:
        return 'bg-muted text-muted-foreground'
    }
  }

  const columns = [
    {
      key: 'query',
      label: 'Query',
      render: (query: Query) => (
        <div className="flex items-start space-x-3">
          <div className="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
            {query.userAvatar ? (
              <img src={query.userAvatar} alt={query.userName} className="w-10 h-10 rounded-full" />
            ) : (
              <User className="w-5 h-5 text-primary-600" />
            )}
          </div>
          <div className="flex-1 min-w-0">
            <p className="font-medium text-gray-900 truncate">{query.subject}</p>
            <p className="text-sm text-gray-500">by {query.userName}</p>
            <div className="flex items-center space-x-2 mt-1">
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${getCategoryColor(query.category)}`}>
                {query.category}
              </span>
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(query.priority)}`}>
                {query.priority}
              </span>
            </div>
          </div>
        </div>
      )
    },
    {
      key: 'status',
      label: 'Status',
      render: (query: Query) => (
        <Badge variant={getStatusColor(query.status) as any}>
          {query.status}
        </Badge>
      )
    },
    {
      key: 'assignedTo',
      label: 'Assigned To',
      render: (query: Query) => (
        <div className="text-sm">
          {query.assignedTo ? (
            <span className="text-gray-900">{query.assignedTo.name}</span>
          ) : (
            <span className="text-gray-500 italic">Unassigned</span>
          )}
        </div>
      )
    },
    {
      key: 'messages',
      label: 'Messages',
      render: (query: Query) => (
        <div className="text-sm">
          <div className="flex items-center space-x-1">
            <MessageSquare className="w-4 h-4 text-gray-400" />
            <span>{query.messageCount} messages</span>
          </div>
          <p className="text-gray-500 truncate">{query.lastMessage || 'No messages yet'}</p>
        </div>
      )
    },
    {
      key: 'created',
      label: 'Created',
      render: (query: Query) => (
        <div className="flex items-center space-x-1">
          <Calendar className="w-4 h-4 text-gray-400" />
          <span className="text-sm">{formatRelativeTime(query.createdAt)}</span>
        </div>
      )
    },
    {
      key: 'lastUpdate',
      label: 'Last Activity',
      render: (query: Query) => (
        <div className="text-sm">
          <div className="flex items-center space-x-1">
            <Calendar className="w-4 h-4 text-gray-400" />
            <span>{formatRelativeTime(query.lastMessageTime)}</span>
          </div>
        </div>
      )
    },
    {
      key: 'actions',
      label: 'Actions',
      render: (query: Query) => (
        <div className="flex items-center space-x-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleViewQuery(query)}
          >
            <Eye className="w-4 h-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleRespondToQuery(query)}
            className="text-blue-600 hover:text-blue-700"
          >
            <MessageSquare className="w-4 h-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleOpenChat(query)}
            className="text-green-600 hover:text-green-700"
            title="Open Chat"
          >
            <MessageCircle className="w-4 h-4" />
          </Button>
          {query.status === 'open' && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleUpdateStatus(query, 'in-progress')}
              className="text-yellow-600 hover:text-yellow-700"
            >
              <Clock className="w-4 h-4" />
            </Button>
          )}
          {(query.status === 'open' || query.status === 'in-progress') && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleResolveQuery(query, 'Query resolved by admin')}
              className="text-green-600 hover:text-green-700"
            >
              <CheckCircle className="w-4 h-4" />
            </Button>
          )}
        </div>
      )
    }
  ]

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Query Management</h1>
          <p className="text-gray-600">Manage customer support queries and tickets</p>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Queries</p>
                <p className="text-2xl font-bold text-gray-900">{queries?.length || 0}</p>
              </div>
              <HelpCircle className="w-8 h-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Open Queries</p>
                <p className="text-2xl font-bold text-gray-900">
                  {filteredQueries.filter(q => q.status === 'open').length}
                </p>
              </div>
              <AlertTriangle className="w-8 h-8 text-red-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">In Progress</p>
                <p className="text-2xl font-bold text-gray-900">
                  {filteredQueries.filter(q => q.status === 'in-progress').length}
                </p>
              </div>
              <Clock className="w-8 h-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Resolved</p>
                <p className="text-2xl font-bold text-gray-900">
                  {filteredQueries.filter(q => q.status === 'resolved').length}
                </p>
              </div>
              <CheckCircle className="w-8 h-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <CardTitle>Support Queries</CardTitle>
          <CardDescription>
            Manage customer support tickets and queries
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search queries by subject, description, or user..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <QueryFilters filters={filters} onFiltersChange={setFilters} />

            <Button variant="outline">
              <Download className="w-4 h-4 mr-2" />
              Export
            </Button>
          </div>

          <DataTable
            data={filteredQueries}
            columns={columns}
            searchQuery={searchQuery}
            isLoading={isLoading}
          />
        </CardContent>
      </Card>

      {/* Query Modal */}
      <QueryModal
        query={selectedQuery}
        mode={modalMode}
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onRespond={async (response) => {
          try {
            if (selectedQuery) {
              await respondToQuery(selectedQuery.id, response)
              setIsModalOpen(false)
            }
          } catch (error) {
            console.error('Error responding to query:', error)
          }
        }}
        onResolve={async (resolutionNote) => {
          try {
            if (selectedQuery) {
              await resolveQuery(selectedQuery.id, resolutionNote)
              setIsModalOpen(false)
            }
          } catch (error) {
            console.error('Error resolving query:', error)
          }
        }}
        onAssign={async (assigneeId) => {
          try {
            if (selectedQuery) {
              await assignQuery(selectedQuery.id, assigneeId)
            }
          } catch (error) {
            console.error('Error assigning query:', error)
          }
        }}
      />


    </motion.div>
  )
}
