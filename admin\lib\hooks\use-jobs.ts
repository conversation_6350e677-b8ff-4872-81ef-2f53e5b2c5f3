import { useState, useEffect } from 'react'
import { collection, query, orderBy, onSnapshot, doc, updateDoc, deleteDoc, addDoc, Timestamp } from 'firebase/firestore'
import { db } from '@/lib/firebase/config'

interface Job {
  id: string
  title: string
  company: string
  category: 'InDrive' | 'Household' | 'Company' | 'Part-time'
  description: string
  requirements: string[]
  salary: {
    min: number
    max: number
    type: 'hourly' | 'daily' | 'monthly'
  }
  location: {
    city: string
    area: string
    coordinates?: { lat: number; lng: number }
  }
  contactInfo: {
    phone: string
    email: string
    whatsapp?: string
  }
  status: 'active' | 'inactive' | 'expired' | 'filled'
  isApproved: boolean
  isFeatured: boolean
  applicationsCount: number
  viewsCount: number
  createdAt: Date
  expiresAt: Date
  approvedAt?: Date
  approvedBy?: string
  postedBy: string
}

export function useJobs() {
  const [jobs, setJobs] = useState<Job[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const jobsQuery = query(
      collection(db, 'jobs'),
      orderBy('createdAt', 'desc')
    )

    const unsubscribe = onSnapshot(
      jobsQuery,
      (snapshot) => {
        const jobsData = snapshot.docs.map(doc => {
          const data = doc.data()
          return {
            id: doc.id,
            title: data.title || '',
            company: data.company || '',
            category: data.category || 'InDrive',
            description: data.description || '',
            requirements: data.requirements || [],
            salary: (() => {
              // Handle both string and object salary formats
              if (typeof data.salary === 'string') {
                // Parse string salary like "25,000" or "20,000-30,000"
                const salaryStr = data.salary.replace(/[^\d-]/g, '') // Remove non-digits except dash
                if (salaryStr.includes('-')) {
                  const [min, max] = salaryStr.split('-').map(s => parseInt(s) || 0)
                  return { min, max, type: 'monthly' }
                } else {
                  const amount = parseInt(salaryStr) || 0
                  return { min: amount, max: amount, type: 'monthly' }
                }
              } else if (data.salary && typeof data.salary === 'object') {
                // Handle object format
                return {
                  min: data.salary.min || 0,
                  max: data.salary.max || 0,
                  type: data.salary.type || 'monthly'
                }
              } else {
                return { min: 0, max: 0, type: 'monthly' }
              }
            })(),
            location: data.location || { city: '', area: '' },
            contactInfo: data.contactInfo || { phone: '', email: '' },
            status: data.status || 'active',
            isApproved: data.isApproved || false,
            isFeatured: data.isFeatured || false,
            applicationsCount: data.applicationsCount || 0,
            viewsCount: data.viewsCount || 0,
            createdAt: data.createdAt?.toDate() || new Date(),
            expiresAt: data.expiresAt?.toDate() || new Date(),
            approvedAt: data.approvedAt?.toDate(),
            approvedBy: data.approvedBy,
            postedBy: data.postedBy || 'Admin',
          } as Job
        })

        setJobs(jobsData)
        setIsLoading(false)
        setError(null)
      },
      (error) => {
        console.error('Error fetching jobs:', error)
        setError('Failed to fetch jobs')
        setIsLoading(false)
      }
    )

    return () => unsubscribe()
  }, [])

  const updateJob = async (jobId: string, updates: Partial<Job>) => {
    try {
      const jobRef = doc(db, 'jobs', jobId)
      const updateData: any = { ...updates }

      // Remove undefined values to prevent Firestore errors
      Object.keys(updateData).forEach(key => {
        if (updateData[key] === undefined) {
          delete updateData[key]
        }
      })

      // Convert dates to Firestore timestamps
      if (updateData.createdAt instanceof Date) {
        updateData.createdAt = Timestamp.fromDate(updateData.createdAt)
      }
      if (updateData.expiresAt instanceof Date) {
        updateData.expiresAt = Timestamp.fromDate(updateData.expiresAt)
      }
      if (updateData.approvedAt instanceof Date) {
        updateData.approvedAt = Timestamp.fromDate(updateData.approvedAt)
      }

      updateData.updatedAt = Timestamp.now()

      await updateDoc(jobRef, updateData)
    } catch (error) {
      console.error('Error updating job:', error)
      throw error
    }
  }

  const deleteJob = async (jobId: string) => {
    try {
      const jobRef = doc(db, 'jobs', jobId)
      await deleteDoc(jobRef)
    } catch (error) {
      console.error('Error deleting job:', error)
      throw error
    }
  }

  const createJob = async (jobData: Partial<Job>) => {
    try {
      const newJobData = {
        ...jobData,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now(),
        isApproved: false,
        applicationsCount: 0,
        viewsCount: 0,
        postedBy: 'Admin', // TODO: Get actual admin name
        expiresAt: jobData.expiresAt ? Timestamp.fromDate(jobData.expiresAt as Date) : Timestamp.fromDate(new Date(Date.now() + 30 * 24 * 60 * 60 * 1000))
      }

      await addDoc(collection(db, 'jobs'), newJobData)
    } catch (error) {
      console.error('Error creating job:', error)
      throw error
    }
  }

  const approveJob = async (jobId: string) => {
    try {
      const jobRef = doc(db, 'jobs', jobId)
      await updateDoc(jobRef, {
        isApproved: true,
        approvedAt: Timestamp.now(),
        approvedBy: 'Admin', // TODO: Get actual admin name
        updatedAt: Timestamp.now()
      })
    } catch (error) {
      console.error('Error approving job:', error)
      throw error
    }
  }

  return {
    jobs,
    isLoading,
    error,
    updateJob,
    deleteJob,
    createJob,
    approveJob,
  }
}
