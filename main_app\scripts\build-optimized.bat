@echo off
REM Enhanced optimized build script for Drive-On main app
REM This script creates optimized builds for both Android and Web platforms
REM with advanced optimization features and environment support

setlocal enabledelayedexpansion

REM Default values
set ENVIRONMENT=production
set PLATFORM=all
set SKIP_TESTS=false
set ENABLE_OBFUSCATION=true
set ENABLE_TREE_SHAKING=true

REM Parse command line arguments
:parse_args
if "%~1"=="" goto :args_done
if "%~1"=="--env" (
    set ENVIRONMENT=%~2
    shift
    shift
    goto :parse_args
)
if "%~1"=="--platform" (
    set PLATFORM=%~2
    shift
    shift
    goto :parse_args
)
if "%~1"=="--skip-tests" (
    set SKIP_TESTS=true
    shift
    goto :parse_args
)
if "%~1"=="--no-obfuscation" (
    set ENABLE_OBFUSCATION=false
    shift
    goto :parse_args
)
if "%~1"=="--help" (
    echo Usage: build-optimized.bat [options]
    echo Options:
    echo   --env ^<environment^>      Environment: development, staging, production ^(default: production^)
    echo   --platform ^<platform^>    Platform: android, web, all ^(default: all^)
    echo   --skip-tests             Skip running tests before build
    echo   --no-obfuscation         Disable code obfuscation
    echo   --help                   Show this help message
    exit /b 0
)
shift
goto :parse_args
:args_done

echo 🚀 Starting optimized build process...
echo 📋 Configuration:
echo    Environment: %ENVIRONMENT%
echo    Platform: %PLATFORM%
echo    Skip Tests: %SKIP_TESTS%
echo    Obfuscation: %ENABLE_OBFUSCATION%
echo.

REM Check Flutter installation
flutter --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Flutter is not installed. Please install Flutter first.
    exit /b 1
)

REM Check if environment file exists
if not exist ".env.%ENVIRONMENT%" (
    echo ❌ Environment file .env.%ENVIRONMENT% not found
    echo Please create the environment file or use --env with a valid environment
    exit /b 1
)

REM Clean previous builds
echo 🧹 Cleaning previous builds...
flutter clean

REM Get dependencies
echo 📦 Getting dependencies...
flutter pub get
if %errorlevel% neq 0 (
    echo ❌ Failed to get dependencies
    exit /b 1
)

REM Check for build_runner
findstr /C:"build_runner" pubspec.yaml >nul 2>&1
if %errorlevel% equ 0 (
    echo 🏗️ Running code generation...
    flutter packages pub run build_runner build --delete-conflicting-outputs
)

REM Analyze code
echo 🔍 Analyzing code...
flutter analyze
if %errorlevel% neq 0 (
    echo ⚠️ Code analysis found issues, continuing...
)

REM Run tests (unless skipped)
if "%SKIP_TESTS%"=="false" (
    echo 🧪 Running tests...
    flutter test
    if %errorlevel% neq 0 (
        echo ❌ Tests failed. Build aborted.
        echo Use --skip-tests to bypass test failures
        exit /b 1
    )
    echo ✅ All tests passed!
)

REM Generate build number
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YY=%dt:~2,2%" & set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
set "HH=%dt:~8,2%" & set "Min=%dt:~10,2%" & set "Sec=%dt:~12,2%"
set "BUILD_NUMBER=%YYYY%%MM%%DD%%HH%%Min%"

echo 🔢 Build number: %BUILD_NUMBER%

REM Build Android (if requested)
if "%PLATFORM%"=="android" goto :build_android
if "%PLATFORM%"=="all" goto :build_android
goto :skip_android

:build_android
echo 📱 Building optimized Android APK...

REM Prepare build command
set "ANDROID_BUILD_CMD=flutter build apk --release"
set "ANDROID_BUILD_CMD=!ANDROID_BUILD_CMD! --build-number=%BUILD_NUMBER%"
set "ANDROID_BUILD_CMD=!ANDROID_BUILD_CMD! --dart-define=ENVIRONMENT=%ENVIRONMENT%"
set "ANDROID_BUILD_CMD=!ANDROID_BUILD_CMD! --shrink"

if "%ENABLE_OBFUSCATION%"=="true" (
    set "ANDROID_BUILD_CMD=!ANDROID_BUILD_CMD! --obfuscate"
    set "ANDROID_BUILD_CMD=!ANDROID_BUILD_CMD! --split-debug-info=build/debug-info"
)

echo Executing: !ANDROID_BUILD_CMD!
!ANDROID_BUILD_CMD!

if %errorlevel% neq 0 (
    echo ❌ Android APK build failed
    exit /b 1
)

REM Build App Bundle
echo 📦 Building Android App Bundle...
set "BUNDLE_BUILD_CMD=flutter build appbundle --release"
set "BUNDLE_BUILD_CMD=!BUNDLE_BUILD_CMD! --build-number=%BUILD_NUMBER%"
set "BUNDLE_BUILD_CMD=!BUNDLE_BUILD_CMD! --dart-define=ENVIRONMENT=%ENVIRONMENT%"

if "%ENABLE_OBFUSCATION%"=="true" (
    set "BUNDLE_BUILD_CMD=!BUNDLE_BUILD_CMD! --obfuscate"
    set "BUNDLE_BUILD_CMD=!BUNDLE_BUILD_CMD! --split-debug-info=build/debug-info"
)

!BUNDLE_BUILD_CMD!

if %errorlevel% neq 0 (
    echo ❌ Android App Bundle build failed
    exit /b 1
)

echo ✅ Android builds completed successfully!

:skip_android

REM Build Web (if requested)
if "%PLATFORM%"=="web" goto :build_web
if "%PLATFORM%"=="all" goto :build_web
goto :skip_web

:build_web
echo 🌐 Building optimized Web app...

REM Prepare web build command
set "WEB_BUILD_CMD=flutter build web --release"
set "WEB_BUILD_CMD=!WEB_BUILD_CMD! --web-renderer html"
set "WEB_BUILD_CMD=!WEB_BUILD_CMD! --dart-define=ENVIRONMENT=%ENVIRONMENT%"
set "WEB_BUILD_CMD=!WEB_BUILD_CMD! --source-maps"
set "WEB_BUILD_CMD=!WEB_BUILD_CMD! --pwa-strategy=offline-first"

if "%ENABLE_TREE_SHAKING%"=="true" (
    set "WEB_BUILD_CMD=!WEB_BUILD_CMD! --tree-shake-icons"
)

echo Executing: !WEB_BUILD_CMD!
!WEB_BUILD_CMD!

if %errorlevel% neq 0 (
    echo ❌ Web build failed
    exit /b 1
)

REM Copy service worker if exists
if exist "web\sw.js" (
    echo ⚙️ Copying service worker...
    copy "web\sw.js" "build\web\sw.js"
)

echo ✅ Web build completed successfully!

:skip_web

REM Display build summary
echo.
echo 🎉 Optimized builds completed successfully!
echo.
echo 📊 Build Summary:
echo    Environment: %ENVIRONMENT%
echo    Build Number: %BUILD_NUMBER%
echo    Obfuscation: %ENABLE_OBFUSCATION%
echo.

if "%PLATFORM%"=="android" goto :android_summary
if "%PLATFORM%"=="all" goto :android_summary
goto :web_summary

:android_summary
echo 📱 Android Artifacts:
echo    APK: build\app\outputs\flutter-apk\app-release.apk
echo    Bundle: build\app\outputs\bundle\release\app-release.aab
if "%ENABLE_OBFUSCATION%"=="true" (
    echo    Debug Info: build\debug-info\
)

:web_summary
if "%PLATFORM%"=="web" goto :web_only
if "%PLATFORM%"=="all" goto :web_only
goto :end

:web_only
echo 🌐 Web Artifacts:
echo    Web App: build\web\
echo    Size:
for /f %%i in ('powershell -command "(Get-ChildItem -Recurse build\web | Measure-Object -Property Length -Sum).Sum / 1MB"') do echo    %%i MB

:end
echo.
pause
