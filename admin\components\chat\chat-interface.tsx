'use client'

import { useEffect, useState } from 'react'
import { motion } from 'framer-motion'
import {
  ArrowLeft,
  Users,
  Phone,
  Video,
  MoreVertical,
  Info,
  Search,
  Pin
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { LoadingSpinner } from '@/components/ui/loading-spinner'
import { MessageBubble } from './message-bubble'
import { MessageInput } from './message-input'
import { useChat } from '@/lib/hooks/use-chat'
import { formatRelativeTime } from '@/lib/utils'

interface ChatInterfaceProps {
  roomId: string
  roomType: 'query' | 'forum'
  currentUserId: string
  currentUserName: string
  currentUserAvatar?: string
  onBack?: () => void
  showHeader?: boolean
}

export function ChatInterface({
  roomId,
  roomType,
  currentUserId,
  currentUserName,
  currentUserAvatar,
  onBack,
  showHeader = true
}: ChatInterfaceProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [showInfo, setShowInfo] = useState(false)

  const {
    messages,
    roomInfo,
    isLoading,
    isSending,
    error,
    replyTo,
    messagesEndRef,
    sendMessage,
    sendVoiceMessage,
    sendImageMessage,
    setReplyMessage,
    clearError,
    scrollToBottom
  } = useChat({
    roomId,
    roomType,
    currentUserId,
    currentUserName,
    currentUserAvatar
  })

  // Auto-scroll to bottom when messages change
  useEffect(() => {
    scrollToBottom()
  }, [messages, scrollToBottom])

  // Filter messages based on search
  const filteredMessages = messages.filter(message => {
    if (!searchQuery) return true
    return message.text.toLowerCase().includes(searchQuery.toLowerCase()) ||
           message.senderName.toLowerCase().includes(searchQuery.toLowerCase())
  })

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-96">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-96 space-y-4">
        <div className="text-red-500 text-center">
          <p className="font-medium">Error loading chat</p>
          <p className="text-sm">{error}</p>
        </div>
        <Button onClick={clearError} variant="outline">
          Try Again
        </Button>
      </div>
    )
  }

  return (
    <div className="flex flex-col h-full bg-white">
      {/* Header */}
      {showHeader && roomInfo && (
        <div className="border-b bg-white px-4 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              {onBack && (
                <Button
                  onClick={onBack}
                  size="sm"
                  variant="ghost"
                >
                  <ArrowLeft className="w-4 h-4" />
                </Button>
              )}

              <div className="flex-1">
                <h2 className="font-semibold text-gray-900 truncate">
                  {roomInfo.title}
                </h2>
                <div className="flex items-center space-x-2 text-sm text-gray-500">
                  <span>
                    {roomInfo.participants.length} participant{roomInfo.participants.length !== 1 ? 's' : ''}
                  </span>
                  {roomInfo.lastMessageTime && (
                    <>
                      <span>•</span>
                      <span>Last active {formatRelativeTime(roomInfo.lastMessageTime)}</span>
                    </>
                  )}
                </div>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Badge variant={roomInfo.isActive ? 'success' : 'secondary'}>
                {roomInfo.isActive ? 'Active' : 'Closed'}
              </Badge>

              <Button
                onClick={() => setShowInfo(!showInfo)}
                size="sm"
                variant="ghost"
              >
                <Info className="w-4 h-4" />
              </Button>
            </div>
          </div>

          {/* Search bar */}
          {showInfo && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="mt-3 pt-3 border-t"
            >
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="Search messages..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500"
                />
              </div>
            </motion.div>
          )}
        </div>
      )}

      {/* Messages area */}
      <div className="flex-1 overflow-y-auto p-4 space-y-1">
        {filteredMessages.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full text-gray-500">
            <div className="text-center">
              <h3 className="text-lg font-medium mb-2">
                {searchQuery ? 'No messages found' : 'No messages yet'}
              </h3>
              <p className="text-sm">
                {searchQuery
                  ? 'Try adjusting your search terms'
                  : 'Start the conversation by sending a message'
                }
              </p>
            </div>
          </div>
        ) : (
          <>
            {filteredMessages.map((message, index) => {
              const isOwn = message.senderId === currentUserId
              // Show avatar for consecutive messages from different senders
              const showAvatar = (
                index === 0 ||
                filteredMessages[index - 1].senderId !== message.senderId
              )

              return (
                <MessageBubble
                  key={message.id}
                  message={message}
                  isOwn={isOwn}
                  showAvatar={showAvatar}
                  onReply={setReplyMessage}
                />
              )
            })}
            <div ref={messagesEndRef} />
          </>
        )}
      </div>

      {/* Message input */}
      {roomInfo?.isActive && (
        <MessageInput
          onSendMessage={sendMessage}
          onSendVoiceMessage={sendVoiceMessage}
          onSendImageMessage={sendImageMessage}
          disabled={isSending}
          placeholder={`Message ${roomType === 'query' ? 'user' : 'forum'}...`}
          replyTo={replyTo}
          onCancelReply={() => setReplyMessage(null)}
          roomType={roomType}
        />
      )}

      {/* Closed room indicator */}
      {!roomInfo?.isActive && (
        <div className="border-t bg-gray-50 p-4 text-center text-gray-500">
          <p className="text-sm">
            This {roomType} has been closed. No new messages can be sent.
          </p>
        </div>
      )}
    </div>
  )
}
