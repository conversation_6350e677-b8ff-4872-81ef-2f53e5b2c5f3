# 🚀 Google Play Store Setup Guide for Drive-On

## Overview
This guide will walk you through setting up your Drive-On app for Google Play Store deployment.

## 📋 Prerequisites Checklist

### 1. Google Play Console Account
- [ ] Google Play Console developer account created
- [ ] $25 registration fee paid
- [ ] Developer account verified

### 2. Production Keystore
- [ ] Production keystore generated (`scripts/generate-keystore.bat`)
- [ ] `key.properties` file created
- [ ] Keystore passwords stored securely

### 3. Store Assets
- [ ] App icon (512x512 PNG)
- [ ] Feature graphic (1024x500 PNG)
- [ ] Phone screenshots (minimum 2)
- [ ] Store descriptions completed

## 🔧 Step-by-Step Setup

### Step 1: Create Google Play Console Account
1. Go to [Google Play Console](https://play.google.com/console)
2. Sign in with your Google account
3. Pay the $25 registration fee
4. Complete developer profile

### Step 2: Generate Production Keystore
```bash
cd main_app
scripts\generate-keystore.bat
```
**Important:** Store the keystore file and passwords securely!

### Step 3: Prepare Store Assets
```bash
scripts\prepare-play-store-assets.bat
```

Required assets:
- **App Icon**: 512x512 PNG → `fastlane/metadata/android/en-US/images/icon/icon.png`
- **Feature Graphic**: 1024x500 PNG → `fastlane/metadata/android/en-US/images/featureGraphic/featureGraphic.png`
- **Screenshots**: → `fastlane/metadata/android/en-US/images/phoneScreenshots/`

### Step 4: Build Production Release
```bash
scripts\build-production.bat
```

This creates:
- `app-release.aab` (for Play Store)
- `app-release.apk` (for testing)
- `mapping.txt` (for crash reporting)

### Step 5: Create App in Play Console
1. Go to Google Play Console
2. Click "Create app"
3. Fill in app details:
   - **App name**: Drive On
   - **Default language**: English (United States)
   - **App or game**: App
   - **Free or paid**: Free
4. Complete declarations and click "Create app"

### Step 6: Upload App Bundle
1. Go to "Release" → "Testing" → "Internal testing"
2. Click "Create new release"
3. Upload the `.aab` file from `releases/` folder
4. Upload the `mapping.txt` file
5. Add release notes from `distribution/whatsnew/en-US/default.txt`

### Step 7: Complete Store Listing
1. Go to "Store presence" → "Main store listing"
2. Fill in all required fields:
   - App name: Drive On
   - Short description: (from `fastlane/metadata/android/en-US/short_description.txt`)
   - Full description: (from `fastlane/metadata/android/en-US/full_description.txt`)
   - App icon: Upload 512x512 PNG
   - Feature graphic: Upload 1024x500 PNG
   - Screenshots: Upload phone screenshots

### Step 8: Content Rating
1. Go to "Policy" → "App content"
2. Complete content rating questionnaire
3. Based on your app's features, select appropriate ratings

### Step 9: Data Safety
1. Go to "Policy" → "Data safety"
2. Complete data safety form based on your app's data collection
3. For Drive-On, typically includes:
   - User account info (if using authentication)
   - App activity (usage analytics)
   - Device identifiers (for crash reporting)

### Step 10: Release to Internal Testing
1. Go back to "Internal testing"
2. Add test users (email addresses)
3. Click "Start rollout to Internal testing"
4. Test thoroughly before proceeding

## 🔐 Security Setup

### GitHub Secrets (for CI/CD)
Add these secrets to your GitHub repository:

1. **ANDROID_KEYSTORE_BASE64**
   ```bash
   # Convert keystore to base64
   certutil -encode android/app/drive-on-release-key.jks keystore.base64
   # Copy content (without headers) to GitHub secret
   ```

2. **ANDROID_KEY_PROPERTIES**
   ```
   storePassword=your_store_password
   keyPassword=your_key_password
   keyAlias=drive-on-key
   storeFile=drive-on-release-key.jks
   ```

3. **PLAY_STORE_SERVICE_ACCOUNT_JSON**
   - Create service account in Google Cloud Console
   - Download JSON key file
   - Copy entire JSON content to GitHub secret

## 🚀 Deployment Tracks

### Internal Testing
- For your team and close testers
- Up to 100 testers
- Instant updates

### Closed Testing (Alpha)
- For larger group of testers
- Up to 2,000 testers
- Requires opt-in

### Open Testing (Beta)
- Public beta testing
- Anyone can join
- Good for final testing

### Production
- Live on Google Play Store
- Available to all users
- Requires review process

## 📱 Testing Checklist

Before releasing to production:
- [ ] App installs and launches correctly
- [ ] All features work as expected
- [ ] No crashes or critical bugs
- [ ] Performance is acceptable
- [ ] UI looks good on different screen sizes
- [ ] Permissions work correctly
- [ ] Network connectivity handled properly
- [ ] Offline functionality works (if applicable)

## 🔄 Update Process

For future updates:
1. Update version in `pubspec.yaml`
2. Run `scripts/build-production.bat`
3. Upload new AAB to Play Console
4. Update release notes
5. Roll out to testing tracks first
6. Promote to production when ready

## 📞 Support Resources

- [Google Play Console Help](https://support.google.com/googleplay/android-developer)
- [Android App Bundle Guide](https://developer.android.com/guide/app-bundle)
- [Play Store Policies](https://play.google.com/about/developer-content-policy/)
- [Fastlane Documentation](https://docs.fastlane.tools/)

## 🎉 Congratulations!

Once you complete these steps, your Drive-On app will be ready for Google Play Store deployment!
