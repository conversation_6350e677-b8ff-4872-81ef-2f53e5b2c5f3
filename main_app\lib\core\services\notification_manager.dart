import 'dart:async';
import 'package:firebase_auth/firebase_auth.dart';
import '../firebase/notification_service.dart';
import 'message_notification_service.dart';
import 'verification_notification_service.dart';
import '../utils/app_logger.dart';
import '../utils/error_handler.dart';
import '../../features/notifications/services/notification_history_service.dart';
import '../../features/news/services/news_notification_service.dart';

/// Central notification manager that coordinates all notification services
class NotificationManager {
  static final NotificationManager _instance = NotificationManager._internal();
  static NotificationManager get instance => _instance;
  NotificationManager._internal();

  final AppLogger _logger = AppLogger('NotificationManager');
  final FirebaseAuth _auth = FirebaseAuth.instance;

  bool _isInitialized = false;
  StreamSubscription<User?>? _authSubscription;

  /// Initialize all notification services
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      _logger.info('Initializing notification manager...');

      // Initialize core notification service first
      await NotificationService.instance.initialize();

      // Setup auth state listener
      _authSubscription = _auth.authStateChanges().listen(_handleAuthStateChange);

      // Initialize other services if user is already authenticated
      if (_auth.currentUser != null) {
        _logger.info('User already authenticated, initializing user-specific services');
        // Add a delay to ensure all Firebase services are ready
        Future.delayed(const Duration(milliseconds: 1000), () {
          _initializeUserSpecificServices();
        });
      }

      _isInitialized = true;
      _logger.info('Notification manager initialized successfully');
    } catch (e, stackTrace) {
      _logger.error('Error initializing notification manager',
          error: e, stackTrace: stackTrace);
      ErrorHandler.reportHandledException(
        e,
        stackTrace,
        context: 'Initialize notification manager',
        severity: ErrorSeverity.high,
      );
      rethrow;
    }
  }

  /// Handle authentication state changes
  void _handleAuthStateChange(User? user) {
    if (user != null) {
      _logger.info('User authenticated, initializing user-specific notification services');
      // Add a small delay to ensure Firebase is fully ready
      Future.delayed(const Duration(milliseconds: 500), () {
        _initializeUserSpecificServices();
      });
    } else {
      _logger.info('User signed out, disposing user-specific notification services');
      _disposeUserSpecificServices();
    }
  }

  /// Initialize user-specific notification services
  Future<void> _initializeUserSpecificServices() async {
    try {
      // Initialize message notification service
      await MessageNotificationService.instance.initialize();

      // Initialize verification notification service
      await VerificationNotificationService.instance.initialize();

      // Initialize news notification service
      await NewsNotificationService.instance.initialize();

      _logger.info('User-specific notification services initialized');
    } catch (e, stackTrace) {
      _logger.error('Error initializing user-specific notification services',
          error: e, stackTrace: stackTrace);
      ErrorHandler.reportHandledException(
        e,
        stackTrace,
        context: 'Initialize user-specific notification services',
        severity: ErrorSeverity.medium,
      );
    }
  }

  /// Dispose user-specific notification services
  void _disposeUserSpecificServices() {
    try {
      MessageNotificationService.instance.dispose();
      VerificationNotificationService.instance.dispose();
      NewsNotificationService.instance.dispose();
      _logger.info('User-specific notification services disposed');
    } catch (e, stackTrace) {
      _logger.error('Error disposing user-specific notification services',
          error: e, stackTrace: stackTrace);
    }
  }

  /// Send a system notification
  Future<void> sendSystemNotification({
    required String title,
    required String body,
    String systemType = 'general',
    String? actionUrl,
    Map<String, dynamic>? additionalData,
  }) async {
    try {
      await NotificationService.instance.sendSystemNotification(
        title: title,
        body: body,
        systemType: systemType,
        actionUrl: actionUrl,
        additionalData: additionalData,
      );

      // Save to notification history
      await _saveToHistory(
        title: title,
        body: body,
        type: 'system',
        data: {
          'systemType': systemType,
          'actionUrl': actionUrl,
          ...?additionalData,
        },
      );
    } catch (e, stackTrace) {
      _logger.error('Error sending system notification',
          error: e, stackTrace: stackTrace);
    }
  }

  /// Send a message notification
  Future<void> sendMessageNotification({
    required String title,
    required String body,
    required String messageType,
    String? senderId,
    String? senderName,
    String? roomId,
    Map<String, dynamic>? additionalData,
  }) async {
    try {
      // Don't send notification to yourself
      final currentUserId = _auth.currentUser?.uid;
      if (senderId != null && senderId == currentUserId) {
        _logger.debug('Skipping notification to sender: $senderId');
        return;
      }

      await NotificationService.instance.sendMessageNotification(
        title: title,
        body: body,
        messageType: messageType,
        senderId: senderId,
        senderName: senderName,
        roomId: roomId,
        additionalData: additionalData,
      );

      // Save to notification history
      await _saveToHistory(
        title: title,
        body: body,
        type: 'message',
        senderId: senderId,
        senderName: senderName,
        roomId: roomId,
        data: {
          'messageType': messageType,
          ...?additionalData,
        },
      );
    } catch (e, stackTrace) {
      _logger.error('Error sending message notification',
          error: e, stackTrace: stackTrace);
    }
  }

  /// Send a verification notification
  Future<void> sendVerificationNotification({
    required String title,
    required String body,
    required String verificationType,
    required String status,
    String? driverId,
    Map<String, dynamic>? additionalData,
  }) async {
    try {
      await NotificationService.instance.sendVerificationNotification(
        title: title,
        body: body,
        verificationType: verificationType,
        status: status,
        driverId: driverId,
        additionalData: additionalData,
      );
    } catch (e, stackTrace) {
      _logger.error('Error sending verification notification',
          error: e, stackTrace: stackTrace);
    }
  }

  /// Send a test notification
  Future<void> sendTestNotification({
    String title = 'Test Notification',
    String body = 'This is a test notification from Drive-On',
    Map<String, dynamic>? payload,
  }) async {
    try {
      await NotificationService.instance.sendTestNotification(
        title: title,
        body: body,
        payload: payload,
      );
    } catch (e, stackTrace) {
      _logger.error('Error sending test notification',
          error: e, stackTrace: stackTrace);
    }
  }

  /// Check if notifications are enabled
  Future<bool> areNotificationsEnabled() async {
    try {
      return await NotificationService.instance.checkPermission();
    } catch (e, stackTrace) {
      _logger.error('Error checking notification permissions',
          error: e, stackTrace: stackTrace);
      return false;
    }
  }

  /// Request notification permissions
  Future<bool> requestNotificationPermissions() async {
    try {
      await NotificationService.instance.initialize();
      return await NotificationService.instance.checkPermission();
    } catch (e, stackTrace) {
      _logger.error('Error requesting notification permissions',
          error: e, stackTrace: stackTrace);
      return false;
    }
  }

  /// Get FCM token
  Future<String?> getFCMToken() async {
    try {
      return await NotificationService.instance.getToken();
    } catch (e, stackTrace) {
      _logger.error('Error getting FCM token',
          error: e, stackTrace: stackTrace);
      return null;
    }
  }

  /// Subscribe to a topic
  Future<void> subscribeToTopic(String topic) async {
    try {
      await NotificationService.instance.subscribeToTopic(topic);
    } catch (e, stackTrace) {
      _logger.error('Error subscribing to topic: $topic',
          error: e, stackTrace: stackTrace);
    }
  }

  /// Unsubscribe from a topic
  Future<void> unsubscribeFromTopic(String topic) async {
    try {
      await NotificationService.instance.unsubscribeFromTopic(topic);
    } catch (e, stackTrace) {
      _logger.error('Error unsubscribing from topic: $topic',
          error: e, stackTrace: stackTrace);
    }
  }

  /// Send welcome notification for new users
  Future<void> sendWelcomeNotification() async {
    await sendSystemNotification(
      title: '🎉 Welcome to Drive-On!',
      body: 'Thank you for joining Drive-On. Start your journey as a driver today!',
      systemType: 'welcome',
    );
  }

  /// Send app update notification
  Future<void> sendAppUpdateNotification({
    required String version,
    required String updateMessage,
    bool isRequired = false,
  }) async {
    await sendSystemNotification(
      title: isRequired ? '🔄 Required App Update' : '📱 App Update Available',
      body: 'Version $version is now available. $updateMessage',
      systemType: isRequired ? 'urgent' : 'update',
      additionalData: {
        'version': version,
        'isRequired': isRequired,
      },
    );
  }



  /// Send maintenance notification
  Future<void> sendMaintenanceNotification({
    required String message,
    DateTime? scheduledTime,
  }) async {
    await sendSystemNotification(
      title: '🔧 Scheduled Maintenance',
      body: message,
      systemType: 'maintenance',
      additionalData: {
        'scheduledTime': scheduledTime?.toIso8601String(),
      },
    );
  }

  /// Save notification to history
  Future<void> _saveToHistory({
    required String title,
    required String body,
    required String type,
    String? senderId,
    String? senderName,
    String? roomId,
    String? imageUrl,
    Map<String, dynamic>? data,
  }) async {
    try {
      await NotificationHistoryService.instance.createNotification(
        title: title,
        body: body,
        type: type,
        senderId: senderId,
        senderName: senderName,
        roomId: roomId,
        imageUrl: imageUrl,
        data: data,
      );
    } catch (e, stackTrace) {
      _logger.error('Error saving notification to history',
          error: e, stackTrace: stackTrace);
      // Don't rethrow - notification history is not critical
    }
  }

  /// Manually trigger initialization of user-specific services (for debugging)
  Future<void> forceInitializeUserServices() async {
    if (_auth.currentUser != null) {
      _logger.info('Manually triggering user-specific notification services initialization');
      await _initializeUserSpecificServices();
    } else {
      _logger.warning('Cannot initialize user services - no authenticated user');
    }
  }

  /// Dispose the notification manager
  void dispose() {
    try {
      _authSubscription?.cancel();
      _disposeUserSpecificServices();
      NotificationService.instance.dispose();
      _isInitialized = false;
      _logger.info('Notification manager disposed');
    } catch (e, stackTrace) {
      _logger.error('Error disposing notification manager',
          error: e, stackTrace: stackTrace);
    }
  }
}
