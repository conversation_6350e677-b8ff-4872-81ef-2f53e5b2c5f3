import 'dart:io';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';
import '../../../core/firebase/firebase_service.dart';
import '../../../core/utils/error_handler.dart';
import '../../../core/utils/app_logger.dart';
import '../../../core/utils/network_error_handler.dart';

import '../models/forum_model.dart';
import 'package:flutter/foundation.dart';
import 'package:path/path.dart' as p;

class ForumService {
  static final FirebaseFirestore _firestore = FirebaseService.firestore;
  static final FirebaseStorage _storage = FirebaseService.storage;
  static final FirebaseAuth _auth = FirebaseService.auth;
  static final AppLogger _logger = AppLogger('ForumService');
  static const String _collection = 'forums';

  // Collection references
  static final CollectionReference _forumsCollection = _firestore.collection('forums');

  // Default forum categories
  static const List<Map<String, String>> defaultForums = [
    {
      'title': 'General',
      'description': 'General discussions about various topics related to driving and transportation.'
    },
    {
      'title': 'Traffic Updates',
      'description': 'Share and discuss traffic conditions, road closures, and other real-time updates.'
    },
    {
      'title': 'Automobile Discussion',
      'description': 'Discuss cars, motorcycles, and other vehicles - models, features, performance, and more.'
    },
    {
      'title': 'Mechanical Problems & Expert Opinions',
      'description': 'Get help with mechanical issues, maintenance advice, and expert opinions on repairs.'
    },
  ];

  // Get current user
  static User? get currentUser => _auth.currentUser;

  // Get forums list stream
  static Stream<QuerySnapshot> getForumsStream() {
    return _forumsCollection
        .orderBy('lastMessageTime', descending: true)
        .snapshots();
  }

  // Get a specific forum stream
  static Stream<DocumentSnapshot> getForumStream(String forumId) {
    return _forumsCollection.doc(forumId).snapshots();
  }

  // Get forum messages stream
  static Stream<QuerySnapshot> getForumMessagesStream(String forumId) {
    return _forumsCollection
        .doc(forumId)
        .collection('messages')
        .orderBy('timestamp', descending: false)
        .snapshots();
  }

  // Setup default forums if they don't exist
  static Future<void> setupDefaultForums() async {
    try {
      // Check if forums collection is empty
      final snapshot = await _firestore.collection(_collection).limit(1).get();

      if (snapshot.docs.isEmpty) {
        ErrorHandler.addLog('Setting up default forums');

        // Create default forums
        final defaultForums = [
          Forum(
            id: 'temp-id-1',
            title: 'General Discussion',
            description: 'General discussion about driving and vehicles',
            createdAt: DateTime.now(),
          ),
          Forum(
            id: 'temp-id-2',
            title: 'Driving Tips',
            description: 'Share and learn driving tips from other drivers',
            createdAt: DateTime.now(),
          ),
          Forum(
            id: 'temp-id-3',
            title: 'Vehicle Maintenance',
            description: 'Discussions about vehicle maintenance and repair',
            createdAt: DateTime.now(),
          ),
        ];

        // Add default forums in batch
        final batch = _firestore.batch();
        for (final forum in defaultForums) {
          final docRef = _firestore.collection(_collection).doc();
          batch.set(docRef, forum.toMap());
        }

        await batch.commit();
        ErrorHandler.addLog('Default forums created successfully');
      }
    } catch (e, stackTrace) {
      // We don't want this to crash the app, just report it
      ErrorHandler.reportHandledException(
        e,
        stackTrace,
        context: 'Setting up default forums',
        severity: ErrorSeverity.low,
      );

      if (kDebugMode) {
        _logger.error('Error setting up default forums: $e');
      }
    }
  }



  // Send a message to a forum
  static Future<void> sendMessage({
    required String forumId,
    required String text,
    String? replyToId,
    String? replyToText,
    String? replyToSenderName,
    List<File>? attachments,
    File? voiceNote,
    int? voiceNoteDuration,
  }) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        throw Exception('User not logged in');
      }

      _logger.debug('Sending message to forum: $forumId');

      // Prepare data for the message
      final messageData = <String, dynamic>{
        'text': text,
        'senderId': currentUser.uid,
        'senderName': currentUser.displayName ?? 'Anonymous',
        'senderAvatar': currentUser.photoURL ?? '',
        'timestamp': FieldValue.serverTimestamp(),
        'reactions': {},
        'isEdited': false,
      };

      // Add reply info if replying to a message
      if (replyToId != null && replyToText != null) {
        messageData['replyToId'] = replyToId;
        messageData['replyToText'] = replyToText;
        messageData['replyToSenderName'] = replyToSenderName;
        _logger.debug('Adding reply information to message');
      }

      // Upload attachments if provided
      if (attachments != null && attachments.isNotEmpty) {
        _logger.debug('Uploading ${attachments.length} attachments');
        final attachmentUrls = <String>[];
        for (var i = 0; i < attachments.length; i++) {
          final file = attachments[i];

          // Enforce 10 MB max size per storage.rules isValidFile()
          const int maxAttachmentBytes = 10 * 1024 * 1024; // 10 MB
          final int fileSize = await file.length();
          if (fileSize > maxAttachmentBytes) {
            _logger.warning('Attachment exceeds 10 MB limit (size: $fileSize bytes)');
            throw Exception('Attachment must be under 10 MB.');
          }

          // Determine content type from extension
          final String ext = p.extension(file.path).toLowerCase();
          String? contentType;
          switch (ext) {
            case '.jpg':
            case '.jpeg':
              contentType = 'image/jpeg';
              break;
            case '.png':
              contentType = 'image/png';
              break;
            case '.webp':
              contentType = 'image/webp';
              break;
            case '.pdf':
              contentType = 'application/pdf';
              break;
            case '.mp3':
              contentType = 'audio/mpeg';
              break;
            case '.wav':
              contentType = 'audio/wav';
              break;
            case '.m4a':
              contentType = 'audio/mp4';
              break;
            default:
              // Unsupported type per current rules
              _logger.warning('Unsupported attachment type: $ext');
              throw Exception('Unsupported attachment type. Allowed: images (jpg, jpeg, png, webp), pdf, audio (mp3, wav, m4a).');
          }

          final objectPath = 'forums/$forumId/attachments/${DateTime.now().millisecondsSinceEpoch}_$i$ext';
          final metadata = SettableMetadata(contentType: contentType);
          final uploadTask = _storage.ref().child(objectPath).putFile(file, metadata);
          final snapshot = await uploadTask;
          final url = await snapshot.ref.getDownloadURL();
          attachmentUrls.add(url);
        }
        messageData['attachments'] = attachmentUrls;
        _logger.debug('Successfully uploaded ${attachmentUrls.length} attachments');
      }

      // Upload voice note if provided
      if (voiceNote != null) {
        _logger.debug('Uploading voice note');

        // Enforce 5 MB max size to align with storage rules
        const int maxVoiceBytes = 5 * 1024 * 1024; // 5 MB
        final int voiceSize = await voiceNote.length();
        if (voiceSize > maxVoiceBytes) {
          _logger.warning('Voice note exceeds 5 MB limit (size: $voiceSize bytes)');
          throw Exception('Voice note must be under 5 MB.');
        }

        final path = 'forums/$forumId/voice_notes/${DateTime.now().millisecondsSinceEpoch}.m4a';

        // Explicit content type to satisfy storage.rules (audio/*)
        final metadata = SettableMetadata(contentType: 'audio/mp4');
        final uploadTask = _storage.ref().child(path).putFile(voiceNote, metadata);
        final snapshot = await uploadTask;
        final url = await snapshot.ref.getDownloadURL();
        messageData['voiceNote'] = url;
        if (voiceNoteDuration != null) {
          messageData['voiceNoteDuration'] = voiceNoteDuration;
        }
        _logger.debug('Voice note uploaded successfully');
      }

      // Add message to forum
      await _forumsCollection
          .doc(forumId)
          .collection('messages')
          .add(messageData);

      // Update forum document with latest message info
      await _forumsCollection.doc(forumId).update({
        'lastMessage': text,
        'lastMessageTime': FieldValue.serverTimestamp(),
        'lastSenderId': currentUser.uid,
        'messageCount': FieldValue.increment(1),
      });

      // Make sure user is in the participants list
      await _forumsCollection.doc(forumId).update({
        'participants': FieldValue.arrayUnion([currentUser.uid]),
        'participantNames.${currentUser.uid}': currentUser.displayName ?? 'Anonymous',
      });

      // Note: Notifications are handled by MessageNotificationService via Firebase listeners
      // This ensures real-time delivery without duplicate notifications
      _logger.debug('Message sent successfully, notification will be handled by MessageNotificationService');

      _logger.info('Message sent successfully to forum: $forumId');
    } catch (e) {
      _logger.error('Error sending message', error: e);
      rethrow;
    }
  }

  // Add reaction to a message
  static Future<void> addReaction({
    required String forumId,
    required String messageId,
    required String reaction,
  }) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        throw Exception('User not logged in');
      }

      await _forumsCollection
          .doc(forumId)
          .collection('messages')
          .doc(messageId)
          .update({
        'reactions.$reaction': FieldValue.arrayUnion([currentUser.uid]),
      });

      _logger.debug('Added reaction $reaction to message $messageId');
    } catch (e) {
      _logger.error('Error adding reaction', error: e);
      rethrow;
    }
  }

  // Remove a reaction from a message
  static Future<void> removeReaction({
    required String forumId,
    required String messageId,
    required String reaction,
  }) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        throw Exception('User not logged in');
      }

      await _forumsCollection
          .doc(forumId)
          .collection('messages')
          .doc(messageId)
          .update({
        'reactions.$reaction': FieldValue.arrayRemove([currentUser.uid]),
      });

      _logger.debug('Removed reaction $reaction from message $messageId');
    } catch (e) {
      _logger.error('Error removing reaction', error: e);
      rethrow;
    }
  }

  // Edit a message
  static Future<void> editMessage({
    required String forumId,
    required String messageId,
    required String text,
  }) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        throw Exception('User not logged in');
      }

      // Get the message to check ownership
      final messageDoc = await _forumsCollection
          .doc(forumId)
          .collection('messages')
          .doc(messageId)
          .get();

      if (!messageDoc.exists) {
        throw Exception('Message not found');
      }

      final messageData = messageDoc.data() as Map<String, dynamic>;
      if (messageData['senderId'] != currentUser.uid) {
        throw Exception('You can only edit your own messages');
      }

      await _forumsCollection
          .doc(forumId)
          .collection('messages')
          .doc(messageId)
          .update({
        'text': text,
        'isEdited': true,
        'editedAt': FieldValue.serverTimestamp(),
      });

      // If this was the last message in the forum, update the forum's last message
      final forumDoc = await _forumsCollection.doc(forumId).get();
      final forumData = forumDoc.data() as Map<String, dynamic>?;

      if (forumData != null &&
          forumData['lastMessage'] == messageData['text'] &&
          forumData['lastSenderId'] == currentUser.uid) {
        await _forumsCollection.doc(forumId).update({
          'lastMessage': text,
        });
      }

      _logger.info('Message $messageId edited successfully');
    } catch (e) {
      _logger.error('Error editing message', error: e);
      rethrow;
    }
  }

  // Delete a message
  static Future<void> deleteMessage({
    required String forumId,
    required String messageId,
  }) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        throw Exception('User not logged in');
      }

      // Get the message to check ownership and get attachments if any
      final messageDoc = await _forumsCollection
          .doc(forumId)
          .collection('messages')
          .doc(messageId)
          .get();

      if (!messageDoc.exists) {
        throw Exception('Message not found');
      }

      final messageData = messageDoc.data() as Map<String, dynamic>;
      if (messageData['senderId'] != currentUser.uid) {
        throw Exception('You can only delete your own messages');
      }

      // Delete any attachments from storage
      if (messageData.containsKey('attachments')) {
        final attachments = List<String>.from(messageData['attachments']);
        for (final url in attachments) {
          try {
            final ref = _storage.refFromURL(url);
            await ref.delete();
            _logger.debug('Deleted attachment: $url');
          } catch (e) {
            // Just log, don't prevent message deletion if attachment deletion fails
            _logger.warning('Failed to delete attachment: $url', error: e);
          }
        }
      }

      // Delete voice note if exists
      if (messageData.containsKey('voiceNote')) {
        try {
          final ref = _storage.refFromURL(messageData['voiceNote']);
          await ref.delete();
          _logger.debug('Deleted voice note');
        } catch (e) {
          _logger.warning('Failed to delete voice note', error: e);
        }
      }

      // Delete the message
      await _forumsCollection
          .doc(forumId)
          .collection('messages')
          .doc(messageId)
          .delete();

      // If this was the last message in the forum, update the forum's last message
      final forumDoc = await _forumsCollection.doc(forumId).get();
      final forumData = forumDoc.data() as Map<String, dynamic>?;

      if (forumData != null &&
          forumData['lastMessage'] == messageData['text'] &&
          forumData['lastSenderId'] == currentUser.uid) {

        // Get the new last message
        final lastMessageQuery = await _forumsCollection
            .doc(forumId)
            .collection('messages')
            .orderBy('timestamp', descending: true)
            .limit(1)
            .get();

        if (lastMessageQuery.docs.isNotEmpty) {
          final lastMessage = lastMessageQuery.docs.first.data();
          await _forumsCollection.doc(forumId).update({
            'lastMessage': lastMessage['text'],
            'lastMessageTime': lastMessage['timestamp'],
            'lastSenderId': lastMessage['senderId'],
            'messageCount': FieldValue.increment(-1),
          });
        } else {
          // No messages left in the forum
          await _forumsCollection.doc(forumId).update({
            'lastMessage': 'No messages',
            'lastMessageTime': FieldValue.serverTimestamp(),
            'messageCount': 0,
          });
        }
      } else {
        // This wasn't the last message, just decrement the count
        await _forumsCollection.doc(forumId).update({
          'messageCount': FieldValue.increment(-1),
        });
      }

      _logger.info('Message $messageId deleted successfully');
    } catch (e) {
      _logger.error('Error deleting message', error: e);
      rethrow;
    }
  }

  // Join a forum
  static Future<void> joinForum(String forumId) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        throw Exception('User not logged in');
      }

      await _forumsCollection.doc(forumId).update({
        'participants': FieldValue.arrayUnion([currentUser.uid]),
        'participantNames.${currentUser.uid}': currentUser.displayName ?? 'Anonymous',
      });

      _logger.info('User ${currentUser.uid} joined forum $forumId');
    } catch (e) {
      _logger.error('Error joining forum', error: e);
      rethrow;
    }
  }

  // Leave a forum
  static Future<void> leaveForum(String forumId) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        throw Exception('User not logged in');
      }

      await _forumsCollection.doc(forumId).update({
        'participants': FieldValue.arrayRemove([currentUser.uid]),
      });

      _logger.info('User ${currentUser.uid} left forum $forumId');
    } catch (e) {
      _logger.error('Error leaving forum', error: e);
      rethrow;
    }
  }

  // Get all forums
  static Future<List<Forum>> getForums() async {
    try {
      return await NetworkErrorHandler.executeWithRetry(
        request: () async {
          final snapshot = await _forumsCollection
              .orderBy('lastMessageTime', descending: true)
              .get();

          return snapshot.docs.map((doc) {
            return Forum.fromMap(doc.data() as Map<String, dynamic>, doc.id);
          }).toList();
        },
        onError: (e) {
          _logger.error('Error getting forums', error: e);
          return [];
        }
      );
    } catch (e) {
      _logger.error('Error getting forums', error: e);
      return [];
    }
  }

  // Get a specific forum
  static Future<Forum?> getForum(String forumId) async {
    try {
      final docSnapshot = await _forumsCollection.doc(forumId).get();

      if (!docSnapshot.exists) {
        return null;
      }

      return Forum.fromMap(docSnapshot.data() as Map<String, dynamic>, docSnapshot.id);
    } catch (e) {
      _logger.error('Error getting forum', error: e);
      return null;
    }
  }

  // Create a forum with the Forum model
  static Future<Forum> createForum(Forum forum) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        throw Exception('User not logged in');
      }

      final Map<String, dynamic> data = forum.toMap();
      data['createdAt'] = FieldValue.serverTimestamp();
      data['creatorId'] = currentUser.uid;
      data['creatorName'] = currentUser.displayName ?? 'Anonymous';
      data['participants'] = [currentUser.uid];
      data['participantNames'] = {currentUser.uid: currentUser.displayName ?? 'Anonymous'};
      data['lastMessage'] = 'Forum created';
      data['lastMessageTime'] = FieldValue.serverTimestamp();
      data['lastSenderId'] = currentUser.uid;
      data['messageCount'] = 0;

      final docRef = await _forumsCollection.add(data);

      // Add welcome message
      await docRef.collection('messages').add({
        'text': 'Welcome to ${forum.title}! This is the beginning of the discussion.',
        'senderId': currentUser.uid,
        'senderName': currentUser.displayName ?? 'Anonymous',
        'senderAvatar': currentUser.photoURL ?? '',
        'timestamp': FieldValue.serverTimestamp(),
        'reactions': {},
        'isEdited': false,
      });

      // Create a new Forum object with the document ID
      return Forum(
        id: docRef.id,
        title: forum.title,
        description: forum.description,
        createdAt: forum.createdAt,
        postCount: forum.postCount,
        imageUrl: forum.imageUrl,
      );
    } catch (e) {
      _logger.error('Error creating forum', error: e);
      rethrow;
    }
  }

}