import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../utils/app_logger.dart';
import '../utils/error_handler.dart';

/// Service to enforce email uniqueness constraints for driver and job requests
class EmailUniquenessService {
  static final EmailUniquenessService _instance = EmailUniquenessService._internal();
  static EmailUniquenessService get instance => _instance;
  EmailUniquenessService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final AppLogger _logger = AppLogger('EmailUniquenessService');

  /// Check if email already has a driver request (comprehensive check including legacy data)
  Future<bool> hasExistingDriverRequest(String email) async {
    try {
      final normalizedEmail = email.toLowerCase().trim();
      _logger.debug('Checking for existing driver request with email: $normalizedEmail');

      // Method 1: Check driver_requests collection with userEmail field (new records)
      final driverRequestsQuery = await _firestore
          .collection('driver_requests')
          .where('userEmail', isEqualTo: normalizedEmail)
          .get();

      if (driverRequestsQuery.docs.isNotEmpty) {
        _logger.info('Found existing driver request with userEmail field for: $normalizedEmail');
        return true;
      }

      // Method 2: Check approved drivers collection with userEmail field (new records)
      final driversQuery = await _firestore
          .collection('drivers')
          .where('userEmail', isEqualTo: normalizedEmail)
          .get();

      if (driversQuery.docs.isNotEmpty) {
        _logger.info('Found existing approved driver with userEmail field for: $normalizedEmail');
        return true;
      }

      // Method 3: Check legacy driver_requests without userEmail field
      final legacyDriverRequestsQuery = await _firestore
          .collection('driver_requests')
          .get();

      for (final doc in legacyDriverRequestsQuery.docs) {
        final data = doc.data();
        final submittedBy = data['submittedBy'] as String?;

        if (submittedBy != null) {
          final userEmail = await _getUserEmailFromUserId(submittedBy);
          if (userEmail != null && userEmail.toLowerCase().trim() == normalizedEmail) {
            _logger.info('Found legacy driver request for email: $normalizedEmail (userId: $submittedBy)');
            // Optionally migrate this record to include userEmail
            await _migrateLegacyDriverRequest(doc.id, normalizedEmail);
            return true;
          }
        }
      }

      // Method 4: Check legacy drivers collection without userEmail field
      final legacyDriversQuery = await _firestore
          .collection('drivers')
          .get();

      for (final doc in legacyDriversQuery.docs) {
        final userId = doc.id; // Driver ID is usually the user ID

        final userEmail = await _getUserEmailFromUserId(userId);
        if (userEmail != null && userEmail.toLowerCase().trim() == normalizedEmail) {
          _logger.info('Found legacy approved driver for email: $normalizedEmail (userId: $userId)');
          // Optionally migrate this record to include userEmail
          await _migrateLegacyDriver(doc.id, normalizedEmail);
          return true;
        }
      }

      _logger.debug('No existing driver request found for email: $normalizedEmail');
      return false;
    } catch (e, stackTrace) {
      _logger.error('Error checking existing driver request',
          error: e, stackTrace: stackTrace);
      ErrorHandler.reportHandledException(
        e,
        stackTrace,
        context: 'Check existing driver request',
        severity: ErrorSeverity.medium,
      );
      // Return true to be safe and prevent potential duplicates
      return true;
    }
  }

  /// Check if email already has a job request (comprehensive check including legacy data)
  Future<bool> hasExistingJobRequest(String email) async {
    try {
      final normalizedEmail = email.toLowerCase().trim();
      _logger.debug('Checking for existing job request with email: $normalizedEmail');

      // Method 1: Check jobs collection with posterEmail field (new records)
      final jobsQuery = await _firestore
          .collection('jobs')
          .where('posterEmail', isEqualTo: normalizedEmail)
          .get();

      if (jobsQuery.docs.isNotEmpty) {
        _logger.info('Found existing job request with posterEmail field for: $normalizedEmail');
        return true;
      }

      // Method 2: Check legacy jobs without posterEmail field
      final legacyJobsQuery = await _firestore
          .collection('jobs')
          .get();

      for (final doc in legacyJobsQuery.docs) {
        final data = doc.data();
        final posterId = data['posterId'] as String?;

        if (posterId != null) {
          final userEmail = await _getUserEmailFromUserId(posterId);
          if (userEmail != null && userEmail.toLowerCase().trim() == normalizedEmail) {
            _logger.info('Found legacy job request for email: $normalizedEmail (userId: $posterId)');
            // Optionally migrate this record to include posterEmail
            await _migrateLegacyJob(doc.id, normalizedEmail);
            return true;
          }
        }
      }

      _logger.debug('No existing job request found for email: $normalizedEmail');
      return false;
    } catch (e, stackTrace) {
      _logger.error('Error checking existing job request',
          error: e, stackTrace: stackTrace);
      ErrorHandler.reportHandledException(
        e,
        stackTrace,
        context: 'Check existing job request',
        severity: ErrorSeverity.medium,
      );
      // Return true to be safe and prevent potential duplicates
      return true;
    }
  }

  /// Get current user's email
  Future<String?> getCurrentUserEmail() async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        _logger.warning('No authenticated user found');
        return null;
      }

      // Get email from Firebase Auth
      String? email = user.email;

      // If no email in auth, try to get from Firestore user document
      if (email == null || email.isEmpty) {
        final userDoc = await _firestore.collection('users').doc(user.uid).get();
        if (userDoc.exists) {
          email = userDoc.data()?['email'] as String?;
        }
      }

      if (email != null) {
        email = email.toLowerCase().trim();
        _logger.debug('Current user email: $email');
      }

      return email;
    } catch (e, stackTrace) {
      _logger.error('Error getting current user email',
          error: e, stackTrace: stackTrace);
      return null;
    }
  }

  /// Validate driver request eligibility
  Future<EmailValidationResult> validateDriverRequestEligibility() async {
    try {
      final email = await getCurrentUserEmail();

      if (email == null || email.isEmpty) {
        return EmailValidationResult(
          isValid: false,
          message: 'Unable to verify your email address. Please ensure you are logged in.',
          errorCode: 'NO_EMAIL',
        );
      }

      final hasExisting = await hasExistingDriverRequest(email);

      if (hasExisting) {
        return EmailValidationResult(
          isValid: false,
          message: 'You have already submitted a driver registration request with this email address. Each email can only be used for one driver registration.',
          errorCode: 'DUPLICATE_DRIVER_REQUEST',
        );
      }

      return EmailValidationResult(
        isValid: true,
        message: 'Email is eligible for driver registration.',
        email: email,
      );
    } catch (e, stackTrace) {
      _logger.error('Error validating driver request eligibility',
          error: e, stackTrace: stackTrace);
      return EmailValidationResult(
        isValid: false,
        message: 'Unable to validate eligibility. Please try again.',
        errorCode: 'VALIDATION_ERROR',
      );
    }
  }

  /// Validate job request eligibility
  Future<EmailValidationResult> validateJobRequestEligibility() async {
    try {
      final email = await getCurrentUserEmail();

      if (email == null || email.isEmpty) {
        return EmailValidationResult(
          isValid: false,
          message: 'Unable to verify your email address. Please ensure you are logged in.',
          errorCode: 'NO_EMAIL',
        );
      }

      final hasExisting = await hasExistingJobRequest(email);

      if (hasExisting) {
        return EmailValidationResult(
          isValid: false,
          message: 'You have already posted a job with this email address. Each email can only be used to post one job.',
          errorCode: 'DUPLICATE_JOB_REQUEST',
        );
      }

      return EmailValidationResult(
        isValid: true,
        message: 'Email is eligible for job posting.',
        email: email,
      );
    } catch (e, stackTrace) {
      _logger.error('Error validating job request eligibility',
          error: e, stackTrace: stackTrace);
      return EmailValidationResult(
        isValid: false,
        message: 'Unable to validate eligibility. Please try again.',
        errorCode: 'VALIDATION_ERROR',
      );
    }
  }

  /// Get existing driver request details for email
  Future<Map<String, dynamic>?> getExistingDriverRequest(String email) async {
    try {
      final query = await _firestore
          .collection('driver_requests')
          .where('userEmail', isEqualTo: email.toLowerCase().trim())
          .limit(1)
          .get();

      if (query.docs.isNotEmpty) {
        return {
          'id': query.docs.first.id,
          ...query.docs.first.data(),
        };
      }

      return null;
    } catch (e, stackTrace) {
      _logger.error('Error getting existing driver request',
          error: e, stackTrace: stackTrace);
      return null;
    }
  }

  /// Get existing job request details for email
  Future<Map<String, dynamic>?> getExistingJobRequest(String email) async {
    try {
      final query = await _firestore
          .collection('jobs')
          .where('posterEmail', isEqualTo: email.toLowerCase().trim())
          .limit(1)
          .get();

      if (query.docs.isNotEmpty) {
        return {
          'id': query.docs.first.id,
          ...query.docs.first.data(),
        };
      }

      return null;
    } catch (e, stackTrace) {
      _logger.error('Error getting existing job request',
          error: e, stackTrace: stackTrace);
      return null;
    }
  }

  /// Get user email from user ID by checking users collection and Firebase Auth
  Future<String?> _getUserEmailFromUserId(String userId) async {
    try {
      // First try to get from users collection
      final userDoc = await _firestore.collection('users').doc(userId).get();
      if (userDoc.exists) {
        final email = userDoc.data()?['email'] as String?;
        if (email != null && email.isNotEmpty) {
          return email.toLowerCase().trim();
        }
      }

      // If not found in users collection, try Firebase Auth (less reliable for other users)
      // This is mainly for the current user
      final currentUser = _auth.currentUser;
      if (currentUser != null && currentUser.uid == userId) {
        final email = currentUser.email;
        if (email != null && email.isNotEmpty) {
          return email.toLowerCase().trim();
        }
      }

      _logger.warning('Could not find email for userId: $userId');
      return null;
    } catch (e, stackTrace) {
      _logger.error('Error getting user email from userId: $userId',
          error: e, stackTrace: stackTrace);
      return null;
    }
  }

  /// Migrate legacy driver request to include userEmail field
  Future<void> _migrateLegacyDriverRequest(String requestId, String email) async {
    try {
      await _firestore.collection('driver_requests').doc(requestId).update({
        'userEmail': email.toLowerCase().trim(),
        'migratedAt': FieldValue.serverTimestamp(),
      });
      _logger.info('Migrated legacy driver request $requestId with email: $email');
    } catch (e, stackTrace) {
      _logger.error('Error migrating legacy driver request $requestId',
          error: e, stackTrace: stackTrace);
    }
  }

  /// Migrate legacy driver to include userEmail field
  Future<void> _migrateLegacyDriver(String driverId, String email) async {
    try {
      await _firestore.collection('drivers').doc(driverId).update({
        'userEmail': email.toLowerCase().trim(),
        'migratedAt': FieldValue.serverTimestamp(),
      });
      _logger.info('Migrated legacy driver $driverId with email: $email');
    } catch (e, stackTrace) {
      _logger.error('Error migrating legacy driver $driverId',
          error: e, stackTrace: stackTrace);
    }
  }

  /// Migrate legacy job to include posterEmail field
  Future<void> _migrateLegacyJob(String jobId, String email) async {
    try {
      await _firestore.collection('jobs').doc(jobId).update({
        'posterEmail': email.toLowerCase().trim(),
        'migratedAt': FieldValue.serverTimestamp(),
      });
      _logger.info('Migrated legacy job $jobId with email: $email');
    } catch (e, stackTrace) {
      _logger.error('Error migrating legacy job $jobId',
          error: e, stackTrace: stackTrace);
    }
  }

  /// Run comprehensive migration for all legacy records
  Future<Map<String, dynamic>> migrateLegacyRecords() async {
    final results = <String, dynamic>{
      'driverRequestsMigrated': 0,
      'driversMigrated': 0,
      'jobsMigrated': 0,
      'errors': <String>[],
    };

    try {
      _logger.info('Starting comprehensive legacy data migration...');

      // Migrate driver requests
      final driverRequestsQuery = await _firestore.collection('driver_requests').get();
      for (final doc in driverRequestsQuery.docs) {
        final data = doc.data();
        if (!data.containsKey('userEmail')) {
          final submittedBy = data['submittedBy'] as String?;
          if (submittedBy != null) {
            final email = await _getUserEmailFromUserId(submittedBy);
            if (email != null) {
              await _migrateLegacyDriverRequest(doc.id, email);
              results['driverRequestsMigrated']++;
            }
          }
        }
      }

      // Migrate drivers
      final driversQuery = await _firestore.collection('drivers').get();
      for (final doc in driversQuery.docs) {
        final data = doc.data();
        if (!data.containsKey('userEmail')) {
          final email = await _getUserEmailFromUserId(doc.id);
          if (email != null) {
            await _migrateLegacyDriver(doc.id, email);
            results['driversMigrated']++;
          }
        }
      }

      // Migrate jobs
      final jobsQuery = await _firestore.collection('jobs').get();
      for (final doc in jobsQuery.docs) {
        final data = doc.data();
        if (!data.containsKey('posterEmail')) {
          final posterId = data['posterId'] as String?;
          if (posterId != null) {
            final email = await _getUserEmailFromUserId(posterId);
            if (email != null) {
              await _migrateLegacyJob(doc.id, email);
              results['jobsMigrated']++;
            }
          }
        }
      }

      _logger.info('Legacy data migration completed: $results');
    } catch (e, stackTrace) {
      _logger.error('Error during legacy data migration',
          error: e, stackTrace: stackTrace);
      results['errors'].add(e.toString());
    }

    return results;
  }

  /// Get comprehensive email usage report
  Future<Map<String, dynamic>> getEmailUsageReport(String email) async {
    final normalizedEmail = email.toLowerCase().trim();
    final report = <String, dynamic>{
      'email': normalizedEmail,
      'driverRequests': <Map<String, dynamic>>[],
      'drivers': <Map<String, dynamic>>[],
      'jobs': <Map<String, dynamic>>[],
      'totalUsage': 0,
    };

    try {
      // Check driver requests
      final driverRequestsQuery = await _firestore
          .collection('driver_requests')
          .where('userEmail', isEqualTo: normalizedEmail)
          .get();

      for (final doc in driverRequestsQuery.docs) {
        report['driverRequests'].add({
          'id': doc.id,
          'createdAt': doc.data()['createdAt'],
          'status': doc.data()['status'],
        });
      }

      // Check drivers
      final driversQuery = await _firestore
          .collection('drivers')
          .where('userEmail', isEqualTo: normalizedEmail)
          .get();

      for (final doc in driversQuery.docs) {
        report['drivers'].add({
          'id': doc.id,
          'createdAt': doc.data()['createdAt'],
          'isVerified': doc.data()['isVerified'],
        });
      }

      // Check jobs
      final jobsQuery = await _firestore
          .collection('jobs')
          .where('posterEmail', isEqualTo: normalizedEmail)
          .get();

      for (final doc in jobsQuery.docs) {
        report['jobs'].add({
          'id': doc.id,
          'createdAt': doc.data()['createdAt'],
          'title': doc.data()['title'],
        });
      }

      report['totalUsage'] = report['driverRequests'].length +
                            report['drivers'].length +
                            report['jobs'].length;

    } catch (e, stackTrace) {
      _logger.error('Error generating email usage report',
          error: e, stackTrace: stackTrace);
      report['error'] = e.toString();
    }

    return report;
  }
}

/// Result of email validation
class EmailValidationResult {
  final bool isValid;
  final String message;
  final String? errorCode;
  final String? email;

  EmailValidationResult({
    required this.isValid,
    required this.message,
    this.errorCode,
    this.email,
  });

  @override
  String toString() {
    return 'EmailValidationResult(isValid: $isValid, message: $message, errorCode: $errorCode, email: $email)';
  }
}
