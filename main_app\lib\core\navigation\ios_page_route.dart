import 'package:flutter/material.dart';
import '../theme/colors.dart';

/// Creates an iOS-style page route transition
class IOSPageRoute<T> extends PageRouteBuilder<T> {
  final Widget page;

  IOSPageRoute({
    required this.page,
    RouteSettings? settings,
  }) : super(
          settings: settings,
          pageBuilder: (context, animation, secondaryAnimation) => page,
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            const begin = Offset(1.0, 0.0);
            const end = Offset.zero;
            const curve = Curves.easeInOut;

            var tween = Tween(begin: begin, end: end).chain(CurveTween(curve: curve));
            var offsetAnimation = animation.drive(tween);

            // Fade animation
            var fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
              CurvedAnimation(
                parent: animation,
                curve: const Interval(0.0, 0.5, curve: Curves.easeInOut),
              ),
            );

            return SlideTransition(
              position: offsetAnimation,
              child: FadeTransition(
                opacity: fadeAnimation,
                child: child,
              ),
            );
          },
          transitionDuration: const Duration(milliseconds: 350), // iOS uses slightly longer transitions
        );
}

/// Creates an iOS-style modal bottom sheet transition
Future<T?> showIOSModalBottomSheet<T>({
  required BuildContext context,
  required Widget child,
  bool isDismissible = true,
  bool enableDrag = true,
  Color? backgroundColor,
}) {
  return showModalBottomSheet<T>(
    context: context,
    isScrollControlled: true,
    isDismissible: isDismissible,
    enableDrag: enableDrag,
    backgroundColor: backgroundColor ?? Colors.white,
    shape: const RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
    ),
    builder: (context) => Padding(
      padding: EdgeInsets.only(
        bottom: MediaQuery.of(context).viewInsets.bottom,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // iOS-style drag indicator
          Container(
            margin: const EdgeInsets.only(top: 8, bottom: 16),
            width: 36,
            height: 5,
            decoration: BoxDecoration(
              color: Colors.grey.shade300,
              borderRadius: BorderRadius.circular(2.5),
            ),
          ),
          child,
        ],
      ),
    ),
  );
}

/// Creates an iOS-style action sheet
Future<T?> showIOSActionSheet<T>({
  required BuildContext context,
  String? title,
  String? message,
  required List<Widget> actions,
  Widget? cancelButton,
}) {
  return showModalBottomSheet<T>(
    context: context,
    backgroundColor: Colors.transparent,
    builder: (context) => Container(
      padding: const EdgeInsets.only(bottom: 8),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (title != null || message != null)
            Container(
              margin: const EdgeInsets.only(left: 8, right: 8, bottom: 8),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
              ),
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  if (title != null)
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: 13,
                        fontWeight: FontWeight.w600,
                        color: Colors.black54,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  if (title != null && message != null)
                    const SizedBox(height: 8),
                  if (message != null)
                    Text(
                      message,
                      style: const TextStyle(
                        fontSize: 13,
                        color: Colors.black54,
                      ),
                      textAlign: TextAlign.center,
                    ),
                ],
              ),
            ),
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 8),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: List.generate(actions.length, (index) {
                final isLast = index == actions.length - 1;
                return Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    actions[index],
                    if (!isLast)
                      const Divider(
                        height: 1,
                        thickness: 0.5,
                        indent: 0,
                        endIndent: 0,
                      ),
                  ],
                );
              }),
            ),
          ),
          if (cancelButton != null)
            Container(
              margin: const EdgeInsets.only(left: 8, right: 8, top: 8),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
              ),
              child: cancelButton,
            ),
        ],
      ),
    ),
  );
}

/// iOS-style action sheet button
class IOSActionSheetButton extends StatelessWidget {
  final String text;
  final VoidCallback onPressed;
  final bool isDestructive;
  final bool isCancel;

  const IOSActionSheetButton({
    Key? key,
    required this.text,
    required this.onPressed,
    this.isDestructive = false,
    this.isCancel = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    Color textColor = isDestructive
        ? Colors.red
        : isCancel
            ? AppColors.primaryYellow
            : AppColors.primaryYellow;

    return InkWell(
      onTap: () {
        Navigator.of(context).pop();
        onPressed();
      },
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(vertical: 16),
        child: Text(
          text,
          style: TextStyle(
            color: textColor,
            fontSize: 20,
            fontWeight: isCancel ? FontWeight.w600 : FontWeight.normal,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }
}
