#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function execCommand(command, description) {
  log(`\n${colors.blue}📋 ${description}...${colors.reset}`);
  try {
    execSync(command, { stdio: 'inherit', cwd: __dirname });
    log(`${colors.green}✅ ${description} completed successfully${colors.reset}`);
  } catch (error) {
    log(`${colors.red}❌ ${description} failed${colors.reset}`);
    process.exit(1);
  }
}

function checkPrerequisites() {
  log(`${colors.cyan}🔍 Checking prerequisites...${colors.reset}`);
  
  // Check if Firebase CLI is installed
  try {
    execSync('firebase --version', { stdio: 'pipe' });
    log(`${colors.green}✅ Firebase CLI is installed${colors.reset}`);
  } catch (error) {
    log(`${colors.red}❌ Firebase CLI is not installed. Please install it first:${colors.reset}`);
    log(`${colors.yellow}npm install -g firebase-tools${colors.reset}`);
    process.exit(1);
  }

  // Check if user is logged in to Firebase
  try {
    execSync('firebase projects:list', { stdio: 'pipe' });
    log(`${colors.green}✅ Firebase authentication verified${colors.reset}`);
  } catch (error) {
    log(`${colors.red}❌ Not logged in to Firebase. Please login first:${colors.reset}`);
    log(`${colors.yellow}firebase login${colors.reset}`);
    process.exit(1);
  }

  // Check if package.json exists
  const packageJsonPath = path.join(__dirname, '..', 'package.json');
  if (!fs.existsSync(packageJsonPath)) {
    log(`${colors.red}❌ package.json not found${colors.reset}`);
    process.exit(1);
  }

  log(`${colors.green}✅ All prerequisites met${colors.reset}`);
}

function getDeploymentTarget() {
  const args = process.argv.slice(2);
  const target = args[0] || 'staging';
  
  if (!['staging', 'production', 'prod'].includes(target)) {
    log(`${colors.red}❌ Invalid deployment target: ${target}${colors.reset}`);
    log(`${colors.yellow}Valid targets: staging, production, prod${colors.reset}`);
    process.exit(1);
  }
  
  return target === 'prod' ? 'production' : target;
}

function main() {
  log(`${colors.bright}${colors.magenta}🚀 Drive-On Admin Panel Deployment Script${colors.reset}`);
  log(`${colors.cyan}================================================${colors.reset}`);
  
  const target = getDeploymentTarget();
  log(`${colors.yellow}📍 Deployment target: ${target}${colors.reset}`);
  
  checkPrerequisites();
  
  // Change to admin directory
  process.chdir(path.join(__dirname, '..'));
  
  // Install dependencies
  execCommand('npm ci', 'Installing dependencies');
  
  // Run type checking
  execCommand('npm run type-check', 'Running type check');
  
  // Run linting
  execCommand('npm run lint', 'Running linting');
  
  // Build the application
  execCommand('npm run build', 'Building application');
  
  // Deploy to Firebase
  const deployCommand = target === 'production' 
    ? 'firebase deploy --only hosting:admin-production'
    : 'firebase deploy --only hosting:admin-staging';
    
  execCommand(deployCommand, `Deploying to ${target}`);
  
  log(`\n${colors.bright}${colors.green}🎉 Deployment to ${target} completed successfully!${colors.reset}`);
  
  // Show deployment URLs
  if (target === 'production') {
    log(`${colors.cyan}🌐 Production URL: https://drive-on-admin-panel.web.app${colors.reset}`);
  } else {
    log(`${colors.cyan}🌐 Staging URL: https://drive-on-admin-staging.web.app${colors.reset}`);
  }
  
  log(`${colors.yellow}📊 You can view deployment details in the Firebase Console${colors.reset}`);
}

// Handle script interruption
process.on('SIGINT', () => {
  log(`\n${colors.yellow}⚠️  Deployment interrupted by user${colors.reset}`);
  process.exit(1);
});

process.on('SIGTERM', () => {
  log(`\n${colors.yellow}⚠️  Deployment terminated${colors.reset}`);
  process.exit(1);
});

main();
