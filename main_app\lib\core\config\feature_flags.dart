/// Class to manage feature flags across different environments
class FeatureFlags {
  /// Enable experimental features that are still in development
  final bool enableExperimentalFeatures;
  
  /// Enable beta features that are being tested but not fully released
  final bool enableBetaFeatures;
  
  /// Enable performance monitoring
  final bool enablePerformanceMonitoring;
  
  /// Enable crash reporting
  final bool enableCrashReporting;
  
  /// Enable analytics
  final bool enableAnalytics;
  
  /// Constructor to initialize feature flags
  FeatureFlags({
    required this.enableExperimentalFeatures,
    required this.enableBetaFeatures,
    required this.enablePerformanceMonitoring,
    required this.enableCrashReporting,
    required this.enableAnalytics,
  });
  
  /// Create a copy of feature flags with some values changed
  FeatureFlags copyWith({
    bool? enableExperimentalFeatures,
    bool? enableBetaFeatures,
    bool? enablePerformanceMonitoring,
    bool? enableCrashReporting,
    bool? enableAnalytics,
  }) {
    return FeatureFlags(
      enableExperimentalFeatures: enableExperimentalFeatures ?? this.enableExperimentalFeatures,
      enableBetaFeatures: enableBetaFeatures ?? this.enableBetaFeatures,
      enablePerformanceMonitoring: enablePerformanceMonitoring ?? this.enablePerformanceMonitoring,
      enableCrashReporting: enableCrashReporting ?? this.enableCrashReporting,
      enableAnalytics: enableAnalytics ?? this.enableAnalytics,
    );
  }
  
  @override
  String toString() {
    return '''FeatureFlags(
      enableExperimentalFeatures: $enableExperimentalFeatures,
      enableBetaFeatures: $enableBetaFeatures,
      enablePerformanceMonitoring: $enablePerformanceMonitoring,
      enableCrashReporting: $enableCrashReporting,
      enableAnalytics: $enableAnalytics
    )''';
  }
} 