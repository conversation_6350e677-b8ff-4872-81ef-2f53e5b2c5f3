'use client'

import { useEffect, useState } from 'react'
import {
  Users,
  Car,
  Building2,
  Briefcase,
  TrendingUp,
  TrendingDown,
  Activity,
  Clock,
} from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { FastLoading } from '@/components/ui/fast-loading'
import { PageWrapper } from '@/components/layout/page-wrapper'
import { DashboardSkeleton } from '@/components/ui/skeleton-screen'
import { MetricCard } from '@/components/dashboard/metric-card'
import { RecentActivity } from '@/components/dashboard/recent-activity'
import { AnalyticsChart } from '@/components/dashboard/analytics-chart'
import { useDashboardMetrics } from '@/lib/hooks/use-dashboard-metrics'
import { useAnalyticsData } from '@/lib/hooks/use-analytics-data'

export default function DashboardPage() {
  const { metrics, isLoading, error } = useDashboardMetrics()
  const { userGrowthData, driverStatusData, isLoading: analyticsLoading } = useAnalyticsData()

  // Show skeleton immediately if no cached data, then replace with real data
  if (isLoading && !metrics) {
    return (
      <PageWrapper title="Dashboard">
        <DashboardSkeleton />
      </PageWrapper>
    )
  }

  if (error) {
    return (
      <PageWrapper title="Dashboard">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <p className="text-red-600 mb-2">Error loading dashboard data</p>
            <p className="text-gray-500">{error}</p>
          </div>
        </div>
      </PageWrapper>
    )
  }

  return (
    <PageWrapper title="Dashboard" className="space-y-6">
      {/* Welcome Section */}
      <div className="bg-gradient-primary rounded-lg p-6 text-white">
        <h1 className="text-2xl font-bold mb-2 text-white">Welcome to Drive-On Admin Panel</h1>
        <p className="text-white">
          Monitor and manage your platform with comprehensive analytics and controls.
        </p>
      </div>

      {/* Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricCard
          title="Total Users"
          value={metrics?.totalUsers || 0}
          change={metrics?.userGrowth || 0}
          icon={Users}
          description={`${metrics?.activeUsers || 0} active users`}
          color="blue"
        />
        <MetricCard
          title="Verified Drivers"
          value={metrics?.verifiedDrivers || 0}
          change={metrics?.driverGrowth || 0}
          icon={Car}
          description={`${metrics?.pendingDrivers || 0} pending verification`}
          color="green"
        />
        <MetricCard
          title="Partners"
          value={metrics?.totalPartners || 0}
          change={metrics?.partnerGrowth || 0}
          icon={Building2}
          description={`${metrics?.approvedPartners || 0} approved partners`}
          color="purple"
        />
        <MetricCard
          title="Active Jobs"
          value={metrics?.activeJobs || 0}
          change={metrics?.jobGrowth || 0}
          icon={Briefcase}
          description={`${metrics?.pendingJobs || 0} pending approval`}
          color="orange"
        />
      </div>

      {/* Charts and Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>User Growth Trend</CardTitle>
            <CardDescription>
              Monthly user and driver registrations over the last 6 months
            </CardDescription>
          </CardHeader>
          <CardContent>
            {analyticsLoading ? (
              <div className="flex items-center justify-center h-[300px]">
                <div className="animate-spin w-8 h-8 border-2 border-primary-600 border-t-transparent rounded-full"></div>
              </div>
            ) : (
              <AnalyticsChart type="line" data={userGrowthData} />
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Driver Verification Status</CardTitle>
            <CardDescription>
              Current status of driver applications
            </CardDescription>
          </CardHeader>
          <CardContent>
            {analyticsLoading ? (
              <div className="flex items-center justify-center h-[300px]">
                <div className="animate-spin w-8 h-8 border-2 border-primary-600 border-t-transparent rounded-full"></div>
              </div>
            ) : (
              <AnalyticsChart type="pie" data={driverStatusData} />
            )}
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity and Quick Stats */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Activity className="w-5 h-5" />
                <span>Recent Activities</span>
              </CardTitle>
              <CardDescription>
                Latest actions and updates across the platform
              </CardDescription>
            </CardHeader>
            <CardContent>
              <RecentActivity />
            </CardContent>
          </Card>
        </div>

        <div>
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Clock className="w-5 h-5" />
                <span>Quick Stats</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Pending Driver Verifications</span>
                <Badge variant="warning">{metrics?.pendingDrivers || 0}</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Open Queries</span>
                <Badge variant="info">{metrics?.openQueries || 0}</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Pending Partner Requests</span>
                <Badge variant="warning">{metrics?.pendingPartners || 0}</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Jobs Pending Approval</span>
                <Badge variant="info">{metrics?.pendingJobs || 0}</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Total Active Users</span>
                <span className="text-sm font-medium">{metrics?.activeUsers || 0}</span>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </PageWrapper>
  )
}
