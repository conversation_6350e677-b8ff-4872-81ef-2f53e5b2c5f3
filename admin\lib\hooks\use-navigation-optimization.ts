'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'

// Preload critical routes for faster navigation
const CRITICAL_ROUTES = [
  '/dashboard',
  '/users',
  '/drivers',
  '/partners',
  '/jobs',
  '/queries',
  '/settings'
]

export function useNavigationOptimization() {
  const router = useRouter()

  useEffect(() => {
    // Prefetch critical routes after initial load
    const prefetchRoutes = async () => {
      // Wait a bit to avoid blocking initial render
      await new Promise(resolve => setTimeout(resolve, 1000))

      // Prefetch routes one by one to avoid overwhelming the browser
      for (const route of CRITICAL_ROUTES) {
        try {
          router.prefetch(route)
          // Small delay between prefetches
          await new Promise(resolve => setTimeout(resolve, 100))
        } catch (error) {
          console.warn(`Failed to prefetch route ${route}:`, error)
        }
      }
    }

    prefetchRoutes()
  }, [router])

  // Instant navigation - UI shows immediately, loading happens after
  const optimizedNavigate = (href: string) => {
    // Add instant navigation class
    document.documentElement.classList.add('instant-nav')

    // Navigate immediately with no delays
    router.push(href)

    // Remove the class after a very short time
    setTimeout(() => {
      document.documentElement.classList.remove('instant-nav')
    }, 50)
  }

  return { optimizedNavigate }
}
