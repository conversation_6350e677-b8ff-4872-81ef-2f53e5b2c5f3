# Drive-On Fastlane Configuration for Google Play Store

default_platform(:android)

platform :android do
  desc "Deploy to Google Play Store Internal Testing"
  lane :internal do
    # Build the app bundle
    gradle(
      task: "bundle",
      build_type: "Release",
      project_dir: "android/"
    )
    
    # Upload to Play Store Internal Testing
    upload_to_play_store(
      track: 'internal',
      aab: 'build/app/outputs/bundle/release/app-release.aab',
      mapping: 'build/app/outputs/mapping/release/mapping.txt',
      skip_upload_apk: true,
      skip_upload_metadata: false,
      skip_upload_images: false,
      skip_upload_screenshots: false
    )
  end

  desc "Deploy to Google Play Store Closed Testing"
  lane :alpha do
    # Build the app bundle
    gradle(
      task: "bundle",
      build_type: "Release",
      project_dir: "android/"
    )
    
    # Upload to Play Store Alpha Testing
    upload_to_play_store(
      track: 'alpha',
      aab: 'build/app/outputs/bundle/release/app-release.aab',
      mapping: 'build/app/outputs/mapping/release/mapping.txt',
      skip_upload_apk: true,
      skip_upload_metadata: false,
      skip_upload_images: false,
      skip_upload_screenshots: false
    )
  end

  desc "Deploy to Google Play Store Open Testing"
  lane :beta do
    # Build the app bundle
    gradle(
      task: "bundle",
      build_type: "Release",
      project_dir: "android/"
    )
    
    # Upload to Play Store Beta Testing
    upload_to_play_store(
      track: 'beta',
      aab: 'build/app/outputs/bundle/release/app-release.aab',
      mapping: 'build/app/outputs/mapping/release/mapping.txt',
      skip_upload_apk: true,
      skip_upload_metadata: false,
      skip_upload_images: false,
      skip_upload_screenshots: false
    )
  end

  desc "Deploy to Google Play Store Production"
  lane :production do
    # Build the app bundle
    gradle(
      task: "bundle",
      build_type: "Release",
      project_dir: "android/"
    )
    
    # Upload to Play Store Production
    upload_to_play_store(
      track: 'production',
      aab: 'build/app/outputs/bundle/release/app-release.aab',
      mapping: 'build/app/outputs/mapping/release/mapping.txt',
      skip_upload_apk: true,
      skip_upload_metadata: false,
      skip_upload_images: false,
      skip_upload_screenshots: false
    )
  end

  desc "Upload metadata only"
  lane :metadata do
    upload_to_play_store(
      skip_upload_apk: true,
      skip_upload_aab: true,
      skip_upload_metadata: false,
      skip_upload_images: false,
      skip_upload_screenshots: false
    )
  end
end
