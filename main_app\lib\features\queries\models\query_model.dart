import 'package:cloud_firestore/cloud_firestore.dart';

class Query {
  final String id;
  final String userId;
  final String userDisplayName;
  final String userEmail;
  final String userPhotoUrl;
  final String topic;
  final String lastMessage;
  final DateTime createdAt;
  final DateTime lastMessageTime;
  final int unreadCount; // Number of unread messages for admin
  final int unreadUserCount; // Number of unread messages for user
  final String status; // 'open', 'closed'

  Query({
    required this.id,
    required this.userId,
    required this.userDisplayName,
    required this.userEmail,
    this.userPhotoUrl = '',
    required this.topic,
    required this.lastMessage,
    required this.createdAt,
    required this.lastMessageTime,
    this.unreadCount = 0,
    this.unreadUserCount = 0,
    this.status = 'open',
  });

  factory Query.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return Query(
      id: doc.id,
      userId: data['userId'] ?? '',
      userDisplayName: data['userDisplayName'] ?? 'User',
      userEmail: data['userEmail'] ?? '',
      userPhotoUrl: data['userPhotoUrl'] ?? '',
      topic: data['topic'] ?? 'General Query',
      lastMessage: data['lastMessage'] ?? '',
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      lastMessageTime: (data['lastMessageTime'] as Timestamp?)?.toDate() ?? DateTime.now(),
      unreadCount: data['unreadCount'] ?? 0,
      unreadUserCount: data['unreadUserCount'] ?? 0,
      status: data['status'] ?? 'open',
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'userId': userId,
      'userDisplayName': userDisplayName,
      'userEmail': userEmail,
      'userPhotoUrl': userPhotoUrl,
      'topic': topic,
      'lastMessage': lastMessage,
      'createdAt': Timestamp.fromDate(createdAt),
      'lastMessageTime': Timestamp.fromDate(lastMessageTime),
      'unreadCount': unreadCount,
      'unreadUserCount': unreadUserCount,
      'status': status,
    };
  }

  Query copyWith({
    String? id,
    String? userId,
    String? userDisplayName,
    String? userEmail,
    String? userPhotoUrl,
    String? topic,
    String? lastMessage,
    DateTime? createdAt,
    DateTime? lastMessageTime,
    int? unreadCount,
    int? unreadUserCount,
    String? status,
  }) {
    return Query(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      userDisplayName: userDisplayName ?? this.userDisplayName,
      userEmail: userEmail ?? this.userEmail,
      userPhotoUrl: userPhotoUrl ?? this.userPhotoUrl,
      topic: topic ?? this.topic,
      lastMessage: lastMessage ?? this.lastMessage,
      createdAt: createdAt ?? this.createdAt,
      lastMessageTime: lastMessageTime ?? this.lastMessageTime,
      unreadCount: unreadCount ?? this.unreadCount,
      unreadUserCount: unreadUserCount ?? this.unreadUserCount,
      status: status ?? this.status,
    );
  }
} 