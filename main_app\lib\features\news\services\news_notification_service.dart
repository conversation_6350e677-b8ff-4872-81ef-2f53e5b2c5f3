import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../../../core/services/notification_manager.dart';
import '../../../core/utils/app_logger.dart';
import '../data/models/news_article.dart';

class NewsNotificationService {
  static final NewsNotificationService _instance = NewsNotificationService._internal();
  factory NewsNotificationService() => _instance;
  NewsNotificationService._internal();

  static NewsNotificationService get instance => _instance;

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final AppLogger _logger = AppLogger('NewsNotificationService');

  StreamSubscription<QuerySnapshot>? _newsSubscription;
  bool _isInitialized = false;
  DateTime? _lastCheckTime;

  /// Initialize the news notification service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      _logger.info('Initializing news notification service');

      // Set initial check time to now to avoid notifications for existing articles
      _lastCheckTime = DateTime.now();

      // Listen for new admin articles
      _setupNewsListener();

      _isInitialized = true;
      _logger.info('News notification service initialized successfully');
    } catch (e, stackTrace) {
      _logger.error('Error initializing news notification service',
          error: e, stackTrace: stackTrace);
    }
  }

  /// Setup listener for new admin articles
  void _setupNewsListener() {
    _newsSubscription = _firestore
        .collection('news_articles')
        .where('source', isEqualTo: 'Admin')
        .orderBy('createdAt', descending: true)
        .limit(10) // Only check recent articles
        .snapshots()
        .listen(
      (snapshot) {
        _handleNewsSnapshot(snapshot);
      },
      onError: (error) {
        _logger.error('Error in news listener', error: error);
      },
    );
  }

  /// Handle news snapshot changes
  void _handleNewsSnapshot(QuerySnapshot snapshot) {
    if (_lastCheckTime == null) return;

    try {
      for (final change in snapshot.docChanges) {
        if (change.type == DocumentChangeType.added) {
          final article = NewsArticle.fromFirestore(change.doc);

          // Only notify for articles created after initialization
          if (article.createdAt.isAfter(_lastCheckTime!)) {
            _sendNewArticleNotification(article);
          }
        }
      }

      // Update last check time
      _lastCheckTime = DateTime.now();
    } catch (e, stackTrace) {
      _logger.error('Error handling news snapshot',
          error: e, stackTrace: stackTrace);
    }
  }

  /// Send notification for new admin article
  Future<void> _sendNewArticleNotification(NewsArticle article) async {
    try {
      // Don't send notification to admin themselves
      final currentUser = _auth.currentUser;
      if (currentUser == null) return;

      await NotificationManager.instance.sendSystemNotification(
        title: '📰 New Article Published',
        body: '${article.title} - ${article.category}',
        systemType: 'news',
        additionalData: {
          'articleId': article.id,
          'category': article.category,
          'source': article.source,
          'screen': 'news_detail',
          'params': article.id,
        },
      );

      _logger.info('News notification sent for article: ${article.title}');
    } catch (e, stackTrace) {
      _logger.error('Error sending news notification',
          error: e, stackTrace: stackTrace);
    }
  }

  /// Send notification for breaking news
  Future<void> sendBreakingNewsNotification(NewsArticle article) async {
    try {
      await NotificationManager.instance.sendSystemNotification(
        title: '🚨 BREAKING NEWS',
        body: article.title,
        systemType: 'urgent',
        additionalData: {
          'articleId': article.id,
          'category': article.category,
          'source': article.source,
          'screen': 'news_detail',
          'params': article.id,
        },
      );

      _logger.info('Breaking news notification sent for: ${article.title}');
    } catch (e, stackTrace) {
      _logger.error('Error sending breaking news notification',
          error: e, stackTrace: stackTrace);
    }
  }

  /// Send notification for featured articles
  Future<void> sendFeaturedArticleNotification(NewsArticle article) async {
    try {
      await NotificationManager.instance.sendSystemNotification(
        title: '⭐ Featured Article',
        body: '${article.title} - Don\'t miss this important update!',
        systemType: 'update',
        additionalData: {
          'articleId': article.id,
          'category': article.category,
          'source': article.source,
          'screen': 'news_detail',
          'params': article.id,
        },
      );

      _logger.info('Featured article notification sent for: ${article.title}');
    } catch (e, stackTrace) {
      _logger.error('Error sending featured article notification',
          error: e, stackTrace: stackTrace);
    }
  }

  /// Manually trigger notification for specific article (for testing)
  Future<void> triggerTestNotification(NewsArticle article) async {
    await _sendNewArticleNotification(article);
  }

  /// Dispose the service
  void dispose() {
    try {
      _newsSubscription?.cancel();
      _newsSubscription = null;
      _isInitialized = false;
      _logger.info('News notification service disposed');
    } catch (e, stackTrace) {
      _logger.error('Error disposing news notification service',
          error: e, stackTrace: stackTrace);
    }
  }
}
