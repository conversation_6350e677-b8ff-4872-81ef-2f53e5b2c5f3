{"hosting": {"public": "out", "ignore": ["firebase.json", "**/.*", "**/node_modules/**", "**/.git/**"], "rewrites": [{"source": "**", "destination": "/index.html"}], "cleanUrls": true, "trailingSlash": false, "headers": [{"source": "**/*.@(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "**/*.@(html|json)", "headers": [{"key": "Cache-Control", "value": "public, max-age=0, must-revalidate"}]}, {"source": "**", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Strict-Transport-Security", "value": "max-age=31536000; includeSubDomains"}]}]}, "emulators": {"hosting": {"port": 5000}, "ui": {"enabled": true, "port": 4000}}}