import 'package:flutter/material.dart';
import 'package:just_audio/just_audio.dart';
import '../../../core/core.dart';

class ForumVoicePlayer extends StatefulWidget {
  final String audioUrl;
  final Duration duration;
  final bool isDarkMode;
  final bool isMe;

  const ForumVoicePlayer({
    super.key,
    required this.audioUrl,
    required this.duration,
    required this.isDarkMode,
    required this.isMe,
  });

  @override
  State<ForumVoicePlayer> createState() => _ForumVoicePlayerState();
}

class _ForumVoicePlayerState extends State<ForumVoicePlayer> {
  late AudioPlayer _audioPlayer;
  bool _isLoading = true;
  bool _isPlaying = false;
  bool _showSpeedOptions = false;
  Duration _currentPosition = Duration.zero;
  Duration _totalDuration = Duration.zero;
  double _playbackSpeed = 1.0;
  bool _isDragging = false;

  @override
  void initState() {
    super.initState();
    _audioPlayer = AudioPlayer();
    _initializeAudio();
  }

  @override
  void dispose() {
    _audioPlayer.dispose();
    super.dispose();
  }

  Future<void> _initializeAudio() async {
    try {
      // Listen to player state changes
      _audioPlayer.playerStateStream.listen((state) {
        if (mounted) {
          setState(() {
            _isPlaying = state.playing;
          });

          // Handle audio completion - stop and reset to beginning
          if (state.processingState == ProcessingState.completed) {
            setState(() {
              _isPlaying = false;
              _currentPosition = Duration.zero;
            });
            // Stop the player and reset to beginning
            _audioPlayer.stop();
            _audioPlayer.seek(Duration.zero);
          }
        }
      });

      // Listen to position changes (only when not dragging)
      _audioPlayer.positionStream.listen((position) {
        if (mounted && !_isDragging) {
          setState(() {
            _currentPosition = position;
          });

          // Additional check: if we've reached the end, stop playing
          if (_totalDuration.inMilliseconds > 0 &&
              position.inMilliseconds >= _totalDuration.inMilliseconds) {
            setState(() {
              _isPlaying = false;
              _currentPosition = Duration.zero;
            });
            _audioPlayer.stop();
            _audioPlayer.seek(Duration.zero);
          }
        }
      });

      // Listen to duration changes
      _audioPlayer.durationStream.listen((duration) {
        if (mounted && duration != null) {
          setState(() {
            _totalDuration = duration;
          });
        }
      });

      // Set initial duration if we have it
      if (widget.duration.inMilliseconds > 0) {
        _totalDuration = widget.duration;
      }

      // Set initial playback speed
      await _audioPlayer.setSpeed(_playbackSpeed);

      // Set audio source
      await _audioPlayer.setUrl(widget.audioUrl);

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading audio: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _togglePlay() async {
    try {
      if (_isPlaying) {
        await _audioPlayer.pause();
      } else {
        // If we're at the end, restart from beginning
        if (_currentPosition.inMilliseconds >= _totalDuration.inMilliseconds &&
            _totalDuration.inMilliseconds > 0) {
          await _audioPlayer.seek(Duration.zero);
          setState(() {
            _currentPosition = Duration.zero;
          });
        }
        await _audioPlayer.play();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error playing audio: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _seekTo(Duration position) {
    _audioPlayer.seek(position);
  }

  void _changeSpeed(double speed) async {
    try {
      await _audioPlayer.setSpeed(speed);
      setState(() {
        _playbackSpeed = speed;
        _showSpeedOptions = false;
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error changing speed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _onSliderChanged(double value) {
    setState(() {
      _isDragging = true;
      _currentPosition = Duration(milliseconds: value.toInt());
    });
  }

  void _onSliderChangeEnd(double value) {
    final newPosition = Duration(milliseconds: value.toInt());
    _seekTo(newPosition);
    setState(() {
      _isDragging = false;
    });
  }

  String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '$minutes:${seconds.toString().padLeft(2, '0')}';
  }

  @override
  Widget build(BuildContext context) {
    final totalMs = widget.duration.inMilliseconds > 0 ? widget.duration.inMilliseconds : _totalDuration.inMilliseconds;
    final currentMs = _currentPosition.inMilliseconds;

    final backgroundColor = widget.isDarkMode
        ? (widget.isMe ? AppColors.darkSurface : AppColors.darkSurface.withOpacity(0.7))
        : (widget.isMe ? AppColors.primaryYellow.withOpacity(0.1) : Colors.white);

    final primaryColor = widget.isMe
        ? AppColors.primaryYellow
        : (widget.isDarkMode ? Colors.white70 : Colors.grey[800]!);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: widget.isDarkMode
              ? (widget.isMe ? AppColors.primaryYellow.withOpacity(0.3) : Colors.transparent)
              : (widget.isMe ? AppColors.primaryYellow.withOpacity(0.3) : Colors.grey.withOpacity(0.2)),
        ),
      ),
      child: _isLoading
          ? Center(
              child: SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  color: primaryColor,
                ),
              ),
            )
          : Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Main player row
                Row(
                  children: [
                    // Play/Pause button
                    Material(
                      color: Colors.transparent,
                      child: InkWell(
                        borderRadius: BorderRadius.circular(20),
                        onTap: _togglePlay,
                        child: Container(
                          width: 36,
                          height: 36,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: primaryColor.withOpacity(0.1),
                          ),
                          child: Icon(
                            _isPlaying ? Icons.pause : Icons.play_arrow,
                            color: primaryColor,
                            size: 20,
                          ),
                        ),
                      ),
                    ),

                    const SizedBox(width: 8),

                    // Current time
                    Text(
                      _formatDuration(_currentPosition),
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: widget.isDarkMode ? Colors.white70 : Colors.grey[700],
                      ),
                    ),

                    const SizedBox(width: 8),

                    // Progress slider
                    Expanded(
                      child: SliderTheme(
                        data: SliderTheme.of(context).copyWith(
                          trackHeight: 3,
                          thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 6),
                          overlayShape: const RoundSliderOverlayShape(overlayRadius: 12),
                          activeTrackColor: primaryColor,
                          inactiveTrackColor: widget.isDarkMode ? Colors.white24 : Colors.grey[300],
                          thumbColor: primaryColor,
                          overlayColor: primaryColor.withOpacity(0.2),
                        ),
                        child: Slider(
                          value: totalMs > 0 ? currentMs.toDouble().clamp(0.0, totalMs.toDouble()) : 0.0,
                          max: totalMs > 0 ? totalMs.toDouble() : 1.0,
                          onChanged: _onSliderChanged,
                          onChangeEnd: _onSliderChangeEnd,
                        ),
                      ),
                    ),

                    const SizedBox(width: 8),

                    // Total duration
                    Text(
                      _formatDuration(widget.duration.inMilliseconds > 0 ? widget.duration : _totalDuration),
                      style: TextStyle(
                        fontSize: 12,
                        color: widget.isDarkMode ? Colors.white54 : Colors.grey[600],
                      ),
                    ),

                    const SizedBox(width: 8),

                    // Speed control button
                    Material(
                      color: Colors.transparent,
                      child: InkWell(
                        borderRadius: BorderRadius.circular(16),
                        onTap: () {
                          setState(() {
                            _showSpeedOptions = !_showSpeedOptions;
                          });
                        },
                        child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: primaryColor.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(16),
                          ),
                          child: Text(
                            '${_playbackSpeed}x',
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                              color: primaryColor,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),

                // Speed options (when expanded)
                if (_showSpeedOptions) ...[
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      _buildSpeedOption(1.0),
                      _buildSpeedOption(1.5),
                      _buildSpeedOption(2.0),
                    ],
                  ),
                ],
              ],
            ),
    );
  }

  Widget _buildSpeedOption(double speed) {
    final isSelected = _playbackSpeed == speed;
    final primaryColor = widget.isMe
        ? AppColors.primaryYellow
        : (widget.isDarkMode ? Colors.white70 : Colors.grey[800]!);

    return Material(
      color: Colors.transparent,
      child: InkWell(
        borderRadius: BorderRadius.circular(20),
        onTap: () => _changeSpeed(speed),
        child: Container(
          width: 50,
          height: 30,
          decoration: BoxDecoration(
            color: isSelected ? primaryColor.withOpacity(0.2) : Colors.transparent,
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: isSelected ? primaryColor : Colors.transparent,
              width: 1,
            ),
          ),
          child: Center(
            child: Text(
              '${speed}x',
              style: TextStyle(
                fontSize: 12,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                color: isSelected ? primaryColor : (widget.isDarkMode ? Colors.white60 : Colors.grey[600]),
              ),
            ),
          ),
        ),
      ),
    );
  }
}