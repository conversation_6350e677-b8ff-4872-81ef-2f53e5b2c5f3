import 'package:flutter/material.dart';

/// A custom card widget with standardized styling for the Drive-On app
class CustomCard extends StatelessWidget {
  /// The title of the card
  final String title;
  
  /// The main content widget for the card
  final Widget child;
  
  /// Optional icon to display next to the title
  final IconData? icon;
  
  /// Optional action buttons to display in the header
  final List<Widget>? actions;
  
  /// Optional padding around the content
  final EdgeInsetsGeometry contentPadding;
  
  /// Optional padding around the card
  final EdgeInsetsGeometry padding;
  
  /// Creates a CustomCard with Drive-On styling
  const CustomCard({
    Key? key,
    required this.title,
    required this.child,
    this.icon,
    this.actions,
    this.contentPadding = const EdgeInsets.all(16.0),
    this.padding = const EdgeInsets.all(0),
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: padding,
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildHeader(context),
            Padding(
              padding: contentPadding,
              child: child,
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest.withOpacity(0.3),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(12),
          topRight: Radius.circular(12),
        ),
      ),
      child: Row(
        children: [
          if (icon != null) ...[
            Icon(icon, size: 20),
            const SizedBox(width: 8),
          ],
          Text(
            title,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const Spacer(),
          if (actions != null) ...actions!,
        ],
      ),
    );
  }
} 