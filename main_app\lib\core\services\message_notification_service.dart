import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';

import '../utils/app_logger.dart';
import '../utils/error_handler.dart';
import 'notification_manager.dart';

/// Robust real-time message notification service
class MessageNotificationService {
  static final MessageNotificationService _instance = MessageNotificationService._internal();
  static MessageNotificationService get instance => _instance;
  MessageNotificationService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final AppLogger _logger = AppLogger('MessageNotificationService');

  final List<StreamSubscription> _subscriptions = [];
  bool _isInitialized = false;
  String? _currentUserId;

  // Track processed messages to prevent duplicates
  final Set<String> _processedMessages = {};

  // Track initialization time to avoid old messages
  DateTime? _initializationTime;

  /// Initialize the message notification service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      _logger.debug('Starting initialization...');

      _initializationTime = DateTime.now();
      _currentUserId = _auth.currentUser?.uid;

      if (_currentUserId == null) {
        _logger.debug('User not authenticated, skipping setup');
        return;
      }

      _logger.debug('Setting up for user: $_currentUserId');

      // Listen for auth state changes
      final authSubscription = _auth.authStateChanges().listen((User? user) {
        if (user != null && user.uid != _currentUserId) {
          _logger.debug('User changed: ${user.uid}');
          _currentUserId = user.uid;
          _cleanup();
          _setupMessageListeners();
        } else if (user == null) {
          _logger.debug('User logged out');
          _currentUserId = null;
          _cleanup();
        }
      });
      _subscriptions.add(authSubscription);

      await _setupMessageListeners();
      _isInitialized = true;
      _logger.info('Message notification service initialized successfully');
    } catch (e, stackTrace) {
      _logger.error('Error initializing', error: e);
      ErrorHandler.reportHandledException(
        e,
        stackTrace,
        context: 'Initialize message notification service',
        severity: ErrorSeverity.medium,
      );
    }
  }

  /// Setup listeners for different message types
  Future<void> _setupMessageListeners() async {
    if (_currentUserId == null) return;

    try {
      _logger.debug('Setting up message listeners...');

      // Clean up existing listeners
      _cleanup();

      // Listen for new forum messages
      await _setupForumMessageListener();

      // Listen for new query messages
      await _setupQueryMessageListener();

      _logger.debug('Message listeners setup completed');
    } catch (e) {
      _logger.error('Error setting up message listeners', error: e);
    }
  }

  /// Setup forum message listener with real-time optimization
  Future<void> _setupForumMessageListener() async {
    try {
      _logger.debug('Setting up forum listener...');

      final subscription = _firestore
          .collection('forums')
          .snapshots(includeMetadataChanges: false)
          .listen(
        (QuerySnapshot snapshot) {
          _logger.debug('Forum snapshot received with ${snapshot.docs.length} documents');
          for (final change in snapshot.docChanges) {
            _logger.debug('Forum change detected: ${change.type} for doc ${change.doc.id}');
            if (change.type == DocumentChangeType.modified ||
                change.type == DocumentChangeType.added) {
              _handleForumUpdate(change.doc);
            }
          }
        },
        onError: (error) {
          _logger.error('Forum listener error', error: error);
        },
      );

      _subscriptions.add(subscription);
      _logger.debug('Forum message listener setup completed');
    } catch (e) {
      _logger.error('Error setting up forum message listener', error: e);
    }
  }

  /// Setup query message listener with real-time optimization
  Future<void> _setupQueryMessageListener() async {
    try {
      _logger.debug('Setting up query listener...');

      final subscription = _firestore
          .collection('queries')
          .snapshots(includeMetadataChanges: false)
          .listen(
        (QuerySnapshot snapshot) {
          for (final change in snapshot.docChanges) {
            if (change.type == DocumentChangeType.modified ||
                change.type == DocumentChangeType.added) {
              _handleQueryUpdate(change.doc);
            }
          }
        },
        onError: (error) {
          _logger.error('Query listener error', error: error);
        },
      );

      _subscriptions.add(subscription);
      _logger.debug('Query message listener setup completed');
    } catch (e) {
      _logger.error('Error setting up query message listener', error: e);
    }
  }

  /// Handle forum document updates
  void _handleForumUpdate(DocumentSnapshot doc) {
    try {
      final data = doc.data() as Map<String, dynamic>?;
      if (data == null) {
        _logger.debug('Forum document data is null for ${doc.id}');
        return;
      }

      _logger.debug('Processing forum update for ${doc.id}');
      _logger.debug('Forum data: ${data.keys.toList()}');

      final lastSenderId = data['lastSenderId'] as String?;
      final lastMessage = data['lastMessage'] as String?;
      final lastMessageTime = data['lastMessageTime'] as Timestamp?;
      final title = data['title'] as String?;

      // Try to get sender name from participantNames map or use a fallback
      final participantNames = data['participantNames'] as Map<String, dynamic>?;
      final lastSenderName = participantNames?[lastSenderId] as String? ?? 'Unknown User';

      _logger.debug('Forum: $title, LastSender: $lastSenderId, Message: $lastMessage');

      // Don't notify for own messages or if no sender ID
      if (lastSenderId == null || lastSenderId == _currentUserId) {
        _logger.debug('Skipping forum notification - user sent the message or no sender ID');
        return;
      }

      // Check if message is new (after initialization)
      if (_initializationTime != null && lastMessageTime != null) {
        final messageTime = lastMessageTime.toDate();
        if (messageTime.isBefore(_initializationTime!)) {
          _logger.debug('Skipping old forum message');
          return;
        }
      }

      // Create unique message ID to prevent duplicates
      final messageId = '${doc.id}_${lastMessageTime?.millisecondsSinceEpoch ?? 0}';
      if (_processedMessages.contains(messageId)) {
        _logger.debug('Skipping duplicate forum message');
        return;
      }
      _processedMessages.add(messageId);

      // Send notification
      if (lastMessage != null && title != null) {
        _logger.info('Sending forum notification: $title');

        NotificationManager.instance.sendMessageNotification(
          title: 'New Forum Message',
          body: '$title: $lastMessage',
          messageType: 'forum',
          senderId: lastSenderId,
          senderName: lastSenderName,
          roomId: doc.id,
          additionalData: {
            'forumId': doc.id,
            'forumTitle': title,
            'timestamp': lastMessageTime?.millisecondsSinceEpoch ?? DateTime.now().millisecondsSinceEpoch,
          },
        );
      }
    } catch (e) {
      _logger.error('Error handling forum update', error: e);
    }
  }

  /// Handle query document updates
  void _handleQueryUpdate(DocumentSnapshot doc) {
    try {
      final data = doc.data() as Map<String, dynamic>?;
      if (data == null) return;

      final lastMessage = data['lastMessage'] as String?;
      final lastSenderId = data['lastSenderId'] as String?;
      final lastMessageTime = data['lastMessageTime'] as Timestamp?;
      final queryTitle = data['title'] as String?;
      final lastSenderName = data['lastSenderName'] as String?;

      // Don't notify for own messages
      if (lastSenderId == null || lastSenderId == _currentUserId) {
        _logger.debug('Skipping query notification - user sent the message or no sender ID');
        return;
      }

      // Check if message is new (after initialization)
      if (_initializationTime != null && lastMessageTime != null) {
        final messageTime = lastMessageTime.toDate();
        if (messageTime.isBefore(_initializationTime!)) {
          _logger.debug('Skipping old query message');
          return;
        }
      }

      // Create unique message ID to prevent duplicates
      final messageId = '${doc.id}_${lastMessageTime?.millisecondsSinceEpoch ?? 0}';
      if (_processedMessages.contains(messageId)) {
        _logger.debug('Skipping duplicate query message');
        return;
      }
      _processedMessages.add(messageId);

      // Send notification
      if (lastMessage != null) {
        _logger.info('Sending query notification: ${queryTitle ?? 'Query'}');

        NotificationManager.instance.sendMessageNotification(
          title: 'New Query Message',
          body: '${queryTitle ?? 'Query'}: $lastMessage',
          messageType: 'query',
          senderId: lastSenderId,
          senderName: lastSenderName ?? 'Unknown User',
          roomId: doc.id,
          additionalData: {
            'queryId': doc.id,
            'queryTitle': queryTitle,
            'timestamp': lastMessageTime?.millisecondsSinceEpoch ?? DateTime.now().millisecondsSinceEpoch,
          },
        );
      }
    } catch (e) {
      _logger.error('Error handling query update', error: e);
    }
  }

  /// Clean up all subscriptions
  void _cleanup() {
    for (final subscription in _subscriptions) {
      subscription.cancel();
    }
    _subscriptions.clear();
    _logger.debug('Message notification listeners cleaned up');
  }

  /// Dispose the service
  void dispose() {
    _cleanup();
    _isInitialized = false;
    _logger.debug('Message notification service disposed');
  }
}
