'use client'

import { Filter } from 'lucide-react'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'

interface ForumFiltersProps {
  filters: {
    category: string
    status: string
    author: string
  }
  onFiltersChange: (filters: any) => void
}

export function ForumFilters({ filters, onFiltersChange }: ForumFiltersProps) {
  const updateFilter = (key: string, value: string) => {
    onFiltersChange({
      ...filters,
      [key]: value
    })
  }

  return (
    <div className="flex space-x-2">
      {/* Category Filter */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" size="sm">
            <Filter className="w-4 h-4 mr-2" />
            Category: {filters.category === 'all' ? 'All' : filters.category}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          <DropdownMenuLabel>Filter by Category</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={() => updateFilter('category', 'all')}>
            All Categories
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('category', 'general')}>
            General
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('category', 'help')}>
            Help
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('category', 'feedback')}>
            Feedback
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('category', 'announcements')}>
            Announcements
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('category', 'off-topic')}>
            Off Topic
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Status Filter */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" size="sm">
            Status: {filters.status === 'all' ? 'All' : filters.status}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          <DropdownMenuLabel>Filter by Status</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={() => updateFilter('status', 'all')}>
            All Status
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('status', 'active')}>
            Active
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('status', 'locked')}>
            Locked
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('status', 'archived')}>
            Archived
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('status', 'flagged')}>
            Flagged
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Author Filter */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" size="sm">
            Author: {filters.author === 'all' ? 'All' : filters.author}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          <DropdownMenuLabel>Filter by Author</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={() => updateFilter('author', 'all')}>
            All Authors
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('author', 'Admin')}>
            Admin
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('author', 'Moderator')}>
            Moderator
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('author', 'User')}>
            Regular Users
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  )
}
