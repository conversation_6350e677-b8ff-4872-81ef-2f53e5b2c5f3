# Drive-On Main App - Android Deployment Guide

This guide covers the deployment of the Drive-On main Flutter application for Android with Firebase App Distribution and CI/CD pipeline.

## 🚀 Quick Start

### Prerequisites

1. **Flutter SDK** (3.19.0 or later)
2. **Firebase CLI** (`npm install -g firebase-tools`)
3. **Git** for version control
4. **GitHub account** for CI/CD pipeline

### Local Development

```bash
# Clone the repository
git clone <your-repo-url>
cd main_app

# Install dependencies
flutter pub get

# Run the app locally
flutter run -d chrome
```

## 🔧 Firebase Setup

### 1. Firebase Project Configuration

The app is configured for Firebase project: `drive-on-b2af8`

- **Project ID**: `drive-on-b2af8`
- **Hosting URL**: `https://drive-on-b2af8.web.app`
- **Firebase Console**: [https://console.firebase.google.com/project/drive-on-b2af8](https://console.firebase.google.com/project/drive-on-b2af8)

### 2. Firebase Services Used

- **Authentication**: User login/signup
- **Firestore**: Database for app data
- **Storage**: File uploads and media
- **Hosting**: Web app deployment
- **Analytics**: User behavior tracking
- **Crashlytics**: Error reporting
- **Performance**: App performance monitoring

## 🚀 Deployment Methods

### Method 1: Automatic CI/CD (Recommended)

The app automatically deploys when code is pushed to the main branch.

1. **Push to main branch**:
   ```bash
   git add .
   git commit -m "Your changes"
   git push origin main
   ```

2. **GitHub Actions will**:
   - Run tests
   - Build the Flutter web app
   - Deploy to Firebase Hosting
   - Provide deployment URL

### Method 2: Manual Deployment

#### Using Scripts (Recommended)

**Windows:**
```bash
scripts\deploy.bat
```

#### Manual Steps

```bash
# 1. Get dependencies
flutter pub get

# 2. Run tests
flutter test

# 3. Build web app
flutter build web --release --web-renderer html

# 4. Deploy to Firebase
firebase deploy --only hosting
```

### Method 3: Docker Deployment

```bash
# Build Docker image
docker build -t drive-on-web .

# Run locally
docker run -p 8080:80 drive-on-web

# Or use docker-compose
docker-compose up -d
```

## 🔄 CI/CD Pipeline

### GitHub Actions Workflows

1. **Production Deployment** (`.github/workflows/firebase-hosting-merge.yml`)
   - Triggers on push to main/master branch
   - Runs tests and builds app
   - Deploys to live Firebase Hosting

2. **Preview Deployment** (`.github/workflows/firebase-hosting-pull-request.yml`)
   - Triggers on pull requests
   - Creates preview deployment
   - Provides preview URL in PR comments

### Required GitHub Secrets

Add these secrets in your GitHub repository settings:

```
FIREBASE_SERVICE_ACCOUNT_DRIVE_ON_B2AF8
```

To get the service account key:
```bash
firebase login
firebase projects:list
firebase serviceaccount:generate-key service-account-key.json --project drive-on-b2af8
```

## 📁 Project Structure

```
main_app/
├── .github/workflows/          # CI/CD workflows
├── lib/                        # Flutter source code
├── web/                        # Web-specific files
├── build/web/                  # Built web app (generated)
├── scripts/                    # Deployment scripts
├── firebase.json               # Firebase configuration
├── .firebaserc                 # Firebase project settings
├── Dockerfile                  # Docker configuration
├── docker-compose.yml          # Docker Compose setup
└── DEPLOYMENT.md              # This file
```

## 🌐 Environment Configuration

### Production Environment

- **URL**: https://drive-on-b2af8.web.app
- **Firebase Project**: drive-on-b2af8
- **Environment**: production

### Development Environment

- **Local URL**: http://localhost:3000
- **Firebase Project**: drive-on-b2af8 (same project, different environment)
- **Environment**: development

## 🔧 Configuration Files

### firebase.json
```json
{
  "hosting": {
    "public": "build/web",
    "ignore": ["firebase.json", "**/.*", "**/node_modules/**"],
    "rewrites": [{"source": "**", "destination": "/index.html"}]
  }
}
```

### .firebaserc
```json
{
  "projects": {
    "default": "drive-on-b2af8"
  }
}
```

## 🚨 Troubleshooting

### Common Issues

1. **Build Fails**
   ```bash
   flutter clean
   flutter pub get
   flutter build web --release
   ```

2. **Firebase CLI Not Found**
   ```bash
   npm install -g firebase-tools
   firebase login
   ```

3. **Permission Denied**
   ```bash
   firebase login
   firebase projects:list
   ```

4. **Web App Not Loading**
   - Check browser console for errors
   - Verify Firebase configuration in `web/index.html`
   - Check network requests in browser dev tools

### Logs and Monitoring

- **Firebase Console**: Monitor hosting metrics
- **GitHub Actions**: Check workflow logs
- **Browser Console**: Debug client-side issues
- **Firebase Performance**: Monitor app performance

## 📞 Support

For deployment issues:
1. Check GitHub Actions logs
2. Review Firebase Console
3. Check this documentation
4. Contact the development team

## 🔄 Updates and Maintenance

### Regular Tasks

1. **Update Dependencies**
   ```bash
   flutter pub upgrade
   ```

2. **Security Updates**
   ```bash
   flutter pub deps
   flutter pub audit
   ```

3. **Performance Monitoring**
   - Check Firebase Performance dashboard
   - Monitor Core Web Vitals
   - Review user feedback

### Rollback Procedure

If deployment fails:
```bash
# Rollback to previous version
firebase hosting:rollback --project drive-on-b2af8
```

---

**Last Updated**: January 2025
**Version**: 1.0.0
