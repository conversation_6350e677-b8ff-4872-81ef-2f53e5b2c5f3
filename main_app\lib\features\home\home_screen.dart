import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../core/core.dart';
import '../notifications/services/notification_history_service.dart';
import '../notifications/screens/notifications_screen.dart';
import 'sections/drivers_section.dart';
import 'sections/jobs_section.dart';
import 'sections/partners_section.dart';
import 'sections/news_section.dart';
import 'sections/forum_section.dart';
import 'sections/queries_section.dart';
import 'sections/products_section.dart';
import 'sections/settings_section.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final PageController _pageController = PageController();
  int _currentPageIndex = 0;

  final List<String> _sectionTitles = [
    'Drivers',
    'Jobs',
    'Partners',
    'News',
    'Forum',
    'Queries',
    'Products',
    'Settings',
  ];

  final List<IconData> _sectionIcons = [
    Icons.directions_car_rounded,
    Icons.work_rounded,
    Icons.handshake_rounded,
    Icons.newspaper_rounded,
    Icons.forum_rounded,
    Icons.help_rounded,
    Icons.shopping_bag_rounded,
    Icons.settings_rounded,
  ];

  // Lazy-loaded sections for better performance
  final Map<int, Widget> _cachedSections = {};

  Widget _getSection(int index) {
    if (_cachedSections.containsKey(index)) {
      return _cachedSections[index]!;
    }

    Widget section;
    switch (index) {
      case 0:
        section = const DriversSection();
        break;
      case 1:
        section = const JobsSection();
        break;
      case 2:
        section = const PartnersSection();
        break;
      case 3:
        section = const NewsSection();
        break;
      case 4:
        section = const ForumSection();
        break;
      case 5:
        section = const QueriesSection();
        break;
      case 6:
        section = const ProductsSection();
        break;
      case 7:
        section = const SettingsSection();
        break;
      default:
        section = const Center(child: Text('Section not found'));
    }

    _cachedSections[index] = section;
    return section;
  }

  // Create a logger for this screen
  final AppLogger _logger = AppLogger('HomeScreen');

  @override
  void initState() {
    super.initState();

    // Initialize home screen
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _logger.info('HomeScreen initialized');
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    _logger.info('HomeScreen disposed');
    super.dispose();
  }

  void _onPageChanged(int index) {
    setState(() {
      _currentPageIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: AppColors.primaryYellow,
        elevation: 0,
        title: Text(
          'Drive-On',
          style: TextStyle(
            color: isDarkMode ? Colors.black : Colors.black,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.dark,
        ),
        actions: [
          // Notification Bell with real-time badge
          StreamBuilder<int>(
            stream: NotificationHistoryService.instance.getUnreadCount(),
            builder: (context, snapshot) {
              final unreadCount = snapshot.data ?? 0;
              return Stack(
                children: [
                  IconButton(
                    icon: Icon(
                      unreadCount > 0 ? Icons.notifications_active : Icons.notifications_outlined,
                      color: Colors.black,
                      size: 24,
                    ),
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const NotificationsScreen(),
                        ),
                      );
                    },
                    tooltip: 'Notifications',
                  ),
                  if (unreadCount > 0)
                    Positioned(
                      right: 8,
                      top: 8,
                      child: Container(
                        padding: const EdgeInsets.all(4),
                        decoration: const BoxDecoration(
                          color: Colors.red,
                          shape: BoxShape.circle,
                        ),
                        constraints: const BoxConstraints(
                          minWidth: 16,
                          minHeight: 16,
                        ),
                        child: Text(
                          unreadCount > 99 ? '99+' : unreadCount.toString(),
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                ],
              );
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Section navigation icons
          Container(
            height: 60,
            decoration: BoxDecoration(
              color: isDarkMode ? AppColors.darkSurface : Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _sectionIcons.length,
              itemBuilder: (context, index) {
                final isActive = index == _currentPageIndex;
                return GestureDetector(
                  onTap: () {
                    _pageController.animateToPage(
                      index,
                      duration: const Duration(milliseconds: 300),
                      curve: Curves.easeInOut,
                    );
                  },
                  child: Container(
                    width: MediaQuery.of(context).size.width / 8.2, // Adjusted to fit all 8 icons
                    padding: const EdgeInsets.symmetric(horizontal: 2), // Reduced padding
                    decoration: BoxDecoration(
                      border: isActive
                          ? const Border(
                              bottom: BorderSide(
                                color: AppColors.primaryYellow,
                                width: 3,
                              ),
                            )
                          : null,
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          _sectionIcons[index],
                          size: 20, // Reduced icon size
                          color: isActive
                              ? AppColors.primaryYellow
                              : isDarkMode
                                  ? Colors.white54
                                  : Colors.black54,
                        ),
                        const SizedBox(height: 2), // Reduced spacing
                        Text(
                          _sectionTitles[index],
                          style: TextStyle(
                            fontSize: 10, // Reduced font size
                            fontWeight: isActive ? FontWeight.bold : FontWeight.normal,
                            color: isActive
                                ? AppColors.primaryYellow
                                : isDarkMode
                                    ? Colors.white54
                                    : Colors.black54,
                          ),
                          textAlign: TextAlign.center,
                          overflow: TextOverflow.ellipsis, // Handle text overflow
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),

          // Main content area with swipeable sections (lazy loaded)
          Expanded(
            child: PageView.builder(
              controller: _pageController,
              onPageChanged: _onPageChanged,
              itemCount: _sectionTitles.length,
              itemBuilder: (context, index) => _getSection(index),
            ),
          ),
        ],
      ),
    );
  }
}