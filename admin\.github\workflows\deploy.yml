name: Deploy Admin Panel to Firebase Hosting

on:
  push:
    branches:
      - main
      - develop
    paths:
      - 'admin/**'
  pull_request:
    branches:
      - main
    paths:
      - 'admin/**'
  workflow_dispatch:

env:
  NODE_VERSION: '18'
  WORKING_DIRECTORY: './admin'

jobs:
  build-and-test:
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ${{ env.WORKING_DIRECTORY }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: admin/package-lock.json

      - name: Install dependencies
        run: npm ci --legacy-peer-deps

      - name: Run type check
        run: npm run type-check

      - name: Run linting
        run: npm run lint

      - name: Run format check
        run: npm run format:check

      - name: Build application
        run: npm run build
        env:
          NODE_ENV: production

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: build-files
          path: |
            admin/.next/
            admin/out/
          retention-days: 1

  deploy-staging:
    needs: build-and-test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/develop' && github.event_name == 'push'
    defaults:
      run:
        working-directory: ${{ env.WORKING_DIRECTORY }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: admin/package-lock.json

      - name: Install dependencies
        run: npm ci --legacy-peer-deps

      - name: Download build artifacts
        uses: actions/download-artifact@v4
        with:
          name: build-files
          path: admin/

      - name: Deploy to Firebase Hosting (Staging)
        uses: FirebaseExtended/action-hosting-deploy@v0
        with:
          repoToken: '${{ secrets.GITHUB_TOKEN }}'
          firebaseServiceAccount: '${{ secrets.FIREBASE_SERVICE_ACCOUNT_DRIVE_ON_B2AF8 }}'
          projectId: drive-on-b2af8
          channelId: staging
          entryPoint: admin

  deploy-production:
    needs: build-and-test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    defaults:
      run:
        working-directory: ${{ env.WORKING_DIRECTORY }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: admin/package-lock.json

      - name: Install dependencies
        run: npm ci --legacy-peer-deps

      - name: Download build artifacts
        uses: actions/download-artifact@v4
        with:
          name: build-files
          path: admin/

      - name: Deploy to Firebase Hosting (Production)
        uses: FirebaseExtended/action-hosting-deploy@v0
        with:
          repoToken: '${{ secrets.GITHUB_TOKEN }}'
          firebaseServiceAccount: '${{ secrets.FIREBASE_SERVICE_ACCOUNT_DRIVE_ON_B2AF8 }}'
          projectId: drive-on-b2af8
          channelId: live
          entryPoint: admin

  notify-deployment:
    needs: [deploy-staging, deploy-production]
    runs-on: ubuntu-latest
    if: always() && (needs.deploy-staging.result != 'skipped' || needs.deploy-production.result != 'skipped')

    steps:
      - name: Notify deployment status
        run: |
          if [[ "${{ needs.deploy-staging.result }}" == "success" ]]; then
            echo "✅ Staging deployment successful"
          elif [[ "${{ needs.deploy-staging.result }}" == "failure" ]]; then
            echo "❌ Staging deployment failed"
          fi

          if [[ "${{ needs.deploy-production.result }}" == "success" ]]; then
            echo "✅ Production deployment successful"
          elif [[ "${{ needs.deploy-production.result }}" == "failure" ]]; then
            echo "❌ Production deployment failed"
          fi
