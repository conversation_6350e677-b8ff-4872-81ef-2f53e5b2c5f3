name: Android Build and Deploy (Main App)

on:
  push:
    branches:
      - main
      - master
      - develop
    paths:
      - 'main_app/**'
  pull_request:
    branches:
      - main
      - master
    paths:
      - 'main_app/**'
  workflow_dispatch:
    inputs:
      environment:
        description: 'Deployment environment'
        required: true
        default: 'production'
        type: choice
        options:
          - staging
          - production
      build_type:
        description: 'Build type'
        required: true
        default: 'both'
        type: choice
        options:
          - apk
          - bundle
          - both
      release_notes:
        description: 'Custom release notes'
        required: false
        type: string

env:
  FLUTTER_VERSION: '3.19.0'
  JAVA_VERSION: '17'

jobs:
  test:
    name: Run Tests
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./main_app

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Java
        uses: actions/setup-java@v4
        with:
          distribution: 'zulu'
          java-version: ${{ env.JAVA_VERSION }}

      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: ${{ env.FLUTTER_VERSION }}
          channel: 'stable'
          cache: true

      - name: Get Flutter dependencies
        run: flutter pub get

      - name: Verify formatting
        run: dart format --output=none --set-exit-if-changed .

      - name: Analyze project source
        run: flutter analyze --no-fatal-infos

      - name: Run unit tests
        run: flutter test --coverage

      - name: Upload coverage reports
        uses: codecov/codecov-action@v3
        with:
          file: ./main_app/coverage/lcov.info
          fail_ci_if_error: false

  build_android:
    name: Build Android
    needs: test
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./main_app

    strategy:
      matrix:
        build_type: [apk, appbundle]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Java
        uses: actions/setup-java@v4
        with:
          distribution: 'zulu'
          java-version: ${{ env.JAVA_VERSION }}

      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: ${{ env.FLUTTER_VERSION }}
          channel: 'stable'
          cache: true

      - name: Get Flutter dependencies
        run: flutter pub get

      - name: Generate build number
        id: build_number
        run: |
          BUILD_NUMBER=$((1000 + ${{ github.run_number }}))
          echo "build_number=$BUILD_NUMBER" >> $GITHUB_OUTPUT
          echo "Build number: $BUILD_NUMBER"

      - name: Build APK
        if: matrix.build_type == 'apk'
        run: |
          flutter build apk --release \
            --build-number=${{ steps.build_number.outputs.build_number }} \
            --dart-define=ENVIRONMENT=production \
            --obfuscate \
            --split-debug-info=build/debug-info

      - name: Build App Bundle
        if: matrix.build_type == 'appbundle'
        run: |
          flutter build appbundle --release \
            --build-number=${{ steps.build_number.outputs.build_number }} \
            --dart-define=ENVIRONMENT=production \
            --obfuscate \
            --split-debug-info=build/debug-info

      - name: Upload APK artifact
        if: matrix.build_type == 'apk'
        uses: actions/upload-artifact@v4
        with:
          name: app-release-apk
          path: main_app/build/app/outputs/flutter-apk/app-release.apk
          retention-days: 30

      - name: Upload App Bundle artifact
        if: matrix.build_type == 'appbundle'
        uses: actions/upload-artifact@v4
        with:
          name: app-release-bundle
          path: main_app/build/app/outputs/bundle/release/app-release.aab
          retention-days: 30

  deploy_firebase:
    name: Deploy to Firebase App Distribution
    needs: build_android
    runs-on: ubuntu-latest
    if: github.event_name == 'push' || github.event_name == 'workflow_dispatch'
    defaults:
      run:
        working-directory: ./main_app

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Download APK artifact
        uses: actions/download-artifact@v4
        with:
          name: app-release-apk
          path: main_app/build/app/outputs/flutter-apk/

      - name: Determine environment
        id: env
        run: |
          if [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
            echo "environment=${{ github.event.inputs.environment }}" >> $GITHUB_OUTPUT
          elif [[ "${{ github.ref }}" == "refs/heads/main" ]] || [[ "${{ github.ref }}" == "refs/heads/master" ]]; then
            echo "environment=production" >> $GITHUB_OUTPUT
          else
            echo "environment=staging" >> $GITHUB_OUTPUT
          fi

      - name: Generate release notes
        id: release_notes
        run: |
          if [[ "${{ github.event.inputs.release_notes }}" != "" ]]; then
            NOTES="${{ github.event.inputs.release_notes }}"
          else
            NOTES="🚀 New build from commit: ${{ github.sha }}
          📱 Branch: ${{ github.ref_name }}
          👤 Author: ${{ github.actor }}
          🌍 Environment: ${{ steps.env.outputs.environment }}

          Changes in this build:
          ${{ github.event.head_commit.message }}"
          fi
          echo "notes<<EOF" >> $GITHUB_OUTPUT
          echo "$NOTES" >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT

      - name: Deploy to Firebase App Distribution
        uses: wzieba/Firebase-Distribution-Github-Action@v1
        with:
          appId: ${{ secrets.FIREBASE_APP_ID }}
          serviceCredentialsFileContent: ${{ secrets.FIREBASE_SERVICE_ACCOUNT_DRIVE_ON_B2AF8 }}
          groups: ${{ steps.env.outputs.environment == 'production' && 'production-testers' || 'staging-testers' }}
          file: main_app/build/app/outputs/flutter-apk/app-release.apk
          releaseNotes: ${{ steps.release_notes.outputs.notes }}
          
      - name: Upload APK Artifact
        uses: actions/upload-artifact@v4
        with:
          name: apk-release
          path: main_app/build/app/outputs/flutter-apk/app-release.apk
