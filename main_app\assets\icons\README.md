# App Icon Instructions

## Required Icon Files

You need to create the following icon files:

1. `app_icon.png` - The main icon that will be used for iOS and other platforms. This should be:
   - 1024x1024 pixels
   - PNG format
   - Include your yellow car design
   - Square with no transparency (solid background)

2. `app_icon_foreground.png` - The foreground layer for Android adaptive icons:
   - 1024x1024 pixels
   - PNG format
   - Include only the yellow car with transparency around it
   - The car should be centered and take up about 60-70% of the image area

## Guidelines for the Yellow Car Icon

1. Use a bright yellow color (suggested: #FFDD00 or #FFC107)
2. Keep the design simple and recognizable at small sizes
3. Ensure good contrast between the car and background
4. For the foreground image, make sure there's adequate padding (at least 10-15% on all sides)
5. Test the icon at small sizes to ensure it's still recognizable

## After Creating the Icons

Once you've created and placed these files in this directory:

1. Run the following command to generate all icon variants:
   ```
   flutter pub get
   flutter pub run flutter_launcher_icons
   ```

2. This will automatically:
   - Create Android adaptive icons (supporting all shapes)
   - Generate iOS app icon set
   - Create web app icons
   - Generate Windows and macOS icons

## Android Adaptive Icons

The Android adaptive icon consists of two layers:
- Background layer: Solid white color (configured in flutter_launcher_icons.yaml)
- Foreground layer: Your yellow car with transparency

This setup allows the system to display the icon in various shapes (square, circle, rounded square, etc.) based on device settings or user preference. 