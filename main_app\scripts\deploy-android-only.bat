@echo off
REM Android-Only Deployment Script for Drive-On Main App
REM This script builds and deploys only the Android version to Firebase App Distribution

setlocal enabledelayedexpansion

REM Default values
set ENVIRONMENT=production
set BUILD_TYPE=both
set SKIP_TESTS=false
set TESTER_GROUP=production-testers

REM Parse command line arguments
:parse_args
if "%~1"=="" goto :args_done
if "%~1"=="--env" (
    set ENVIRONMENT=%~2
    shift
    shift
    goto :parse_args
)
if "%~1"=="--build-type" (
    set BUILD_TYPE=%~2
    shift
    shift
    goto :parse_args
)
if "%~1"=="--testers" (
    set TESTER_GROUP=%~2
    shift
    shift
    goto :parse_args
)
if "%~1"=="--skip-tests" (
    set SKIP_TESTS=true
    shift
    goto :parse_args
)
if "%~1"=="--help" (
    echo Usage: deploy-android-only.bat [options]
    echo Options:
    echo   --env ^<environment^>      Environment: development, staging, production ^(default: production^)
    echo   --build-type ^<type^>      Build type: apk, bundle, both ^(default: both^)
    echo   --testers ^<group^>        Tester group: staging-testers, production-testers ^(default: production-testers^)
    echo   --skip-tests             Skip running tests before build
    echo   --help                   Show this help message
    exit /b 0
)
shift
goto :parse_args
:args_done

echo 🚀 Android-Only Deployment for Drive-On Main App
echo =================================================
echo 📋 Configuration:
echo    Environment: %ENVIRONMENT%
echo    Build Type: %BUILD_TYPE%
echo    Tester Group: %TESTER_GROUP%
echo    Skip Tests: %SKIP_TESTS%
echo.

REM Check prerequisites
echo 📋 Checking prerequisites...

REM Check Flutter
flutter --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Flutter is not installed. Please install Flutter first.
    exit /b 1
) else (
    echo ✅ Flutter is installed
)

REM Check Firebase CLI
firebase --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Firebase CLI is not installed. Please install it first:
    echo npm install -g firebase-tools
    exit /b 1
) else (
    echo ✅ Firebase CLI is installed
)

REM Check if environment file exists
if not exist ".env.%ENVIRONMENT%" (
    echo ❌ Environment file .env.%ENVIRONMENT% not found
    echo Please create the environment file or use --env with a valid environment
    exit /b 1
) else (
    echo ✅ Environment file found: .env.%ENVIRONMENT%
)

REM Clean previous builds
echo 🧹 Cleaning previous builds...
flutter clean

REM Get Flutter dependencies
echo 📦 Getting Flutter dependencies...
flutter pub get
if %errorlevel% neq 0 (
    echo ❌ Failed to get Flutter dependencies
    exit /b 1
)

REM Run tests (unless skipped)
if "%SKIP_TESTS%"=="false" (
    echo 🧪 Running tests...
    flutter test
    if %errorlevel% neq 0 (
        echo ❌ Tests failed. Deployment aborted.
        echo Use --skip-tests to bypass test failures
        exit /b 1
    )
    echo ✅ All tests passed!
)

REM Generate build number
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YY=%dt:~2,2%" & set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
set "HH=%dt:~8,2%" & set "Min=%dt:~10,2%" & set "Sec=%dt:~12,2%"
set "BUILD_NUMBER=%YYYY%%MM%%DD%%HH%%Min%"

echo 🔢 Build number: %BUILD_NUMBER%

REM Build APK (if requested)
if "%BUILD_TYPE%"=="apk" goto :build_apk
if "%BUILD_TYPE%"=="both" goto :build_apk
goto :skip_apk

:build_apk
echo 📱 Building Android APK...
flutter build apk --release ^
    --build-number=%BUILD_NUMBER% ^
    --dart-define=ENVIRONMENT=%ENVIRONMENT% ^
    --shrink ^
    --obfuscate ^
    --split-debug-info=build/debug-info

if %errorlevel% neq 0 (
    echo ❌ APK build failed
    exit /b 1
)
echo ✅ APK build completed successfully!

:skip_apk

REM Build App Bundle (if requested)
if "%BUILD_TYPE%"=="bundle" goto :build_bundle
if "%BUILD_TYPE%"=="both" goto :build_bundle
goto :skip_bundle

:build_bundle
echo 📦 Building Android App Bundle...
flutter build appbundle --release ^
    --build-number=%BUILD_NUMBER% ^
    --dart-define=ENVIRONMENT=%ENVIRONMENT% ^
    --obfuscate ^
    --split-debug-info=build/debug-info

if %errorlevel% neq 0 (
    echo ❌ App Bundle build failed
    exit /b 1
)
echo ✅ App Bundle build completed successfully!

:skip_bundle

REM Deploy to Firebase App Distribution
echo 🚀 Deploying to Firebase App Distribution...

REM Generate release notes
set "RELEASE_NOTES=🚀 Drive-On Main App - Build %BUILD_NUMBER%

📱 Environment: %ENVIRONMENT%
🔢 Build: %BUILD_NUMBER%
📅 Date: %DATE% %TIME%

✨ Features:
- Complete Flutter mobile app for Drive-On platform
- Real-time notifications and messaging
- Offline storage and sync capabilities
- News aggregation with AI processing
- Forum and query management systems
- Driver and job management

🔧 Technical:
- Optimized release build with obfuscation
- Environment-specific configuration
- Enhanced security and performance"

REM Deploy APK if it exists
if exist "build\app\outputs\flutter-apk\app-release.apk" (
    echo 📤 Deploying APK to Firebase App Distribution...
    firebase appdistribution:distribute build\app\outputs\flutter-apk\app-release.apk ^
        --app 1:206767723448:android:6a8e1d9c3c8992992d754d ^
        --groups "%TESTER_GROUP%" ^
        --release-notes "%RELEASE_NOTES%"
    
    if %errorlevel% equ 0 (
        echo ✅ APK successfully deployed to Firebase App Distribution!
    ) else (
        echo ❌ APK deployment failed
        exit /b 1
    )
)

echo.
echo 🎉 Android Deployment Completed Successfully!
echo.
echo 📊 Deployment Summary:
echo    Environment: %ENVIRONMENT%
echo    Build Number: %BUILD_NUMBER%
echo    Tester Group: %TESTER_GROUP%
echo.
echo 📱 Android Artifacts:
if exist "build\app\outputs\flutter-apk\app-release.apk" (
    echo    ✅ APK: build\app\outputs\flutter-apk\app-release.apk
)
if exist "build\app\outputs\bundle\release\app-release.aab" (
    echo    ✅ Bundle: build\app\outputs\bundle\release\app-release.aab
)
echo.
echo 🔗 Firebase App Distribution:
echo    📊 Console: https://console.firebase.google.com/project/drive-on-b2af8/appdistribution
echo    📧 Testers will receive email notifications
echo    📲 Download link will be available in the Firebase console
echo.
echo 📋 Next Steps:
echo    1. Check Firebase App Distribution console for deployment status
echo    2. Notify testers to check their email for download links
echo    3. Monitor app performance and user feedback
echo    4. Prepare for Play Store submission if ready
echo.
pause
