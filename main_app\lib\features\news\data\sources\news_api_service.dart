import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import '../models/news_article.dart';
import 'deepseek_ai_service.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../../core/utils/app_logger.dart';

class NewsApiService {
  final String _apiKey;
  final String _baseUrl;
  final http.Client _httpClient;
  final DeepSeekAIService _aiService;
  final AppLogger _logger = AppLogger('NewsApiService');

  // Request tracking for free tier management
  static const String _requestCountKey = 'news_api_request_count';
  static const String _lastResetDateKey = 'news_api_last_reset_date';
  static const int _maxDailyRequests = 100;

  NewsApiService({
    required String apiKey,
    String baseUrl = 'https://newsapi.org/v2',
    http.Client? httpClient,
    required String deepSeekApiKey,
  })  : _apiKey = apiKey,
        _baseUrl = baseUrl,
        _httpClient = httpClient ?? http.Client(),
        _aiService = DeepSeekAIService(apiKey: deepSeekApiKey);

  /// Check if we can make API requests today
  Future<bool> canMakeRequest() async {
    final prefs = await SharedPreferences.getInstance();
    final today = DateTime.now().toIso8601String().substring(0, 10);
    final lastResetDate = prefs.getString(_lastResetDateKey);

    // Reset counter if it's a new day
    if (lastResetDate != today) {
      await prefs.setString(_lastResetDateKey, today);
      await prefs.setInt(_requestCountKey, 0);
      return true;
    }

    final requestCount = prefs.getInt(_requestCountKey) ?? 0;
    return requestCount < _maxDailyRequests;
  }

  /// Increment request counter
  Future<void> _incrementRequestCount() async {
    final prefs = await SharedPreferences.getInstance();
    final currentCount = prefs.getInt(_requestCountKey) ?? 0;
    await prefs.setInt(_requestCountKey, currentCount + 1);
  }

  /// Get remaining requests for today
  Future<int> getRemainingRequests() async {
    final prefs = await SharedPreferences.getInstance();
    final requestCount = prefs.getInt(_requestCountKey) ?? 0;
    return _maxDailyRequests - requestCount;
  }

  Future<List<Map<String, dynamic>>> fetchNewsFromAPI({
    required String query,
    String language = 'en',
    int pageSize = 20,
    int page = 1,
    String? sources,
    String? domains,
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    // Check if we can make the request
    if (!await canMakeRequest()) {
      throw Exception('Daily API limit reached. Please try again tomorrow.');
    }

    // Build URL with optional parameters
    var urlString = '$_baseUrl/everything?q=$query&language=$language&pageSize=$pageSize&page=$page&apiKey=$_apiKey';

    if (sources != null && sources.isNotEmpty) {
      urlString += '&sources=$sources';
    }

    if (domains != null && domains.isNotEmpty) {
      urlString += '&domains=$domains';
    }

    // Add date parameters for recent news only
    if (fromDate != null) {
      urlString += '&from=${fromDate.toIso8601String()}';
    }

    if (toDate != null) {
      urlString += '&to=${toDate.toIso8601String()}';
    }

    final url = Uri.parse(urlString);
    final response = await _httpClient.get(url);

    // Increment counter after successful request
    await _incrementRequestCount();

    if (response.statusCode == 200) {
      // Parse JSON in background isolate to avoid blocking UI
      final jsonData = await compute(_parseNewsResponse, response.body);

      if (jsonData['status'] == 'ok') {
        return List<Map<String, dynamic>>.from(jsonData['articles']);
      } else {
        throw Exception('Failed to load news: ${jsonData['message']}');
      }
    } else {
      throw Exception('Failed to load news: ${response.statusCode}');
    }
  }

  /// Fetch and enhance automotive news using AI processing with targeted queries
  Future<List<NewsArticle>> fetchAutomotiveNews() async {
    try {
      // Check if we can make requests
      if (!await canMakeRequest()) {
        throw Exception('Daily API limit reached. Remaining requests: ${await getRemainingRequests()}');
      }

      _logger.info('Starting targeted news fetching for last 24 hours...');
      _logger.debug('Remaining API requests: ${await getRemainingRequests()}');

      // Set date range for last 24 hours only
      final now = DateTime.now();
      final yesterday = now.subtract(const Duration(hours: 24));

      _logger.debug('Fetching news from: ${yesterday.toIso8601String()} to: ${now.toIso8601String()}');

      // Use multiple targeted queries to get more relevant content
      final List<NewsArticle> allArticles = [];

      // Define highly targeted queries with Pakistani domains for better relevance
      final targetedQueries = [
        {
          'query': 'Pakistan car prices 2024 OR "vehicle prices Pakistan" OR "automobile cost Pakistan"',
          'domains': 'dawn.com,tribune.com.pk,geo.tv,ary.digital,pakwheels.com,carspiritpk.com',
        },
        {
          'query': 'Pakistan petrol price OR "fuel prices Pakistan" OR "diesel price Pakistan" OR OGRA',
          'domains': 'dawn.com,tribune.com.pk,geo.tv,brecorder.com,thenews.com.pk',
        },
        {
          'query': 'Pakistan road safety OR "traffic accidents Pakistan" OR "highway safety Pakistan"',
          'domains': 'dawn.com,tribune.com.pk,geo.tv,ary.digital,thenews.com.pk',
        },
        {
          'query': 'Pakistan automobile industry OR "car manufacturing Pakistan" OR "automotive sector Pakistan"',
          'domains': 'dawn.com,tribune.com.pk,brecorder.com,pakwheels.com,carspiritpk.com',
        },
        {
          'query': 'Pakistan vehicle tax OR "car registration Pakistan" OR "motor vehicle ordinance Pakistan"',
          'domains': 'dawn.com,tribune.com.pk,geo.tv,thenews.com.pk',
        },
      ];

      // Fetch from each targeted query
      for (int i = 0; i < targetedQueries.length && await canMakeRequest(); i++) {
        final queryData = targetedQueries[i];
        final queryString = queryData['query']!;
        final domains = queryData['domains'];

        _logger.debug('Fetching with targeted query ${i + 1}/${targetedQueries.length}: ${queryString.substring(0, 50)}...');

        try {
          // Fetch raw articles with smaller page size for more targeted results
          final rawArticles = await fetchNewsFromAPI(
            query: queryString,
            pageSize: 20, // Smaller size for more targeted results
            language: 'en',
            domains: domains,
            fromDate: yesterday, // Only last 24 hours
            toDate: now,
          );

          _logger.debug('Query ${i + 1} returned ${rawArticles.length} articles');

          if (rawArticles.isNotEmpty) {
            // Apply strict relevance filtering
            final relevantArticles = _filterForRelevance(rawArticles);
            _logger.debug('After relevance filtering: ${relevantArticles.length} articles');

            if (relevantArticles.isNotEmpty) {
              // Process through AI for enhancement
              final enhancedArticles = await _aiService.enhanceNewsArticles(relevantArticles);
              allArticles.addAll(enhancedArticles);
              _logger.info('Added ${enhancedArticles.length} enhanced articles from query ${i + 1}');
            }
          }

          // Small delay between requests to be respectful to the API
          await Future.delayed(const Duration(milliseconds: 500));

        } catch (e) {
          _logger.error('Error with query ${i + 1}', error: e);
          continue; // Continue with next query
        }
      }

      // Remove duplicates based on URL
      final uniqueArticles = _removeDuplicates(allArticles);
      _logger.info('Final result: ${uniqueArticles.length} unique, relevant articles');

      return uniqueArticles;
    } catch (e) {
      _logger.error('Error in fetchAutomotiveNews', error: e);
      return [];
    }
  }

  /// Apply strict relevance filtering for Pakistani automotive content
  List<Map<String, dynamic>> _filterForRelevance(List<Map<String, dynamic>> articles) {
    return articles.where((article) {
      final title = (article['title'] ?? '').toLowerCase();
      final description = (article['description'] ?? '').toLowerCase();
      final source = (article['source']?['name'] ?? '').toLowerCase();
      final content = '$title $description $source';

      // Must have Pakistani context OR be from Pakistani sources
      final hasPakistaniContext = content.contains('pakistan') ||
                                  content.contains('karachi') ||
                                  content.contains('lahore') ||
                                  content.contains('islamabad') ||
                                  content.contains('rawalpindi') ||
                                  source.contains('pakistan') ||
                                  source.contains('dawn') ||
                                  source.contains('tribune') ||
                                  source.contains('geo') ||
                                  source.contains('ary');

      // Must be automotive/transport related
      final isAutomotiveRelated = content.contains('car') ||
                                  content.contains('vehicle') ||
                                  content.contains('automotive') ||
                                  content.contains('automobile') ||
                                  content.contains('transport') ||
                                  content.contains('traffic') ||
                                  content.contains('road') ||
                                  content.contains('highway') ||
                                  content.contains('fuel') ||
                                  content.contains('petrol') ||
                                  content.contains('diesel') ||
                                  content.contains('price') ||
                                  content.contains('tax') ||
                                  content.contains('registration') ||
                                  content.contains('license') ||
                                  content.contains('driving') ||
                                  content.contains('motor');

      // Exclude irrelevant content
      final isIrrelevant = content.contains('cricket') ||
                          content.contains('politics') ||
                          content.contains('election') ||
                          content.contains('entertainment') ||
                          content.contains('bollywood') ||
                          content.contains('celebrity') ||
                          content.contains('sports') ||
                          content.contains('weather') ||
                          title.contains('removed') ||
                          title.contains('[removed]');

      // Must be very recent (not older than 24 hours)
      final publishedAt = article['publishedAt'] as String?;
      bool isVeryRecent = true;
      if (publishedAt != null) {
        try {
          final publishDate = DateTime.parse(publishedAt);
          final hoursDifference = DateTime.now().difference(publishDate).inHours;
          isVeryRecent = hoursDifference <= 24; // Only last 24 hours
        } catch (e) {
          // If we can't parse the date, assume it's not recent
          isVeryRecent = false;
        }
      }

      return (hasPakistaniContext || isAutomotiveRelated) && !isIrrelevant && isVeryRecent;
    }).toList();
  }

  /// Remove duplicate articles based on URL
  List<NewsArticle> _removeDuplicates(List<NewsArticle> articles) {
    final seen = <String>{};
    return articles.where((article) {
      final url = article.url.toLowerCase();
      if (seen.contains(url)) {
        return false;
      }
      seen.add(url);
      return true;
    }).toList();
  }

  /// Fetch news for specific category (for targeted requests) - Last 24 hours only
  Future<List<NewsArticle>> fetchCategoryNews(String category) async {
    try {
      if (!await canMakeRequest()) {
        throw Exception('Daily API limit reached');
      }

      // Set date range for last 24 hours only
      final now = DateTime.now();
      final yesterday = now.subtract(const Duration(hours: 24));

      String query;
      String domains;

      switch (category.toLowerCase()) {
        case 'car prices':
          query = 'Pakistan car prices 2024 OR "vehicle prices Pakistan" OR "automobile cost Pakistan"';
          domains = 'dawn.com,tribune.com.pk,pakwheels.com,carspiritpk.com';
          break;
        case 'fuel prices':
          query = 'Pakistan petrol price OR "fuel prices Pakistan" OR "diesel price Pakistan" OR OGRA';
          domains = 'dawn.com,tribune.com.pk,geo.tv,brecorder.com';
          break;
        case 'road safety':
          query = 'Pakistan road safety OR "traffic accidents Pakistan" OR "highway safety Pakistan"';
          domains = 'dawn.com,tribune.com.pk,geo.tv,ary.digital';
          break;
        case 'traffic updates':
          query = 'Pakistan traffic updates OR "road conditions Pakistan" OR "highway updates Pakistan"';
          domains = 'dawn.com,tribune.com.pk,geo.tv,thenews.com.pk';
          break;
        case 'industry news':
          query = 'Pakistan automobile industry OR "car manufacturing Pakistan" OR "automotive sector Pakistan"';
          domains = 'dawn.com,tribune.com.pk,brecorder.com,pakwheels.com';
          break;
        case 'taxes & duties':
          query = 'Pakistan vehicle tax OR "car registration Pakistan" OR "motor vehicle ordinance Pakistan"';
          domains = 'dawn.com,tribune.com.pk,geo.tv,thenews.com.pk';
          break;
        case 'accident reports':
          query = 'Pakistan road accident OR "traffic accident Pakistan" OR "vehicle crash Pakistan"';
          domains = 'dawn.com,tribune.com.pk,geo.tv,ary.digital';
          break;
        default:
          query = 'Pakistan automotive OR "car Pakistan" OR "vehicle Pakistan"';
          domains = 'dawn.com,tribune.com.pk,geo.tv,pakwheels.com';
      }

      final rawArticles = await fetchNewsFromAPI(
        query: query,
        pageSize: 20,
        domains: domains,
        fromDate: yesterday, // Only last 24 hours
        toDate: now,
      );

      // Apply strict filtering for 24-hour recency
      final recentArticles = _filterForRelevance(rawArticles);

      return await _aiService.enhanceNewsArticles(recentArticles);
    } catch (e) {
      _logger.error('Error fetching category news', error: e);
      return [];
    }
  }

  void dispose() {
    _httpClient.close();
  }

  /// Static method to parse news response JSON in isolate
  static Map<String, dynamic> _parseNewsResponse(String responseBody) {
    return json.decode(responseBody);
  }
}