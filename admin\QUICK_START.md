# Quick Start Guide - Drive-On Admin Panel

## 🚀 Get Started in 5 Minutes

### Prerequisites
- Node.js 18+ installed
- Firebase project access
- Service account key (already provided)

### Step 1: Install Dependencies
```bash
cd admin
npm install
```

### Step 2: Create First Admin User
```bash
npm run setup-admin
```
Follow the prompts to create your admin account.

### Step 3: Start Development Server
```bash
npm run dev
```

### Step 4: Access Admin Panel
Open [http://localhost:3001](http://localhost:3001) and login with your admin credentials.

## 🎯 What You Get

### Immediate Access To:
- **Dashboard** - Real-time metrics and analytics
- **User Management** - View and manage app users
- **Driver Management** - Review driver applications
- **Partner Management** - Approve business partnerships
- **Job Management** - Moderate job postings
- **News Management** - Create and publish news
- **Forum Management** - Moderate community discussions
- **Query Management** - Handle customer support
- **Chat Management** - Monitor real-time conversations
- **Analytics** - Advanced 3D data visualizations

### Key Features:
✅ **Real-time Data** - Live updates from Firebase
✅ **Responsive Design** - Works on all devices
✅ **Secure Authentication** - Firebase Admin SDK
✅ **Role-based Access** - Granular permissions
✅ **Modern UI** - Yellow theme matching mobile app
✅ **Smooth Animations** - Framer Motion powered
✅ **Document Verification** - AI-powered analysis
✅ **Email Notifications** - Automated workflows

## 🔧 Configuration

### Environment Variables
The `.env.local` file is pre-configured with Firebase settings. Update if needed:

```env
NEXT_PUBLIC_FIREBASE_PROJECT_ID=drive-on-b2af8
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=drive-on-b2af8.firebaseapp.com
# ... other Firebase config
```

### Firebase Security Rules
Ensure your Firestore rules allow admin access:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Admin access
    match /{document=**} {
      allow read, write: if request.auth != null &&
        get(/databases/$(database)/documents/admins/$(request.auth.uid)).data.isAdmin == true;
    }
  }
}
```

## 📱 Mobile App Integration

The admin panel is designed to work seamlessly with your existing Drive-On mobile app:

- **Shared Firebase Project** - Same database and authentication
- **Consistent Branding** - Yellow theme matching mobile app
- **Real-time Sync** - Changes reflect immediately in mobile app
- **Document Verification** - Approve driver documents with email notifications

## 🛠️ Development Commands

```bash
# Development
npm run dev              # Start development server
npm run build           # Build for production
npm run start           # Start production server

# Code Quality
npm run lint            # Run ESLint
npm run type-check      # TypeScript type checking
npm run format          # Format code with Prettier

# Setup
npm run setup-admin     # Create admin user
npm run setup           # Full setup (install + create admin)
```

## 🔐 Security Features

- **Firebase Admin SDK** - Server-side authentication
- **Custom Claims** - Role-based access control
- **Middleware Protection** - Route-level security
- **Audit Logging** - Track all admin actions
- **Input Validation** - Prevent malicious data
- **Rate Limiting** - Protect against abuse

## 📊 Analytics & Monitoring

### Built-in Analytics:
- User registration trends
- Driver verification rates
- Job posting statistics
- Query resolution times
- Geographic distribution
- Performance metrics

### Interactive Visualizations:
- Interactive charts for user distribution
- Animated bar charts for engagement metrics
- Dynamic pie charts for demographics
- Real-time animated counters

## 🎨 UI Components

### Pre-built Components:
- **Data Tables** - Sortable, filterable, paginated
- **Forms** - Validation, auto-save, multi-step
- **Charts** - Line, bar, pie, interactive visualizations
- **Modals** - Confirmation dialogs, image viewers
- **Cards** - Metric cards, info cards, action cards
- **Navigation** - Sidebar, breadcrumbs, tabs

### Theme System:
- **Primary Yellow** - #F59E0B (matching mobile app)
- **Secondary Orange** - #F97316
- **Success Green** - #22C55E
- **Error Red** - #EF4444
- **Warning Yellow** - #F59E0B
- **Info Blue** - #3B82F6

## 🚨 Troubleshooting

### Common Issues:

**1. Firebase Connection Error**
```bash
# Check service account key
ls -la serviceAccountKey.json

# Verify Firebase project ID
grep "project_id" serviceAccountKey.json
```

**2. Admin User Creation Failed**
```bash
# Run setup script again
npm run setup-admin

# Check Firebase Auth console for existing users
```

**3. Build Errors**
```bash
# Clear Next.js cache
rm -rf .next

# Reinstall dependencies
rm -rf node_modules package-lock.json
npm install
```

**4. Permission Denied**
```bash
# Check Firestore security rules
# Ensure admin collection exists
# Verify custom claims are set
```

## 📞 Support

- **Documentation**: Check README.md for detailed information
- **Issues**: Create GitHub issues for bugs
- **Features**: Submit feature requests
- **Security**: Report security issues privately

## 🎉 Next Steps

1. **Explore the Dashboard** - Familiarize yourself with the interface
2. **Configure Settings** - Customize the admin panel
3. **Add Team Members** - Create additional admin accounts
4. **Set Up Notifications** - Configure email alerts
5. **Customize Branding** - Adjust colors and logos
6. **Deploy to Production** - Use Vercel or your preferred platform

---

**Welcome to Drive-On Admin Panel!** 🚗✨

You now have complete control over your ride-hailing platform with a modern, secure, and feature-rich admin interface.
