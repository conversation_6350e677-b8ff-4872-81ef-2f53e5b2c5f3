import 'dart:io';
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../../core/core.dart';
import '../models/forum_message.dart';
import '../services/forum_service.dart';
import 'voice_player.dart';
import 'package:timeago/timeago.dart' as timeago;

class MessageBubble extends StatefulWidget {
  final ForumMessage message;
  final bool isMe;
  final String currentUserId;
  final String forumId;
  final Function(ForumMessage) onReply;

  const MessageBubble({
    super.key,
    required this.message,
    required this.isMe,
    required this.currentUserId,
    required this.forumId,
    required this.onReply,
  });

  @override
  State<MessageBubble> createState() => _MessageBubbleState();
}

class _MessageBubbleState extends State<MessageBubble> {
  bool _showReactions = false;
  bool _showOptions = false;
  bool _isEditing = false;
  late TextEditingController _editController;

  // List of available reactions
  final List<String> _availableReactions = ['👍', '❤️', '😂', '😮', '😢', '👏'];

  @override
  void initState() {
    super.initState();
    _editController = TextEditingController(text: widget.message.text);
  }

  @override
  void dispose() {
    _editController.dispose();
    super.dispose();
  }

  // Add a reaction
  void _addReaction(String emoji) {
    ForumService.addReaction(
      forumId: widget.forumId,
      messageId: widget.message.id,
      reaction: emoji,
    );
    setState(() {
      _showReactions = false;
    });
  }

  // Remove a reaction
  void _removeReaction(String emoji) {
    ForumService.removeReaction(
      forumId: widget.forumId,
      messageId: widget.message.id,
      reaction: emoji,
    );
  }

  // Delete message
  void _deleteMessage() {
    // Show confirmation dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Message'),
        content: const Text('Are you sure you want to delete this message?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              ForumService.deleteMessage(
                forumId: widget.forumId,
                messageId: widget.message.id,
              );
            },
            child: const Text('Delete', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  // Edit message
  void _startEditing() {
    setState(() {
      _isEditing = true;
      _showOptions = false;
    });
  }

  // Save edited message
  void _saveEdit() {
    final newText = _editController.text.trim();
    if (newText.isEmpty || newText == widget.message.text) {
      setState(() {
        _isEditing = false;
      });
      return;
    }

    ForumService.editMessage(
      forumId: widget.forumId,
      messageId: widget.message.id,
      text: newText,
    );

    setState(() {
      _isEditing = false;
    });
  }

  // Cancel editing
  void _cancelEditing() {
    setState(() {
      _isEditing = false;
      _editController.text = widget.message.text;
    });
  }

  Widget _buildPendingVoiceNote(bool isDarkMode) {
    final backgroundColor = widget.isMe
        ? AppColors.primaryYellow.withOpacity(0.1)
        : (isDarkMode ? AppColors.darkSurface.withOpacity(0.7) : Colors.white);

    final primaryColor = widget.isMe
        ? AppColors.primaryYellow
        : (isDarkMode ? Colors.white70 : Colors.grey[800]!);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: widget.isMe
              ? AppColors.primaryYellow.withOpacity(0.3)
              : Colors.grey.withOpacity(0.2),
        ),
      ),
      child: Row(
        children: [
          // Loading indicator or failed icon
          Container(
            width: 36,
            height: 36,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: primaryColor.withOpacity(0.1),
            ),
            child: widget.message.isUploading
                ? SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: primaryColor,
                    ),
                  )
                : const Icon(
                    Icons.error_outline,
                    color: Colors.red,
                    size: 20,
                  ),
          ),

          const SizedBox(width: 8),

          // Voice note info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.message.isUploading ? 'Sending voice note...' : 'Failed to send',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: widget.message.isUploading
                        ? (isDarkMode ? Colors.white70 : Colors.grey[700])
                        : Colors.red,
                  ),
                ),
                Text(
                  '${widget.message.voiceNoteDuration ?? 0}s',
                  style: TextStyle(
                    fontSize: 10,
                    color: isDarkMode ? Colors.white54 : Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPendingImages(bool isDarkMode) {
    return SizedBox(
      height: 160,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: widget.message.attachments!.length,
        padding: const EdgeInsets.symmetric(horizontal: 12),
        itemBuilder: (context, index) {
          final imagePath = widget.message.attachments![index];

          return Container(
            width: 160,
            margin: const EdgeInsets.only(right: 8),
            child: Stack(
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Image.file(
                    File(imagePath),
                    fit: BoxFit.cover,
                    width: 160,
                    height: 160,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        width: 160,
                        height: 160,
                        color: AppColors.primaryYellow.withOpacity(0.2),
                        child: const Icon(Icons.error),
                      );
                    },
                  ),
                ),

                // Loading overlay
                if (widget.message.isUploading)
                  Container(
                    width: 160,
                    height: 160,
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.5),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Center(
                      child: CircularProgressIndicator(
                        color: Colors.white,
                        strokeWidth: 3,
                      ),
                    ),
                  ),

                // Error overlay
                if (!widget.message.isUploading)
                  Container(
                    width: 160,
                    height: 160,
                    decoration: BoxDecoration(
                      color: Colors.red.withOpacity(0.7),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Center(
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.error_outline,
                            color: Colors.white,
                            size: 32,
                          ),
                          SizedBox(height: 4),
                          Text(
                            'Failed to send',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                // Upload progress text
                if (widget.message.isUploading)
                  Positioned(
                    bottom: 8,
                    left: 8,
                    right: 8,
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.black.withOpacity(0.7),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Text(
                        'Uploading...',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                          fontWeight: FontWeight.w500,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
              ],
            ),
          );
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final hasReactions = widget.message.reactions.isNotEmpty;

    // Check if current user has reacted
    final myReactions = <String>[];
    for (var reaction in widget.message.reactions) {
      if (reaction.userIds.contains(widget.currentUserId)) {
        myReactions.add(reaction.emoji);
      }
    }

    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: Column(
        crossAxisAlignment: widget.isMe ? CrossAxisAlignment.end : CrossAxisAlignment.start,
        children: [
          // Sender name
          if (!widget.isMe)
            Padding(
              padding: const EdgeInsets.only(left: 16, bottom: 4),
              child: Text(
                widget.message.senderName,
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 12,
                  color: isDarkMode ? Colors.grey[400] : Colors.grey[700],
                ),
              ),
            ),

          // Reply indicator
          if (widget.message.replyToId != null)
            Container(
              margin: EdgeInsets.only(
                left: widget.isMe ? 0 : 16,
                right: widget.isMe ? 16 : 0,
                bottom: 8,
              ),
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: isDarkMode
                    ? Colors.grey.shade800.withOpacity(0.7)
                    : Colors.grey.shade200,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: isDarkMode ? Colors.grey.shade700 : Colors.grey.shade300,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(Icons.reply, size: 12),
                      const SizedBox(width: 4),
                      Text(
                        'Reply to ${widget.message.replyToSenderName}',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 10,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 2),
                  Text(
                    widget.message.replyToText ?? '',
                    style: const TextStyle(fontSize: 11),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),

          // Message bubble
          GestureDetector(
            onLongPress: () {
              setState(() {
                _showOptions = true;
                _showReactions = false;
              });
            },
            onTap: () {
              if (_showOptions || _showReactions) {
                setState(() {
                  _showOptions = false;
                  _showReactions = false;
                });
              }
            },
            child: Container(
              margin: EdgeInsets.only(
                left: widget.isMe ? 64 : 0,
                right: widget.isMe ? 0 : 64,
              ),
              decoration: BoxDecoration(
                color: widget.isMe
                    ? AppColors.primaryYellow
                    : isDarkMode
                        ? AppColors.darkSurface
                        : Colors.grey.shade200,
                borderRadius: BorderRadius.circular(16),
              ),
              child: Stack(
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // If editing, show text field
                      if (_isEditing)
                        Padding(
                          padding: const EdgeInsets.all(12.0),
                          child: Column(
                            children: [
                              TextField(
                                controller: _editController,
                                decoration: InputDecoration(
                                  filled: true,
                                  fillColor: isDarkMode ? Colors.grey.shade800 : Colors.white,
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                    borderSide: BorderSide.none,
                                  ),
                                ),
                                maxLines: null,
                                autofocus: true,
                              ),
                              const SizedBox(height: 8),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  TextButton(
                                    onPressed: _cancelEditing,
                                    child: const Text('Cancel'),
                                  ),
                                  TextButton(
                                    onPressed: _saveEdit,
                                    child: const Text('Save'),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        )
                      else
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Message text
                            if (widget.message.text.isNotEmpty)
                              Padding(
                                padding: EdgeInsets.only(
                                  left: 12,
                                  right: 12,
                                  top: 12,
                                  bottom: widget.message.attachments?.isNotEmpty == true ||
                                          widget.message.voiceNote != null ? 8 : 12,
                                ),
                                child: Text(
                                  widget.message.text,
                                  style: TextStyle(
                                    fontSize: 16,
                                    color: widget.isMe
                                        ? Colors.black
                                        : isDarkMode
                                            ? Colors.white
                                            : Colors.black,
                                  ),
                                ),
                              ),

                            // Attachments
                            if (widget.message.attachments?.isNotEmpty == true)
                              Padding(
                                padding: const EdgeInsets.only(bottom: 12),
                                child: widget.message.isPending
                                    ? _buildPendingImages(isDarkMode)
                                    : SizedBox(
                                        height: 160,
                                        child: ListView.builder(
                                          scrollDirection: Axis.horizontal,
                                          itemCount: widget.message.attachments!.length,
                                          padding: const EdgeInsets.symmetric(horizontal: 12),
                                          itemBuilder: (context, index) {
                                            return Container(
                                              width: 160,
                                              margin: const EdgeInsets.only(right: 8),
                                              child: ClipRRect(
                                                borderRadius: BorderRadius.circular(8),
                                                child: CachedNetworkImage(
                                                  imageUrl: widget.message.attachments![index],
                                                  fit: BoxFit.cover,
                                                  placeholder: (context, url) => Container(
                                                    color: AppColors.primaryYellow.withOpacity(0.2),
                                                    child: const Center(child: CircularProgressIndicator()),
                                                  ),
                                                  errorWidget: (context, url, error) => Container(
                                                    color: AppColors.primaryYellow.withOpacity(0.2),
                                                    child: const Icon(Icons.error),
                                                  ),
                                                ),
                                              ),
                                            );
                                          },
                                        ),
                                      ),
                              ),

                            // Voice note
                            if (widget.message.voiceNote != null)
                              Padding(
                                padding: const EdgeInsets.only(
                                  left: 12,
                                  right: 12,
                                  bottom: 12,
                                ),
                                child: widget.message.isPending
                                    ? _buildPendingVoiceNote(isDarkMode)
                                    : ForumVoicePlayer(
                                        audioUrl: widget.message.voiceNote!,
                                        duration: Duration(seconds: widget.message.voiceNoteDuration ?? 0),
                                        isDarkMode: isDarkMode,
                                        isMe: widget.isMe,
                                      ),
                              ),
                          ],
                        ),
                    ],
                  ),

                  // Edit indicator
                  if (widget.message.isEdited && !_isEditing)
                    Positioned(
                      right: 8,
                      bottom: 4,
                      child: Text(
                        'edited',
                        style: TextStyle(
                          fontSize: 10,
                          fontStyle: FontStyle.italic,
                          color: widget.isMe
                              ? Colors.black54
                              : isDarkMode
                                  ? Colors.white54
                                  : Colors.black54,
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),

          // Timestamp and reaction buttons
          Padding(
            padding: EdgeInsets.only(
              top: 4,
              left: widget.isMe ? 0 : 16,
              right: widget.isMe ? 16 : 0,
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  timeago.format(widget.message.timestamp),
                  style: TextStyle(
                    fontSize: 11,
                    color: isDarkMode ? Colors.grey[500] : Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),

          // Reactions display
          if (hasReactions)
            Container(
              margin: EdgeInsets.only(
                top: 8,
                left: widget.isMe ? 0 : 16,
                right: widget.isMe ? 16 : 0,
              ),
              decoration: BoxDecoration(
                color: isDarkMode ? Colors.grey.shade800 : Colors.grey.shade200,
                borderRadius: BorderRadius.circular(16),
              ),
              child: Wrap(
                children: widget.message.reactions.map((reaction) {
                  final hasReacted = reaction.userIds.contains(widget.currentUserId);
                  return InkWell(
                    onTap: () {
                      if (hasReacted) {
                        _removeReaction(reaction.emoji);
                      } else {
                        _addReaction(reaction.emoji);
                      }
                    },
                    borderRadius: BorderRadius.circular(16),
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      margin: const EdgeInsets.symmetric(horizontal: 2),
                      decoration: BoxDecoration(
                        color: hasReacted
                            ? AppColors.primaryYellow.withOpacity(0.3)
                            : null,
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(reaction.emoji, style: const TextStyle(fontSize: 16)),
                          const SizedBox(width: 4),
                          Text(
                            reaction.userIds.length.toString(),
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                              color: isDarkMode ? Colors.white70 : Colors.black87,
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                }).toList(),
              ),
            ),

          // Message options popup
          if (_showOptions)
            Container(
              margin: EdgeInsets.only(
                top: 8,
                left: widget.isMe ? 0 : 16,
                right: widget.isMe ? 16 : 0,
              ),
              decoration: BoxDecoration(
                color: isDarkMode ? AppColors.darkSurface : Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Reply option
                  IconButton(
                    icon: const Icon(Icons.reply, size: 20),
                    onPressed: () {
                      widget.onReply(widget.message);
                      setState(() {
                        _showOptions = false;
                      });
                    },
                    tooltip: 'Reply',
                  ),

                  // React option
                  IconButton(
                    icon: const Icon(Icons.emoji_emotions_outlined, size: 20),
                    onPressed: () {
                      setState(() {
                        _showReactions = true;
                        _showOptions = false;
                      });
                    },
                    tooltip: 'React',
                  ),

                  // Edit option (only for own messages)
                  if (widget.isMe)
                    IconButton(
                      icon: const Icon(Icons.edit, size: 20),
                      onPressed: _startEditing,
                      tooltip: 'Edit',
                    ),

                  // Delete option (only for own messages)
                  if (widget.isMe)
                    IconButton(
                      icon: const Icon(Icons.delete, size: 20),
                      onPressed: _deleteMessage,
                      tooltip: 'Delete',
                    ),
                ],
              ),
            ),

          // Reactions picker
          if (_showReactions)
            Container(
              margin: EdgeInsets.only(
                top: 8,
                left: widget.isMe ? 0 : 16,
                right: widget.isMe ? 16 : 0,
              ),
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
              decoration: BoxDecoration(
                color: isDarkMode ? AppColors.darkSurface : Colors.white,
                borderRadius: BorderRadius.circular(24),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: _availableReactions.map((emoji) {
                  final hasReacted = myReactions.contains(emoji);
                  return InkWell(
                    onTap: () {
                      if (hasReacted) {
                        _removeReaction(emoji);
                      } else {
                        _addReaction(emoji);
                      }
                    },
                    borderRadius: BorderRadius.circular(16),
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: hasReacted
                            ? AppColors.primaryYellow.withOpacity(0.3)
                            : null,
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Text(
                        emoji,
                        style: const TextStyle(fontSize: 20),
                      ),
                    ),
                  );
                }).toList(),
              ),
            ),
        ],
      ),
    );
  }
}