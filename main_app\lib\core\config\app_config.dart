import 'package:flutter/foundation.dart';

/// Represents the application's environment
enum Environment {
  /// Development environment for local testing
  development,
  
  /// Staging environment for pre-production testing
  staging,
  
  /// Production environment for released app
  production,
}

/// Class that contains environment-specific configuration
class AppConfig {
  /// Singleton instance of AppConfig
  static late AppConfig _instance;
  
  /// Get the singleton instance of AppConfig
  static AppConfig get instance => _instance;
  
  /// The current environment
  final Environment environment;
  
  /// Base URL for API requests
  final String apiBaseUrl;
  
  /// Application version from pubspec.yaml
  final String appVersion;
  
  /// Build number from pubspec.yaml
  final String buildNumber;
  
  /// Whether verbose logging is enabled
  final bool enableVerboseLogging;
  
  /// Sentry DSN for error reporting
  final String sentryDsn;
  
  /// Firebase project ID
  final String firebaseProjectId;
  
  /// Feature flags for toggling features based on environment
  late final FeatureFlags featureFlags;
  
  /// Constructor that initializes the configuration
  /// This also sets the singleton instance
  AppConfig({
    required this.environment,
    required this.apiBaseUrl,
    required this.appVersion,
    required this.buildNumber,
    required this.enableVerboseLogging,
    required this.sentryDsn,
    required this.firebaseProjectId,
  }) {
    // Initialize environment-specific feature flags
    _initFeatureFlags();
    
    // Set the singleton instance
    _instance = this;
  }
  
  /// Initialize feature flags based on environment
  void _initFeatureFlags() {
    switch (environment) {
      case Environment.development:
        featureFlags = FeatureFlags(
          enableExperimentalFeatures: true,
          enableBetaFeatures: true,
          enablePerformanceMonitoring: false, // Disabled to keep interface clean
          enableCrashReporting: true,
          enableAnalytics: false, // Typically disabled in dev to avoid polluting analytics
        );
        break;
      case Environment.staging:
        featureFlags = FeatureFlags(
          enableExperimentalFeatures: false,
          enableBetaFeatures: true,
          enablePerformanceMonitoring: false, // Disabled to keep interface clean
          enableCrashReporting: true,
          enableAnalytics: true,
        );
        break;
      case Environment.production:
        featureFlags = FeatureFlags(
          enableExperimentalFeatures: false,
          enableBetaFeatures: false,
          enablePerformanceMonitoring: false, // Disabled to keep interface clean
          enableCrashReporting: true,
          enableAnalytics: true,
        );
        break;
    }
  }
  
  /// Check if running in development environment
  bool get isDevelopment => environment == Environment.development;
  
  /// Check if running in staging environment
  bool get isStaging => environment == Environment.staging;
  
  /// Check if running in production environment
  bool get isProduction => environment == Environment.production;
  
  /// Check if debug mode is enabled
  bool get isDebug => kDebugMode;
  
  /// Check if the app is in development mode (development environment and debug mode)
  bool get isDevelopmentMode => isDevelopment && isDebug;
  
  /// Performance overlay is disabled to keep interface clean
  bool get showPerformanceOverlay => false;
  
  /// Get full URL for a specific API endpoint
  String apiUrl(String endpoint) {
    // Ensure endpoint doesn't start with a slash
    final cleanEndpoint = endpoint.startsWith('/') ? endpoint.substring(1) : endpoint;
    // Ensure base URL doesn't end with a slash
    final cleanBaseUrl = apiBaseUrl.endsWith('/') 
        ? apiBaseUrl.substring(0, apiBaseUrl.length - 1) 
        : apiBaseUrl;
    
    return '$cleanBaseUrl/$cleanEndpoint';
  }
  
  /// Get the full app version string (version + build number)
  String get fullVersionString => '$appVersion+$buildNumber';
  
  /// String representation of the config for debugging
  @override
  String toString() {
    return 'AppConfig(environment: $environment, '
        'apiBaseUrl: $apiBaseUrl, '
        'appVersion: $appVersion, '
        'buildNumber: $buildNumber, '
        'enableVerboseLogging: $enableVerboseLogging, '
        'sentryDsn: ${sentryDsn.isNotEmpty ? '[REDACTED]' : 'not set'}, '
        'firebaseProjectId: $firebaseProjectId, '
        'featureFlags: $featureFlags)';
  }
}

/// Feature flags for toggling features based on environment
class FeatureFlags {
  /// Whether experimental features are enabled
  final bool enableExperimentalFeatures;
  
  /// Whether beta features are enabled
  final bool enableBetaFeatures;
  
  /// Whether performance monitoring is enabled
  final bool enablePerformanceMonitoring;
  
  /// Whether crash reporting is enabled
  final bool enableCrashReporting;
  
  /// Whether analytics tracking is enabled
  final bool enableAnalytics;
  
  /// Constructor for initializing feature flags
  FeatureFlags({
    required this.enableExperimentalFeatures,
    required this.enableBetaFeatures,
    required this.enablePerformanceMonitoring,
    required this.enableCrashReporting,
    required this.enableAnalytics,
  });
  
  /// Create a new instance with some values changed
  FeatureFlags copyWith({
    bool? enableExperimentalFeatures,
    bool? enableBetaFeatures,
    bool? enablePerformanceMonitoring,
    bool? enableCrashReporting,
    bool? enableAnalytics,
  }) {
    return FeatureFlags(
      enableExperimentalFeatures: enableExperimentalFeatures ?? this.enableExperimentalFeatures,
      enableBetaFeatures: enableBetaFeatures ?? this.enableBetaFeatures,
      enablePerformanceMonitoring: enablePerformanceMonitoring ?? this.enablePerformanceMonitoring,
      enableCrashReporting: enableCrashReporting ?? this.enableCrashReporting,
      enableAnalytics: enableAnalytics ?? this.enableAnalytics,
    );
  }
  
  /// String representation of the feature flags for debugging
  @override
  String toString() {
    return 'FeatureFlags(experimental: $enableExperimentalFeatures, '
        'beta: $enableBetaFeatures, '
        'performance: $enablePerformanceMonitoring, '
        'crash: $enableCrashReporting, '
        'analytics: $enableAnalytics)';
  }
} 