const admin = require('firebase-admin');
const readline = require('readline');

// Initialize Firebase Admin SDK
const serviceAccount = require('../serviceAccountKey.json');

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
  databaseURL: "https://drive-on-b2af8-default-rtdb.firebaseio.com"
});

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(query) {
  return new Promise(resolve => rl.question(query, resolve));
}

async function createAdminUser() {
  try {
    console.log('🚀 Drive-On Admin Panel Setup');
    console.log('================================\n');

    const email = await question('Enter admin email: ');
    const password = await question('Enter admin password (min 6 characters): ');
    const displayName = await question('Enter admin display name: ');

    console.log('\n⏳ Creating admin user...');

    // Create user in Firebase Auth
    const userRecord = await admin.auth().createUser({
      email,
      password,
      displayName,
      emailVerified: true,
    });

    console.log('✅ User created in Firebase Auth');

    // Set custom claims for admin role
    await admin.auth().setCustomUserClaims(userRecord.uid, { 
      admin: true,
      role: 'admin',
      permissions: [
        'read', 
        'write', 
        'delete', 
        'manage_users', 
        'manage_drivers', 
        'manage_partners',
        'manage_jobs',
        'manage_news',
        'manage_forums',
        'manage_queries',
        'manage_chats',
        'view_analytics',
        'manage_settings'
      ]
    });

    console.log('✅ Admin claims set');

    // Create admin document in Firestore
    await admin.firestore().collection('admins').doc(userRecord.uid).set({
      email,
      displayName,
      role: 'admin',
      isActive: true,
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      permissions: [
        'read', 
        'write', 
        'delete', 
        'manage_users', 
        'manage_drivers', 
        'manage_partners',
        'manage_jobs',
        'manage_news',
        'manage_forums',
        'manage_queries',
        'manage_chats',
        'view_analytics',
        'manage_settings'
      ],
      lastLoginAt: null,
      createdBy: 'setup-script'
    });

    console.log('✅ Admin document created in Firestore');

    console.log('\n🎉 Admin user created successfully!');
    console.log('================================');
    console.log(`Email: ${email}`);
    console.log(`UID: ${userRecord.uid}`);
    console.log(`Display Name: ${displayName}`);
    console.log('\n📝 You can now login to the admin panel with these credentials.');
    console.log('🌐 Admin Panel URL: http://localhost:3001');

  } catch (error) {
    console.error('\n❌ Error creating admin user:', error.message);
    
    if (error.code === 'auth/email-already-exists') {
      console.log('\n💡 The email address is already in use. Try with a different email.');
    } else if (error.code === 'auth/weak-password') {
      console.log('\n💡 Password is too weak. Please use at least 6 characters.');
    } else if (error.code === 'auth/invalid-email') {
      console.log('\n💡 Invalid email address format.');
    }
  } finally {
    rl.close();
    process.exit();
  }
}

// Check if Firebase is properly initialized
console.log('🔧 Checking Firebase connection...');
admin.firestore().collection('test').limit(1).get()
  .then(() => {
    console.log('✅ Firebase connection successful\n');
    createAdminUser();
  })
  .catch((error) => {
    console.error('❌ Firebase connection failed:', error.message);
    console.log('\n💡 Please check your serviceAccountKey.json file and Firebase configuration.');
    rl.close();
    process.exit(1);
  });
