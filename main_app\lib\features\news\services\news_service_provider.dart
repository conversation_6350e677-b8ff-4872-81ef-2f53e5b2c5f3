import '../data/sources/news_api_service.dart';
import '../data/repositories/news_repository.dart';
import 'news_scheduler_service.dart';
import '../../../core/utils/app_logger.dart';

/// Centralized provider for all news services with API keys
class NewsServiceProvider {
  // API Keys
  static const String _newsApiKey = '********************************';
  static const String _deepSeekApiKey = '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';

  // Service instances
  static NewsApiService? _newsApiService;
  static NewsRepository? _newsRepository;
  static NewsSchedulerService? _schedulerService;
  static final AppLogger _logger = AppLogger('NewsServiceProvider');

  /// Initialize all news services
  static Future<void> initialize() async {
    // Check if already initialized
    if (isInitialized) {
      _logger.info('NewsServiceProvider already initialized, skipping...');
      return;
    }

    try {
      _logger.info('Starting NewsServiceProvider initialization...');

      // Initialize NewsAPI service with both API keys
      _logger.debug('Initializing NewsAPI service...');
      _newsApiService = NewsApiService(
        apiKey: _newsApiKey,
        deepSeekApiKey: _deepSeekApiKey,
      );
      _logger.info('NewsAPI service initialized');

      // Initialize repository with the API service
      _logger.debug('Initializing news repository...');
      _newsRepository = NewsRepository(
        newsApiService: _newsApiService,
      );
      _logger.info('News repository initialized');

      // Initialize scheduler service
      _logger.debug('Initializing scheduler service...');
      _schedulerService = NewsSchedulerService(
        newsRepository: _newsRepository!,
        newsApiService: _newsApiService!,
      );
      _logger.info('Scheduler service initialized');

      // Start the automatic refresh cycle
      _logger.debug('Starting automatic refresh cycle...');
      await _schedulerService!.initialize();
      _logger.info('Automatic refresh cycle started');

      _logger.info('News services initialized successfully');
      _logger.info('API Keys configured:');
      _logger.info('   - NewsAPI: ${_newsApiKey.substring(0, 8)}...');
      _logger.info('   - DeepSeek R1 Zero: ${_deepSeekApiKey.substring(0, 12)}...');
    } catch (e, stackTrace) {
      _logger.error('Error during NewsServiceProvider initialization', error: e, stackTrace: stackTrace);

      // Clean up any partially initialized services
      _schedulerService?.dispose();
      _newsApiService?.dispose();
      _schedulerService = null;
      _newsApiService = null;
      _newsRepository = null;

      rethrow;
    }
  }

  /// Get NewsAPI service instance
  static NewsApiService get newsApiService {
    if (_newsApiService == null) {
      throw Exception('NewsServiceProvider not initialized. Call initialize() first.');
    }
    return _newsApiService!;
  }

  /// Get news repository instance
  static NewsRepository get newsRepository {
    if (_newsRepository == null) {
      throw Exception('NewsServiceProvider not initialized. Call initialize() first.');
    }
    return _newsRepository!;
  }

  /// Get scheduler service instance
  static NewsSchedulerService get schedulerService {
    if (_schedulerService == null) {
      throw Exception('NewsServiceProvider not initialized. Call initialize() first.');
    }
    return _schedulerService!;
  }

  /// Force refresh news manually
  static Future<void> forceRefreshNews() async {
    await schedulerService.forceRefresh();
  }

  /// Get refresh status information
  static Future<Map<String, dynamic>> getRefreshStatus() async {
    return await schedulerService.getRefreshStatus();
  }

  /// Get remaining API requests for today
  static Future<int> getRemainingApiRequests() async {
    return await newsApiService.getRemainingRequests();
  }

  /// Check if services are initialized
  static bool get isInitialized {
    return _newsApiService != null &&
           _newsRepository != null &&
           _schedulerService != null;
  }

  /// Dispose all services
  static void dispose() {
    _schedulerService?.dispose();
    _newsApiService?.dispose();
    _schedulerService = null;
    _newsApiService = null;
    _newsRepository = null;
    _logger.info('News services disposed');
  }
}
