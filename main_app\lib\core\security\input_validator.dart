/// Utility class providing methods for validating and sanitizing user inputs
class InputValidator {
  // Email validation regex
  static final RegExp _emailRegex = RegExp(
    r'^[a-zA-Z0-9.!#$%&*+/=?^_`{|}~-]+@[a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)*$',
  );
  
  // Pakistan phone number validation regex
  static final RegExp _pakistanPhoneRegex = RegExp(r'^03\d{2}[\s-]?\d{7}$');
  
  // Pakistan CNIC validation regex (13 digits)
  static final RegExp _pakistanCnicRegex = RegExp(r'^\d{13}$');
  
  // URL validation regex
  static final RegExp _urlRegex = RegExp(
    r'^(https?:\/\/)?(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$',
  );
  
  // Password strength regex (at least 8 chars, 1 uppercase, 1 lowercase, 1 number)
  static final RegExp _strongPasswordRegex = RegExp(
    r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d\W]{8,}$',
  );
  
  /// Validates an email address
  static bool isValidEmail(String email) {
    return _emailRegex.hasMatch(email.trim());
  }
  
  /// Validates a Pakistan phone number
  static bool isValidPakistanPhone(String phone) {
    return _pakistanPhoneRegex.hasMatch(phone.trim());
  }
  
  /// Validates a Pakistan CNIC
  static bool isValidCnic(String cnic) {
    // Remove any dashes before validation
    final cleanCnic = cnic.replaceAll('-', '');
    return _pakistanCnicRegex.hasMatch(cleanCnic);
  }
  
  /// Validates a URL
  static bool isValidUrl(String url) {
    return _urlRegex.hasMatch(url.trim());
  }
  
  /// Validates if password meets strength requirements
  static bool isStrongPassword(String password) {
    return _strongPasswordRegex.hasMatch(password);
  }
  
  /// Validates not empty
  static bool notEmpty(String? value) {
    return value != null && value.trim().isNotEmpty;
  }
  
  /// Validates a number is in range
  static bool isInRange(num value, num min, num max) {
    return value >= min && value <= max;
  }
  
  /// Validates text is within length limits
  static bool isValidLength(String text, int minLength, int maxLength) {
    final length = text.trim().length;
    return length >= minLength && length <= maxLength;
  }
  
  /// Sanitizes user input by removing potentially harmful characters
  static String sanitizeInput(String input) {
    // Remove script tags and other potentially dangerous HTML/JS
    var sanitized = input.replaceAll(RegExp(r'<script.*?>.*?</script>', caseSensitive: false), '');
    sanitized = sanitized.replaceAll(RegExp(r'<.*?>', caseSensitive: false), '');
    
    // Remove common SQL injection patterns
    sanitized = sanitized.replaceAll(RegExp(r'(\bSELECT\b|\bUNION\b|\bINSERT\b|\bDROP\b)', caseSensitive: false), '');
    
    return sanitized.trim();
  }
  
  /// Formats a phone number for consistent storage (removes spaces, dashes)
  static String formatPhoneNumber(String phone) {
    return phone.replaceAll(RegExp(r'[\s-]'), '');
  }
  
  /// Formats a CNIC for consistent storage (removes dashes)
  static String formatCnic(String cnic) {
    return cnic.replaceAll('-', '');
  }
  
  /// Get validation error message for email
  static String? validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please enter an email address';
    }
    if (!isValidEmail(value)) {
      return 'Please enter a valid email address';
    }
    return null;
  }
  
  /// Get validation error message for password
  static String? validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please enter a password';
    }
    if (!isStrongPassword(value)) {
      return 'Password must be at least 8 characters with uppercase, lowercase, and number';
    }
    return null;
  }
  
  /// Get validation error message for Pakistan phone number
  static String? validatePakistanPhone(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please enter a phone number';
    }
    if (!isValidPakistanPhone(value)) {
      return 'Please enter a valid Pakistani mobile number (03XX-XXXXXXX)';
    }
    return null;
  }
  
  /// Get validation error message for CNIC
  static String? validateCnic(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please enter CNIC number';
    }
    if (!isValidCnic(value)) {
      return 'Please enter a valid 13-digit CNIC number';
    }
    return null;
  }
  
  /// Get validation error message for required fields
  static String? validateRequired(String? value, String fieldName) {
    if (value == null || value.trim().isEmpty) {
      return 'Please enter $fieldName';
    }
    return null;
  }
  
  /// Get validation error message for numeric input
  static String? validateNumeric(String? value, String fieldName) {
    if (value == null || value.isEmpty) {
      return 'Please enter $fieldName';
    }
    if (int.tryParse(value) == null) {
      return 'Please enter a valid number for $fieldName';
    }
    return null;
  }
} 