'use client'

import { Filter } from 'lucide-react'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'

interface JobFiltersProps {
  filters: {
    category: string
    status: string
    city: string
    salaryRange: string
  }
  onFiltersChange: (filters: any) => void
}

export function JobFilters({ filters, onFiltersChange }: JobFiltersProps) {
  const updateFilter = (key: string, value: string) => {
    onFiltersChange({
      ...filters,
      [key]: value
    })
  }

  return (
    <div className="flex space-x-2">
      {/* Category Filter */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" size="sm">
            <Filter className="w-4 h-4 mr-2" />
            Category: {filters.category === 'all' ? 'All' : filters.category}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          <DropdownMenuLabel>Filter by Category</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={() => updateFilter('category', 'all')}>
            All Categories
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('category', 'InDrive')}>
            InDrive
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('category', 'Household')}>
            Household
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('category', 'Company')}>
            Company
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('category', 'Part-time')}>
            Part-time
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Status Filter */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" size="sm">
            Status: {filters.status === 'all' ? 'All' : filters.status}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          <DropdownMenuLabel>Filter by Status</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={() => updateFilter('status', 'all')}>
            All Status
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('status', 'active')}>
            Active
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('status', 'inactive')}>
            Inactive
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('status', 'expired')}>
            Expired
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('status', 'filled')}>
            Filled
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* City Filter */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" size="sm">
            City: {filters.city === 'all' ? 'All' : filters.city}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          <DropdownMenuLabel>Filter by City</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={() => updateFilter('city', 'all')}>
            All Cities
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('city', 'Karachi')}>
            Karachi
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('city', 'Lahore')}>
            Lahore
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('city', 'Islamabad')}>
            Islamabad
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('city', 'Rawalpindi')}>
            Rawalpindi
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('city', 'Faisalabad')}>
            Faisalabad
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Salary Range Filter */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" size="sm">
            Salary: {filters.salaryRange === 'all' ? 'All' : filters.salaryRange}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          <DropdownMenuLabel>Filter by Salary Range</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={() => updateFilter('salaryRange', 'all')}>
            All Ranges
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('salaryRange', '0-25000')}>
            $0 - $25,000
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('salaryRange', '25000-50000')}>
            $25,000 - $50,000
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('salaryRange', '50000-75000')}>
            $50,000 - $75,000
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('salaryRange', '75000+')}>
            $75,000+
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  )
}
