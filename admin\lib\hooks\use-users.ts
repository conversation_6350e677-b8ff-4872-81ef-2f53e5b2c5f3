import { useState, useEffect } from 'react'
import { collection, query, orderBy, onSnapshot, doc, updateDoc, deleteDoc, addDoc, Timestamp } from 'firebase/firestore'
import { db } from '@/lib/firebase/config'

// Helper function to safely convert Firestore data to Date
function toDate(value: any): Date | null {
  if (!value) return null

  // If it's already a Date object
  if (value instanceof Date) return value

  // If it's a Firestore Timestamp
  if (value && typeof value.toDate === 'function') {
    return value.toDate()
  }

  // If it's a timestamp number
  if (typeof value === 'number') {
    return new Date(value)
  }

  // If it's a string that can be parsed as a date
  if (typeof value === 'string') {
    const parsed = new Date(value)
    return isNaN(parsed.getTime()) ? null : parsed
  }

  // If it's an object with seconds (Firestore timestamp format)
  if (value && typeof value === 'object' && value.seconds) {
    return new Date(value.seconds * 1000)
  }

  return null
}

interface User {
  id: string
  email: string
  displayName: string
  role: 'user' | 'admin'
  isActive: boolean
  createdAt: Date
  lastLoginAt: Date | null
  profileImage?: string
  phoneNumber?: string
  city?: string
}

// Simple cache for users data
let usersCache: User[] | null = null
let usersCacheTimestamp = 0
const CACHE_DURATION = 2 * 60 * 1000 // 2 minutes

export function useUsers() {
  const [users, setUsers] = useState<User[]>(usersCache || [])
  const [isLoading, setIsLoading] = useState(!usersCache)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    // Check if we have fresh cached data
    const now = Date.now()
    if (usersCache && (now - usersCacheTimestamp) < CACHE_DURATION) {
      setUsers(usersCache)
      setIsLoading(false)
      return
    }

    // Set up real-time listener immediately (no delay)
    const usersQuery = query(
      collection(db, 'users'),
      orderBy('createdAt', 'desc')
    )

    const unsubscribe = onSnapshot(
      usersQuery,
      (snapshot) => {
        const usersData = snapshot.docs.map(doc => {
          const data = doc.data()
          return {
            id: doc.id,
            email: data.email || '',
            displayName: data.displayName || '',
            role: data.role || 'user',
            isActive: data.isActive ?? true,
            createdAt: toDate(data.createdAt) || new Date(),
            lastLoginAt: toDate(data.lastLoginAt),
            profileImage: data.profileImage,
            phoneNumber: data.phoneNumber,
            city: data.city,
          } as User
        })

        // Update cache
        usersCache = usersData
        usersCacheTimestamp = Date.now()

        setUsers(usersData)
        setIsLoading(false)
        setError(null)
      },
      (error) => {
        console.error('Error fetching users:', error)
        setError('Failed to fetch users')
        setIsLoading(false)
      }
    )

    return unsubscribe
  }, [])

  const updateUser = async (userId: string, updates: Partial<User>) => {
    try {
      const userRef = doc(db, 'users', userId)
      const updateData: any = { ...updates }

      // Remove undefined values to prevent Firestore errors
      Object.keys(updateData).forEach(key => {
        if (updateData[key] === undefined) {
          delete updateData[key]
        }
      })

      // Convert dates to Firestore timestamps
      if (updateData.createdAt instanceof Date) {
        updateData.createdAt = Timestamp.fromDate(updateData.createdAt)
      }
      if (updateData.lastLoginAt instanceof Date) {
        updateData.lastLoginAt = Timestamp.fromDate(updateData.lastLoginAt)
      }

      updateData.updatedAt = Timestamp.now()

      await updateDoc(userRef, updateData)
    } catch (error) {
      console.error('Error updating user:', error)
      throw error
    }
  }

  const deleteUser = async (userId: string) => {
    try {
      const userRef = doc(db, 'users', userId)
      await deleteDoc(userRef)
    } catch (error) {
      console.error('Error deleting user:', error)
      throw error
    }
  }

  const createUser = async (userData: Partial<User>) => {
    try {
      const newUserData = {
        ...userData,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now(),
        isActive: userData.isActive ?? true,
        role: userData.role || 'user',
      }

      await addDoc(collection(db, 'users'), newUserData)
    } catch (error) {
      console.error('Error creating user:', error)
      throw error
    }
  }

  return {
    users,
    isLoading,
    error,
    updateUser,
    deleteUser,
    createUser,
  }
}
