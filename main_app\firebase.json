{"firestore": {"rules": "firestore.rules", "indexes": "firestore.indexes.json"}, "storage": {"rules": "storage.rules"}, "functions": {"predeploy": ["npm --prefix \"$RESOURCE_DIR\" run lint", "npm --prefix \"$RESOURCE_DIR\" run build"]}, "emulators": {"auth": {"port": 9099}, "firestore": {"port": 8080}, "hosting": {"port": 5000}, "storage": {"port": 9199}, "ui": {"enabled": true}, "singleProjectMode": true}}