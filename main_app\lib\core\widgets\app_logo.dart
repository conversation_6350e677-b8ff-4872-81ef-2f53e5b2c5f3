import 'package:flutter/material.dart';
import '../theme/colors.dart';

class AppLogo extends StatelessWidget {
  final double size;
  final bool isVertical;

  const AppLogo({
    super.key,
    this.size = 80,
    this.isVertical = false,
  });

  @override
  Widget build(BuildContext context) {
    return isVertical ? _buildVerticalLogo(context) : _buildHorizontalLogo(context);
  }

  Widget _buildHorizontalLogo(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildLogoIcon(context),
        const SizedBox(width: 16),
        _buildLogoText(context),
      ],
    );
  }

  Widget _buildVerticalLogo(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildLogoIcon(context),
        const SizedBox(height: 20),
        _buildLogoText(context),
      ],
    );
  }

  Widget _buildLogoIcon(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final iconColor = isDarkMode ? Colors.white.withOpacity(0.9) : Colors.black87;
    
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: AppColors.primaryYellow,
        borderRadius: BorderRadius.circular(size * 0.2),
        boxShadow: [
          BoxShadow(
            color: AppColors.primaryYellow.withOpacity(isDarkMode ? 0.5 : 0.4),
            blurRadius: 15,
            offset: const Offset(0, 6),
            spreadRadius: 2,
          ),
        ],
      ),
      child: Stack(
        children: [
          // Background glow effect
          Positioned.fill(
            child: DecoratedBox(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(size * 0.2),
                gradient: const RadialGradient(
                  colors: [
                    AppColors.secondaryYellow,
                    AppColors.primaryYellow,
                  ],
                  center: Alignment.topLeft,
                  radius: 1.0,
                ),
              ),
            ),
          ),
          // Car icon
          Center(
            child: Icon(
              Icons.directions_car_rounded,
              color: iconColor,
              size: size * 0.55,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLogoText(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final titleColor = isDarkMode ? Colors.white.withOpacity(0.9) : Colors.black87;
    final subtitleColor = isDarkMode ? Colors.white70 : Colors.black54;
    
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          'Drive-On',
          style: TextStyle(
            fontSize: size * 0.38,
            fontWeight: FontWeight.bold,
            color: titleColor,
            letterSpacing: 1.2,
            height: 1.1,
          ),
        ),
        Text(
          'Your journey companion',
          style: TextStyle(
            fontSize: size * 0.16,
            fontWeight: FontWeight.w500,
            color: subtitleColor,
            letterSpacing: 0.5,
          ),
        ),
      ],
    );
  }
} 