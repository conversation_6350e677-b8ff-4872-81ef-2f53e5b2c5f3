@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@100;200;300;400;500;600;700;800&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: var(--primary-hue) 95.8% 53.1%;
    --primary-foreground: 26 83.3% 14.1%;
    --secondary: 210 40% 98%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 98%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 98%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: var(--primary-hue) 95.8% 53.1%;
    --radius: 0.5rem;

    /* Dynamic theme variables */
    --primary-hue: 45; /* Default yellow */
    --base-font-size: 16px; /* Default medium */
    --motion-duration: 0.2s;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: var(--primary-hue) 95.8% 53.1%;
    --primary-foreground: 26 83.3% 14.1%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: var(--primary-hue) 95.8% 53.1%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
    font-size: var(--base-font-size);
  }

  /* Dynamic font size classes */
  .text-dynamic {
    font-size: var(--base-font-size);
  }

  .text-dynamic-sm {
    font-size: calc(var(--base-font-size) * 0.875);
  }

  .text-dynamic-lg {
    font-size: calc(var(--base-font-size) * 1.125);
  }

  .text-dynamic-xl {
    font-size: calc(var(--base-font-size) * 1.25);
  }

  .text-dynamic-2xl {
    font-size: calc(var(--base-font-size) * 1.5);
  }
}

@layer components {
  /* Custom scrollbar - Light mode */
  .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: rgba(245, 158, 11, 0.3) transparent;
  }

  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: rgba(245, 158, 11, 0.3);
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: rgba(245, 158, 11, 0.5);
  }

  /* Custom scrollbar - Dark mode */
  .dark .custom-scrollbar {
    scrollbar-color: rgba(245, 158, 11, 0.4) transparent;
  }

  .dark .custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: rgba(245, 158, 11, 0.4);
  }

  .dark .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: rgba(245, 158, 11, 0.6);
  }

  /* Dynamic gradient backgrounds using CSS variables */
  .gradient-primary {
    background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(calc(var(--primary-hue) + 15) 85% 55%) 100%);
  }

  .gradient-secondary {
    background: linear-gradient(135deg, hsl(calc(var(--primary-hue) + 15) 85% 55%) 0%, hsl(calc(var(--primary-hue) + 30) 80% 60%) 100%);
  }

  .gradient-card {
    background: linear-gradient(135deg, hsla(var(--primary-hue), 95%, 53%, 0.05) 0%, hsla(calc(var(--primary-hue) + 15), 85%, 55%, 0.05) 100%);
  }

  /* Accessibility features */
  .reduce-motion * {
    animation-duration: var(--motion-duration) !important;
    transition-duration: var(--motion-duration) !important;
  }

  .high-contrast {
    filter: contrast(1.2);
  }

  .compact-mode {
    --spacing-scale: 0.75;
  }

  .compact-mode .p-4 {
    padding: calc(1rem * var(--spacing-scale, 1));
  }

  .compact-mode .p-6 {
    padding: calc(1.5rem * var(--spacing-scale, 1));
  }

  .compact-mode .space-y-4 > * + * {
    margin-top: calc(1rem * var(--spacing-scale, 1));
  }

  .compact-mode .space-y-6 > * + * {
    margin-top: calc(1.5rem * var(--spacing-scale, 1));
  }

  /* Instant navigation - no delays, no animations */
  .instant-nav * {
    transition: none !important;
    animation: none !important;
    transform: none !important;
  }

  .instant-nav .motion-reduce {
    animation: none !important;
    transition: none !important;
  }

  /* Force instant display */
  .instant-load {
    opacity: 1 !important;
    transform: none !important;
    transition: none !important;
    animation: none !important;
  }

  /* Instant page transitions */
  .instant-nav .page-transition {
    transition: none !important;
    animation: none !important;
  }

  /* Glass morphism effect */
  .glass {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .dark .glass {
    background: rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  /* Ensure consistent dark mode for all elements */
  .dark {
    color-scheme: dark;
  }

  /* Fix any remaining hardcoded colors */
  .dark .bg-white {
    background-color: hsl(var(--background)) !important;
  }

  .dark .text-gray-900 {
    color: hsl(var(--foreground)) !important;
  }

  .dark .text-gray-500 {
    color: hsl(var(--muted-foreground)) !important;
  }

  .dark .border-gray-200 {
    border-color: hsl(var(--border)) !important;
  }

  .dark .border-gray-300 {
    border-color: hsl(var(--border)) !important;
  }

  .dark .bg-gray-50 {
    background-color: hsl(var(--muted)) !important;
  }

  .dark .bg-gray-100 {
    background-color: hsl(var(--muted)) !important;
  }

  /* Loading animations */
  .loading-dots {
    display: inline-block;
    position: relative;
    width: 80px;
    height: 80px;
  }

  .loading-dots div {
    position: absolute;
    top: 33px;
    width: 13px;
    height: 13px;
    border-radius: 50%;
    background: #F59E0B;
    animation-timing-function: cubic-bezier(0, 1, 1, 0);
  }

  .loading-dots div:nth-child(1) {
    left: 8px;
    animation: loading-dots1 0.6s infinite;
  }

  .loading-dots div:nth-child(2) {
    left: 8px;
    animation: loading-dots2 0.6s infinite;
  }

  .loading-dots div:nth-child(3) {
    left: 32px;
    animation: loading-dots2 0.6s infinite;
  }

  .loading-dots div:nth-child(4) {
    left: 56px;
    animation: loading-dots3 0.6s infinite;
  }

  @keyframes loading-dots1 {
    0% { transform: scale(0); }
    100% { transform: scale(1); }
  }

  @keyframes loading-dots3 {
    0% { transform: scale(1); }
    100% { transform: scale(0); }
  }

  @keyframes loading-dots2 {
    0% { transform: translate(0, 0); }
    100% { transform: translate(24px, 0); }
  }

  /* Hover effects */
  .hover-lift {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
  }

  .hover-lift:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  }

  /* Status indicators */
  .status-online {
    @apply bg-green-500;
    box-shadow: 0 0 0 2px rgba(34, 197, 94, 0.3);
  }

  .status-offline {
    @apply bg-gray-400;
    box-shadow: 0 0 0 2px rgba(156, 163, 175, 0.3);
  }

  .status-busy {
    @apply bg-red-500;
    box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.3);
  }

  .status-away {
    @apply bg-yellow-500;
    box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.3);
  }
}

/* React Quill Editor Styles */
.ql-editor {
  min-height: 200px;
}

.ql-toolbar {
  border-top: 1px solid #e5e7eb;
  border-left: 1px solid #e5e7eb;
  border-right: 1px solid #e5e7eb;
}

.ql-container {
  border-bottom: 1px solid #e5e7eb;
  border-left: 1px solid #e5e7eb;
  border-right: 1px solid #e5e7eb;
}



/* Chart animations */
.recharts-wrapper {
  animation: fade-in 0.8s ease-out;
}

/* Table styles */
.data-table {
  @apply w-full border-collapse;
}

.data-table th {
  @apply bg-gray-50 px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
}

.data-table td {
  @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
}

.data-table tr:nth-child(even) {
  @apply bg-gray-50;
}

.data-table tr:hover {
  @apply bg-yellow-50;
}

/* Mobile responsive utilities */
@media (max-width: 768px) {
  .mobile-hidden {
    display: none;
  }

  .mobile-full {
    width: 100%;
  }

  .mobile-stack {
    flex-direction: column;
  }
}
