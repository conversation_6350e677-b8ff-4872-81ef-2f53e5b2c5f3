import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import '../services/network_state_manager.dart';
import '../theme/colors.dart';

/// Widget to display offline status and sync progress
/// 
/// Features:
/// - Real-time connectivity status
/// - Sync progress indicator
/// - Offline mode indicator
/// - Manual sync trigger
class OfflineStatusWidget extends StatefulWidget {
  final bool showSyncButton;
  final bool compact;

  const OfflineStatusWidget({
    Key? key,
    this.showSyncButton = true,
    this.compact = false,
  }) : super(key: key);

  @override
  State<OfflineStatusWidget> createState() => _OfflineStatusWidgetState();
}

class _OfflineStatusWidgetState extends State<OfflineStatusWidget>
    with SingleTickerProviderStateMixin {
  final NetworkStateManager _networkManager = GetIt.instance<NetworkStateManager>();
  
  late AnimationController _animationController;
  late Animation<double> _pulseAnimation;
  
  bool _isOnline = true;
  bool _isSyncing = false;
  int _syncQueueLength = 0;

  @override
  void initState() {
    super.initState();
    
    _animationController = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    );
    
    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _initializeState();
    _listenToNetworkChanges();
  }

  void _initializeState() {
    _isOnline = _networkManager.isOnline;
    _syncQueueLength = _networkManager.syncQueueLength;
    
    if (!_isOnline) {
      _animationController.repeat(reverse: true);
    }
  }

  void _listenToNetworkChanges() {
    // Listen to connectivity changes
    _networkManager.connectivityStream.listen((isOnline) {
      if (mounted) {
        setState(() {
          _isOnline = isOnline;
        });
        
        if (!isOnline) {
          _animationController.repeat(reverse: true);
        } else {
          _animationController.stop();
          _animationController.reset();
        }
      }
    });

    // Listen to network events
    _networkManager.networkEventsStream.listen((event) {
      if (mounted) {
        setState(() {
          switch (event) {
            case NetworkEvent.syncStarted:
              _isSyncing = true;
              break;
            case NetworkEvent.syncCompleted:
            case NetworkEvent.syncFailed:
              _isSyncing = false;
              _syncQueueLength = _networkManager.syncQueueLength;
              break;
            default:
              break;
          }
        });
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    if (widget.compact) {
      return _buildCompactWidget(isDarkMode);
    }
    
    return _buildFullWidget(isDarkMode);
  }

  Widget _buildCompactWidget(bool isDarkMode) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: _getStatusColor().withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _getStatusColor().withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          AnimatedBuilder(
            animation: _pulseAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: _isOnline ? 1.0 : _pulseAnimation.value,
                child: Icon(
                  _getStatusIcon(),
                  size: 16,
                  color: _getStatusColor(),
                ),
              );
            },
          ),
          if (_syncQueueLength > 0) ...[
            const SizedBox(width: 4),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
              decoration: BoxDecoration(
                color: AppColors.primaryYellow,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                '$_syncQueueLength',
                style: const TextStyle(
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildFullWidget(bool isDarkMode) {
    return Container(
      padding: const EdgeInsets.all(12),
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: isDarkMode ? AppColors.darkSurface : AppColors.lightSurface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _getStatusColor().withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          AnimatedBuilder(
            animation: _pulseAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: _isOnline ? 1.0 : _pulseAnimation.value,
                child: Icon(
                  _getStatusIcon(),
                  size: 24,
                  color: _getStatusColor(),
                ),
              );
            },
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _getStatusText(),
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: isDarkMode ? AppColors.darkText : AppColors.lightText,
                  ),
                ),
                if (_syncQueueLength > 0 || _isSyncing) ...[
                  const SizedBox(height: 4),
                  Text(
                    _getSyncText(),
                    style: TextStyle(
                      fontSize: 12,
                      color: isDarkMode ? AppColors.darkTextSecondary : AppColors.lightTextSecondary,
                    ),
                  ),
                ],
              ],
            ),
          ),
          if (widget.showSyncButton && _isOnline && _syncQueueLength > 0) ...[
            const SizedBox(width: 8),
            IconButton(
              onPressed: _isSyncing ? null : _triggerSync,
              icon: _isSyncing
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          AppColors.primaryYellow,
                        ),
                      ),
                    )
                  : const Icon(
                      Icons.sync,
                      color: AppColors.primaryYellow,
                    ),
              tooltip: 'Sync now',
            ),
          ],
        ],
      ),
    );
  }

  IconData _getStatusIcon() {
    if (_isSyncing) return Icons.sync;
    if (_isOnline) return Icons.wifi;
    return Icons.wifi_off;
  }

  Color _getStatusColor() {
    if (_isSyncing) return AppColors.primaryYellow;
    if (_isOnline) return Colors.green;
    return Colors.orange;
  }

  String _getStatusText() {
    if (_isSyncing) return 'Syncing...';
    if (_isOnline) return 'Online';
    return 'Offline Mode';
  }

  String _getSyncText() {
    if (_isSyncing) return 'Synchronizing data...';
    if (_syncQueueLength > 0) {
      return '$_syncQueueLength item${_syncQueueLength == 1 ? '' : 's'} pending sync';
    }
    return 'All data synchronized';
  }

  Future<void> _triggerSync() async {
    if (_isSyncing) return;
    
    setState(() {
      _isSyncing = true;
    });
    
    try {
      await _networkManager.forceSync();
    } catch (e) {
      // Handle sync error
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Sync failed: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSyncing = false;
          _syncQueueLength = _networkManager.syncQueueLength;
        });
      }
    }
  }
}
