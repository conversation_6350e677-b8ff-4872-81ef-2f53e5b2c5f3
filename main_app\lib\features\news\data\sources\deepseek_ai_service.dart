import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/news_article.dart';
import '../../../../core/utils/app_logger.dart';

class DeepSeekAIService {
  final String _apiKey;
  final String _baseUrl = 'https://openrouter.ai/api/v1';
  final http.Client _httpClient;
  final AppLogger _logger = AppLogger('DeepSeekAIService');

  DeepSeekAIService({
    required String apiKey,
    http.Client? httpClient,
  })  : _apiKey = apiKey,
        _httpClient = httpClient ?? http.Client();

  /// Process and enhance news articles using DeepSeek R1 Zero
  Future<List<NewsArticle>> enhanceNewsArticles(
    List<Map<String, dynamic>> rawArticles,
  ) async {
    if (rawArticles.isEmpty) return [];

    try {
      // Create a comprehensive prompt for Pakistani automotive news processing
      final prompt = _createEnhancementPrompt(rawArticles);

      final response = await _callDeepSeekAPI(prompt);

      if (response != null) {
        return _parseEnhancedArticles(response, rawArticles);
      }
    } catch (e) {
      // Log error but don't crash - fallback to basic processing
      if (e.toString().contains('rate limit') || e.toString().contains('quota')) {
        _logger.warning('DeepSeek API rate limit reached, using fallback processing');
      } else {
        _logger.error('Error enhancing articles with DeepSeek', error: e);
      }
    }

    // Fallback: return basic processed articles if AI enhancement fails
    return _createFallbackArticles(rawArticles);
  }

  /// Create a detailed prompt for DeepSeek R1 Zero to process news articles
  String _createEnhancementPrompt(List<Map<String, dynamic>> articles) {
    final articlesJson = articles.map((article) => {
      'title': article['title'] ?? '',
      'description': article['description'] ?? '',
      'url': article['url'] ?? '',
      'source': article['source']?['name'] ?? 'Unknown',
      'publishedAt': article['publishedAt'] ?? '',
      'urlToImage': article['urlToImage'] ?? '',
    }).toList();

    return '''
You are an expert Pakistani automotive news analyst. Process these news articles and enhance them for a Pakistani driving app.

ARTICLES TO PROCESS:
${jsonEncode(articlesJson)}

TASK: For each article, determine:
1. RELEVANCE: Is this relevant to Pakistani drivers/automotive market? (score 1-10)
   - Score 8-10: Directly about Pakistan automotive/transport
   - Score 6-7: General automotive but applicable to Pakistan
   - Score 1-5: Irrelevant or not applicable to Pakistani market

2. CATEGORY: Assign to one of these categories ONLY:
   - Car Prices (vehicle costs, pricing, affordability in Pakistan)
   - Industry News (automotive manufacturing, companies, market trends)
   - Fuel Prices (petrol, diesel, CNG prices and policies)
   - Taxes & Duties (vehicle registration, customs, government fees)
   - Road Safety (accident prevention, safety measures, awareness)
   - Traffic Updates (road conditions, traffic management, infrastructure)
   - Accident Reports (traffic accidents, safety incidents)
   - General (other automotive-related content)

3. ENHANCED_TITLE: Create an engaging, localized title for Pakistani drivers
4. ENHANCED_DESCRIPTION: Write 2-3 sentences with Pakistani context and relevance
5. LOCAL_CONTEXT: Add specific Pakistani market/regulatory context

STRICT REQUIREMENTS:
- ONLY include articles with relevance score ≥ 8 (very strict filtering)
- Must be directly relevant to Pakistani automotive market or drivers
- Must be VERY RECENT (published within last 24 hours only)
- Exclude: sports cars, luxury vehicles not sold in Pakistan, international racing, celebrity car news
- Exclude: old news, outdated information, anything older than 1 day
- Focus on: practical vehicles, local market conditions, government policies, road safety, fuel economy
- Add Pakistani context: mention local brands (Suzuki, Honda, Toyota Pakistan), local conditions, regulations
- Keep descriptions practical and useful for Pakistani drivers
- Prioritize breaking news, latest updates, current events only

RESPONSE FORMAT (JSON):
{
  "enhanced_articles": [
    {
      "original_index": 0,
      "relevance_score": 8,
      "category": "Car Prices",
      "enhanced_title": "...",
      "enhanced_description": "...",
      "local_context": "...",
      "keep_original_image": true
    }
  ]
}

Respond only with valid JSON.
''';
  }

  /// Call DeepSeek R1 Zero API
  Future<String?> _callDeepSeekAPI(String prompt) async {
    try {
      final response = await _httpClient.post(
        Uri.parse('$_baseUrl/chat/completions'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $_apiKey',
          'HTTP-Referer': 'https://drive-on-app.com',
          'X-Title': 'Drive On News App',
        },
        body: jsonEncode({
          'model': 'deepseek/deepseek-r1-zero:free',
          'messages': [
            {
              'role': 'system',
              'content': 'You are an expert Pakistani automotive news analyst. Always respond with valid JSON only.'
            },
            {
              'role': 'user',
              'content': prompt,
            }
          ],
          'temperature': 0.3,
          'max_tokens': 4000,
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return data['choices']?[0]?['message']?['content'];
      } else {
        _logger.error('DeepSeek API error: ${response.statusCode} - ${response.body}');
        return null;
      }
    } catch (e) {
      _logger.error('Error calling DeepSeek API', error: e);
      return null;
    }
  }

  /// Parse enhanced articles from DeepSeek response
  List<NewsArticle> _parseEnhancedArticles(
    String aiResponse,
    List<Map<String, dynamic>> originalArticles,
  ) {
    try {
      // Clean the response to extract JSON
      String cleanResponse = aiResponse.trim();
      if (cleanResponse.startsWith('```json')) {
        cleanResponse = cleanResponse.substring(7);
      }
      if (cleanResponse.endsWith('```')) {
        cleanResponse = cleanResponse.substring(0, cleanResponse.length - 3);
      }

      final parsed = jsonDecode(cleanResponse);
      final enhancedList = parsed['enhanced_articles'] as List;

      return enhancedList.map<NewsArticle>((enhanced) {
        final originalIndex = enhanced['original_index'] as int;
        final original = originalArticles[originalIndex];

        return NewsArticle(
          id: '', // Will be set when saved to Firestore
          title: enhanced['enhanced_title'] ?? original['title'] ?? '',
          description: enhanced['enhanced_description'] ?? original['description'] ?? '',
          url: original['url'] ?? '',
          source: original['source']?['name'] ?? 'Unknown',
          publishedAt: DateTime.parse(original['publishedAt'] ?? DateTime.now().toIso8601String()),
          imageUrl: enhanced['keep_original_image'] == true
              ? (original['urlToImage'] ?? '')
              : '',
          views: 0,
          pinned: false,
          adminComment: enhanced['local_context'],
          reactionCounts: {},
          createdAt: DateTime.now(),
          category: enhanced['category'] ?? 'General',
        );
      }).toList();
    } catch (e) {
      _logger.error('Error parsing enhanced articles', error: e);
      return _createFallbackArticles(originalArticles);
    }
  }

  /// Create fallback articles if AI enhancement fails
  List<NewsArticle> _createFallbackArticles(List<Map<String, dynamic>> articles) {
    return articles.take(10).map<NewsArticle>((article) {
      // Basic category assignment based on keywords
      String category = 'General';
      final title = (article['title'] ?? '').toLowerCase();
      final description = (article['description'] ?? '').toLowerCase();
      final content = '$title $description';

      if (content.contains('price') || content.contains('cost')) {
        category = 'Car Prices';
      } else if (content.contains('fuel') || content.contains('petrol') || content.contains('diesel')) {
        category = 'Fuel Prices';
      } else if (content.contains('tax') || content.contains('duty')) {
        category = 'Taxes & Duties';
      } else if (content.contains('accident') || content.contains('crash')) {
        category = 'Accident Reports';
      } else if (content.contains('traffic') || content.contains('road')) {
        category = 'Traffic Updates';
      } else if (content.contains('safety')) {
        category = 'Road Safety';
      } else if (content.contains('industry') || content.contains('company')) {
        category = 'Industry News';
      }

      return NewsArticle(
        id: '',
        title: article['title'] ?? '',
        description: article['description'] ?? '',
        url: article['url'] ?? '',
        source: article['source']?['name'] ?? 'Unknown',
        publishedAt: DateTime.parse(article['publishedAt'] ?? DateTime.now().toIso8601String()),
        imageUrl: article['urlToImage'] ?? '',
        views: 0,
        pinned: false,
        adminComment: null,
        reactionCounts: {},
        createdAt: DateTime.now(),
        category: category,
      );
    }).toList();
  }

  void dispose() {
    _httpClient.close();
  }
}
