@echo off
REM Setup script for Drive-On Main App (Windows)
REM This script sets up the development environment and prepares for deployment

echo 🚀 Setting up Drive-On Main App...

REM Check if Flutter is installed
flutter --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Flutter is not installed. Please install Flutter first:
    echo https://docs.flutter.dev/get-started/install
    pause
    exit /b 1
)

REM Check Flutter version
echo 📱 Checking Flutter version...
flutter --version

REM Check if Firebase CLI is installed
firebase --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 🔧 Installing Firebase CLI...
    npm --version >nul 2>&1
    if %errorlevel% neq 0 (
        echo ❌ npm is not installed. Please install Node.js and npm first:
        echo https://nodejs.org/
        pause
        exit /b 1
    )
    npm install -g firebase-tools
)

REM Login to Firebase
echo 🔑 Logging into Firebase...
firebase login

REM Set Firebase project
echo 🔥 Setting Firebase project...
firebase use drive-on-b2af8

REM Get Flutter dependencies
echo 📦 Getting Flutter dependencies...
flutter pub get
if %errorlevel% neq 0 (
    echo ❌ Failed to get Flutter dependencies
    pause
    exit /b 1
)

REM Run Flutter doctor
echo 🩺 Running Flutter doctor...
flutter doctor

REM Create necessary directories
echo 📁 Creating necessary directories...
if not exist "build\web" mkdir build\web
if not exist ".github\workflows" mkdir .github\workflows

REM Initialize Firebase (if not already done)
echo 🔥 Checking Firebase initialization...
if not exist ".firebaserc" (
    echo Initializing Firebase...
    firebase use drive-on-b2af8
)

REM Check Android setup
echo 🤖 Checking Android setup...
flutter doctor

REM Build Android APK to ensure it compiles
echo 🔨 Building Android APK to verify setup...
flutter build apk --debug
if %errorlevel% neq 0 (
    echo ❌ Build failed
    pause
    exit /b 1
)

echo ✅ Setup completed successfully!
echo.
echo 🎉 You're ready to deploy!
echo.
echo Next steps:
echo 1. Connect an Android device or start an emulator
echo 2. Run 'flutter run' for local development
echo 3. Run 'scripts\build-android.bat' to build APK
echo 4. Run 'scripts\deploy-android.bat' to distribute via Firebase
echo 5. Push to main branch for automatic CI/CD deployment
echo.
echo 📚 See DEPLOYMENT.md for detailed instructions
pause
