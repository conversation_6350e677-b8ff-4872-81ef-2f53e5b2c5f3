import 'package:get_it/get_it.dart';
import '../../features/auth/repositories/auth_repository.dart';
import '../../features/auth/repositories/firebase_auth_repository.dart';
import '../../features/auth/repositories/user_repository.dart';
import '../../features/auth/auth_service.dart';
import '../security/secure_storage.dart';
import '../security/encryption_service.dart';
import '../security/security_manager.dart';
import '../services/offline_storage_service.dart';
import '../services/enhanced_cache_service.dart';
import '../services/network_state_manager.dart';
import '../../features/jobs/repositories/offline_jobs_repository.dart';
import '../../features/drivers/repositories/offline_drivers_repository.dart';

/// Global service locator instance
final GetIt serviceLocator = GetIt.instance;

/// Initialize the service locator with all dependencies
Future<void> setupServiceLocator() async {
  // Security services
  serviceLocator.registerSingleton<SecureStorage>(SecureStorage());
  serviceLocator.registerSingleton<EncryptionService>(EncryptionService());

  final securityManager = SecurityManager();
  await securityManager.initialize();
  serviceLocator.registerSingleton<SecurityManager>(securityManager);

  // Offline storage services
  final offlineStorage = OfflineStorageService();
  await offlineStorage.initialize();
  serviceLocator.registerSingleton<OfflineStorageService>(offlineStorage);

  final enhancedCache = EnhancedCacheService();
  await enhancedCache.initialize();
  serviceLocator.registerSingleton<EnhancedCacheService>(enhancedCache);

  final networkStateManager = NetworkStateManager();
  await networkStateManager.initialize();
  serviceLocator.registerSingleton<NetworkStateManager>(networkStateManager);

  // Repositories
  serviceLocator.registerLazySingleton<AuthRepository>(
    () => FirebaseAuthRepository(),
  );
  serviceLocator.registerLazySingleton<UserRepository>(
    () => UserRepository(),
  );

  // Offline repositories
  final offlineJobsRepo = OfflineJobsRepository();
  await offlineJobsRepo.initialize();
  serviceLocator.registerSingleton<OfflineJobsRepository>(offlineJobsRepo);

  final offlineDriversRepo = OfflineDriversRepository();
  await offlineDriversRepo.initialize();
  serviceLocator.registerSingleton<OfflineDriversRepository>(offlineDriversRepo);

  // Services
  serviceLocator.registerLazySingleton<AuthService>(
    () => AuthService(
      authRepository: serviceLocator<AuthRepository>(),
      userRepository: serviceLocator<UserRepository>(),
    ),
  );
}

