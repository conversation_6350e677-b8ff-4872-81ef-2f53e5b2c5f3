'use client'

import { Filter } from 'lucide-react'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'

interface QueryFiltersProps {
  filters: {
    category: string
    status: string
    priority: string
    assignedTo: string
  }
  onFiltersChange: (filters: any) => void
}

export function QueryFilters({ filters, onFiltersChange }: QueryFiltersProps) {
  const updateFilter = (key: string, value: string) => {
    onFiltersChange({
      ...filters,
      [key]: value
    })
  }

  return (
    <div className="flex space-x-2">
      {/* Category Filter */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" size="sm">
            <Filter className="w-4 h-4 mr-2" />
            Category: {filters.category === 'all' ? 'All' : filters.category}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          <DropdownMenuLabel>Filter by Category</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={() => updateFilter('category', 'all')}>
            All Categories
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('category', 'technical')}>
            Technical
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('category', 'billing')}>
            Billing
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('category', 'account')}>
            Account
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('category', 'general')}>
            General
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('category', 'complaint')}>
            Complaint
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('category', 'feature-request')}>
            Feature Request
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Status Filter */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" size="sm">
            Status: {filters.status === 'all' ? 'All' : filters.status}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          <DropdownMenuLabel>Filter by Status</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={() => updateFilter('status', 'all')}>
            All Status
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('status', 'open')}>
            Open
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('status', 'in-progress')}>
            In Progress
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('status', 'resolved')}>
            Resolved
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('status', 'closed')}>
            Closed
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Priority Filter */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" size="sm">
            Priority: {filters.priority === 'all' ? 'All' : filters.priority}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          <DropdownMenuLabel>Filter by Priority</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={() => updateFilter('priority', 'all')}>
            All Priorities
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('priority', 'low')}>
            Low
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('priority', 'medium')}>
            Medium
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('priority', 'high')}>
            High
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('priority', 'urgent')}>
            Urgent
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Assigned To Filter */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" size="sm">
            Assigned: {filters.assignedTo === 'all' ? 'All' : filters.assignedTo === 'unassigned' ? 'Unassigned' : filters.assignedTo}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          <DropdownMenuLabel>Filter by Assignment</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={() => updateFilter('assignedTo', 'all')}>
            All Assignments
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('assignedTo', 'unassigned')}>
            Unassigned
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('assignedTo', 'John Admin')}>
            John Admin
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('assignedTo', 'Sarah Support')}>
            Sarah Support
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('assignedTo', 'Mike Manager')}>
            Mike Manager
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  )
}
