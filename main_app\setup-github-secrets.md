# 🔐 GitHub Secrets Setup for Drive-On CI/CD Pipeline

## Repository Information
- **Repository**: `UzairDevelops/Drive_on`
- **GitHub Email**: `<EMAIL>`
- **Firebase Project**: `drive-on-b2af8`

## Required GitHub Secrets

### 1. FIREBASE_SERVICE_ACCOUNT_DRIVE_ON_B2AF8

**Step 1: Generate Firebase Service Account Key**

1. Open your terminal/command prompt
2. Login to Firebase CLI:
   ```bash
   firebase login --email <EMAIL>
   ```

3. Generate service account key:
   ```bash
   firebase serviceaccount:generate-key service-account-key.json --project drive-on-b2af8
   ```

4. This will create a `service-account-key.json` file. Open it and copy the entire JSON content.

**Step 2: Add to GitHub Secrets**

1. Go to: https://github.com/UzairDevelops/Drive_on/settings/secrets/actions
2. Click "New repository secret"
3. Name: `FIREBASE_SERVICE_ACCOUNT_DRIVE_ON_B2AF8`
4. Value: Paste the entire JSON content from `service-account-key.json`
5. Click "Add secret"

### 2. FIREBASE_APP_ID

**Step 1: Get Firebase App ID**

1. Go to Firebase Console: https://console.firebase.google.com/project/drive-on-b2af8
2. Click on "Project Settings" (gear icon)
3. Scroll down to "Your apps" section
4. Find your Android app
5. Copy the "App ID" (format: `1:************:android:xxxxx`)

**Step 2: Add to GitHub Secrets**

1. Go to: https://github.com/UzairDevelops/Drive_on/settings/secrets/actions
2. Click "New repository secret"
3. Name: `FIREBASE_APP_ID`
4. Value: `1:************:android:6a8e1d9c3c8992992d754d`
5. Click "Add secret"

### 3. CODECOV_TOKEN (Optional)

**Step 1: Get Codecov Token**

1. Go to: https://codecov.io/
2. Sign up/login with your GitHub account
3. Add your repository: `UzairDevelops/Drive_on`
4. Copy the repository token

**Step 2: Add to GitHub Secrets**

1. Go to: https://github.com/UzairDevelops/Drive_on/settings/secrets/actions
2. Click "New repository secret"
3. Name: `CODECOV_TOKEN`
4. Value: Your Codecov token
5. Click "Add secret"

## Quick Setup Commands

Run these commands in your main_app directory:

```bash
# 1. Login to Firebase
firebase login --email <EMAIL>

# 2. Set Firebase project
firebase use drive-on-b2af8

# 3. Generate service account key
firebase serviceaccount:generate-key service-account-key.json --project drive-on-b2af8

# 4. Display the service account key (copy this to GitHub secrets)
type service-account-key.json

# 5. Get Firebase App ID
firebase apps:list --project drive-on-b2af8
```

## Verification Steps

After adding the secrets, verify your setup:

1. **Check Secrets**: Go to https://github.com/UzairDevelops/Drive_on/settings/secrets/actions
   - You should see: `FIREBASE_SERVICE_ACCOUNT_DRIVE_ON_B2AF8`
   - You should see: `FIREBASE_APP_ID`
   - Optional: `CODECOV_TOKEN`

2. **Test Deployment**: Push a commit to trigger the CI/CD pipeline
   ```bash
   git add .
   git commit -m "Setup CI/CD pipeline"
   git push origin main
   ```

3. **Monitor Actions**: Check https://github.com/UzairDevelops/Drive_on/actions

## Firebase App Distribution Setup

### Create Tester Groups

1. Go to Firebase Console: https://console.firebase.google.com/project/drive-on-b2af8/appdistribution
2. Click on "Testers & Groups"
3. Create two groups:
   - **staging-testers**: For staging environment testing
   - **production-testers**: For production release testing

### Add Testers

1. Click "Add testers"
2. Add email addresses of your testers
3. Assign them to appropriate groups

## Environment Variables Setup

Update your environment files with actual values:

### .env.production
```env
ENVIRONMENT=production
FIREBASE_PROJECT_ID=drive-on-b2af8
FIREBASE_API_KEY=AIzaSyBQehlAZFI-WngAMwzblEu7JBdMcCr-P80
FIREBASE_AUTH_DOMAIN=drive-on-b2af8.firebaseapp.com
FIREBASE_STORAGE_BUCKET=drive-on-b2af8.firebasestorage.app
FIREBASE_MESSAGING_SENDER_ID=************
FIREBASE_APP_ID=1:************:android:6a8e1d9c3c8992992d754d
```

## Troubleshooting

### Common Issues

1. **Service Account Permission Error**
   - Ensure the service account has "Firebase Admin SDK Administrator Service Agent" role
   - Check project permissions in Firebase Console

2. **App ID Not Found**
   - Verify the Firebase App ID in Project Settings
   - Ensure the Android app is properly configured

3. **GitHub Actions Failing**
   - Check the Actions tab for detailed error logs
   - Verify all secrets are properly set

### Support Commands

```bash
# Check Firebase login status
firebase login:list

# List Firebase projects
firebase projects:list

# Check current project
firebase use

# List Firebase apps
firebase apps:list --project drive-on-b2af8

# Test Firebase deployment
firebase deploy --only hosting --project drive-on-b2af8
```

## Security Notes

⚠️ **Important Security Reminders:**

1. **Never commit** the `service-account-key.json` file to your repository
2. **Add to .gitignore**:
   ```
   service-account-key.json
   *.json
   .env.*
   ```
3. **Rotate keys** periodically for security
4. **Use least privilege** principle for service accounts

## Next Steps

After setting up the secrets:

1. ✅ Push code to trigger first deployment
2. ✅ Monitor GitHub Actions for successful builds
3. ✅ Check Firebase Hosting for web deployment
4. ✅ Verify Firebase App Distribution for Android
5. ✅ Test different environments (staging/production)

## Support

If you encounter issues:
1. Check GitHub Actions logs
2. Review Firebase Console for errors
3. Verify all secrets are correctly set
4. Ensure Firebase project permissions are correct
