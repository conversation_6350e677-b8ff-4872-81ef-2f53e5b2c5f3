@echo off
REM Production Build Script for Google Play Store
REM This script builds signed APK and AAB files ready for Play Store submission

setlocal enabledelayedexpansion

echo 🚀 Drive-On Production Build for Google Play Store
echo ==================================================

REM Check prerequisites
echo 🔍 Checking prerequisites...

REM Check Flutter
where flutter >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ Error: Flutter not found in PATH
    echo Please install Flutter and ensure it's in your PATH
    pause
    exit /b 1
)

REM Check if keystore exists
if not exist "android\key.properties" (
    echo ❌ Error: key.properties not found
    echo Please run scripts\generate-keystore.bat first to create production keystore
    pause
    exit /b 1
)

if not exist "android\app\drive-on-release-key.jks" (
    echo ❌ Error: Production keystore not found
    echo Please run scripts\generate-keystore.bat first to create production keystore
    pause
    exit /b 1
)

echo ✅ Prerequisites check passed!
echo.

REM Clean previous builds
echo 🧹 Cleaning previous builds...
flutter clean
if %errorlevel% neq 0 (
    echo ❌ Error: Flutter clean failed
    pause
    exit /b 1
)

REM Get dependencies
echo 📦 Getting dependencies...
flutter pub get
if %errorlevel% neq 0 (
    echo ❌ Error: Flutter pub get failed
    pause
    exit /b 1
)

echo ✅ Dependencies updated successfully!
echo.

REM Generate build number
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YY=%dt:~2,2%" & set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
set "HH=%dt:~8,2%" & set "Min=%dt:~10,2%" & set "Sec=%dt:~12,2%"
set "BUILD_NUMBER=%YYYY%%MM%%DD%%HH%%Min%"

echo 🔢 Build number: %BUILD_NUMBER%
echo.

REM Build App Bundle (AAB) for Play Store
echo 📱 Building Android App Bundle (AAB) for Play Store...
flutter build appbundle --release ^
    --build-number=%BUILD_NUMBER% ^
    --dart-define=ENVIRONMENT=production ^
    --obfuscate ^
    --split-debug-info=build/debug-info

if %errorlevel% neq 0 (
    echo ❌ Error: App Bundle build failed
    pause
    exit /b 1
)

echo ✅ App Bundle built successfully!
echo.

REM Build APK for testing
echo 📱 Building Android APK for testing...
flutter build apk --release ^
    --build-number=%BUILD_NUMBER% ^
    --dart-define=ENVIRONMENT=production ^
    --obfuscate ^
    --split-debug-info=build/debug-info

if %errorlevel% neq 0 (
    echo ❌ Error: APK build failed
    pause
    exit /b 1
)

echo ✅ APK built successfully!
echo.

REM Verify build outputs
echo 🔍 Verifying build outputs...

set AAB_PATH=build\app\outputs\bundle\release\app-release.aab
set APK_PATH=build\app\outputs\flutter-apk\app-release.apk

if exist "%AAB_PATH%" (
    echo ✅ App Bundle found: %AAB_PATH%
    for %%A in ("%AAB_PATH%") do echo    Size: %%~zA bytes
) else (
    echo ❌ App Bundle not found at expected location
)

if exist "%APK_PATH%" (
    echo ✅ APK found: %APK_PATH%
    for %%A in ("%APK_PATH%") do echo    Size: %%~zA bytes
) else (
    echo ❌ APK not found at expected location
)

echo.

REM Create release directory
set RELEASE_DIR=releases\v1.0.0-build-%BUILD_NUMBER%
mkdir "%RELEASE_DIR%" 2>nul

REM Copy build artifacts
if exist "%AAB_PATH%" (
    copy "%AAB_PATH%" "%RELEASE_DIR%\drive-on-v1.0.0-build-%BUILD_NUMBER%.aab" >nul
    echo ✅ App Bundle copied to releases directory
)

if exist "%APK_PATH%" (
    copy "%APK_PATH%" "%RELEASE_DIR%\drive-on-v1.0.0-build-%BUILD_NUMBER%.apk" >nul
    echo ✅ APK copied to releases directory
)

REM Copy mapping file for crash reporting
if exist "build\app\outputs\mapping\release\mapping.txt" (
    copy "build\app\outputs\mapping\release\mapping.txt" "%RELEASE_DIR%\mapping.txt" >nul
    echo ✅ ProGuard mapping file copied
)

echo.
echo 🎉 PRODUCTION BUILD COMPLETE!
echo ================================
echo.
echo 📦 BUILD ARTIFACTS:
echo - App Bundle (AAB): %RELEASE_DIR%\drive-on-v1.0.0-build-%BUILD_NUMBER%.aab
echo - APK (Testing): %RELEASE_DIR%\drive-on-v1.0.0-build-%BUILD_NUMBER%.apk
echo - ProGuard Mapping: %RELEASE_DIR%\mapping.txt
echo.
echo 📋 NEXT STEPS FOR GOOGLE PLAY STORE:
echo.
echo 1. 🔍 TEST THE APK:
echo    - Install on test device: adb install "%RELEASE_DIR%\drive-on-v1.0.0-build-%BUILD_NUMBER%.apk"
echo    - Test all functionality thoroughly
echo    - Verify no crashes or issues
echo.
echo 2. 🎨 PREPARE STORE ASSETS:
echo    - Run: scripts\prepare-play-store-assets.bat
echo    - Create app icon (512x512)
echo    - Create feature graphic (1024x500)
echo    - Take screenshots (minimum 2)
echo.
echo 3. 🏢 GOOGLE PLAY CONSOLE:
echo    - Create app in Play Console
echo    - Upload AAB file: %RELEASE_DIR%\drive-on-v1.0.0-build-%BUILD_NUMBER%.aab
echo    - Upload mapping file: %RELEASE_DIR%\mapping.txt
echo    - Complete store listing
echo.
echo 4. 🚀 RELEASE:
echo    - Start with Internal Testing
echo    - Progress to Closed Testing
echo    - Finally publish to Production
echo.

pause
