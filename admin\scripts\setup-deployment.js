#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function execCommand(command, description, options = {}) {
  log(`\n${colors.blue}📋 ${description}...${colors.reset}`);
  try {
    const result = execSync(command, { 
      stdio: options.silent ? 'pipe' : 'inherit', 
      cwd: path.join(__dirname, '..'),
      ...options 
    });
    log(`${colors.green}✅ ${description} completed successfully${colors.reset}`);
    return result;
  } catch (error) {
    if (!options.optional) {
      log(`${colors.red}❌ ${description} failed${colors.reset}`);
      if (options.exitOnError !== false) {
        process.exit(1);
      }
    } else {
      log(`${colors.yellow}⚠️  ${description} skipped (optional)${colors.reset}`);
    }
    return null;
  }
}

function checkPrerequisites() {
  log(`${colors.cyan}🔍 Checking prerequisites...${colors.reset}`);
  
  // Check Node.js version
  const nodeVersion = process.version;
  const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
  if (majorVersion >= 18) {
    log(`${colors.green}✅ Node.js ${nodeVersion} is compatible${colors.reset}`);
  } else {
    log(`${colors.red}❌ Node.js ${nodeVersion} is not compatible. Please upgrade to Node.js 18+${colors.reset}`);
    process.exit(1);
  }
  
  // Check npm version
  try {
    const npmVersion = execSync('npm --version', { stdio: 'pipe' }).toString().trim();
    log(`${colors.green}✅ npm ${npmVersion} is available${colors.reset}`);
  } catch (error) {
    log(`${colors.red}❌ npm is not available${colors.reset}`);
    process.exit(1);
  }
}

function setupEnvironment() {
  log(`${colors.yellow}🌍 Setting up environment files...${colors.reset}`);
  
  const envExample = path.join(__dirname, '..', '.env.example');
  const envLocal = path.join(__dirname, '..', '.env.local');
  const envProduction = path.join(__dirname, '..', '.env.production');
  
  // Copy .env.example to .env.local if it doesn't exist
  if (!fs.existsSync(envLocal) && fs.existsSync(envExample)) {
    fs.copyFileSync(envExample, envLocal);
    log(`${colors.green}✅ Created .env.local from template${colors.reset}`);
  }
  
  // Ensure .env.production exists
  if (!fs.existsSync(envProduction) && fs.existsSync(envExample)) {
    fs.copyFileSync(envExample, envProduction);
    log(`${colors.green}✅ Created .env.production from template${colors.reset}`);
  }
}

function makeScriptsExecutable() {
  log(`${colors.yellow}🔧 Making scripts executable...${colors.reset}`);
  
  const scriptsDir = path.join(__dirname);
  const scripts = [
    'deploy.js',
    'pre-deploy-check.js',
    'setup-deployment.js'
  ];
  
  scripts.forEach(script => {
    const scriptPath = path.join(scriptsDir, script);
    if (fs.existsSync(scriptPath)) {
      try {
        fs.chmodSync(scriptPath, '755');
        log(`${colors.green}✅ Made ${script} executable${colors.reset}`);
      } catch (error) {
        log(`${colors.yellow}⚠️  Could not make ${script} executable (this is okay)${colors.reset}`);
      }
    }
  });
}

function installDependencies() {
  log(`${colors.yellow}📦 Installing dependencies...${colors.reset}`);
  
  // Check if node_modules exists
  const nodeModulesPath = path.join(__dirname, '..', 'node_modules');
  if (fs.existsSync(nodeModulesPath)) {
    log(`${colors.blue}📋 Dependencies already installed, checking for updates...${colors.reset}`);
    execCommand('npm ci', 'Installing/updating dependencies');
  } else {
    execCommand('npm install', 'Installing dependencies');
  }
}

function checkFirebaseSetup() {
  log(`${colors.yellow}🔥 Checking Firebase setup...${colors.reset}`);
  
  // Check if Firebase CLI is installed globally
  try {
    const firebaseVersion = execSync('firebase --version', { stdio: 'pipe' }).toString().trim();
    log(`${colors.green}✅ Firebase CLI ${firebaseVersion} is installed globally${colors.reset}`);
  } catch (error) {
    log(`${colors.yellow}⚠️  Firebase CLI not installed globally${colors.reset}`);
    log(`${colors.cyan}💡 You can install it with: npm install -g firebase-tools${colors.reset}`);
  }
  
  // Check if user is logged in
  try {
    execSync('firebase projects:list', { stdio: 'pipe' });
    log(`${colors.green}✅ Firebase authentication verified${colors.reset}`);
  } catch (error) {
    log(`${colors.yellow}⚠️  Not logged in to Firebase${colors.reset}`);
    log(`${colors.cyan}💡 You can login with: firebase login${colors.reset}`);
  }
  
  // Check Firebase configuration files
  const firebaseJson = path.join(__dirname, '..', 'firebase.json');
  const firebaseRc = path.join(__dirname, '..', '.firebaserc');
  
  if (fs.existsSync(firebaseJson)) {
    log(`${colors.green}✅ firebase.json exists${colors.reset}`);
  } else {
    log(`${colors.red}❌ firebase.json not found${colors.reset}`);
  }
  
  if (fs.existsSync(firebaseRc)) {
    log(`${colors.green}✅ .firebaserc exists${colors.reset}`);
  } else {
    log(`${colors.red}❌ .firebaserc not found${colors.reset}`);
  }
}

function runTests() {
  log(`${colors.yellow}🧪 Running tests...${colors.reset}`);
  
  execCommand('npm run type-check', 'TypeScript type checking');
  execCommand('npm run lint', 'ESLint checks', { optional: true });
  execCommand('npm run format:check', 'Code formatting check', { optional: true });
}

function displayNextSteps() {
  log(`\n${colors.bright}${colors.magenta}🎉 Setup completed successfully!${colors.reset}`);
  log(`${colors.cyan}================================================${colors.reset}`);
  
  log(`\n${colors.yellow}📋 Next Steps:${colors.reset}`);
  log(`${colors.cyan}1. Update environment variables in .env.production${colors.reset}`);
  log(`${colors.cyan}2. Ensure Firebase CLI is installed: npm install -g firebase-tools${colors.reset}`);
  log(`${colors.cyan}3. Login to Firebase: firebase login${colors.reset}`);
  log(`${colors.cyan}4. Run pre-deployment check: npm run pre-deploy${colors.reset}`);
  log(`${colors.cyan}5. Deploy to staging: npm run deploy:staging${colors.reset}`);
  
  log(`\n${colors.yellow}🚀 Quick Commands:${colors.reset}`);
  log(`${colors.green}npm run dev${colors.reset}                 # Start development server`);
  log(`${colors.green}npm run pre-deploy${colors.reset}          # Run pre-deployment checks`);
  log(`${colors.green}npm run deploy:staging${colors.reset}      # Deploy to staging`);
  log(`${colors.green}npm run deploy:prod${colors.reset}         # Deploy to production`);
  log(`${colors.green}npm run deploy:check${colors.reset}        # Deploy with checks`);
  
  log(`\n${colors.yellow}📚 Documentation:${colors.reset}`);
  log(`${colors.cyan}• DEPLOYMENT_GUIDE.md - Detailed deployment instructions${colors.reset}`);
  log(`${colors.cyan}• README.md - Project overview and setup${colors.reset}`);
  
  log(`\n${colors.bright}${colors.green}✨ Your admin panel is ready for deployment!${colors.reset}`);
}

function main() {
  log(`${colors.bright}${colors.magenta}🚀 Drive-On Admin Panel Deployment Setup${colors.reset}`);
  log(`${colors.cyan}==============================================${colors.reset}`);
  
  checkPrerequisites();
  setupEnvironment();
  makeScriptsExecutable();
  installDependencies();
  checkFirebaseSetup();
  runTests();
  displayNextSteps();
}

// Handle script interruption
process.on('SIGINT', () => {
  log(`\n${colors.yellow}⚠️  Setup interrupted by user${colors.reset}`);
  process.exit(1);
});

process.on('SIGTERM', () => {
  log(`\n${colors.yellow}⚠️  Setup terminated${colors.reset}`);
  process.exit(1);
});

main();
