@echo off
echo ===================================
echo Drive-On App Icon Generator
echo ===================================
echo.
echo This script will generate app icons for all platforms.
echo Please ensure you have the following files in the assets/icons directory:
echo  - app_icon.png (1024x1024 for iOS and other platforms)
echo  - app_icon_foreground.png (1024x1024 for Android adaptive icon)
echo.
echo Checking for required files...

IF NOT EXIST assets\icons\app_icon.png (
  echo ERROR: app_icon.png not found in assets\icons directory!
  echo Please create this file and try again.
  exit /b 1
)

IF NOT EXIST assets\icons\app_icon_foreground.png (
  echo ERROR: app_icon_foreground.png not found in assets\icons directory!
  echo Please create this file and try again.
  exit /b 1
)

echo All required image files found. Generating icons...
echo.

echo Step 1: Running flutter pub get...
call flutter pub get

echo.
echo Step 2: Running flutter_launcher_icons...
call flutter pub run flutter_launcher_icons

echo.
echo Icons successfully generated for all platforms!
echo.
echo Android: Check android/app/src/main/res directory
echo iOS: Check ios/Runner/Assets.xcassets/AppIcon.appiconset directory
echo Web: Check web/icons directory
echo.
echo Done! 