'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import {
  X,
  User,
  Phone,
  MapPin,
  GraduationCap,
  Calendar,
  FileText,
  CheckCircle,
  XCircle,
  Download,
  ZoomIn,
  Mail
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { formatDate } from '@/lib/utils'

interface Driver {
  id: string
  name: string
  mobile: string
  city: string
  maritalStatus: string
  education: string
  experience: number
  isVerified: boolean
  status: 'pending' | 'verified' | 'rejected'
  documents: {
    cnic?: string
    license?: string
    photo?: string
  }
  documentUrls?: { [key: string]: string }
  createdAt: Date
  verifiedAt?: Date
  verifiedBy?: string
  rejectionReason?: string
  submittedBy?: string
  submittedAt?: Date
}

interface DriverRequest {
  id: string
  name: string
  mobile: string
  city: string
  maritalStatus: string
  education: string
  experience: number
  documentUrls: { [key: string]: string }
  status: 'pending'
  isVerified: false
  documentsVerified: false
  createdAt: Date
  submittedBy: string
  submittedAt: Date
}

interface DriverModalProps {
  driver: Driver | DriverRequest | null
  isOpen: boolean
  onClose: () => void
  onVerify?: (driver: Driver) => Promise<void>
  onReject?: (driver: Driver, reason: string) => Promise<void>
  onApprove?: (request: DriverRequest, comments?: string) => Promise<void>
  onRejectRequest?: (request: DriverRequest, reason: string) => Promise<void>
}

export function DriverModal({
  driver,
  isOpen,
  onClose,
  onVerify,
  onReject,
  onApprove,
  onRejectRequest
}: DriverModalProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [rejectionReason, setRejectionReason] = useState('')
  const [showRejectForm, setShowRejectForm] = useState(false)
  const [selectedDocument, setSelectedDocument] = useState<string | null>(null)
  const [approvalComments, setApprovalComments] = useState('')

  // Check if this is a driver request (pending) or approved driver
  const isDriverRequest = driver && 'documentUrls' in driver && !('documents' in driver)

  const handleVerify = async () => {
    if (!driver || !onVerify) return

    setIsLoading(true)
    try {
      await onVerify(driver as Driver)
      onClose()
    } catch (error) {
      console.error('Error verifying driver:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleReject = async () => {
    if (!driver || !rejectionReason.trim()) return

    setIsLoading(true)
    try {
      if (isDriverRequest && onRejectRequest) {
        await onRejectRequest(driver as DriverRequest, rejectionReason)
      } else if (onReject) {
        await onReject(driver as Driver, rejectionReason)
      }
      onClose()
    } catch (error) {
      console.error('Error rejecting driver:', error)
    } finally {
      setIsLoading(false)
      setShowRejectForm(false)
      setRejectionReason('')
    }
  }

  const handleApprove = async () => {
    if (!driver || !isDriverRequest || !onApprove) return

    setIsLoading(true)
    try {
      await onApprove(driver as DriverRequest, approvalComments)
      onClose()
    } catch (error) {
      console.error('Error approving driver request:', error)
    } finally {
      setIsLoading(false)
      setApprovalComments('')
    }
  }

  const sendVerificationEmail = async () => {
    // TODO: Implement email sending
    console.log('Sending verification email to driver')
  }

  if (!isOpen || !driver) return null

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="absolute inset-0 bg-black/50"
        onClick={onClose}
      />

      {/* Modal */}
      <motion.div
        initial={{ opacity: 0, scale: 0.95, y: 20 }}
        animate={{ opacity: 1, scale: 1, y: 0 }}
        exit={{ opacity: 0, scale: 0.95, y: 20 }}
        className="relative w-full max-w-4xl mx-4 bg-background rounded-lg shadow-xl max-h-[90vh] overflow-y-auto"
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-border">
          <div className="flex items-center space-x-3">
            <h2 className="text-xl font-semibold text-foreground">Driver Application</h2>
            <Badge variant={
              driver.status === 'verified' ? 'success' :
              driver.status === 'rejected' ? 'error' : 'warning'
            }>
              {driver.status}
            </Badge>
          </div>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="w-4 h-4" />
          </Button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Driver Information */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Personal Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-3">
                  <User className="w-5 h-5 text-muted-foreground" />
                  <div>
                    <p className="font-medium text-foreground">{driver.name}</p>
                    <p className="text-sm text-muted-foreground">Full Name</p>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <Phone className="w-5 h-5 text-muted-foreground" />
                  <div>
                    <p className="font-medium text-foreground">{driver.mobile}</p>
                    <p className="text-sm text-muted-foreground">Mobile Number</p>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <MapPin className="w-5 h-5 text-muted-foreground" />
                  <div>
                    <p className="font-medium text-foreground">{driver.city}</p>
                    <p className="text-sm text-muted-foreground">City</p>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <GraduationCap className="w-5 h-5 text-muted-foreground" />
                  <div>
                    <p className="font-medium text-foreground">{driver.education}</p>
                    <p className="text-sm text-muted-foreground">Education</p>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <Calendar className="w-5 h-5 text-muted-foreground" />
                  <div>
                    <p className="font-medium text-foreground">{driver.experience} years</p>
                    <p className="text-sm text-muted-foreground">Driving Experience</p>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <User className="w-5 h-5 text-muted-foreground" />
                  <div>
                    <p className="font-medium text-foreground">{driver.maritalStatus}</p>
                    <p className="text-sm text-muted-foreground">Marital Status</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Application Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <p className="text-sm text-muted-foreground">Application Date</p>
                  <p className="font-medium text-foreground">{formatDate(driver.createdAt)}</p>
                </div>

                {('verifiedAt' in driver && driver.verifiedAt) && (
                  <div>
                    <p className="text-sm text-muted-foreground">Verification Date</p>
                    <p className="font-medium text-foreground">
                      {'verifiedAt' in driver && driver.verifiedAt
                        ? formatDate((driver as any).verifiedAt)
                        : 'Not verified'
                      }
                    </p>
                  </div>
                )}

                {('verifiedBy' in driver && driver.verifiedBy) && (
                  <div>
                    <p className="text-sm text-muted-foreground">Verified By</p>
                    <p className="font-medium text-foreground">{(driver as any).verifiedBy}</p>
                  </div>
                )}

                {('rejectionReason' in driver && driver.rejectionReason) && (
                  <div>
                    <p className="text-sm text-muted-foreground">Rejection Reason</p>
                    <p className="font-medium text-destructive">{(driver as any).rejectionReason}</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Documents Section */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center space-x-2">
                <FileText className="w-5 h-5" />
                <span>Documents</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {/* Debug section - remove in production */}
              {process.env.NODE_ENV === 'development' && (
                <div className="mb-4 p-3 bg-muted rounded text-xs">
                  <strong className="text-foreground">Debug - Available Document Keys:</strong>
                  <pre className="mt-1 text-xs overflow-x-auto text-muted-foreground">
                    {JSON.stringify(Object.keys(isDriverRequest
                      ? (driver as DriverRequest).documentUrls
                      : (driver as Driver).documents || (driver as Driver).documentUrls || {}), null, 2)}
                  </pre>
                </div>
              )}

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {(() => {
                  // Get documents from either documents or documentUrls
                  const docs = isDriverRequest
                    ? (driver as DriverRequest).documentUrls
                    : (driver as Driver).documents || (driver as Driver).documentUrls || {}

                  // Debug: Log the documents to see what's available
                  console.log('Available documents:', docs)
                  console.log('Is driver request:', isDriverRequest)
                  console.log('Driver data:', driver)

                  // Define the document types that match the main app
                  const documentTypes = [
                    { key: 'Driver License', label: 'Driver License', required: true },
                    { key: 'ID Card (Front)', label: 'ID Card (Front)', required: true },
                    { key: 'ID Card (Back)', label: 'ID Card (Back)', required: true },
                    { key: 'Current Month Electricity Bill', label: 'Electricity Bill', required: true },
                    { key: 'Police Verification', label: 'Police Verification', required: true },
                    { key: 'Passport Photo', label: 'Passport Photo', required: true },
                    { key: 'Education Certificate', label: 'Education Certificate', required: false },
                    { key: 'Previous Experience', label: 'Previous Experience', required: false }
                  ]

                  // Filter to show only documents that exist or are required
                  const documentsToShow = documentTypes.filter(({ key, required }) =>
                    (docs as any)[key] || required
                  )

                  return documentsToShow.map(({ key, label, required }) => {
                    const docUrl = (docs as any)[key]
                    return (
                      <div key={key} className="border border-border rounded-lg p-4">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-medium text-sm text-foreground">{label}</h4>
                          <div className="flex items-center space-x-2">
                            {required && (
                              <Badge variant="secondary" className="text-xs">Required</Badge>
                            )}
                            {docUrl ? (
                              <Badge variant="success" className="text-xs">Uploaded</Badge>
                            ) : (
                              <Badge variant="error" className="text-xs">Missing</Badge>
                            )}
                          </div>
                        </div>
                        {docUrl ? (
                          <div className="space-y-2">
                            <img
                              src={docUrl}
                              alt={label}
                              className="w-full h-32 object-cover rounded cursor-pointer hover:opacity-90 transition-opacity"
                              onClick={() => setSelectedDocument(docUrl)}
                              onError={(e) => {
                                console.error('Failed to load image:', docUrl)
                                e.currentTarget.src = '/placeholder-document.png'
                              }}
                            />
                            <div className="flex space-x-2">
                              <Button
                                size="sm"
                                variant="outline"
                                className="flex-1"
                                onClick={() => setSelectedDocument(docUrl)}
                              >
                                <ZoomIn className="w-4 h-4 mr-1" />
                                View
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                                className="flex-1"
                                onClick={() => window.open(docUrl, '_blank')}
                              >
                                <Download className="w-4 h-4 mr-1" />
                                Download
                              </Button>
                            </div>
                          </div>
                        ) : (
                          <div className="h-32 bg-muted rounded flex items-center justify-center">
                            <p className="text-muted-foreground text-sm text-center">
                              {required ? 'Required document not uploaded' : 'Optional document not provided'}
                            </p>
                          </div>
                        )}
                      </div>
                    )
                  })
                })()}
              </div>
            </CardContent>
          </Card>

          {/* Rejection Form */}
          {showRejectForm && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg text-destructive">Reject Application</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="rejectionReason">Reason for Rejection</Label>
                  <Input
                    id="rejectionReason"
                    value={rejectionReason}
                    onChange={(e) => setRejectionReason(e.target.value)}
                    placeholder="Please provide a reason for rejection..."
                    className="mt-1"
                  />
                </div>
                <div className="flex space-x-3">
                  <Button
                    onClick={handleReject}
                    disabled={!rejectionReason.trim() || isLoading}
                    variant="destructive"
                  >
                    {isLoading ? 'Rejecting...' : 'Confirm Rejection'}
                  </Button>
                  <Button
                    onClick={() => setShowRejectForm(false)}
                    variant="outline"
                  >
                    Cancel
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Actions */}
          {isDriverRequest ? (
            // Actions for pending driver requests
            <div className="flex justify-end space-x-3 pt-6 border-t border-border">
              <Button
                onClick={() => setShowRejectForm(true)}
                variant="outline"
                className="text-destructive border-destructive hover:bg-destructive/10"
              >
                <XCircle className="w-4 h-4 mr-2" />
                Reject
              </Button>
              <Button
                onClick={sendVerificationEmail}
                variant="outline"
              >
                <Mail className="w-4 h-4 mr-2" />
                Send Email
              </Button>
              <Button
                onClick={handleApprove}
                disabled={isLoading}
                className="gradient-primary"
              >
                <CheckCircle className="w-4 h-4 mr-2" />
                {isLoading ? 'Approving...' : 'Approve Driver'}
              </Button>
            </div>
          ) : (
            // Actions for approved drivers
            driver && (driver as Driver).status === 'verified' && (
              <div className="flex justify-end space-x-3 pt-6 border-t border-border">
                <Button
                  onClick={() => setShowRejectForm(true)}
                  variant="outline"
                  className="text-destructive border-destructive hover:bg-destructive/10"
                >
                  <XCircle className="w-4 h-4 mr-2" />
                  Revoke Verification
                </Button>
                <Button
                  onClick={sendVerificationEmail}
                  variant="outline"
                >
                  <Mail className="w-4 h-4 mr-2" />
                  Send Email
                </Button>
              </div>
            )
          )}
        </div>
      </motion.div>

      {/* Document Viewer Modal */}
      {selectedDocument && (
        <div className="fixed inset-0 z-60 flex items-center justify-center bg-black/80">
          <div className="relative max-w-4xl max-h-[90vh] p-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setSelectedDocument(null)}
              className="absolute top-2 right-2 z-10 bg-white/20 hover:bg-white/30"
            >
              <X className="w-4 h-4 text-white" />
            </Button>
            <img
              src={selectedDocument}
              alt="Document"
              className="max-w-full max-h-full object-contain rounded"
            />
          </div>
        </div>
      )}
    </div>
  )
}
