// Generate static params for static export
export async function generateStaticParams() {
  return [
    { id: 'sample-query-1' },
    { id: 'sample-query-2' },
    { id: 'sample-query-3' },
    { id: 'help-request' },
    { id: 'technical-issue' },
    { id: 'zWKZhBNOlYbiYcBXhswD' },
    { id: 'u6gvvWagehFkI0Ai7aN6' },
    { id: 'query-1' },
    { id: 'query-2' },
    { id: 'query-3' },
    { id: 'query-4' },
    { id: 'query-5' },
    { id: 'support-ticket' },
    { id: 'bug-report' },
    { id: 'feature-request' },
    { id: 'general-inquiry' },
    { id: 'test-query' },
    { id: 'sample-test' },
    { id: 'demo-query' },
    { id: 'admin-test' },
  ]
}

export default function QueryChatLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return <>{children}</>
}
