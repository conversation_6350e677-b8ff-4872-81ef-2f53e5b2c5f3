# Drive-On Admin Panel

This is the admin panel for the Drive-On application with automated CI/CD deployment.

## Features
- User Management
- Driver Management  
- Partner Management
- News Management
- Forum Management
- Query Management

## Deployment
Automatically deployed via GitHub Actions to Firebase Hosting.

**Repository**: https://github.com/UzairDevelops/drive-on-admin-panel
**Firebase Project**: drive-on-b2af8



## 🚀 Deployment Status
- **GitHub Repository**: ✅ Connected
- **Firebase Project**: drive-on-b2af8
- **CI/CD Pipeline**: ✅ Active
- **Service Account**: ✅ Configured

**Last Updated**: 06/03/2025 00:37:54

