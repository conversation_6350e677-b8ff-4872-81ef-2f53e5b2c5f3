'use client'

import { Filter } from 'lucide-react'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'

interface DriverFiltersProps {
  filters: {
    status: string
    city: string
    experience: string
  }
  onFiltersChange: (filters: any) => void
}

export function DriverFilters({ filters, onFiltersChange }: DriverFiltersProps) {
  const updateFilter = (key: string, value: string) => {
    onFiltersChange({
      ...filters,
      [key]: value
    })
  }

  return (
    <div className="flex space-x-2">
      {/* Status Filter */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" size="sm">
            <Filter className="w-4 h-4 mr-2" />
            Status: {filters.status === 'all' ? 'All' : filters.status}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          <DropdownMenuLabel>Filter by Status</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={() => updateFilter('status', 'all')}>
            All Status
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('status', 'pending')}>
            Pending
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('status', 'verified')}>
            Verified
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('status', 'rejected')}>
            Rejected
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* City Filter */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" size="sm">
            City: {filters.city === 'all' ? 'All' : filters.city}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          <DropdownMenuLabel>Filter by City</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={() => updateFilter('city', 'all')}>
            All Cities
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('city', 'Karachi')}>
            Karachi
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('city', 'Lahore')}>
            Lahore
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('city', 'Islamabad')}>
            Islamabad
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('city', 'Rawalpindi')}>
            Rawalpindi
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('city', 'Faisalabad')}>
            Faisalabad
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Experience Filter */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" size="sm">
            Experience: {filters.experience === 'all' ? 'All' : filters.experience}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          <DropdownMenuLabel>Filter by Experience</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={() => updateFilter('experience', 'all')}>
            All Experience
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('experience', '0-2')}>
            0-2 years
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('experience', '3-5')}>
            3-5 years
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('experience', '6-10')}>
            6-10 years
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('experience', '10+')}>
            10+ years
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  )
}
