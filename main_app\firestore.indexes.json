{"indexes": [{"collectionGroup": "news", "queryScope": "COLLECTION", "fields": [{"fieldPath": "pinned", "order": "DESCENDING"}, {"fieldPath": "publishedAt", "order": "DESCENDING"}]}, {"collectionGroup": "news", "queryScope": "COLLECTION", "fields": [{"fieldPath": "category", "order": "ASCENDING"}, {"fieldPath": "pinned", "order": "DESCENDING"}, {"fieldPath": "publishedAt", "order": "DESCENDING"}]}, {"collectionGroup": "news", "queryScope": "COLLECTION", "fields": [{"fieldPath": "views", "order": "DESCENDING"}, {"fieldPath": "publishedAt", "order": "DESCENDING"}]}, {"collectionGroup": "queries", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "lastMessageTime", "order": "DESCENDING"}]}, {"collectionGroup": "queries", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "unreadUserCount", "order": "DESCENDING"}]}, {"collectionGroup": "queries", "queryScope": "COLLECTION", "fields": [{"fieldPath": "unreadCount", "order": "DESCENDING"}]}, {"collectionGroup": "messages", "queryScope": "COLLECTION_GROUP", "fields": [{"fieldPath": "timestamp", "order": "ASCENDING"}]}, {"collectionGroup": "messages", "queryScope": "COLLECTION_GROUP", "fields": [{"fieldPath": "isRead", "order": "ASCENDING"}, {"fieldPath": "isAdmin", "order": "ASCENDING"}]}, {"collectionGroup": "drivers", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isVerified", "order": "DESCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "jobs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "type", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "jobs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "city", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}], "fieldOverrides": []}