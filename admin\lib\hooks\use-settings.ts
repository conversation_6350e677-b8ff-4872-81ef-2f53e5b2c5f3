import { useState, useEffect, useCallback } from 'react'
import { doc, getDoc, setDoc, updateDoc } from 'firebase/firestore'
import { db } from '@/lib/firebase/config'

interface ProfileSettings {
  name: string
  email: string
  avatar: string
  role: string
  department: string
  phone: string
  timezone: string
  language: string
}

interface SecuritySettings {
  twoFactorEnabled: boolean
  sessionTimeout: number
  passwordExpiry: number
  loginNotifications: boolean
  ipWhitelist: string[]
  apiKeyRotation: number
}

interface NotificationSettings {
  emailNotifications: boolean
  pushNotifications: boolean
  smsNotifications: boolean
  newUserAlerts: boolean
  systemAlerts: boolean
  securityAlerts: boolean
  marketingEmails: boolean
  weeklyReports: boolean
}

interface DatabaseSettings {
  autoBackup: boolean
  backupFrequency: string
  retentionPeriod: number
  compressionEnabled: boolean
  encryptionEnabled: boolean
  replicationEnabled: boolean
}

interface EmailSettings {
  provider: string
  smtpHost: string
  smtpPort: number
  username: string
  password: string
  encryption: string
  fromName: string
  fromEmail: string
}

interface SystemSettings {
  maintenanceMode: boolean
  debugMode: boolean
  cacheEnabled: boolean
  compressionEnabled: boolean
  rateLimiting: boolean
  maxRequestsPerMinute: number
  sessionDuration: number
  logLevel: string
}

interface ApiSettings {
  googleMapsKey: string
  firebaseConfig: object
  stripeKey: string
  twilioConfig: object
  pushNotificationKeys: object
  analyticsKey: string
  rateLimits: object
}

interface AdminSettings {
  profile: ProfileSettings
  security: SecuritySettings
  notifications: NotificationSettings
  database: DatabaseSettings
  email: EmailSettings
  system: SystemSettings
  api: ApiSettings
}

const defaultSettings: AdminSettings = {
  profile: {
    name: '',
    email: '',
    avatar: '',
    role: 'admin',
    department: '',
    phone: '',
    timezone: 'UTC',
    language: 'en'
  },
  security: {
    twoFactorEnabled: false,
    sessionTimeout: 30,
    passwordExpiry: 90,
    loginNotifications: true,
    ipWhitelist: [],
    apiKeyRotation: 30
  },
  notifications: {
    emailNotifications: true,
    pushNotifications: true,
    smsNotifications: false,
    newUserAlerts: true,
    systemAlerts: true,
    securityAlerts: true,
    marketingEmails: false,
    weeklyReports: true
  },
  database: {
    autoBackup: true,
    backupFrequency: 'daily',
    retentionPeriod: 30,
    compressionEnabled: true,
    encryptionEnabled: true,
    replicationEnabled: false
  },
  email: {
    provider: 'smtp',
    smtpHost: '',
    smtpPort: 587,
    username: '',
    password: '',
    encryption: 'tls',
    fromName: '',
    fromEmail: ''
  },
  system: {
    maintenanceMode: false,
    debugMode: false,
    cacheEnabled: true,
    compressionEnabled: true,
    rateLimiting: true,
    maxRequestsPerMinute: 100,
    sessionDuration: 24,
    logLevel: 'info'
  },
  api: {
    googleMapsKey: '',
    firebaseConfig: {},
    stripeKey: '',
    twilioConfig: {},
    pushNotificationKeys: {},
    analyticsKey: '',
    rateLimits: {}
  }
}

export function useSettings() {
  const [settings, setSettings] = useState<AdminSettings>(defaultSettings)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Load settings from Firebase on mount
  useEffect(() => {
    loadSettings()
  }, [])

  const loadSettings = async () => {
    try {
      setIsLoading(true)
      const settingsDoc = await getDoc(doc(db, 'admin_settings', 'main'))
      
      if (settingsDoc.exists()) {
        const data = settingsDoc.data()
        setSettings({ ...defaultSettings, ...data })
      } else {
        // Create default settings document
        await setDoc(doc(db, 'admin_settings', 'main'), defaultSettings)
        setSettings(defaultSettings)
      }
    } catch (error) {
      console.error('Error loading settings:', error)
      setError('Failed to load settings')
      // Fallback to localStorage
      const localSettings = localStorage.getItem('admin-settings')
      if (localSettings) {
        try {
          setSettings({ ...defaultSettings, ...JSON.parse(localSettings) })
        } catch (parseError) {
          console.error('Error parsing local settings:', parseError)
        }
      }
    } finally {
      setIsLoading(false)
    }
  }

  const updateSettings = useCallback(async (newSettings: Partial<AdminSettings>) => {
    try {
      const updatedSettings = { ...settings, ...newSettings }
      
      // Update Firebase
      await updateDoc(doc(db, 'admin_settings', 'main'), updatedSettings)
      
      // Update local state
      setSettings(updatedSettings)
      
      // Backup to localStorage
      localStorage.setItem('admin-settings', JSON.stringify(updatedSettings))
      
      setError(null)
    } catch (error) {
      console.error('Error updating settings:', error)
      setError('Failed to update settings')
      
      // Fallback to localStorage only
      const updatedSettings = { ...settings, ...newSettings }
      setSettings(updatedSettings)
      localStorage.setItem('admin-settings', JSON.stringify(updatedSettings))
    }
  }, [settings])

  const updateProfileSettings = useCallback(async (profileSettings: Partial<ProfileSettings>) => {
    await updateSettings({
      profile: { ...settings.profile, ...profileSettings }
    })
  }, [settings.profile, updateSettings])

  const updateSecuritySettings = useCallback(async (securitySettings: Partial<SecuritySettings>) => {
    await updateSettings({
      security: { ...settings.security, ...securitySettings }
    })
  }, [settings.security, updateSettings])

  const updateNotificationSettings = useCallback(async (notificationSettings: Partial<NotificationSettings>) => {
    await updateSettings({
      notifications: { ...settings.notifications, ...notificationSettings }
    })
  }, [settings.notifications, updateSettings])

  const updateDatabaseSettings = useCallback(async (databaseSettings: Partial<DatabaseSettings>) => {
    await updateSettings({
      database: { ...settings.database, ...databaseSettings }
    })
  }, [settings.database, updateSettings])

  const updateEmailSettings = useCallback(async (emailSettings: Partial<EmailSettings>) => {
    await updateSettings({
      email: { ...settings.email, ...emailSettings }
    })
  }, [settings.email, updateSettings])

  const updateSystemSettings = useCallback(async (systemSettings: Partial<SystemSettings>) => {
    await updateSettings({
      system: { ...settings.system, ...systemSettings }
    })
  }, [settings.system, updateSettings])

  const updateApiSettings = useCallback(async (apiSettings: Partial<ApiSettings>) => {
    await updateSettings({
      api: { ...settings.api, ...apiSettings }
    })
  }, [settings.api, updateSettings])

  const resetSettings = useCallback(async () => {
    try {
      await setDoc(doc(db, 'admin_settings', 'main'), defaultSettings)
      setSettings(defaultSettings)
      localStorage.removeItem('admin-settings')
      setError(null)
    } catch (error) {
      console.error('Error resetting settings:', error)
      setError('Failed to reset settings')
    }
  }, [])

  const exportSettings = useCallback(() => {
    const dataStr = JSON.stringify(settings, null, 2)
    const dataBlob = new Blob([dataStr], { type: 'application/json' })
    const url = URL.createObjectURL(dataBlob)
    const link = document.createElement('a')
    link.href = url
    link.download = `admin-settings-${new Date().toISOString().split('T')[0]}.json`
    link.click()
    URL.revokeObjectURL(url)
  }, [settings])

  const importSettings = useCallback(async (file: File) => {
    try {
      const text = await file.text()
      const importedSettings = JSON.parse(text)
      
      // Validate imported settings structure
      const validatedSettings = { ...defaultSettings, ...importedSettings }
      
      await updateSettings(validatedSettings)
    } catch (error) {
      console.error('Error importing settings:', error)
      setError('Failed to import settings. Please check the file format.')
    }
  }, [updateSettings])

  const testEmailSettings = useCallback(async () => {
    try {
      // TODO: Implement email test functionality
      console.log('Testing email settings...')
      return { success: true, message: 'Email test sent successfully' }
    } catch (error) {
      console.error('Error testing email:', error)
      return { success: false, message: 'Failed to send test email' }
    }
  }, [settings.email])

  const testDatabaseConnection = useCallback(async () => {
    try {
      // Test Firebase connection
      await getDoc(doc(db, 'admin_settings', 'main'))
      return { success: true, message: 'Database connection successful' }
    } catch (error) {
      console.error('Error testing database:', error)
      return { success: false, message: 'Database connection failed' }
    }
  }, [])

  return {
    settings,
    isLoading,
    error,
    
    // Update functions
    updateSettings,
    updateProfileSettings,
    updateSecuritySettings,
    updateNotificationSettings,
    updateDatabaseSettings,
    updateEmailSettings,
    updateSystemSettings,
    updateApiSettings,
    
    // Utility functions
    resetSettings,
    exportSettings,
    importSettings,
    testEmailSettings,
    testDatabaseConnection,
    
    // Reload function
    loadSettings,
  }
}
