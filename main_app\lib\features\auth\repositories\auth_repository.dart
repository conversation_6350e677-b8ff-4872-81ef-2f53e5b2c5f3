import 'package:firebase_auth/firebase_auth.dart';

/// Repository interface for authentication operations
/// 
/// This abstraction allows for better testing and separation of concerns
abstract class AuthRepository {
  /// Get the currently authenticated user
  Future<User?> getCurrentUser();
  
  /// Sign in with email and password
  Future<UserCredential> signInWithEmailPassword(String email, String password);
  
  /// Create a new user with email and password
  Future<UserCredential> createUserWithEmailPassword(String email, String password);
  
  /// Sign in with Google account
  Future<UserCredential?> signInWithGoogle();
  
  /// Sign out the current user
  Future<void> signOut();
  
  /// Send password reset email
  Future<void> sendPasswordResetEmail(String email);
  
  /// Stream of auth state changes
  Stream<User?> get authStateChanges;
  
  /// Get the current auth instance
  FirebaseAuth get authInstance;
} 