import { useState, useEffect, useCallback } from 'react'

type Theme = 'light' | 'dark' | 'system'
type AccentColor = 'yellow' | 'blue' | 'green' | 'purple' | 'red' | 'orange' | 'pink' | 'indigo'
type FontSize = 'small' | 'medium' | 'large' | 'xl'

interface ThemeSettings {
  theme: Theme
  accentColor: AccentColor
  fontSize: FontSize
  reducedMotion: boolean
  highContrast: boolean
  compactMode: boolean
}

const defaultSettings: ThemeSettings = {
  theme: 'system',
  accentColor: 'yellow',
  fontSize: 'medium',
  reducedMotion: false,
  highContrast: false,
  compactMode: false,
}

export function useTheme() {
  const [settings, setSettings] = useState<ThemeSettings>(defaultSettings)
  const [resolvedTheme, setResolvedTheme] = useState<'light' | 'dark'>('light')

  // Load settings from localStorage on mount
  useEffect(() => {
    const savedSettings = localStorage.getItem('admin-theme-settings')
    if (savedSettings) {
      try {
        const parsed = JSON.parse(savedSettings)
        setSettings({ ...defaultSettings, ...parsed })
      } catch (error) {
        console.error('Error parsing theme settings:', error)
      }
    }
  }, [])

  // Save settings to localStorage whenever they change
  useEffect(() => {
    localStorage.setItem('admin-theme-settings', JSON.stringify(settings))
  }, [settings])

  // Handle system theme detection
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')

    const updateResolvedTheme = () => {
      if (settings.theme === 'system') {
        setResolvedTheme(mediaQuery.matches ? 'dark' : 'light')
      } else {
        setResolvedTheme(settings.theme as 'light' | 'dark')
      }
    }

    updateResolvedTheme()
    mediaQuery.addEventListener('change', updateResolvedTheme)

    return () => mediaQuery.removeEventListener('change', updateResolvedTheme)
  }, [settings.theme])

  // Apply theme to document
  useEffect(() => {
    const root = document.documentElement

    // Remove existing theme classes
    root.classList.remove('light', 'dark')

    // Add current theme class
    root.classList.add(resolvedTheme)

    // Apply accent color with full HSL values
    const colorValues = getAccentColorValues(settings.accentColor)
    root.style.setProperty('--primary-hue', colorValues.hue)
    root.style.setProperty('--primary', `${colorValues.hue} ${colorValues.saturation} ${colorValues.lightness}`)
    root.style.setProperty('--ring', `${colorValues.hue} ${colorValues.saturation} ${colorValues.lightness}`)

    // Apply font size
    root.style.setProperty('--base-font-size', getFontSizeValue(settings.fontSize))

    // Apply accessibility settings
    if (settings.reducedMotion) {
      root.style.setProperty('--motion-duration', '0.01s')
      root.classList.add('reduce-motion')
    } else {
      root.style.setProperty('--motion-duration', '0.2s')
      root.classList.remove('reduce-motion')
    }

    if (settings.highContrast) {
      root.classList.add('high-contrast')
    } else {
      root.classList.remove('high-contrast')
    }

    if (settings.compactMode) {
      root.classList.add('compact-mode')
    } else {
      root.classList.remove('compact-mode')
    }

    // Force a repaint to ensure changes are applied immediately
    root.style.display = 'none'
    root.offsetHeight // Trigger reflow
    root.style.display = ''

  }, [resolvedTheme, settings])

  const getAccentColorHue = (color: AccentColor): string => {
    const colorMap: Record<AccentColor, string> = {
      yellow: '45',
      blue: '210',
      green: '120',
      purple: '270',
      red: '0',
      orange: '30',
      pink: '330',
      indigo: '240',
    }
    return colorMap[color]
  }

  const getAccentColorValues = (color: AccentColor) => {
    const colorValues: Record<AccentColor, { hue: string; saturation: string; lightness: string }> = {
      yellow: { hue: '45', saturation: '95.8%', lightness: '53.1%' },
      blue: { hue: '210', saturation: '95.8%', lightness: '53.1%' },
      green: { hue: '120', saturation: '95.8%', lightness: '53.1%' },
      purple: { hue: '270', saturation: '95.8%', lightness: '53.1%' },
      red: { hue: '0', saturation: '95.8%', lightness: '53.1%' },
      orange: { hue: '30', saturation: '95.8%', lightness: '53.1%' },
      pink: { hue: '330', saturation: '95.8%', lightness: '53.1%' },
      indigo: { hue: '240', saturation: '95.8%', lightness: '53.1%' },
    }
    return colorValues[color]
  }

  const getFontSizeValue = (size: FontSize): string => {
    const sizeMap: Record<FontSize, string> = {
      small: '14px',
      medium: '16px',
      large: '18px',
      xl: '20px',
    }
    return sizeMap[size]
  }

  const setTheme = useCallback((theme: Theme) => {
    setSettings(prev => ({ ...prev, theme }))
  }, [])

  const setAccentColor = useCallback((accentColor: AccentColor) => {
    setSettings(prev => ({ ...prev, accentColor }))
  }, [])

  const setFontSize = useCallback((fontSize: FontSize) => {
    setSettings(prev => ({ ...prev, fontSize }))
  }, [])

  const setReducedMotion = useCallback((reducedMotion: boolean) => {
    setSettings(prev => ({ ...prev, reducedMotion }))
  }, [])

  const setHighContrast = useCallback((highContrast: boolean) => {
    setSettings(prev => ({ ...prev, highContrast }))
  }, [])

  const setCompactMode = useCallback((compactMode: boolean) => {
    setSettings(prev => ({ ...prev, compactMode }))
  }, [])

  const resetTheme = useCallback(() => {
    setSettings(defaultSettings)
    localStorage.removeItem('admin-theme-settings')
  }, [])

  const exportTheme = useCallback(() => {
    const dataStr = JSON.stringify(settings, null, 2)
    const dataBlob = new Blob([dataStr], { type: 'application/json' })
    const url = URL.createObjectURL(dataBlob)
    const link = document.createElement('a')
    link.href = url
    link.download = 'admin-theme-settings.json'
    link.click()
    URL.revokeObjectURL(url)
  }, [settings])

  const importTheme = useCallback((file: File) => {
    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const imported = JSON.parse(e.target?.result as string)
        setSettings({ ...defaultSettings, ...imported })
      } catch (error) {
        console.error('Error importing theme settings:', error)
      }
    }
    reader.readAsText(file)
  }, [])

  return {
    // Current settings
    theme: settings.theme,
    accentColor: settings.accentColor,
    fontSize: settings.fontSize,
    reducedMotion: settings.reducedMotion,
    highContrast: settings.highContrast,
    compactMode: settings.compactMode,

    // Resolved theme (light/dark)
    resolvedTheme,

    // Setters
    setTheme,
    setAccentColor,
    setFontSize,
    setReducedMotion,
    setHighContrast,
    setCompactMode,

    // Utilities
    resetTheme,
    exportTheme,
    importTheme,

    // Full settings object
    settings,
  }
}
