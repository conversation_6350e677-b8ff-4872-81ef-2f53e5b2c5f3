'use client'

import { useEffect, useState, use } from 'react'
import { useRouter } from 'next/navigation'
import { motion } from 'framer-motion'
import { ArrowLeft, MessageSquare, Users, Clock, Tag, Globe } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { LoadingSpinner } from '@/components/ui/loading-spinner'
import { ChatInterface } from '@/components/chat/chat-interface'
import { useForums } from '@/lib/hooks/use-forums'
import { formatDate } from '@/lib/utils'

interface ForumChatPageProps {
  params: Promise<{
    id: string
  }>
}

export default function ForumChatPage({ params }: ForumChatPageProps) {
  const router = useRouter()
  const { forums, isLoading } = useForums()
  const [forum, setForum] = useState<any>(null)
  const resolvedParams = use(params)

  // Mock current admin user - in real app, get from auth context
  const currentAdmin = {
    id: 'admin_1',
    name: 'Admin User',
    avatar: undefined
  }

  useEffect(() => {
    if (resolvedParams.id) {
      // First try to find from actual forums data
      let foundForum = null
      if (forums && forums.length > 0) {
        foundForum = forums.find(f => f.id === resolvedParams.id)
      }

      // If not found, create a fallback forum for static export
      if (!foundForum) {
        foundForum = {
          id: resolvedParams.id,
          title: `${resolvedParams.id.charAt(0).toUpperCase() + resolvedParams.id.slice(1)} Forum`,
          description: `Discussion forum for ${resolvedParams.id} topics`,
          category: resolvedParams.id,
          status: 'active',
          isPublic: true,
          creatorName: 'Admin',
          creatorAvatar: null,
          createdAt: new Date().toISOString(),
          messageCount: 0,
          participantCount: 1,
          participants: [],
          tags: [resolvedParams.id],
          lastMessageTime: null
        }
      }

      setForum(foundForum)
    }
  }, [forums, resolvedParams.id])

  const handleBack = () => {
    router.push('/forums')
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'success'
      case 'archived':
        return 'secondary'
      case 'locked':
        return 'warning'
      default:
        return 'secondary'
    }
  }

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'general':
        return 'bg-blue-100 text-blue-800'
      case 'technical':
        return 'bg-purple-100 text-purple-800'
      case 'announcements':
        return 'bg-green-100 text-green-800'
      case 'feedback':
        return 'bg-yellow-100 text-yellow-800'
      case 'support':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-96">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  if (!forum) {
    return (
      <div className="flex flex-col items-center justify-center h-96 space-y-4">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Forum Not Found</h2>
          <p className="text-gray-600">The forum you&apos;re looking for doesn&apos;t exist or has been removed.</p>
        </div>
        <Button onClick={handleBack} variant="outline">
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Forums
        </Button>
      </div>
    )
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="h-full flex flex-col"
    >
      {/* Page Header */}
      <div className="bg-white border-b px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button
              onClick={handleBack}
              variant="ghost"
              size="sm"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Forums
            </Button>

            <div className="h-6 w-px bg-gray-300" />

            <div>
              <h1 className="text-xl font-semibold text-gray-900">Forum Chat</h1>
              <p className="text-sm text-gray-600">Participate in forum discussions</p>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Badge variant={getStatusColor(forum.status) as any}>
              {forum.status}
            </Badge>
            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getCategoryColor(forum.category)}`}>
              {forum.category}
            </span>
            {forum.isPublic && (
              <Badge variant="outline" className="text-green-600 border-green-600">
                <Globe className="w-3 h-3 mr-1" />
                Public
              </Badge>
            )}
          </div>
        </div>
      </div>

      {/* Forum Info Sidebar and Chat */}
      <div className="flex-1 flex overflow-hidden">
        {/* Forum Info Sidebar */}
        <div className="w-80 bg-gray-50 border-r overflow-y-auto">
          <div className="p-4 space-y-4">
            {/* Forum Details */}
            <Card>
              <CardHeader>
                <CardTitle className="text-sm flex items-center space-x-2">
                  <MessageSquare className="w-4 h-4" />
                  <span>Forum Details</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <h3 className="font-medium text-gray-900 mb-1">{forum.title}</h3>
                  <p className="text-sm text-gray-600 whitespace-pre-wrap">{forum.description}</p>
                </div>

                <div className="flex items-center space-x-2 text-sm text-gray-500">
                  <Clock className="w-4 h-4" />
                  <span>Created {formatDate(forum.createdAt)}</span>
                </div>

                {forum.tags && forum.tags.length > 0 && (
                  <div>
                    <div className="flex items-center space-x-2 mb-2">
                      <Tag className="w-4 h-4 text-gray-400" />
                      <span className="text-sm font-medium text-gray-700">Tags</span>
                    </div>
                    <div className="flex flex-wrap gap-1">
                      {forum.tags.map((tag: string, index: number) => (
                        <Badge key={index} variant="secondary" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Creator Info */}
            <Card>
              <CardHeader>
                <CardTitle className="text-sm flex items-center space-x-2">
                  <Users className="w-4 h-4" />
                  <span>Created By</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                    {forum.creatorAvatar ? (
                      <img src={forum.creatorAvatar} alt={forum.creatorName} className="w-10 h-10 rounded-full" />
                    ) : (
                      <Users className="w-5 h-5 text-blue-600" />
                    )}
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">{forum.creatorName}</p>
                    <p className="text-sm text-gray-500">Forum Creator</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Participants */}
            <Card>
              <CardHeader>
                <CardTitle className="text-sm flex items-center space-x-2">
                  <Users className="w-4 h-4" />
                  <span>Participants ({forum.participantCount || 0})</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {forum.participants && forum.participants.length > 0 ? (
                    forum.participants.slice(0, 5).map((participant: any, index: number) => (
                      <div key={index} className="flex items-center space-x-2">
                        <div className="w-6 h-6 bg-gray-100 rounded-full flex items-center justify-center">
                          <Users className="w-3 h-3 text-gray-600" />
                        </div>
                        <span className="text-sm text-gray-700">{participant.name || 'Anonymous'}</span>
                      </div>
                    ))
                  ) : (
                    <p className="text-sm text-gray-500">No participants yet</p>
                  )}

                  {forum.participants && forum.participants.length > 5 && (
                    <p className="text-xs text-gray-500">
                      +{forum.participants.length - 5} more participants
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Forum Stats */}
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Forum Statistics</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-500">Messages:</span>
                  <span className="font-medium">{forum.messageCount || 0}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-500">Participants:</span>
                  <span className="font-medium">{forum.participantCount || 0}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-500">Last Activity:</span>
                  <span className="font-medium">
                    {forum.lastMessageTime ? formatDate(forum.lastMessageTime) : 'No activity'}
                  </span>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Chat Interface */}
        <div className="flex-1 flex flex-col">
          <ChatInterface
            roomId={resolvedParams.id}
            roomType="forum"
            currentUserId={currentAdmin.id}
            currentUserName={currentAdmin.name}
            currentUserAvatar={currentAdmin.avatar}
            showHeader={false}
          />
        </div>
      </div>
    </motion.div>
  )
}
