import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:device_info_plus/device_info_plus.dart';

/// Utility class to check required permissions for file uploads
class FirebasePermissionsCheck {
  
  /// Check if all required permissions are granted for voice recording and file uploads
  static Future<bool> checkRequiredPermissions({bool includeCamera = true}) async {
    try {
      if (Platform.isAndroid) {
        final androidInfo = await DeviceInfoPlugin().androidInfo;
        final sdkVersion = androidInfo.version.sdkInt;
        
        if (kDebugMode) {
          print('Android SDK version: $sdkVersion');
        }
        
        // Check camera permission if required
        if (includeCamera) {
          final cameraStatus = await Permission.camera.status;
          if (!cameraStatus.isGranted) {
            final cameraRequest = await Permission.camera.request();
            if (!cameraRequest.isGranted) {
              if (kDebugMode) {
                print('Camera permission denied');
              }
              return false;
            }
          }
        }
        
        // Check permissions based on Android version
        if (sdkVersion >= 33) {
          // Android 13+ (API 33) requires granular media permissions
          final List<Permission> mediaPermissions = [
            Permission.photos,
            Permission.videos,
          ];
          
          for (final permission in mediaPermissions) {
            final status = await permission.status;
            if (!status.isGranted) {
              final request = await permission.request();
              if (!request.isGranted) {
                if (kDebugMode) {
                  print('Media permission denied: $permission');
                }
                return false;
              }
            }
          }
        } else {
          // Android 12 and below - use traditional storage permission
        final storageStatus = await Permission.storage.status;
        if (!storageStatus.isGranted) {
          final storageRequest = await Permission.storage.request();
          if (!storageRequest.isGranted) {
              if (kDebugMode) {
                print('Storage permission denied');
              }
              return false;
            }
          }
        }
        
        // Check microphone permission for audio features
        final micStatus = await Permission.microphone.status;
        if (!micStatus.isGranted) {
          final micRequest = await Permission.microphone.request();
          if (!micRequest.isGranted) {
            if (kDebugMode) {
              print('Microphone permission denied');
            }
            return false;
          }
        }
      } else if (Platform.isIOS) {
        // iOS permission handling
        final permissions = <Permission>[
          Permission.photos,
          if (includeCamera) Permission.camera,
          Permission.microphone,
        ];
        
        for (final permission in permissions) {
          final status = await permission.status;
          if (!status.isGranted) {
            final request = await permission.request();
            if (!request.isGranted) {
              if (kDebugMode) {
                print('iOS permission denied: $permission');
              }
            return false;
            }
          }
        }
      }
      
      // If we get here, all required permissions are granted
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('Error checking permissions: $e');
      }
      
      // Return false on error to prevent unauthorized access
      return false;
    }
  }
  
  /// Check if photo picker is available (Android 13+ or backported via Google Play Services)
  static Future<bool> isPhotoPickerAvailable() async {
      if (Platform.isAndroid) {
      final androidInfo = await DeviceInfoPlugin().androidInfo;
      final sdkVersion = androidInfo.version.sdkInt;
      
      // Native photo picker available on Android 13+
      if (sdkVersion >= 33) {
        return true;
      }
      
      // For older versions, assume Google Play Services backport is available
      // This is safer than trying to detect it programmatically
      return sdkVersion >= 19; // Android 4.4+
    }
    return false;
  }
  
  /// Get a user-friendly permission explanation
  static String getPermissionExplanation({bool includeCamera = true}) {
    if (Platform.isAndroid) {
      return includeCamera 
        ? 'This app needs camera and storage permissions to take photos and access your gallery.'
        : 'This app needs storage permissions to access your photos.';
    } else if (Platform.isIOS) {
      return includeCamera
        ? 'This app needs camera and photo library permissions to take and select photos.'
        : 'This app needs photo library permissions to select photos.';
    }
    return 'This app needs permissions to access your photos.';
  }
} 