# 🔐 GitHub Secrets Quick Reference

## Repository Information
- **Repository URL**: https://github.com/UzairDevelops/Drive_on
- **Secrets URL**: https://github.com/UzairDevelops/Drive_on/settings/secrets/actions
- **Actions URL**: https://github.com/UzairDevelops/Drive_on/actions

## Required Secrets

### 1. FIREBASE_SERVICE_ACCOUNT_DRIVE_ON_B2AF8
```
Name: FIREBASE_SERVICE_ACCOUNT_DRIVE_ON_B2AF8
Type: Repository Secret
Value: [Complete JSON from service account key file]
```

**How to get the value:**
1. Run: `firebase serviceaccount:generate-key service-account-key.json --project drive-on-b2af8`
2. Copy the entire JSON content from the generated file
3. Paste it as the secret value

### 2. FIREBASE_APP_ID
```
Name: FIREBASE_APP_ID
Type: Repository Secret
Value: 1:************:android:6a8e1d9c3c8992992d754d
```

**How to verify:**
1. Go to Firebase Console: https://console.firebase.google.com/project/drive-on-b2af8
2. Project Settings → Your apps → Android app
3. Copy the App ID

### 3. CODECOV_TOKEN (Optional)
```
Name: CODECOV_TOKEN
Type: Repository Secret
Value: [Your Codecov token]
```

**How to get:**
1. Go to https://codecov.io/
2. Sign in with GitHub
3. Add repository: UzairDevelops/Drive_on
4. Copy the repository token

## Quick Setup Commands

```bash
# Navigate to main app directory
cd main_app

# Run the GitHub setup script
scripts\setup-github.bat

# Or manual setup:
firebase login --email <EMAIL>
firebase use drive-on-b2af8
firebase serviceaccount:generate-key service-account-key.json --project drive-on-b2af8
```

## Verification Checklist

- [ ] Repository exists: https://github.com/UzairDevelops/Drive_on
- [ ] All 3 secrets are added to GitHub
- [ ] Firebase project is accessible
- [ ] Service account has proper permissions
- [ ] Firebase App Distribution is enabled
- [ ] Tester groups created (staging-testers, production-testers)

## Test Deployment

After setting up secrets, test with:

```bash
git add .
git commit -m "Test CI/CD pipeline"
git push origin main
```

Then check: https://github.com/UzairDevelops/Drive_on/actions

## Troubleshooting

### Secret Not Working
1. Check secret name matches exactly
2. Verify JSON format is valid
3. Ensure no extra spaces or characters

### Firebase Permission Error
1. Verify service account has "Firebase Admin SDK Administrator Service Agent" role
2. Check project access in Firebase Console

### App ID Error
1. Confirm App ID format: `1:PROJECT_NUMBER:android:APP_ID`
2. Verify Android app is configured in Firebase

## Support Links

- **Firebase Console**: https://console.firebase.google.com/project/drive-on-b2af8
- **GitHub Repository**: https://github.com/UzairDevelops/Drive_on
- **GitHub Actions**: https://github.com/UzairDevelops/Drive_on/actions
- **Firebase App Distribution**: https://console.firebase.google.com/project/drive-on-b2af8/appdistribution
