import 'package:flutter/material.dart';
import 'package:timeago/timeago.dart' as timeago;
import 'package:cached_network_image/cached_network_image.dart';
import '../../data/models/news_article.dart';

class ArticleCard extends StatelessWidget {
  final NewsArticle article;
  final bool isAdmin;
  final Function() onTap;
  final Function(NewsArticle)? onPinToggle;

  const ArticleCard({
    super.key,
    required this.article,
    required this.onTap,
    this.isAdmin = false,
    this.onPinToggle,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Image section
            if (article.imageUrl.isNotEmpty)
              ClipRRect(
                borderRadius: const BorderRadius.vertical(top: Radius.circular(4)),
                child: Stack(
                  children: [
                    AspectRatio(
                      aspectRatio: 16 / 9,
                      child: CachedNetworkImage(
                        imageUrl: article.imageUrl,
                        fit: BoxFit.cover,
                        memCacheWidth: 800, // Limit memory cache size
                        memCacheHeight: 450, // 16:9 ratio
                        maxWidthDiskCache: 800, // Limit disk cache size
                        placeholder: (context, url) => Container(
                          color: Colors.grey[200],
                          child: const Center(
                            child: CircularProgressIndicator(strokeWidth: 2),
                          ),
                        ),
                        errorWidget: (context, url, error) => Container(
                          color: Colors.grey[300],
                          child: const Center(
                            child: Icon(Icons.broken_image, size: 40),
                          ),
                        ),
                      ),
                    ),
                    if (article.pinned)
                      Positioned(
                        top: 8,
                        right: 8,
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: Theme.of(context).primaryColor,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: const Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(Icons.push_pin, color: Colors.white, size: 16),
                              SizedBox(width: 4),
                              Text(
                                'PINNED',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                  ],
                ),
              ),

            // Content section
            Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.grey[200],
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          article.category,
                          style: TextStyle(
                            color: Theme.of(context).primaryColor,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      if (article.source == 'Admin') ...[
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: Colors.orange[100],
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.orange[300]!),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(Icons.admin_panel_settings,
                                   size: 12, color: Colors.orange[700]),
                              const SizedBox(width: 2),
                              Text(
                                'ADMIN',
                                style: TextStyle(
                                  color: Colors.orange[700],
                                  fontSize: 10,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                      const Spacer(),
                      if (isAdmin && onPinToggle != null)
                        IconButton(
                          icon: Icon(
                            article.pinned ? Icons.push_pin : Icons.push_pin_outlined,
                            color: article.pinned
                                ? Theme.of(context).primaryColor
                                : Colors.grey,
                          ),
                          onPressed: () => onPinToggle!(article),
                          tooltip: article.pinned ? 'Unpin article' : 'Pin article',
                          padding: EdgeInsets.zero,
                          constraints: const BoxConstraints(),
                          iconSize: 20,
                        ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    article.title,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                      height: 1.3,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  // Show description for regular articles or adminContent preview for admin articles
                  if ((article.source == 'Admin' && article.adminContent != null && article.adminContent!.isNotEmpty) ||
                      (article.source != 'Admin' && article.description.isNotEmpty)) ...[
                    const SizedBox(height: 8),
                    Text(
                      article.source == 'Admin' && article.adminContent != null
                          ? article.adminContent!.length > 150
                              ? '${article.adminContent!.substring(0, 150)}...'
                              : article.adminContent!
                          : article.description,
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[700],
                        height: 1.3,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      Icon(Icons.source, size: 14, color: Colors.grey[600]),
                      const SizedBox(width: 4),
                      Text(
                        article.source,
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                      const SizedBox(width: 16),
                      Icon(Icons.access_time, size: 14, color: Colors.grey[600]),
                      const SizedBox(width: 4),
                      Text(
                        timeago.format(article.publishedAt),
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                      const Spacer(),
                      if (article.views > 0) ...[
                        Icon(Icons.visibility, size: 14, color: Colors.grey[600]),
                        const SizedBox(width: 4),
                        Text(
                          _formatViewCount(article.views),
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ],
                  ),

                  // Reactions summary
                  if (article.reactionCounts.isNotEmpty) ...[
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        _buildReactionSummary(article.reactionCounts),
                      ],
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReactionSummary(Map<String, int> reactionCounts) {
    final totalReactions = reactionCounts.values.fold<int>(
      0, (previousValue, count) => previousValue + count);

    if (totalReactions == 0) return const SizedBox.shrink();

    return Row(
      children: [
        Icon(Icons.thumb_up, size: 14, color: Colors.grey[600]),
        const SizedBox(width: 4),
        Text(
          totalReactions.toString(),
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  String _formatViewCount(int count) {
    if (count >= 1000000) {
      return '${(count / 1000000).toStringAsFixed(1)}M';
    } else if (count >= 1000) {
      return '${(count / 1000).toStringAsFixed(1)}K';
    }
    return count.toString();
  }
}