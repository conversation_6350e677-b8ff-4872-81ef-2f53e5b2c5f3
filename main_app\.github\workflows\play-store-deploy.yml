name: Deploy to Google Play Store

on:
  push:
    tags:
      - 'v*'
  workflow_dispatch:
    inputs:
      track:
        description: 'Deployment track'
        required: true
        default: 'internal'
        type: choice
        options:
          - internal
          - alpha
          - beta
          - production

jobs:
  deploy-play-store:
    name: Deploy to Google Play Store
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./main_app

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Java
        uses: actions/setup-java@v4
        with:
          distribution: 'zulu'
          java-version: '17'

      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.24.5'
          channel: 'stable'

      - name: Get Flutter dependencies
        run: flutter pub get

      - name: Setup Android keystore
        env:
          KEYSTORE_BASE64: ${{ secrets.ANDROID_KEYSTORE_BASE64 }}
          KEY_PROPERTIES: ${{ secrets.ANDROID_KEY_PROPERTIES }}
        run: |
          echo "$KEYSTORE_BASE64" | base64 --decode > android/app/drive-on-release-key.jks
          echo "$KEY_PROPERTIES" > android/key.properties

      - name: Build Android App Bundle
        run: |
          flutter build appbundle --release \
            --dart-define=ENVIRONMENT=production \
            --obfuscate \
            --split-debug-info=build/debug-info

      - name: Setup Ruby for Fastlane
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: '3.0'
          bundler-cache: true
          working-directory: main_app

      - name: Create Gemfile
        run: |
          cat > Gemfile << EOF
          source "https://rubygems.org"
          
          gem "fastlane"
          gem "supply"
          EOF

      - name: Install Fastlane
        run: |
          gem install bundler
          bundle install

      - name: Setup Play Store service account
        env:
          PLAY_STORE_SERVICE_ACCOUNT: ${{ secrets.PLAY_STORE_SERVICE_ACCOUNT_JSON }}
        run: |
          echo "$PLAY_STORE_SERVICE_ACCOUNT" > fastlane/play-store-service-account.json

      - name: Determine deployment track
        id: track
        run: |
          if [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
            echo "track=${{ github.event.inputs.track }}" >> $GITHUB_OUTPUT
          elif [[ "${{ github.ref }}" =~ refs/tags/v.*-alpha ]]; then
            echo "track=alpha" >> $GITHUB_OUTPUT
          elif [[ "${{ github.ref }}" =~ refs/tags/v.*-beta ]]; then
            echo "track=beta" >> $GITHUB_OUTPUT
          elif [[ "${{ github.ref }}" =~ refs/tags/v.* ]]; then
            echo "track=production" >> $GITHUB_OUTPUT
          else
            echo "track=internal" >> $GITHUB_OUTPUT
          fi

      - name: Deploy to Play Store
        run: |
          case "${{ steps.track.outputs.track }}" in
            "internal")
              bundle exec fastlane android internal
              ;;
            "alpha")
              bundle exec fastlane android alpha
              ;;
            "beta")
              bundle exec fastlane android beta
              ;;
            "production")
              bundle exec fastlane android production
              ;;
          esac

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: play-store-release-${{ steps.track.outputs.track }}
          path: |
            main_app/build/app/outputs/bundle/release/app-release.aab
            main_app/build/app/outputs/mapping/release/mapping.txt

      - name: Create GitHub Release
        if: startsWith(github.ref, 'refs/tags/v')
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: ${{ github.ref_name }}
          release_name: Drive-On ${{ github.ref_name }}
          body: |
            🚀 Drive-On Release ${{ github.ref_name }}
            
            Deployed to Google Play Store: ${{ steps.track.outputs.track }} track
            
            📱 **What's New:**
            - Check the Play Store listing for detailed release notes
            
            🔧 **Technical Details:**
            - Built with Flutter 3.24.5
            - Target SDK: Android 14 (API 35)
            - Minimum SDK: Android 6.0 (API 23)
            - Signed with production keystore
            - ProGuard obfuscation enabled
          draft: false
          prerelease: ${{ steps.track.outputs.track != 'production' }}

      - name: Cleanup sensitive files
        if: always()
        run: |
          rm -f android/app/drive-on-release-key.jks
          rm -f android/key.properties
          rm -f fastlane/play-store-service-account.json
