'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import {
  X,
  Building2,
  Mail,
  Phone,
  MapPin,
  Calendar,
  CheckCircle,
  XCircle,
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { formatDate } from '@/lib/utils'

interface Partner {
  id: string
  companyName: string
  email: string
  phone: string
  address: string
  description: string
  status: 'pending' | 'approved' | 'rejected'
  createdAt: Date
  approvedAt?: Date
  approvedBy?: string
  rejectionReason?: string
  welcomeEmailSent?: boolean
}

interface PartnerModalProps {
  partner: Partner | null
  mode: 'view' | 'edit' | 'create'
  isOpen: boolean
  onClose: () => void
  onSave: (partnerData: Partial<Partner>) => Promise<void>
}

// Import the ContactInfoModal component
import { ContactInfoModal } from './contact-info-modal'

export function PartnerModal({ partner, mode, isOpen, onClose, onSave }: PartnerModalProps) {
  const [formData, setFormData] = useState({
    companyName: '',
    email: '',
    phone: '',
    address: '',
    description: '',
    status: 'pending' as Partner['status'],
    welcomeEmailSent: false
  })
  const [isLoading, setIsLoading] = useState(false)
  const [contactModalOpen, setContactModalOpen] = useState(false)
  const [contactType, setContactType] = useState<'email' | 'phone'>('email')

  useEffect(() => {
    if (partner && (mode === 'view' || mode === 'edit')) {
      setFormData({
        companyName: partner.companyName || '',
        email: partner.email || '',
        phone: partner.phone || '',
        address: partner.address || '',
        description: partner.description || '',
        status: partner.status || 'pending',
        welcomeEmailSent: partner.welcomeEmailSent || false
      })
    } else if (mode === 'create') {
      setFormData({
        companyName: '',
        email: '',
        phone: '',
        address: '',
        description: '',
        status: 'pending',
        welcomeEmailSent: false
      })
    }
  }, [partner, mode])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      await onSave(formData)
    } catch (error) {
      console.error('Error saving partner:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleShowEmail = () => {
    if (!partner) return
    setContactType('email')
    setContactModalOpen(true)
  }

  const handleShowPhone = () => {
    if (!partner) return
    setContactType('phone')
    setContactModalOpen(true)
  }



  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="absolute inset-0 bg-black/50"
        onClick={onClose}
      />

      {/* Modal */}
      <motion.div
        initial={{ opacity: 0, scale: 0.95, y: 20 }}
        animate={{ opacity: 1, scale: 1, y: 0 }}
        exit={{ opacity: 0, scale: 0.95, y: 20 }}
        className="relative w-full max-w-4xl mx-4 bg-background rounded-lg shadow-xl max-h-[90vh] overflow-y-auto"
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-border">
          <div className="flex items-center space-x-3">
            <h2 className="text-xl font-semibold text-foreground">
              {mode === 'create' ? 'Add New Partner' :
               mode === 'edit' ? 'Edit Partner' : 'Partner Details'}
            </h2>
            {partner && (
              <Badge variant={
                partner.status === 'approved' ? 'success' :
                partner.status === 'rejected' ? 'error' : 'info'
              }>
                {partner.status}
              </Badge>
            )}
          </div>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="w-4 h-4" />
          </Button>
        </div>

        {/* Content */}
        <div className="p-6">
          {mode === 'view' ? (
            // View Mode
            <div className="space-y-6">
              {/* Partner Header */}
              <div className="flex items-start space-x-4">
                <div className="w-16 h-16 bg-primary-100 rounded-lg flex items-center justify-center">
                  <Building2 className="w-8 h-8 text-primary-600" />
                </div>
                <div className="flex-1">
                  <h3 className="text-xl font-semibold text-gray-900">{partner?.companyName}</h3>
                  <p className="text-gray-600">{partner?.email}</p>
                  <p className="text-sm text-gray-500">{partner?.description}</p>
                </div>
              </div>

              {/* Partner Details */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">Contact Information</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex items-center space-x-2">
                      <Mail className="w-4 h-4 text-gray-400" />
                      <span className="text-sm">{partner?.email}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Phone className="w-4 h-4 text-gray-400" />
                      <span className="text-sm">{partner?.phone}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <MapPin className="w-4 h-4 text-gray-400" />
                      <span className="text-sm">{partner?.address}</span>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">Partnership Information</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex items-center space-x-2">
                      <Calendar className="w-4 h-4 text-gray-400" />
                      <span className="text-sm">Applied {partner?.createdAt ? formatDate(partner.createdAt) : 'N/A'}</span>
                    </div>
                    {partner?.approvedAt && (
                      <div className="flex items-center space-x-2">
                        <CheckCircle className="w-4 h-4 text-gray-400" />
                        <span className="text-sm">Approved {formatDate(partner.approvedAt)}</span>
                      </div>
                    )}
                    {partner?.rejectionReason && (
                      <div className="flex items-center space-x-2">
                        <XCircle className="w-4 h-4 text-gray-400" />
                        <span className="text-sm text-red-600">{partner.rejectionReason}</span>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>

              {/* Description */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Description</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-600">{partner?.description || 'No description provided'}</p>
                </CardContent>
              </Card>

              {/* Contact Actions */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Contact Partner</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-3">
                    <Button
                      onClick={handleShowEmail}
                      variant="outline"
                      className="text-blue-600 border-blue-600 hover:bg-blue-50"
                    >
                      <Mail className="w-4 h-4 mr-2" />
                      Show Partner Email
                    </Button>
                    <Button
                      onClick={handleShowPhone}
                      variant="outline"
                      className="text-green-600 border-green-600 hover:bg-green-50"
                    >
                      <Phone className="w-4 h-4 mr-2" />
                      Show Partner Phone
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          ) : (
            // Edit/Create Mode
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="companyName">Company Name</Label>
                  <Input
                    id="companyName"
                    value={formData.companyName}
                    onChange={(e) => handleInputChange('companyName', e.target.value)}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    value={formData.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="phone">Phone</Label>
                  <Input
                    id="phone"
                    value={formData.phone}
                    onChange={(e) => handleInputChange('phone', e.target.value)}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="address">Address</Label>
                  <Input
                    id="address"
                    value={formData.address}
                    onChange={(e) => handleInputChange('address', e.target.value)}
                    required
                  />
                </div>
              </div>

              {/* Description */}
              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Input
                  id="description"
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  placeholder="e.g., Number of cars: 5"
                />
              </div>

              {/* Actions */}
              <div className="flex justify-end space-x-3 pt-6 border-t">
                <Button type="button" variant="outline" onClick={onClose}>
                  Cancel
                </Button>
                <Button type="submit" disabled={isLoading} className="gradient-primary">
                  {isLoading ? 'Saving...' : mode === 'create' ? 'Create Partner' : 'Save Changes'}
                </Button>
              </div>
            </form>
          )}
        </div>
      </motion.div>

      {/* Contact Info Modal */}
      <ContactInfoModal
        partner={partner}
        type={contactType}
        isOpen={contactModalOpen}
        onClose={() => setContactModalOpen(false)}
      />
    </div>
  )
}
