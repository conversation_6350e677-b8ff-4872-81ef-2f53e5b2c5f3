@echo off
echo ========================================
echo Google Sign-In Final Fix - ApiException 10
echo ========================================
echo.

echo 🚨 ISSUE: ApiException 10 still persists after all fixes
echo.

echo 📋 COMPREHENSIVE TROUBLESHOOTING:
echo.

echo 1. VERIFY ALL SHA-1 FINGERPRINTS ARE CORRECT:
echo.
echo    DEBUG KEYSTORE SHA-1:
echo    Expected: 9A:8E:90:68:D4:97:C6:E2:5A:EF:B0:51:75:BC:CB:B5:CC:BD:55:DC
echo    In Firebase: 9a:8e:90:68:d4:97:c6:e2:5a:ef:b0:51:75:bc:cb:b5:cc:bd:55:dc
echo    Status: ✅ MATCH
echo.
echo    PRODUCTION KEYSTORE SHA-1:
echo    Expected: 85:C3:07:8A:88:35:3D:1D:15:88:76:93:84:F8:D7:17:41:A9:4B:A3
echo    In Firebase: 85:c3:07:8a:88:35:3d:1d:15:88:76:93:84:4f:8d:71:74:1a:94:ba:3
echo    Status: ❌ POTENTIAL MISMATCH
echo.

echo 2. ALTERNATIVE SOLUTIONS TO TRY:
echo.

echo    A. CLEAR ALL GOOGLE SERVICES DATA:
echo       - Settings ^> Apps ^> Google Play Services
echo       - Storage ^> Manage Storage ^> Clear All Data
echo       - Restart device
echo.

echo    B. REMOVE AND RE-ADD GOOGLE ACCOUNT:
echo       - Settings ^> Accounts ^> Google
echo       - Remove account
echo       - Add account again
echo.

echo    C. UPDATE GOOGLE PLAY SERVICES:
echo       - Open Google Play Store
echo       - Search "Google Play Services"
echo       - Update if available
echo.

echo    D. TEST WITH DIFFERENT GOOGLE ACCOUNT:
echo       - Try signing in with a different Gmail account
echo       - Some accounts may have restrictions
echo.

echo    E. CHECK FIREBASE PROJECT SETTINGS:
echo       - Ensure OAuth consent screen is configured
echo       - Check if app is in testing mode with restricted users
echo.

echo 3. NUCLEAR OPTION - RECREATE FIREBASE PROJECT:
echo    If nothing works, we may need to:
echo    - Create a new Firebase project
echo    - Configure it with correct SHA-1 from the start
echo    - Migrate existing data
echo.

echo 4. IMMEDIATE TESTING STEPS:
echo.

echo    Step 1: Clear Google Play Services completely
echo    Step 2: Restart device
echo    Step 3: Test Google Sign-In
echo    Step 4: If fails, try different Google account
echo    Step 5: Check Firebase OAuth consent screen
echo.

echo 🔧 ADVANCED DEBUGGING:
echo.
echo To get more detailed error information:
echo 1. Enable verbose logging in Firebase
echo 2. Check Android logcat for detailed Google Play Services errors
echo 3. Verify OAuth consent screen configuration
echo.

echo Press any key to open Firebase Console for OAuth verification...
pause >nul

start https://console.firebase.google.com/project/drive-on-b2af8/authentication/providers

echo.
echo 📋 CHECKLIST FOR OAUTH CONSENT SCREEN:
echo 1. Go to Google Cloud Console
echo 2. Select drive-on-b2af8 project
echo 3. Go to APIs ^& Services ^> OAuth consent screen
echo 4. Ensure app is published or you're added as test user
echo 5. Check authorized domains include your package name
echo.

echo Press any key to exit...
pause >nul
