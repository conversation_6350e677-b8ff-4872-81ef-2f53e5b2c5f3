import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:image_picker/image_picker.dart';
import '../../../core/core.dart';
import '../../../core/security/input_validator.dart';
import '../../../core/firebase/firebase_permissions_check.dart';
import '../../../core/services/email_uniqueness_service.dart';
import '../models/driver.dart';
import 'package:path/path.dart' as path;

// Create a logger directly
final _logger = AppLogger('UnifiedDriverRegistration');

class UnifiedDriverRegistrationScreen extends StatefulWidget {
  final VoidCallback? onRegistrationComplete;

  const UnifiedDriverRegistrationScreen({
    super.key,
    this.onRegistrationComplete,
  });

  @override
  State<UnifiedDriverRegistrationScreen> createState() => _UnifiedDriverRegistrationScreenState();
}

class _UnifiedDriverRegistrationScreenState extends State<UnifiedDriverRegistrationScreen> {
  final _formKey = GlobalKey<FormState>();

  // Personal details controllers
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _mobileController = TextEditingController();
  final TextEditingController _cityController = TextEditingController();
  final TextEditingController _educationController = TextEditingController();
  final TextEditingController _experienceController = TextEditingController();

  // Email field (read-only, fetched automatically)
  String? _userEmail;

  String _maritalStatus = 'Single';
  final List<String> _maritalStatusOptions = ['Single', 'Married', 'Divorced', 'Widowed'];

  // Document upload state
  final List<DocumentType> _documentTypes = [
    DocumentType(name: 'Driver License', isRequired: true, description: 'Clear image of valid driving license'),
    DocumentType(name: 'ID Card (Front)', isRequired: true, description: 'Front side of your identity card'),
    DocumentType(name: 'ID Card (Back)', isRequired: true, description: 'Back side of your identity card'),
    DocumentType(name: 'Current Month Electricity Bill', isRequired: true, description: 'This month\'s bill with your name or your father\'s name'),
    DocumentType(name: 'Police Verification', isRequired: true, description: 'Police character certificate or verification'),
    DocumentType(name: 'Passport Photo', isRequired: true, description: 'Recent passport-sized photograph'),
    DocumentType(name: 'Education Certificate', isRequired: false, description: 'Your highest education certificate'),
    DocumentType(name: 'Previous Experience', isRequired: false, description: 'Proof of previous driving experience'),
  ];

  final Map<String, File?> _selectedFiles = {};
  final Map<String, bool> _isUploading = {};
  final Map<String, String> _uploadedUrls = {};
  final Map<String, double> _uploadProgress = {};

  bool _isSubmitting = false;

  // Current step in the registration process
  int _currentStep = 0;
  final PageController _pageController = PageController();

  @override
  void initState() {
    super.initState();
    // Initialize maps for each document type
    for (final docType in _documentTypes) {
      _selectedFiles[docType.name] = null;
      _isUploading[docType.name] = false;
      _uploadProgress[docType.name] = 0.0;
    }
    // Fetch user email
    _fetchUserEmail();
  }

  /// Fetch the current user's email
  Future<void> _fetchUserEmail() async {
    try {
      final email = await EmailUniquenessService.instance.getCurrentUserEmail();
      if (mounted) {
        setState(() {
          _userEmail = email;
        });
      }
      _logger.info('Fetched user email: $email');
    } catch (e) {
      _logger.error('Error fetching user email', error: e);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Error fetching user email. Please try again.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _mobileController.dispose();
    _cityController.dispose();
    _educationController.dispose();
    _experienceController.dispose();
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      appBar: AppBar(
        title: Text(_currentStep == 0 ? 'Driver Registration' : 'Upload Documents'),
        backgroundColor: isDarkMode ? Colors.black : Colors.white,
        elevation: 0,
      ),
      body: SafeArea(
        child: _isSubmitting
            ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const CircularProgressIndicator(
                      color: AppColors.primaryYellow,
                    ),
                    const SizedBox(height: 24),
                    Text(
                      'Submitting your registration request...',
                      style: TextStyle(
                        color: isDarkMode ? AppColors.darkText : AppColors.lightText,
                      ),
                    ),
                  ],
                ),
              )
            : PageView(
                controller: _pageController,
                physics: const NeverScrollableScrollPhysics(),
                children: [
                  // Step 1: Personal Information
                  _buildPersonalInfoStep(context),
                  // Step 2: Document Upload
                  _buildDocumentUploadStep(context),
                ],
              ),
      ),
    );
  }

  Widget _buildPersonalInfoStep(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return SingleChildScrollView(
      physics: const BouncingScrollPhysics(),
      padding: const EdgeInsets.all(16.0),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Center(
              child: Column(
                children: [
                  const Icon(
                    Icons.person_add_outlined,
                    size: 64,
                    color: AppColors.primaryYellow,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Register as a Driver',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: isDarkMode ? AppColors.darkText : AppColors.lightText,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Complete your personal information to get started',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 16,
                      color: isDarkMode ? AppColors.darkTextSecondary : AppColors.lightTextSecondary,
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 32),

            // Progress indicator
            _buildStepIndicator(context, 0),

            const SizedBox(height: 32),

            // Personal Information Section
            _buildSectionHeader(context, 'Personal Information'),
            const SizedBox(height: 16),

            // Email field (read-only)
            _buildEmailField(context),
            const SizedBox(height: 16),

            _buildTextFormField(
              context: context,
              controller: _nameController,
              label: 'Full Name',
              hint: 'Enter your full name',
              icon: Icons.person_outline,
              validator: (value) => InputValidator.validateRequired(value, 'full name'),
            ),

            const SizedBox(height: 16),

            _buildTextFormField(
              context: context,
              controller: _mobileController,
              label: 'Mobile Number',
              hint: 'Enter your mobile number',
              icon: Icons.phone_outlined,
              keyboardType: TextInputType.phone,
              validator: InputValidator.validatePakistanPhone,
            ),

            const SizedBox(height: 16),

            _buildTextFormField(
              context: context,
              controller: _cityController,
              label: 'City',
              hint: 'Enter your city',
              icon: Icons.location_city_outlined,
              validator: (value) => InputValidator.validateRequired(value, 'city'),
            ),

            const SizedBox(height: 16),

            // Marital Status Dropdown
            _buildDropdownField(
              context: context,
              label: 'Marital Status',
              icon: Icons.family_restroom_outlined,
              value: _maritalStatus,
              items: _maritalStatusOptions.map((String status) {
                return DropdownMenuItem<String>(
                  value: status,
                  child: Text(status),
                );
              }).toList(),
              onChanged: (String? newValue) {
                if (newValue != null) {
                  setState(() {
                    _maritalStatus = newValue;
                  });
                }
              },
            ),

            const SizedBox(height: 32),

            // Professional Information Section
            _buildSectionHeader(context, 'Professional Information'),
            const SizedBox(height: 16),

            _buildTextFormField(
              context: context,
              controller: _educationController,
              label: 'Education',
              hint: 'Enter your highest education',
              icon: Icons.school_outlined,
              validator: (value) => InputValidator.validateRequired(value, 'education'),
            ),

            const SizedBox(height: 16),

            _buildTextFormField(
              context: context,
              controller: _experienceController,
              label: 'Driving Experience (Years)',
              hint: 'Enter years of driving experience',
              icon: Icons.drive_eta_outlined,
              keyboardType: TextInputType.number,
              validator: (value) => InputValidator.validateNumeric(value, 'driving experience'),
            ),

            const SizedBox(height: 32),

            // Continue Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _goToDocumentUpload,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primaryYellow,
                  foregroundColor: Colors.black,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: const Text(
                  'Continue to Document Upload',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),

            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildDocumentUploadStep(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return SingleChildScrollView(
      physics: const BouncingScrollPhysics(),
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Center(
            child: Column(
              children: [
                const Icon(
                  Icons.upload_file,
                  size: 64,
                  color: AppColors.primaryYellow,
                ),
                const SizedBox(height: 16),
                Text(
                  'Upload Your Documents',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: isDarkMode ? AppColors.darkText : AppColors.lightText,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Please upload all required documents to complete your registration',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 16,
                    color: isDarkMode ? AppColors.darkTextSecondary : AppColors.lightTextSecondary,
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Progress indicator
          _buildStepIndicator(context, 1),

          const SizedBox(height: 24),

          // Warning box
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.warning.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: AppColors.warning,
                width: 1,
              ),
            ),
            child: Column(
              children: [
                const Row(
                  children: [
                    Icon(
                      Icons.warning_amber_rounded,
                      color: AppColors.warning,
                      size: 24,
                    ),
                    SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'Document Verification Notice',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: AppColors.warning,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  'Please ensure all uploaded documents are clear, legible, and valid. Uploading incorrect, expired, or unclear documents will result in immediate rejection of your application.',
                  style: TextStyle(
                    color: isDarkMode ? AppColors.darkText : AppColors.lightText,
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Document upload progress indicator
          _buildDocumentProgress(context),

          const SizedBox(height: 24),

          // Required Documents Section
          _buildSectionHeader(context, 'Required Documents'),
          const SizedBox(height: 16),

          ..._documentTypes.where((doc) => doc.isRequired).map((doc) =>
            _buildDocumentUploadCard(context, doc),
          ),

          const SizedBox(height: 32),

          // Optional Documents Section
          _buildSectionHeader(context, 'Optional Documents'),
          const SizedBox(height: 16),

          ..._documentTypes.where((doc) => !doc.isRequired).map((doc) =>
            _buildDocumentUploadCard(context, doc),
          ),

          const SizedBox(height: 32),

          // Navigation buttons
          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: _goBackToPersonalInfo,
                  style: OutlinedButton.styleFrom(
                    foregroundColor: AppColors.primaryYellow,
                    side: const BorderSide(color: AppColors.primaryYellow),
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: const Text(
                    'Back',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                flex: 2,
                child: ElevatedButton(
                  onPressed: _canSubmit() ? _submitRegistration : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _canSubmit() ? AppColors.primaryYellow : Colors.grey.shade300,
                    foregroundColor: _canSubmit() ? Colors.black : Colors.grey.shade700,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: const Text(
                    'Submit Registration',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 32),
        ],
      ),
    );
  }

  /// Build email field (read-only, shows fetched email)
  Widget _buildEmailField(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Email Address',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: isDarkMode ? AppColors.darkText : AppColors.lightText,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
          decoration: BoxDecoration(
            color: isDarkMode ? AppColors.darkSurface : Colors.grey.shade100,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: isDarkMode ? Colors.grey.shade600 : Colors.grey.shade300,
              width: 1,
            ),
          ),
          child: Row(
            children: [
              const Icon(
                Icons.email_outlined,
                color: AppColors.primaryYellow,
                size: 20,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  _userEmail ?? 'Loading email...',
                  style: TextStyle(
                    fontSize: 16,
                    color: _userEmail != null
                        ? (isDarkMode ? AppColors.darkText : AppColors.lightText)
                        : Colors.grey,
                  ),
                ),
              ),
              if (_userEmail != null)
                const Icon(
                  Icons.verified_user,
                  color: AppColors.success,
                  size: 20,
                ),
            ],
          ),
        ),
        const SizedBox(height: 4),
        Text(
          'This email will be used for driver verification and communication',
          style: TextStyle(
            fontSize: 12,
            color: isDarkMode ? AppColors.darkTextSecondary : AppColors.lightTextSecondary,
          ),
        ),
      ],
    );
  }

  Widget _buildStepIndicator(BuildContext context, int currentStep) {
    return Row(
      children: [
        // Step 1
        Container(
          width: 30,
          height: 30,
          decoration: BoxDecoration(
            color: currentStep >= 0 ? AppColors.primaryYellow : Colors.grey.shade300,
            shape: BoxShape.circle,
          ),
          child: Center(
            child: currentStep > 0
                ? const Icon(Icons.check, color: Colors.black, size: 16)
                : Text(
                    '1',
                    style: TextStyle(
                      color: currentStep >= 0 ? Colors.black : Colors.grey.shade600,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
          ),
        ),
        Expanded(
          child: Container(
            height: 2,
            color: currentStep >= 1 ? AppColors.primaryYellow : Colors.grey.shade300,
          ),
        ),
        // Step 2
        Container(
          width: 30,
          height: 30,
          decoration: BoxDecoration(
            color: currentStep >= 1 ? AppColors.primaryYellow : Colors.grey.shade300,
            shape: BoxShape.circle,
          ),
          child: Center(
            child: Text(
              '2',
              style: TextStyle(
                color: currentStep >= 1 ? Colors.black : Colors.grey.shade600,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDocumentProgress(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    // Get required document types
    final requiredDocTypes = _documentTypes.where((doc) => doc.isRequired).toList();

    // Count how many required documents have been uploaded
    final uploadedCount = requiredDocTypes
        .where((doc) => _uploadedUrls.containsKey(doc.name))
        .length;

    final totalRequired = requiredDocTypes.length;
    final progress = totalRequired > 0 ? uploadedCount / totalRequired : 0.0;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDarkMode ? AppColors.darkSurface : Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Document Upload Progress',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: isDarkMode ? AppColors.darkText : AppColors.lightText,
                ),
              ),
              Text(
                '$uploadedCount/$totalRequired',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: progress == 1.0
                      ? AppColors.success
                      : AppColors.primaryYellow,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: progress,
            backgroundColor: Colors.grey.shade300,
            color: progress == 1.0
                ? AppColors.success
                : AppColors.primaryYellow,
            minHeight: 8,
            borderRadius: BorderRadius.circular(4),
          ),
          const SizedBox(height: 8),
          Text(
            progress == 1.0
                ? 'All required documents uploaded!'
                : 'Please upload all required documents to proceed',
            style: TextStyle(
              fontSize: 14,
              color: isDarkMode ? AppColors.darkTextSecondary : AppColors.lightTextSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(BuildContext context, String title) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: isDarkMode ? AppColors.darkText : AppColors.lightText,
          ),
        ),
        const SizedBox(height: 8),
        Divider(
          color: isDarkMode ? Colors.white24 : Colors.black12,
          thickness: 1,
        ),
      ],
    );
  }

  Widget _buildTextFormField({
    required BuildContext context,
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
  }) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontWeight: FontWeight.w500,
            color: isDarkMode ? AppColors.darkText : AppColors.lightText,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            color: isDarkMode ? AppColors.darkSurface : Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: TextFormField(
            controller: controller,
            keyboardType: keyboardType,
            style: TextStyle(
              color: isDarkMode ? AppColors.darkText : AppColors.lightText,
            ),
            decoration: InputDecoration(
              hintText: hint,
              hintStyle: TextStyle(
                color: isDarkMode ? Colors.white38 : Colors.black38,
              ),
              prefixIcon: Icon(
                icon,
                color: AppColors.primaryYellow,
              ),
              border: InputBorder.none,
              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
            ),
            validator: validator,
          ),
        ),
      ],
    );
  }

  Widget _buildDropdownField({
    required BuildContext context,
    required String label,
    required IconData icon,
    required String value,
    required List<DropdownMenuItem<String>> items,
    required void Function(String?) onChanged,
  }) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontWeight: FontWeight.w500,
            color: isDarkMode ? AppColors.darkText : AppColors.lightText,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            color: isDarkMode ? AppColors.darkSurface : Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: DropdownButtonFormField<String>(
            value: value,
            items: items,
            onChanged: onChanged,
            decoration: InputDecoration(
              prefixIcon: Icon(
                icon,
                color: AppColors.primaryYellow,
              ),
              border: InputBorder.none,
              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            ),
            style: TextStyle(
              color: isDarkMode ? AppColors.darkText : AppColors.lightText,
            ),
            dropdownColor: isDarkMode ? AppColors.darkSurface : Colors.white,
            icon: const Icon(
              Icons.arrow_drop_down,
              color: AppColors.primaryYellow,
            ),
          ),
        ),
      ],
    );
  }

  // Navigation methods
  Future<void> _goToDocumentUpload() async {
    if (_formKey.currentState!.validate()) {
      // Check email uniqueness before proceeding
      final validation = await EmailUniquenessService.instance.validateDriverRequestEligibility();

      if (!validation.isValid) {
        if (mounted) {
          _showErrorDialog(
            'Registration Not Allowed',
            validation.message,
          );
        }
        return;
      }

      setState(() {
        _currentStep = 1;
      });
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _goBackToPersonalInfo() {
    setState(() {
      _currentStep = 0;
    });
    _pageController.previousPage(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  bool _canSubmit() {
    // Check if any upload is in progress
    final anyUploading = _isUploading.values.any((isUploading) => isUploading);

    // Get the list of required document types
    final requiredDocTypes = _documentTypes.where((doc) => doc.isRequired).map((doc) => doc.name).toList();

    // Check if all required documents have been uploaded
    final allRequiredDocumentsUploaded = requiredDocTypes.every((docType) => _uploadedUrls.containsKey(docType));

    // Only enable button if:
    // 1. No uploads are in progress
    // 2. Not already submitting
    // 3. All required documents have been uploaded
    return !anyUploading && !_isSubmitting && allRequiredDocumentsUploaded;
  }

  Widget _buildDocumentUploadCard(BuildContext context, DocumentType documentType) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final isSelected = _selectedFiles[documentType.name] != null;
    final isUploading = _isUploading[documentType.name] ?? false;
    final isUploaded = _uploadedUrls.containsKey(documentType.name);
    final progress = _uploadProgress[documentType.name] ?? 0.0;

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDarkMode ? AppColors.darkSurface : AppColors.lightSurface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: isDarkMode
                ? Colors.black.withOpacity(0.3)
                : Colors.black.withOpacity(0.05),
            blurRadius: isDarkMode ? 8 : 10,
            offset: const Offset(0, 2),
          ),
        ],
        border: isSelected
            ? Border.all(
                color: isUploaded
                    ? AppColors.success
                    : AppColors.primaryYellow,
                width: 2,
              )
            : null,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                _getDocumentIcon(documentType.name),
                color: AppColors.primaryYellow,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      documentType.name,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: isDarkMode ? AppColors.darkText : AppColors.lightText,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                          decoration: BoxDecoration(
                            color: documentType.isRequired
                                ? AppColors.warning.withOpacity(0.2)
                                : Colors.grey.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            documentType.isRequired ? 'Required' : 'Optional',
                            style: TextStyle(
                              fontSize: 12,
                              color: documentType.isRequired
                                  ? AppColors.warning
                                  : Colors.grey,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Text(
                      documentType.description,
                      style: TextStyle(
                        fontSize: 12,
                        color: isDarkMode ? AppColors.darkTextSecondary : AppColors.lightTextSecondary,
                      ),
                    ),
                  ],
                ),
              ),
              if (isUploaded)
                const Icon(
                  Icons.check_circle_outline,
                  color: AppColors.success,
                ),
            ],
          ),

          // Show upload progress when uploading
          if (isUploading)
            Column(
              children: [
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              const Text(
                                'Uploading...',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                  color: AppColors.primaryYellow,
                                ),
                              ),
                              Text(
                                '${(progress * 100).toInt()}%',
                                style: const TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.primaryYellow,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          LinearProgressIndicator(
                            value: progress,
                            backgroundColor: Colors.grey.shade300,
                            color: AppColors.primaryYellow,
                            minHeight: 6,
                            borderRadius: BorderRadius.circular(3),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),

          // Show image preview for uploaded documents
          if (isUploaded)
            Column(
              children: [
                const SizedBox(height: 16),
                Container(
                  height: 120,
                  width: double.infinity,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: AppColors.success.withOpacity(0.3),
                      width: 2,
                    ),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(6),
                    child: Stack(
                      children: [
                        // Image preview
                        Positioned.fill(
                          child: Image.network(
                            _uploadedUrls[documentType.name]!,
                            fit: BoxFit.cover,
                            loadingBuilder: (context, child, loadingProgress) {
                              if (loadingProgress == null) return child;
                              return Center(
                                child: CircularProgressIndicator(
                                  value: loadingProgress.expectedTotalBytes != null
                                      ? loadingProgress.cumulativeBytesLoaded /
                                          loadingProgress.expectedTotalBytes!
                                      : null,
                                  color: AppColors.primaryYellow,
                                ),
                              );
                            },
                            errorBuilder: (context, error, stackTrace) {
                              return Container(
                                color: Colors.grey.shade200,
                                child: const Center(
                                  child: Icon(
                                    Icons.image_not_supported,
                                    color: Colors.grey,
                                    size: 40,
                                  ),
                                ),
                              );
                            },
                          ),
                        ),
                        // Success overlay
                        Positioned(
                          top: 8,
                          right: 8,
                          child: Container(
                            padding: const EdgeInsets.all(4),
                            decoration: BoxDecoration(
                              color: AppColors.success,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: const Icon(
                              Icons.check,
                              color: Colors.white,
                              size: 16,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),

          const SizedBox(height: 16),

          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              if (isUploaded)
                const Expanded(
                  child: Text(
                    'Document uploaded successfully',
                    style: TextStyle(
                      color: AppColors.success,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                )
              else if (!isUploading)
                Expanded(
                  child: Text(
                    'Select a clear, readable image',
                    style: TextStyle(
                      color: isDarkMode ? AppColors.darkTextSecondary : AppColors.lightTextSecondary,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                )
              else
                const Expanded(child: SizedBox()),

              const SizedBox(width: 8),

              // Action buttons
              if (!isUploaded && !isUploading)
                ElevatedButton.icon(
                  onPressed: () => _pickDocument(documentType.name),
                  icon: const Icon(
                    Icons.add_photo_alternate,
                    size: 18,
                  ),
                  label: const Text('Select Image'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primaryYellow,
                    foregroundColor: Colors.black,
                  ),
                )
              else if (isUploaded)
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Replace button
                    OutlinedButton.icon(
                      onPressed: () => _replaceDocument(documentType.name),
                      icon: const Icon(Icons.edit_outlined, size: 16),
                      label: const Text('Replace'),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: AppColors.primaryYellow,
                        side: const BorderSide(color: AppColors.primaryYellow),
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      ),
                    ),
                    const SizedBox(width: 8),
                    // Delete button
                    OutlinedButton.icon(
                      onPressed: () => _removeDocument(documentType.name),
                      icon: const Icon(Icons.delete_outline, size: 16),
                      label: const Text('Delete'),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: AppColors.error,
                        side: const BorderSide(color: AppColors.error),
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      ),
                    ),
                  ],
                ),
            ],
          ),
        ],
      ),
    );
  }

  IconData _getDocumentIcon(String documentType) {
    switch (documentType) {
      case 'Driver License':
        return Icons.drive_eta;
      case 'ID Card (Front)':
        return Icons.perm_identity;
      case 'ID Card (Back)':
        return Icons.perm_identity;
      case 'Current Month Electricity Bill':
        return Icons.electric_bolt;
      case 'Police Verification':
        return Icons.verified_user;
      case 'Passport Photo':
        return Icons.portrait;
      case 'Education Certificate':
        return Icons.school;
      case 'Previous Experience':
        return Icons.work;
      default:
        return Icons.description;
    }
  }

  Future<void> _pickDocument(String documentType) async {
    final ImagePicker picker = ImagePicker();

    try {
      // Check permissions first
      if (!await FirebasePermissionsCheck.checkRequiredPermissions()) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Storage and camera permissions are required'),
              backgroundColor: Colors.red,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
        return;
      }

      // Always pick a new file (for both new selection and replacement)
      final XFile? pickedFile = await picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1200,
        maxHeight: 1200,
        imageQuality: 85,
      );

      if (pickedFile != null) {
        final file = File(pickedFile.path);

        // Verify the file exists and has content
        if (await file.exists()) {
          final fileSize = await file.length();
          if (fileSize > 0) {
            // Enforce max size of 1 MB for driver document images
            const int maxBytes = 1 * 1024 * 1024; // 1 MB
            if (fileSize > maxBytes) {
              _showErrorSnackBar('Image too large. Please select an image up to 1 MB.');
              return;
            }

            // Extra safety: ensure selected file has an image extension
            final String lowerPath = file.path.toLowerCase();
            final bool isImage = lowerPath.endsWith('.jpg') ||
                lowerPath.endsWith('.jpeg') ||
                lowerPath.endsWith('.png') ||
                lowerPath.endsWith('.webp');
            if (!isImage) {
              _showErrorSnackBar('Only image files (JPG, JPEG, PNG, WEBP) are allowed.');
              return;
            }
            if (kDebugMode) {
              print('Selected file exists with size: $fileSize bytes at ${file.absolute.path}');
            }

            // Set the selected file and immediately start uploading
            setState(() {
              _selectedFiles[documentType] = file;
            });

            // Automatically start upload after selection
            await _uploadDocument(documentType);
          } else {
            _showErrorSnackBar('Selected file is empty. Please try again with a valid image.');
            return;
          }
        } else {
          _showErrorSnackBar('Selected file does not exist. Please try again.');
          return;
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error picking/uploading document: $e');
      }

      _showErrorSnackBar('Error: $e');
    }
  }

  Future<void> _replaceDocument(String documentType) async {
    // Simply call _pickDocument which will now handle replacement automatically
    await _pickDocument(documentType);
  }

  Future<void> _removeDocument(String documentType) async {
    final url = _uploadedUrls[documentType];

    if (url == null) return;

    try {
      setState(() {
        _isUploading[documentType] = true;
      });

      // Delete from Firestore document reference first
      await FirebaseFirestore.instance
          .collection('drivers')
          .doc('temp_driver_id') // We'll use a temp ID since we haven't created the driver yet
          .collection('documents')
          .doc(documentType)
          .delete();

      // Delete from storage
      final storageRef = FirebaseStorage.instance.refFromURL(url);
      await storageRef.delete();

      setState(() {
        _uploadedUrls.remove(documentType);
        _selectedFiles[documentType] = null;
        _isUploading[documentType] = false;
        _uploadProgress[documentType] = 0.0;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('$documentType removed successfully'),
            backgroundColor: AppColors.info,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } catch (e) {
      _logger.error('Error removing document', tag: 'DocumentUpload', error: e);
      setState(() {
        _isUploading[documentType] = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error removing $documentType: $e'),
            backgroundColor: AppColors.error,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  void _showErrorSnackBar(String message) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.error,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  Future<void> _showErrorDialog(String title, String message) async {
    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        final isDarkMode = Theme.of(context).brightness == Brightness.dark;

        return AlertDialog(
          backgroundColor: isDarkMode ? AppColors.darkSurface : Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              const Icon(
                Icons.error_outline,
                color: AppColors.error,
                size: 28,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: isDarkMode ? AppColors.darkText : AppColors.lightText,
                  ),
                ),
              ),
            ],
          ),
          content: Text(
            message,
            style: TextStyle(
              fontSize: 16,
              color: isDarkMode ? AppColors.darkTextSecondary : AppColors.lightTextSecondary,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              style: TextButton.styleFrom(
                foregroundColor: AppColors.primaryYellow,
              ),
              child: const Text(
                'OK',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Future<void> _uploadDocument(String documentType) async {
    try {
      // Check if user is authenticated
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) {
        _logger.error('User not authenticated', tag: 'DocumentUpload');
        _showErrorSnackBar('You must be logged in to upload documents. Please log in and try again.');
        return;
      }

      // Check if we have a selected file
      final file = _selectedFiles[documentType];
      if (file == null) {
        _showErrorSnackBar('No file selected for $documentType');
        return;
      }

      // If this is a replacement, delete the old document first
      final existingUrl = _uploadedUrls[documentType];
      if (existingUrl != null) {
        try {
          final storageRef = FirebaseStorage.instance.refFromURL(existingUrl);
          await storageRef.delete();
          _logger.info('Deleted old document for replacement: $documentType', tag: 'DocumentUpload');
        } catch (e) {
          _logger.warning('Could not delete old document (may not exist): $e', tag: 'DocumentUpload');
          // Continue with upload even if deletion fails
        }
      }

      if (!mounted) return;
      setState(() {
        _uploadProgress[documentType] = 0.0;
        _isUploading[documentType] = true;
      });

      try {
        final fileSize = await file.length();
        _logger.info('Uploading file: ${file.absolute.path} ($fileSize bytes)', tag: 'DocumentUpload');

        // Create a unique filename with timestamp
        final String fileName = path.basename(file.path);
        final String extension = path.extension(fileName);
        final timestamp = DateTime.now().millisecondsSinceEpoch;

        // Use a temporary storage path since we don't have driver ID yet
        final storagePath = 'temp_uploads/${currentUser.uid}/documents/${documentType.replaceAll(' ', '_')}_$timestamp$extension';
        _logger.debug('Uploading to Firebase Storage path: $storagePath', tag: 'DocumentUpload');

        // Convert file to bytes for more reliable upload (run in background)
        final bytes = await compute(_readFileBytes, file.path);

        // Use our improved StorageService to handle the upload
        final url = await StorageService.uploadBytes(
          bytes,
          storagePath,
          onProgress: (progress) {
            _logger.debug('Upload progress for $documentType: ${(progress * 100).toStringAsFixed(1)}%', tag: 'DocumentUpload');
            // Throttle setState calls to reduce UI overhead (only update every 5%)
            if (mounted && (progress * 100).round() % 5 == 0) {
              setState(() {
                _uploadProgress[documentType] = progress;
              });
            }
          }
        );
        _logger.info('Upload successful, got download URL: $url', tag: 'DocumentUpload');

        if (!mounted) return;
        setState(() {
          _uploadedUrls[documentType] = url;
          _isUploading[documentType] = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('$documentType uploaded successfully'),
            backgroundColor: AppColors.success,
            behavior: SnackBarBehavior.floating,
          ),
        );
      } catch (e) {
        _logger.error('Error uploading document', tag: 'DocumentUpload', error: e);
        if (!mounted) return;

        setState(() {
          _isUploading[documentType] = false;
        });

        // Provide more specific error messages based on the error type
        if (e is FirebaseException) {
          switch (e.code) {
            case 'object-not-found':
              _showErrorSnackBar('Storage location not found. Please check your connection and try again.');
              break;
            case 'unauthorized':
              _showErrorSnackBar('You are not authorized to upload files. Please log in again.');
              break;
            case 'canceled':
              _showErrorSnackBar('Upload was canceled.');
              break;
            default:
              _showErrorSnackBar('Error uploading $documentType: ${e.message}');
          }
        } else {
          _showErrorSnackBar('Error uploading $documentType: $e');
        }
      }
    } catch (e) {
      _logger.error('Error picking/uploading document', tag: 'DocumentUpload', error: e);
      if (!mounted) return;

      setState(() {
        _isUploading[documentType] = false;
      });

      _showErrorSnackBar('Error: $e');
    }
  }

  Future<void> _submitRegistration() async {
    try {
      setState(() {
        _isSubmitting = true;
      });

      // Check if user is authenticated
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) {
        _showErrorSnackBar('You must be logged in to submit registration. Please log in and try again.');
        setState(() {
          _isSubmitting = false;
        });
        return;
      }

      // Create driver object with personal information
      final driver = Driver(
        id: '', // Will be assigned by Firestore
        name: _nameController.text.trim(),
        mobile: _mobileController.text.trim(),
        city: _cityController.text.trim(),
        maritalStatus: _maritalStatus,
        education: _educationController.text.trim(),
        experience: int.parse(_experienceController.text.trim()),
        isVerified: false,
        createdAt: DateTime.now(),
        documents: _uploadedUrls, // Include uploaded document URLs
      );

      // Get user email for uniqueness constraint
      final userEmail = await EmailUniquenessService.instance.getCurrentUserEmail();
      if (userEmail == null) {
        _showErrorSnackBar('Unable to verify your email address. Please ensure you are logged in.');
        setState(() {
          _isSubmitting = false;
        });
        return;
      }

      // Create the driver registration request in Firestore
      final driverRequestData = {
        'name': driver.name,
        'mobile': driver.mobile,
        'city': driver.city,
        'maritalStatus': driver.maritalStatus,
        'education': driver.education,
        'experience': driver.experience,
        'documentUrls': _uploadedUrls,
        'status': 'pending', // Status for admin approval
        'isVerified': false,
        'documentsVerified': false,
        'createdAt': FieldValue.serverTimestamp(),
        'submittedBy': currentUser.uid,
        'submittedAt': FieldValue.serverTimestamp(),
        'userEmail': userEmail, // Add email for uniqueness constraint
      };

      // Save the driver registration request to Firestore
      final docRef = await FirebaseFirestore.instance
          .collection('driver_requests') // Use a separate collection for requests
          .add(driverRequestData);

      _logger.info('Driver registration request submitted with ID: ${docRef.id}', tag: 'DriverRegistration');

      if (!mounted) return;

      setState(() {
        _isSubmitting = false;
      });

      // Show success dialog with professional message
      await _showSuccessDialog();

    } catch (e) {
      _logger.error('Error submitting driver registration', tag: 'DriverRegistration', error: e);

      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });

        _showErrorSnackBar('Error submitting registration: $e');
      }
    }
  }

  Future<void> _showSuccessDialog() async {
    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        final isDarkMode = Theme.of(context).brightness == Brightness.dark;

        return AlertDialog(
          backgroundColor: isDarkMode ? AppColors.darkSurface : Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: AppColors.success.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.check_circle_outline,
                  color: AppColors.success,
                  size: 50,
                ),
              ),
              const SizedBox(height: 24),
              Text(
                'Registration Request Submitted',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: isDarkMode ? AppColors.darkText : AppColors.lightText,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              Text(
                'Thank you for your interest in joining our platform. Your registration request has been successfully submitted and is now under review.\n\nOur team will carefully examine your details and uploaded documents. If all information and documentation meet our requirements, you will be registered as a verified driver on our platform.\n\nYou will receive a notification once the review process is complete.',
                style: TextStyle(
                  fontSize: 14,
                  color: isDarkMode ? AppColors.darkTextSecondary : AppColors.lightTextSecondary,
                  height: 1.5,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () {
                    Navigator.of(context).pop(); // Close dialog
                    // Call the callback if provided
                    widget.onRegistrationComplete?.call();
                    // Navigate back to the previous screen (drivers section)
                    if (Navigator.of(context).canPop()) {
                      Navigator.of(context).pop();
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primaryYellow,
                    foregroundColor: Colors.black,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text(
                    'Continue',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  /// Static method to read file bytes in isolate
  static Future<Uint8List> _readFileBytes(String filePath) async {
    final file = File(filePath);
    return await file.readAsBytes();
  }
}

class DocumentType {
  final String name;
  final bool isRequired;
  final String description;

  DocumentType({
    required this.name,
    required this.isRequired,
    this.description = '',
  });
}