'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import {
  Key,
  Eye,
  EyeOff,
  Copy,
  RefreshCw,
  Plus,
  Trash2,
  Shield,
  Globe,
  Smartphone,
  CreditCard,
  MessageSquare,
  BarChart3,
  Save,
  CheckCircle,
} from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { useSettings } from '@/lib/hooks/use-settings'

const apiServices = [
  {
    id: 'googleMapsKey',
    name: 'Google Maps API',
    description: 'Maps, geocoding, and location services',
    icon: <Globe className="w-5 h-5" />,
    color: 'blue',
    required: true
  },
  {
    id: 'stripeKey',
    name: 'Stripe API',
    description: 'Payment processing and billing',
    icon: <CreditCard className="w-5 h-5" />,
    color: 'purple',
    required: true
  },
  {
    id: 'twilioConfig',
    name: 'Twilio API',
    description: 'SMS and voice communications',
    icon: <MessageSquare className="w-5 h-5" />,
    color: 'green',
    required: false
  },
  {
    id: 'pushNotificationKeys',
    name: 'Push Notifications',
    description: 'Firebase Cloud Messaging',
    icon: <Smartphone className="w-5 h-5" />,
    color: 'orange',
    required: true
  },
  {
    id: 'analyticsKey',
    name: 'Analytics API',
    description: 'Google Analytics tracking',
    icon: <BarChart3 className="w-5 h-5" />,
    color: 'red',
    required: false
  }
]

export function ApiSettings() {
  const { settings, updateApiSettings } = useSettings()
  const [isLoading, setIsLoading] = useState(false)
  const [formData, setFormData] = useState(settings.api)
  const [visibleKeys, setVisibleKeys] = useState<Record<string, boolean>>({})
  const [copiedKey, setCopiedKey] = useState<string | null>(null)

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleSave = async () => {
    setIsLoading(true)
    try {
      await updateApiSettings(formData)
      // Show success message
    } catch (error) {
      console.error('Error saving API settings:', error)
      // Show error message
    } finally {
      setIsLoading(false)
    }
  }

  const toggleKeyVisibility = (keyId: string) => {
    setVisibleKeys(prev => ({ ...prev, [keyId]: !prev[keyId] }))
  }

  const copyToClipboard = async (text: string, keyId: string) => {
    try {
      await navigator.clipboard.writeText(text)
      setCopiedKey(keyId)
      setTimeout(() => setCopiedKey(null), 2000)
    } catch (error) {
      console.error('Failed to copy to clipboard:', error)
    }
  }

  const generateApiKey = () => {
    return Array.from(crypto.getRandomValues(new Uint8Array(32)))
      .map(b => b.toString(16).padStart(2, '0'))
      .join('')
  }

  const maskApiKey = (key: string) => {
    if (!key) return ''
    if (key.length <= 8) return '••••••••'
    return key.substring(0, 4) + '••••••••••••••••' + key.substring(key.length - 4)
  }

  const getServiceStatus = (serviceId: string) => {
    const value = formData[serviceId as keyof typeof formData]
    if (typeof value === 'string') {
      return value.length > 0
    } else if (typeof value === 'object' && value !== null) {
      return Object.keys(value).length > 0
    }
    return false
  }

  return (
    <div className="space-y-6">
      {/* API Services */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Key className="w-5 h-5" />
            <span>API Services</span>
          </CardTitle>
          <CardDescription>
            Configure API keys and credentials for external services
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {apiServices.map((service) => (
            <div key={service.id} className="p-4 border rounded-lg dark:border-gray-700">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-4">
                  <div className={`w-10 h-10 bg-${service.color}-100 dark:bg-${service.color}-900/20 rounded-full flex items-center justify-center`}>
                    <div className={`text-${service.color}-600 dark:text-${service.color}-400`}>
                      {service.icon}
                    </div>
                  </div>
                  <div>
                    <div className="flex items-center space-x-2">
                      <h3 className="font-medium text-gray-900 dark:text-white">{service.name}</h3>
                      {service.required && (
                        <Badge variant="error" className="text-xs">Required</Badge>
                      )}
                    </div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">{service.description}</p>
                  </div>
                </div>
                <Badge variant={getServiceStatus(service.id) ? 'success' : 'error'}>
                  {getServiceStatus(service.id) ? 'Configured' : 'Not Configured'}
                </Badge>
              </div>

              <div className="space-y-3">
                {service.id === 'googleMapsKey' && (
                  <div className="space-y-2">
                    <Label htmlFor={service.id}>API Key</Label>
                    <div className="flex space-x-2">
                      <div className="relative flex-1">
                        <Input
                          id={service.id}
                          type={visibleKeys[service.id] ? 'text' : 'password'}
                          value={formData[service.id as keyof typeof formData] as string}
                          onChange={(e) => handleInputChange(service.id, e.target.value)}
                          placeholder="Enter Google Maps API key"
                        />
                        <button
                          type="button"
                          onClick={() => toggleKeyVisibility(service.id)}
                          className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                        >
                          {visibleKeys[service.id] ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                        </button>
                      </div>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => copyToClipboard(formData[service.id as keyof typeof formData] as string, service.id)}
                      >
                        {copiedKey === service.id ? (
                          <CheckCircle className="w-4 h-4 text-green-600" />
                        ) : (
                          <Copy className="w-4 h-4" />
                        )}
                      </Button>
                    </div>
                  </div>
                )}

                {service.id === 'stripeKey' && (
                  <div className="space-y-2">
                    <Label htmlFor={service.id}>Secret Key</Label>
                    <div className="flex space-x-2">
                      <div className="relative flex-1">
                        <Input
                          id={service.id}
                          type={visibleKeys[service.id] ? 'text' : 'password'}
                          value={formData[service.id as keyof typeof formData] as string}
                          onChange={(e) => handleInputChange(service.id, e.target.value)}
                          placeholder="sk_test_..."
                        />
                        <button
                          type="button"
                          onClick={() => toggleKeyVisibility(service.id)}
                          className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                        >
                          {visibleKeys[service.id] ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                        </button>
                      </div>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => copyToClipboard(formData[service.id as keyof typeof formData] as string, service.id)}
                      >
                        {copiedKey === service.id ? (
                          <CheckCircle className="w-4 h-4 text-green-600" />
                        ) : (
                          <Copy className="w-4 h-4" />
                        )}
                      </Button>
                    </div>
                  </div>
                )}

                {service.id === 'analyticsKey' && (
                  <div className="space-y-2">
                    <Label htmlFor={service.id}>Tracking ID</Label>
                    <div className="flex space-x-2">
                      <Input
                        id={service.id}
                        value={formData[service.id as keyof typeof formData] as string}
                        onChange={(e) => handleInputChange(service.id, e.target.value)}
                        placeholder="G-XXXXXXXXXX"
                        className="flex-1"
                      />
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => copyToClipboard(formData[service.id as keyof typeof formData] as string, service.id)}
                      >
                        {copiedKey === service.id ? (
                          <CheckCircle className="w-4 h-4 text-green-600" />
                        ) : (
                          <Copy className="w-4 h-4" />
                        )}
                      </Button>
                    </div>
                  </div>
                )}

                {(service.id === 'twilioConfig' || service.id === 'pushNotificationKeys') && (
                  <div className="space-y-3">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      <div className="space-y-2">
                        <Label>Account SID / Project ID</Label>
                        <Input
                          placeholder={service.id === 'twilioConfig' ? 'ACxxxxxxxx' : 'project-id'}
                          className="font-mono text-sm"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label>Auth Token / Server Key</Label>
                        <div className="relative">
                          <Input
                            type={visibleKeys[service.id] ? 'text' : 'password'}
                            placeholder="••••••••••••••••"
                            className="font-mono text-sm"
                          />
                          <button
                            type="button"
                            onClick={() => toggleKeyVisibility(service.id)}
                            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                          >
                            {visibleKeys[service.id] ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          ))}
        </CardContent>
      </Card>

      {/* Firebase Configuration */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Shield className="w-5 h-5" />
            <span>Firebase Configuration</span>
          </CardTitle>
          <CardDescription>
            Firebase project configuration for authentication and database
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
            <div className="flex items-center space-x-2 mb-2">
              <Shield className="w-4 h-4 text-blue-600 dark:text-blue-400" />
              <span className="font-medium text-blue-800 dark:text-blue-200">
                Firebase Status
              </span>
            </div>
            <p className="text-sm text-blue-700 dark:text-blue-300">
              Firebase is configured and connected. Project ID: ride-share-admin
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
              <div className="w-12 h-12 bg-green-100 dark:bg-green-800 rounded-full flex items-center justify-center mx-auto mb-3">
                <CheckCircle className="w-6 h-6 text-green-600 dark:text-green-400" />
              </div>
              <h3 className="font-medium text-gray-900 dark:text-white">Authentication</h3>
              <Badge variant="success">Active</Badge>
            </div>

            <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
              <div className="w-12 h-12 bg-green-100 dark:bg-green-800 rounded-full flex items-center justify-center mx-auto mb-3">
                <CheckCircle className="w-6 h-6 text-green-600 dark:text-green-400" />
              </div>
              <h3 className="font-medium text-gray-900 dark:text-white">Firestore</h3>
              <Badge variant="success">Connected</Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Rate Limits */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Shield className="w-5 h-5" />
            <span>API Rate Limits</span>
          </CardTitle>
          <CardDescription>
            Configure rate limiting for API endpoints
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="authLimit">Authentication (per minute)</Label>
              <Input
                id="authLimit"
                type="number"
                defaultValue="60"
                min="1"
                max="1000"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="apiLimit">General API (per minute)</Label>
              <Input
                id="apiLimit"
                type="number"
                defaultValue="100"
                min="1"
                max="1000"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="uploadLimit">File Upload (per hour)</Label>
              <Input
                id="uploadLimit"
                type="number"
                defaultValue="50"
                min="1"
                max="500"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* API Status Summary */}
      <Card>
        <CardHeader>
          <CardTitle>API Status Summary</CardTitle>
          <CardDescription>
            Overview of all configured API services
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            {apiServices.map((service) => (
              <div key={service.id} className="text-center p-3 border rounded-lg dark:border-gray-700">
                <div className={`w-8 h-8 bg-${service.color}-100 dark:bg-${service.color}-900/20 rounded-full flex items-center justify-center mx-auto mb-2`}>
                  <div className={`text-${service.color}-600 dark:text-${service.color}-400`}>
                    {service.icon}
                  </div>
                </div>
                <h4 className="font-medium text-xs text-gray-900 dark:text-white mb-1">
                  {service.name.split(' ')[0]}
                </h4>
                <Badge 
                  variant={getServiceStatus(service.id) ? 'success' : 'error'}
                  className="text-xs"
                >
                  {getServiceStatus(service.id) ? 'OK' : 'Missing'}
                </Badge>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Save Button */}
      <div className="flex justify-end space-x-3">
        <Button
          variant="outline"
          onClick={() => setFormData(settings.api)}
        >
          Reset Changes
        </Button>
        <Button
          onClick={handleSave}
          disabled={isLoading}
          className="gradient-primary"
        >
          {isLoading ? (
            <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
          ) : (
            <Save className="w-4 h-4 mr-2" />
          )}
          {isLoading ? 'Saving...' : 'Save Changes'}
        </Button>
      </div>
    </div>
  )
}
