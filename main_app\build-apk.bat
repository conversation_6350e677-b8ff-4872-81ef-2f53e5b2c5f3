@echo off
echo 🚀 Building Drive-On Release APK
echo ================================

REM Set Android SDK environment variables with proper escaping
set ANDROID_HOME=C:\Users\<USER>\AppData\Local\Android\Sdk
set ANDROID_SDK_ROOT=C:\Users\<USER>\AppData\Local\Android\Sdk
set PATH=%ANDROID_HOME%\platform-tools;%ANDROID_HOME%\build-tools\35.0.1;%PATH%

echo 📍 Android SDK Path: %ANDROID_HOME%
echo.

REM Check if Android SDK exists
if not exist "%ANDROID_HOME%" (
    echo ❌ Android SDK not found at: %ANDROID_HOME%
    echo Please check the path and try again.
    pause
    exit /b 1
)

echo ✅ Android SDK found!
echo.

REM Clean and get dependencies
echo 🧹 Cleaning previous build...
flutter clean
if %errorlevel% neq 0 (
    echo ❌ Flutter clean failed
    pause
    exit /b 1
)

echo 📦 Getting dependencies...
flutter pub get
if %errorlevel% neq 0 (
    echo ❌ Flutter pub get failed
    pause
    exit /b 1
)

echo ✅ Dependencies updated!
echo.

REM Build release APK
echo 🔨 Building release APK...
flutter build apk --release --dart-define=ENVIRONMENT=production
if %errorlevel% neq 0 (
    echo ❌ APK build failed
    pause
    exit /b 1
)

echo.
echo 🎉 APK BUILD SUCCESSFUL!
echo ========================
echo.
echo 📱 Your APK is ready at:
echo build\app\outputs\flutter-apk\app-release.apk
echo.
echo 🎯 Features in your updated app:
echo ✅ Google-only login screen
echo ✅ Persistent authentication
echo ✅ All animations preserved
echo ✅ Production optimized
echo.

pause
