import 'dart:async';
import 'package:connectivity_plus/connectivity_plus.dart';
import '../utils/app_logger.dart';

/// Network state manager for handling connectivity changes
/// 
/// Features:
/// - Real-time connectivity monitoring
/// - Network state notifications
/// - Offline operation queue
/// - Sync scheduling
class NetworkStateManager {
  static final NetworkStateManager _instance = NetworkStateManager._internal();
  factory NetworkStateManager() => _instance;
  NetworkStateManager._internal();

  static const String _tag = 'NetworkStateManager';
  static final AppLogger _logger = AppLogger(_tag);

  final Connectivity _connectivity = Connectivity();
  
  // State management
  bool _isOnline = true;
  ConnectivityResult _currentConnectivity = ConnectivityResult.none;
  
  // Stream controllers
  final StreamController<bool> _connectivityController = StreamController<bool>.broadcast();
  final StreamController<NetworkEvent> _networkEventController = StreamController<NetworkEvent>.broadcast();
  
  // Sync queue
  final List<OfflineOperation> _syncQueue = [];
  Timer? _syncTimer;
  
  // Configuration
  static const Duration _syncInterval = Duration(minutes: 2);
  static const Duration _retryDelay = Duration(seconds: 30);
  static const int _maxRetryAttempts = 3;

  /// Initialize the network state manager
  Future<void> initialize() async {
    try {
      // Check initial connectivity
      _currentConnectivity = await _connectivity.checkConnectivity();
      _isOnline = _currentConnectivity != ConnectivityResult.none;
      
      _logger.info('Initial connectivity: ${_currentConnectivity.name}, online: $_isOnline');
      
      // Listen for connectivity changes
      _connectivity.onConnectivityChanged.listen(_onConnectivityChanged);
      
      // Start periodic sync if online
      if (_isOnline) {
        _startPeriodicSync();
      }
      
      _logger.info('Network state manager initialized');
    } catch (e) {
      _logger.error('Failed to initialize network state manager', error: e);
    }
  }

  /// Handle connectivity changes
  void _onConnectivityChanged(ConnectivityResult result) {
    final wasOnline = _isOnline;
    _currentConnectivity = result;
    _isOnline = result != ConnectivityResult.none;
    
    _logger.info('Connectivity changed: ${result.name}, online: $_isOnline');
    
    // Notify listeners
    _connectivityController.add(_isOnline);
    
    if (!wasOnline && _isOnline) {
      // Came back online
      _networkEventController.add(NetworkEvent.connectionRestored);
      _onConnectionRestored();
    } else if (wasOnline && !_isOnline) {
      // Went offline
      _networkEventController.add(NetworkEvent.connectionLost);
      _onConnectionLost();
    }
  }

  /// Handle connection restored
  void _onConnectionRestored() {
    _logger.info('Connection restored, processing sync queue');
    
    // Start periodic sync
    _startPeriodicSync();
    
    // Process pending operations
    _processSyncQueue();
  }

  /// Handle connection lost
  void _onConnectionLost() {
    _logger.info('Connection lost, stopping periodic sync');
    
    // Stop periodic sync
    _stopPeriodicSync();
  }

  /// Start periodic sync
  void _startPeriodicSync() {
    _stopPeriodicSync(); // Stop existing timer
    
    _syncTimer = Timer.periodic(_syncInterval, (timer) {
      if (_isOnline && _syncQueue.isNotEmpty) {
        _processSyncQueue();
      }
    });
  }

  /// Stop periodic sync
  void _stopPeriodicSync() {
    _syncTimer?.cancel();
    _syncTimer = null;
  }

  /// Add operation to sync queue
  void addToSyncQueue(OfflineOperation operation) {
    _syncQueue.add(operation);
    _logger.debug('Added operation to sync queue: ${operation.type}');
    
    // Try to process immediately if online
    if (_isOnline) {
      _processSyncQueue();
    }
  }

  /// Process sync queue
  Future<void> _processSyncQueue() async {
    if (!_isOnline || _syncQueue.isEmpty) return;
    
    _logger.info('Processing sync queue: ${_syncQueue.length} operations');
    
    final operationsToProcess = List<OfflineOperation>.from(_syncQueue);
    
    for (final operation in operationsToProcess) {
      try {
        await _processOperation(operation);
        _syncQueue.remove(operation);
        _logger.debug('Successfully processed operation: ${operation.type}');
      } catch (e) {
        _logger.error('Failed to process operation: ${operation.type}', error: e);
        
        // Increment retry count
        operation.retryCount++;
        
        // Remove if max retries exceeded
        if (operation.retryCount >= _maxRetryAttempts) {
          _syncQueue.remove(operation);
          _logger.warning('Removing operation after max retries: ${operation.type}');
        }
      }
    }
    
    if (_syncQueue.isEmpty) {
      _logger.info('Sync queue processed successfully');
      _networkEventController.add(NetworkEvent.syncCompleted);
    }
  }

  /// Process individual operation
  Future<void> _processOperation(OfflineOperation operation) async {
    switch (operation.type) {
      case OperationType.create:
        await _processCreateOperation(operation);
        break;
      case OperationType.update:
        await _processUpdateOperation(operation);
        break;
      case OperationType.delete:
        await _processDeleteOperation(operation);
        break;
    }
  }

  /// Process create operation
  Future<void> _processCreateOperation(OfflineOperation operation) async {
    // Implementation would depend on the specific data type
    // This is a placeholder for the actual sync logic
    await Future.delayed(const Duration(milliseconds: 100));
  }

  /// Process update operation
  Future<void> _processUpdateOperation(OfflineOperation operation) async {
    // Implementation would depend on the specific data type
    // This is a placeholder for the actual sync logic
    await Future.delayed(const Duration(milliseconds: 100));
  }

  /// Process delete operation
  Future<void> _processDeleteOperation(OfflineOperation operation) async {
    // Implementation would depend on the specific data type
    // This is a placeholder for the actual sync logic
    await Future.delayed(const Duration(milliseconds: 100));
  }

  /// Get current connectivity status
  bool get isOnline => _isOnline;

  /// Get current connectivity result
  ConnectivityResult get currentConnectivity => _currentConnectivity;

  /// Get connectivity stream
  Stream<bool> get connectivityStream => _connectivityController.stream;

  /// Get network events stream
  Stream<NetworkEvent> get networkEventsStream => _networkEventController.stream;

  /// Get sync queue length
  int get syncQueueLength => _syncQueue.length;

  /// Force sync
  Future<void> forceSync() async {
    if (_isOnline) {
      await _processSyncQueue();
    } else {
      _logger.warning('Cannot force sync while offline');
    }
  }

  /// Clear sync queue
  void clearSyncQueue() {
    _syncQueue.clear();
    _logger.info('Sync queue cleared');
  }

  /// Get network statistics
  Map<String, dynamic> getNetworkStats() {
    return {
      'isOnline': _isOnline,
      'connectivity': _currentConnectivity.name,
      'syncQueueLength': _syncQueue.length,
      'pendingOperations': _syncQueue.map((op) => {
        'type': op.type.name,
        'collection': op.collection,
        'retryCount': op.retryCount,
        'timestamp': op.timestamp.toIso8601String(),
      }).toList(),
    };
  }

  /// Dispose resources
  void dispose() {
    _stopPeriodicSync();
    _connectivityController.close();
    _networkEventController.close();
  }
}

/// Offline operation for sync queue
class OfflineOperation {
  final OperationType type;
  final String collection;
  final String documentId;
  final Map<String, dynamic> data;
  final DateTime timestamp;
  int retryCount;

  OfflineOperation({
    required this.type,
    required this.collection,
    required this.documentId,
    required this.data,
    required this.timestamp,
    this.retryCount = 0,
  });
}

/// Operation types
enum OperationType {
  create,
  update,
  delete,
}

/// Network events
enum NetworkEvent {
  connectionRestored,
  connectionLost,
  syncStarted,
  syncCompleted,
  syncFailed,
}
