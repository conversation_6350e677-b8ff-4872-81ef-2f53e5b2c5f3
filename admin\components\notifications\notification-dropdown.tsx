'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import {
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Filter,
  Clock,
  AlertTriangle,
  Users,
  MessageSquare,
  HelpCircle,
  UserPlus,
  Car,
  Building2
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Separator } from '@/components/ui/separator'
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { useNotifications } from '@/lib/hooks/use-notifications'
import { AdminNotification } from '@/lib/services/notification-service'
import { formatDistanceToNow } from 'date-fns'
import Link from 'next/link'

interface NotificationDropdownProps {
  onClose: () => void
}

const notificationIcons = {
  driver_request: Car,
  partner_request: Building2,
  forum_message: MessageSquare,
  query: HelpCircle,
  user_registration: UserPlus,
  system: Settings,
  urgent: AlertTriangle
}

const priorityColors = {
  low: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
  medium: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
  high: 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300',
  urgent: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
}

function NotificationItem({
  notification,
  onMarkAsRead,
  onClose
}: {
  notification: AdminNotification
  onMarkAsRead: (id: string) => void
  onClose: () => void
}) {
  const Icon = notificationIcons[notification.type]

  const handleClick = () => {
    if (!notification.isRead) {
      onMarkAsRead(notification.id)
    }
    if (notification.actionUrl) {
      onClose()
    }
  }

  const content = (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      className={`p-3 border-l-4 cursor-pointer transition-colors hover:bg-gray-50 dark:hover:bg-gray-800 ${
        notification.isRead
          ? 'border-gray-200 dark:border-gray-700'
          : 'border-primary bg-primary/5 dark:bg-primary/10'
      }`}
      onClick={handleClick}
    >
      <div className="flex items-start space-x-3">
        <div className={`p-2 rounded-full ${priorityColors[notification.priority]}`}>
          <Icon className="h-4 w-4" />
        </div>

        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between">
            <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
              {notification.title}
            </p>
            <div className="flex items-center space-x-2">
              <Badge variant="outline" className="text-xs">
                {notification.priority}
              </Badge>
              {!notification.isRead && (
                <div className="w-2 h-2 bg-primary rounded-full" />
              )}
            </div>
          </div>

          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1 line-clamp-2">
            {notification.message}
          </p>

          <div className="flex items-center justify-between mt-2">
            <span className="text-xs text-gray-500 dark:text-gray-400 flex items-center">
              <Clock className="h-3 w-3 mr-1" />
              {formatDistanceToNow(notification.createdAt, { addSuffix: true })}
            </span>

            {!notification.isRead && (
              <Button
                variant="ghost"
                size="sm"
                className="h-6 px-2 text-xs"
                onClick={(e) => {
                  e.stopPropagation()
                  onMarkAsRead(notification.id)
                }}
              >
                <Check className="h-3 w-3" />
              </Button>
            )}
          </div>
        </div>
      </div>
    </motion.div>
  )

  if (notification.actionUrl) {
    return (
      <Link href={notification.actionUrl}>
        {content}
      </Link>
    )
  }

  return content
}

export function NotificationDropdown({ onClose }: NotificationDropdownProps) {
  const {
    notifications,
    unreadCount,
    markAsRead,
    markAllAsRead,
    getNotificationsByType,
    getUnreadNotifications
  } = useNotifications()

  const [activeTab, setActiveTab] = useState('all')

  const unreadNotifications = getUnreadNotifications()
  const driverRequests = getNotificationsByType('driver_request')
  const partnerRequests = getNotificationsByType('partner_request')
  const forumMessages = getNotificationsByType('forum_message')
  const queries = getNotificationsByType('query')

  const getFilteredNotifications = () => {
    switch (activeTab) {
      case 'unread':
        return unreadNotifications
      case 'drivers':
        return driverRequests
      case 'partners':
        return partnerRequests
      case 'forums':
        return forumMessages
      case 'queries':
        return queries
      default:
        return notifications
    }
  }

  const filteredNotifications = getFilteredNotifications()

  return (
    <div className="w-full">
      {/* Header */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Bell className="h-5 w-5" />
            <h3 className="font-semibold text-gray-900 dark:text-white">
              Notifications
            </h3>
            {unreadCount > 0 && (
              <Badge variant="destructive" className="text-xs">
                {unreadCount}
              </Badge>
            )}
          </div>

          <div className="flex items-center space-x-2">
            {unreadCount > 0 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={markAllAsRead}
                className="text-xs"
              >
                <CheckCheck className="h-4 w-4 mr-1" />
                Mark all read
              </Button>
            )}

            <Button variant="ghost" size="sm" asChild>
              <Link href="/settings?tab=notifications">
                <Settings className="h-4 w-4" />
              </Link>
            </Button>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <div className="px-4 pt-2">
          <TabsList className="grid w-full grid-cols-6 h-8">
            <TabsTrigger value="all" className="text-xs">
              All
            </TabsTrigger>
            <TabsTrigger value="unread" className="text-xs">
              Unread
              {unreadCount > 0 && (
                <Badge variant="destructive" className="ml-1 h-4 w-4 p-0 text-xs">
                  {unreadCount > 9 ? '9+' : unreadCount}
                </Badge>
              )}
            </TabsTrigger>
            <TabsTrigger value="drivers" className="text-xs">
              <Car className="h-3 w-3" />
            </TabsTrigger>
            <TabsTrigger value="partners" className="text-xs">
              <Building2 className="h-3 w-3" />
            </TabsTrigger>
            <TabsTrigger value="forums" className="text-xs">
              <MessageSquare className="h-3 w-3" />
            </TabsTrigger>
            <TabsTrigger value="queries" className="text-xs">
              <HelpCircle className="h-3 w-3" />
            </TabsTrigger>
          </TabsList>
        </div>

        {/* Content */}
        <TabsContent value={activeTab} className="mt-0">
          <ScrollArea className="h-96">
            {filteredNotifications.length > 0 ? (
              <div className="space-y-1">
                {filteredNotifications.map((notification) => (
                  <NotificationItem
                    key={notification.id}
                    notification={notification}
                    onMarkAsRead={markAsRead}
                    onClose={onClose}
                  />
                ))}
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center h-32 text-gray-500 dark:text-gray-400">
                <Bell className="h-8 w-8 mb-2 opacity-50" />
                <p className="text-sm">
                  {activeTab === 'unread'
                    ? 'No unread notifications'
                    : 'No notifications yet'
                  }
                </p>
              </div>
            )}
          </ScrollArea>
        </TabsContent>
      </Tabs>

      {/* Footer */}
      <Separator />
      <div className="p-3">
        <Button variant="outline" className="w-full text-sm" asChild>
          <Link href="/notifications">
            View All Notifications
          </Link>
        </Button>
      </div>
    </div>
  )
}
