# Drive-On Environment Variables Example
# Copy this file as .env.development, .env.staging, or .env.production
# and fill in the appropriate values

# Application Environment
ENVIRONMENT=development  # development, staging, or production
APP_NAME=Drive-On
APP_VERSION=1.0.0

# API Configuration
API_BASE_URL=https://api.drive-on.com
API_TIMEOUT=30000
API_RETRY_ATTEMPTS=3

# Firebase Configuration
FIREBASE_PROJECT_ID=drive-on-b2af8
FIREBASE_API_KEY=AIzaSyBQehlAZFI-WngAMwzblEu7JBdMcCr-P80
FIREBASE_AUTH_DOMAIN=drive-on-b2af8.firebaseapp.com
FIREBASE_STORAGE_BUCKET=drive-on-b2af8.firebasestorage.app
FIREBASE_MESSAGING_SENDER_ID=206767723448
FIREBASE_APP_ID=1:206767723448:android:6a8e1d9c3c8992992d754d

# Feature Flags
ENABLE_ANALYTICS=true
ENABLE_CRASHLYTICS=true
ENABLE_PERFORMANCE_MONITORING=true
ENABLE_DEBUG_LOGGING=false
ENABLE_OFFLINE_MODE=true
ENABLE_EXPERIMENTAL_FEATURES=false
ENABLE_BETA_FEATURES=false

# News API Configuration
NEWS_API_KEY=your_news_api_key_here
NEWS_UPDATE_INTERVAL=45
YOUTUBE_API_KEY=***************************************
YOUTUBE_CHANNEL_ID=UCBlrOI2QsgIq3rk305SfuHw

# Security Configuration
ENCRYPTION_KEY=your_encryption_key_here
JWT_SECRET=your_jwt_secret_here
API_KEY=your_api_key

# Notification Configuration
FCM_SERVER_KEY=your_fcm_server_key_here
NOTIFICATION_SOUND=default

# Storage Configuration
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=jpg,jpeg,png,pdf,doc,docx

# Cache Configuration
CACHE_DURATION=3600
MAX_CACHE_SIZE=104857600

# Performance Configuration
IMAGE_CACHE_SIZE=50
NETWORK_TIMEOUT=30
MAX_CONCURRENT_REQUESTS=5

# Logging and Monitoring
ENABLE_VERBOSE_LOGGING=true
SENTRY_DSN=https://your-sentry-dsn.com

# Development Tools
ENABLE_INSPECTOR=false
ENABLE_PERFORMANCE_OVERLAY=false
SHOW_DEBUG_BANNER=false