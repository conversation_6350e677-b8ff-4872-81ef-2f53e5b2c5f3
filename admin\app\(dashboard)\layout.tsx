'use client'

import { useEffect, memo } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/lib/stores/auth-store'
import { Sidebar } from '@/components/layout/sidebar'
import { Header } from '@/components/layout/header'
import { LoadingSpinner } from '@/components/ui/loading-spinner'
import { notificationService } from '@/lib/services/notification-service'
import { useNavigationOptimization } from '@/lib/hooks/use-navigation-optimization'
// Only import PerformanceMonitor in development
const PerformanceMonitor = process.env.NODE_ENV === 'development'
  ? require('@/components/debug/performance-monitor').PerformanceMonitor
  : null

// Memoized components to prevent unnecessary re-renders
const MemoizedSidebar = memo(Sidebar)
const MemoizedHeader = memo(Header)

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const { isAuthenticated, isLoading, user } = useAuth()
  const router = useRouter()

  // Initialize navigation optimization
  useNavigationOptimization()

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/login')
    }
  }, [isAuthenticated, isLoading, router])

  // Initialize notification service when authenticated (only once)
  useEffect(() => {
    if (isAuthenticated && user) {
      // Initialize immediately without delay for faster navigation
      notificationService.initialize()

      // Cleanup on unmount
      return () => {
        notificationService.cleanup()
      }
    }
  }, [isAuthenticated, user])

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  if (!isAuthenticated || !user) {
    return null
  }

  return (
    <div className="min-h-screen bg-background">
      <MemoizedSidebar />
      <div className="lg:pl-64">
        <MemoizedHeader />
        <main className="py-6">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            {children}
          </div>
        </main>
      </div>
      {PerformanceMonitor && <PerformanceMonitor />}
    </div>
  )
}
