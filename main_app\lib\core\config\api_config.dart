import 'app_config.dart';

/// Class to manage API-related configuration
class ApiConfig {
  /// Get the API base URL for the current environment
  static String get baseUrl => AppConfig.instance.apiBaseUrl;
  
  /// API endpoints
  static final endpoints = _Endpoints();
  
  /// API request timeouts in seconds
  static final timeouts = _Timeouts();
  
  /// API keys and auth-related settings
  static final auth = _AuthSettings();
}

/// API endpoints for different features
class _Endpoints {
  /// Authentication API endpoints
  final auth = _AuthEndpoints();
  
  /// User API endpoints
  final user = _UserEndpoints();
  
  /// Driver API endpoints
  final driver = _DriverEndpoints();
  
  /// Jobs API endpoints
  final jobs = _JobsEndpoints();
  
  /// News API endpoints
  final news = _NewsEndpoints();
  
  /// Forum API endpoints
  final forum = _ForumEndpoints();
  
  /// Construct full URL for a given endpoint
  String fullUrl(String endpoint) => AppConfig.instance.apiUrl(endpoint);
}

/// Authentication API endpoints
class _AuthEndpoints {
  /// Sign in endpoint
  String get signIn => 'auth/signin';
  
  /// Sign up endpoint
  String get signUp => 'auth/signup';
  
  /// Forgot password endpoint
  String get forgotPassword => 'auth/forgot-password';
  
  /// Verify email endpoint
  String get verifyEmail => 'auth/verify-email';
  
  /// Reset password endpoint
  String get resetPassword => 'auth/reset-password';
}

/// User API endpoints
class _UserEndpoints {
  /// Get user profile endpoint
  String get profile => 'users/profile';
  
  /// Update user profile endpoint
  String get updateProfile => 'users/profile';
  
  /// User preferences endpoint
  String get preferences => 'users/preferences';
}

/// Driver API endpoints
class _DriverEndpoints {
  /// Get driver status endpoint
  String get status => 'drivers/status';
  
  /// Update driver location endpoint
  String get updateLocation => 'drivers/location';
  
  /// Driver availability endpoint
  String get availability => 'drivers/availability';
}

/// Jobs API endpoints
class _JobsEndpoints {
  /// List jobs endpoint
  String get list => 'jobs';
  
  /// Get job details endpoint
  String jobDetails(String jobId) => 'jobs/$jobId';
  
  /// Create job endpoint
  String get create => 'jobs';
  
  /// Update job status endpoint
  String updateStatus(String jobId) => 'jobs/$jobId/status';
}

/// News API endpoints
class _NewsEndpoints {
  /// List news articles endpoint
  String get list => 'news';
  
  /// Get news article details endpoint
  String articleDetails(String articleId) => 'news/$articleId';
}

/// Forum API endpoints
class _ForumEndpoints {
  /// List forums endpoint
  String get list => 'forums';
  
  /// Get forum details endpoint
  String forumDetails(String forumId) => 'forums/$forumId';
  
  /// Create forum endpoint
  String get create => 'forums';
  
  /// Get forum messages endpoint
  String messages(String forumId) => 'forums/$forumId/messages';
}

/// API request timeouts
class _Timeouts {
  /// Default timeout for API requests in seconds
  final int defaultTimeout = 30;
  
  /// Timeout for authentication requests in seconds
  final int authTimeout = 20;
  
  /// Timeout for data upload requests in seconds
  final int uploadTimeout = 60;
  
  /// Timeout for long-running operations in seconds
  final int longRunningOperationTimeout = 120;
}

/// API authentication settings
class _AuthSettings {
  /// API key for development environment
  final String devApiKey = const String.fromEnvironment('DEV_API_KEY', defaultValue: '');
  
  /// API key for staging environment
  final String stagingApiKey = const String.fromEnvironment('STAGING_API_KEY', defaultValue: '');
  
  /// API key for production environment
  final String prodApiKey = const String.fromEnvironment('PROD_API_KEY', defaultValue: '');
  
  /// Get the API key for the current environment
  String get apiKey {
    switch (AppConfig.instance.environment) {
      case Environment.development:
        return devApiKey;
      case Environment.staging:
        return stagingApiKey;
      case Environment.production:
        return prodApiKey;
    }
  }
  
  /// Authorization header name
  final String authHeaderName = 'Authorization';
  
  /// API key header name
  final String apiKeyHeaderName = 'X-API-Key';
  
  /// Get default headers for API requests
  Map<String, String> get defaultHeaders => {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    apiKeyHeaderName: apiKey,
  };
  
  /// Add authorization token to headers
  Map<String, String> addAuthToken(Map<String, String> headers, String token) {
    final newHeaders = Map<String, String>.from(headers);
    newHeaders[authHeaderName] = 'Bearer $token';
    return newHeaders;
  }
} 