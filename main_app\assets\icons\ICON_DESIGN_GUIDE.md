# Yellow Car Icon Design Guide

## Overview

This guide provides detailed instructions for creating a yellow car icon that will adapt to different system shapes (square, circle, rounded square) on various platforms.

## Design Requirements

### 1. Main App Icon (`app_icon.png`)

* **Dimensions**: 1024x1024 pixels
* **Format**: PNG with no transparency
* **Content**: Yellow car design on a solid background
* **Purpose**: Used for iOS and as a fallback for other platforms

### 2. Foreground Layer (`app_icon_foreground.png`)

* **Dimensions**: 1024x1024 pixels
* **Format**: PNG with transparency
* **Content**: Only the yellow car with transparent background
* **Purpose**: Used as the foreground layer for Android adaptive icons

## Design Tips for Optimal Results

### Yellow Car Design

1. **Color**: Use a bright, vibrant yellow such as:
   * `#FFDD00` (RGB: 255, 221, 0) - Bright yellow
   * `#FFC107` (RGB: 255, 193, 7) - Amber yellow
   * `#FFD700` (RGB: 255, 215, 0) - Gold yellow

2. **Car Style**:
   * Use a simple, recognizable car silhouette
   * Focus on the key elements that make a car identifiable (wheels, body, windows)
   * Avoid excessive details that may not be visible at smaller sizes

3. **Safe Zone**:
   * For Android adaptive icons, the visible area can change based on the device's mask
   * Keep important elements within the central 70% of the image
   * See the diagram below for the safe zone guidelines

```
┌─────────────────────────────┐
│                             │
│     ┌─────────────────┐     │
│     │                 │     │
│     │                 │     │
│     │    Safe Zone    │     │
│     │                 │     │
│     │                 │     │
│     └─────────────────┘     │
│                             │
└─────────────────────────────┘
```

## Icon Examples for Different Shapes

When properly designed, your yellow car icon will appear as follows in different shapes:

1. **Square**: The full icon with straight edges
2. **Circle**: The car centered in a circular mask
3. **Rounded Square**: The car with softly rounded corners
4. **Squircle**: A square with more dramatically rounded corners

## Icon Creation Process

1. **Design Options**:
   * Use graphic design software like Adobe Illustrator, Photoshop, or Figma
   * Use online tools like [Canva](https://www.canva.com/) or [Icon Kitchen](https://icon.kitchen/)
   * Hire a designer from platforms like Fiverr or Upwork

2. **Testing Your Icon**:
   * View the icon at different sizes (16x16, 32x32, 48x48, etc.)
   * Check how it looks in both light and dark environments
   * Ensure it's recognizable even at the smallest sizes

## Example Design Elements for a Yellow Car Icon

```
  ┌─────────────────────┐
  │     ┌─────────┐     │
  │  ┌──┘         └──┐  │
  │  │    YELLOW      │  │
  │  │      CAR       │  │
  │  └──┐         ┌──┘  │
  │     └─────────┘     │
  │  O             O    │
  └─────────────────────┘
```

## Professional Tool Recommendations

For the best results, consider using these tools to create your icon:

1. **[Icon Kitchen](https://icon.kitchen/)**: Free online tool that supports adaptive icons
2. **[Sketch App](https://www.sketch.com/)**: Professional design tool with icon templates
3. **[Adobe Illustrator](https://www.adobe.com/products/illustrator.html)**: Industry-standard vector graphics software
4. **[Figma](https://www.figma.com/)**: Free collaborative design tool with icon design capabilities