# Google Sign-In Error Fix Guide

## Problem
You're encountering `ApiException: 10` (ERROR_DEVELOPER_ERROR) when trying to sign in with Google. This typically indicates a configuration mismatch between your app and Firebase project.

## Root Cause Analysis
From your `google-services.json` file, I can see:

### Debug Configuration (`com.driver.drive_on.debug`)
- Certificate Hash: `c7e86afedb6d616988dff76f1f94a958cc7900c3`
- Client ID: `************-8e8gf4ovkc7ojqlai051rusdveclhiai.apps.googleusercontent.com`

### Release Configuration (`com.driver.drive_on`)
- Certificate Hashes: 
  - `85c3078a88353d1d15887693844f8d71741a94ba3`
  - `9a8e9068d497c6e25aefb05175bccbb5ccbd55dc`

## Solutions

### Solution 1: Get Your Current SHA-1 Fingerprint

#### Option A: Using Gradle (Recommended)
```bash
cd android
./gradlew signingReport
```

#### Option B: Using keytool directly
```bash
# For Windows (adjust path if needed)
keytool -list -v -keystore "C:\Users\<USER>\.android\debug.keystore" -alias androiddebugkey -storepass android -keypass android

# For macOS/Linux
keytool -list -v -keystore ~/.android/debug.keystore -alias androiddebugkey -storepass android -keypass android
```

#### Option C: Using Android Studio
1. Open Android Studio
2. Go to Gradle panel (right side)
3. Navigate to `android > Tasks > android > signingReport`
4. Double-click to run

### Solution 2: Update Firebase Configuration

1. **Go to Firebase Console**: https://console.firebase.google.com/
2. **Select your project**: `drive-on-b2af8`
3. **Go to Project Settings** (gear icon)
4. **Select "Your apps" tab**
5. **Find your Android app** (`com.driver.drive_on`)
6. **Add SHA certificate fingerprints**:
   - Click "Add fingerprint"
   - Add your current debug keystore SHA-1 fingerprint
   - Add your current release keystore SHA-1 fingerprint (if different)

### Solution 3: Download Updated google-services.json

After updating the SHA-1 fingerprints in Firebase Console:
1. **Download the updated `google-services.json`**
2. **Replace** the current file at `android/app/google-services.json`
3. **Clean and rebuild** your project

### Solution 4: Verify Package Names

Ensure these match:
- Firebase Console: `com.driver.drive_on` (release) and `com.driver.drive_on.debug` (debug)
- `android/app/build.gradle`: `applicationId "com.driver.drive_on"`
- Debug suffix: `applicationIdSuffix ".debug"`

### Solution 5: Clean and Rebuild

```bash
# Flutter clean
flutter clean
flutter pub get

# Android clean
cd android
./gradlew clean

# Rebuild
flutter build apk --debug
```

## Quick Fix Commands

Run these commands in sequence:

```bash
# 1. Get SHA-1 fingerprint
cd android && ./gradlew signingReport

# 2. Clean project
cd .. && flutter clean && flutter pub get

# 3. Clean Android
cd android && ./gradlew clean

# 4. Rebuild
cd .. && flutter build apk --debug
```

## Alternative: Generate New Debug Keystore

If the above doesn't work, you can generate a new debug keystore:

```bash
# Delete existing debug keystore
rm ~/.android/debug.keystore  # macOS/Linux
# or
del "%USERPROFILE%\.android\debug.keystore"  # Windows

# Flutter will generate a new one on next build
flutter build apk --debug
```

Then get the new SHA-1 and update Firebase accordingly.

## Verification

After implementing the fix:
1. **Clean build**: `flutter clean && flutter build apk --debug`
2. **Test Google Sign-In** on a physical device or emulator
3. **Check logs** for any remaining errors

## Common Issues

1. **Multiple Certificate Hashes**: Your Firebase project has multiple certificate hashes. Ensure your current keystore matches at least one of them.
2. **Debug vs Release**: Make sure you're testing with the correct build type (debug/release).
3. **Package Name Case Sensitivity**: Ensure exact match including capitalization.
4. **Keystore Location**: Android Studio might be using a different keystore location.

## Need Help?

If the issue persists:
1. Share the output of `gradlew signingReport`
2. Confirm which build type you're testing (debug/release)
3. Verify the exact error message and stack trace