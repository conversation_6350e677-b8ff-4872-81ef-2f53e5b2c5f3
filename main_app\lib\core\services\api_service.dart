import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import '../utils/network_logger.dart';
import '../config/app_config.dart';
import '../utils/app_logger.dart';

import 'cache_service.dart';

/// A standardized service for making HTTP API requests with built-in logging
///
/// This class provides a consistent interface for network communication with
/// remote APIs, handling common tasks such as:
///   - Standardized request/response handling for different HTTP methods
///   - Automatic logging of network traffic
///   - Error handling and error response normalization
///   - Header management and authorization
///   - Timeout handling
///
/// The service integrates with the app's logging infrastructure to provide
/// visibility into all network operations, facilitating debugging and monitoring.
class ApiService {
  /// Default timeout for all requests in seconds
  static const int _defaultTimeoutSeconds = 30;

  /// Logger tag for API-related logs
  static const String _tag = 'ApiService';

  /// The HTTP client used for making requests
  final http.Client _client;



  /// Default headers to be included with every request
  final Map<String, String> _defaultHeaders;

  /// Logger instance for API-related messages
  final AppLogger _logger = AppLogger(_tag);



  /// Cache service for request caching
  final CacheService _cacheService = CacheService();

  /// Map to track ongoing requests for deduplication
  final Map<String, Future<dynamic>> _ongoingRequests = {};

  /// Creates a new ApiService instance
  ///
  /// Parameters:
  /// - [defaultHeaders] Headers to include with every request
  ApiService({
    Map<String, String>? defaultHeaders,
  }) :
    _defaultHeaders = defaultHeaders ?? {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    },
    _client = NetworkLogger.createLoggingClient();

  /// Performs a GET request to fetch data from the specified endpoint with caching and deduplication
  ///
  /// Parameters:
  /// - [endpoint] The API endpoint to request (can be a path or full URL)
  /// - [queryParams] Optional query parameters to include in the URL
  /// - [headers] Optional additional headers to merge with defaults
  /// - [timeoutSeconds] Optional custom timeout (defaults to 30 seconds)
  /// - [useCache] Whether to use caching for this request (default: true)
  /// - [cacheTtl] Cache time-to-live duration (default: 30 minutes)
  ///
  /// Returns the decoded response body on success, or throws an exception on error
  Future<dynamic> get(
    String endpoint, {
    Map<String, dynamic>? queryParams,
    Map<String, String>? headers,
    int? timeoutSeconds,
    bool useCache = true,
    Duration? cacheTtl,
  }) async {
    final uri = _buildUri(endpoint, queryParams);
    final mergedHeaders = _mergeHeaders(headers);

    // Create cache key for request deduplication and caching
    final cacheKey = _createCacheKey('GET', uri.toString(), mergedHeaders);

    // Check for ongoing request (deduplication)
    if (_ongoingRequests.containsKey(cacheKey)) {
      _logger.debug('Request deduplication: $uri');
      return await _ongoingRequests[cacheKey]!;
    }

    // Use cache if enabled
    if (useCache) {
      final cachedResult = await _cacheService.get<dynamic>(
        key: cacheKey,
        fetcher: () => _performGetRequest(uri, mergedHeaders, timeoutSeconds, endpoint),
        ttl: cacheTtl,
      );
      return cachedResult;
    }

    // Perform request without caching
    return await _performGetRequest(uri, mergedHeaders, timeoutSeconds, endpoint);
  }

  /// Internal method to perform the actual GET request
  Future<dynamic> _performGetRequest(
    Uri uri,
    Map<String, String> headers,
    int? timeoutSeconds,
    String endpoint,
  ) async {
    final cacheKey = _createCacheKey('GET', uri.toString(), headers);

    // Track ongoing request for deduplication
    final requestFuture = _executeGetRequest(uri, headers, timeoutSeconds, endpoint);
    _ongoingRequests[cacheKey] = requestFuture;

    try {
      final result = await requestFuture;
      return result;
    } finally {
      // Remove from ongoing requests
      _ongoingRequests.remove(cacheKey);
    }
  }

  /// Execute the actual GET request
  Future<dynamic> _executeGetRequest(
    Uri uri,
    Map<String, String> headers,
    int? timeoutSeconds,
    String endpoint,
  ) async {
    // Performance monitoring removed

    try {
      final stopwatch = Stopwatch()..start();
      final response = await _client
          .get(uri, headers: headers)
          .timeout(Duration(seconds: timeoutSeconds ?? _defaultTimeoutSeconds));
      stopwatch.stop();

      return _handleResponse(response);
    } on TimeoutException {
      _logError('GET request timed out: $uri');
      throw ApiException('Request timed out', ApiErrorType.timeout);
    } on SocketException catch (e) {
      _logError('Network error during GET request: $uri', error: e);
      throw ApiException('Network error: ${e.message}', ApiErrorType.network);
    } catch (e, stackTrace) {
      _logError('Error during GET request: $uri', error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Performs a POST request to send data to the specified endpoint
  ///
  /// Parameters:
  /// - [endpoint] The API endpoint to request (can be a path or full URL)
  /// - [body] The request body to send (will be JSON encoded)
  /// - [queryParams] Optional query parameters to include in the URL
  /// - [headers] Optional additional headers to merge with defaults
  /// - [timeoutSeconds] Optional custom timeout (defaults to 30 seconds)
  ///
  /// Returns the decoded response body on success, or throws an exception on error
  Future<dynamic> post(
    String endpoint, {
    dynamic body,
    Map<String, dynamic>? queryParams,
    Map<String, String>? headers,
    int? timeoutSeconds,
  }) async {
    final uri = _buildUri(endpoint, queryParams);
    final mergedHeaders = _mergeHeaders(headers);
    final encodedBody = body != null ? jsonEncode(body) : null;

    // Performance monitoring removed

    try {
      final stopwatch = Stopwatch()..start();
      final response = await _client
          .post(uri, headers: mergedHeaders, body: encodedBody)
          .timeout(Duration(seconds: timeoutSeconds ?? _defaultTimeoutSeconds));
      stopwatch.stop();

      return _handleResponse(response);
    } on TimeoutException {
      _logError('POST request timed out: $uri');
      throw ApiException('Request timed out', ApiErrorType.timeout);
    } on SocketException catch (e) {
      _logError('Network error during POST request: $uri', error: e);
      throw ApiException('Network error: ${e.message}', ApiErrorType.network);
    } catch (e, stackTrace) {
      _logError('Error during POST request: $uri', error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Performs a PUT request to update data at the specified endpoint
  ///
  /// Parameters:
  /// - [endpoint] The API endpoint to request (can be a path or full URL)
  /// - [body] The request body to send (will be JSON encoded)
  /// - [queryParams] Optional query parameters to include in the URL
  /// - [headers] Optional additional headers to merge with defaults
  /// - [timeoutSeconds] Optional custom timeout (defaults to 30 seconds)
  ///
  /// Returns the decoded response body on success, or throws an exception on error
  Future<dynamic> put(
    String endpoint, {
    dynamic body,
    Map<String, dynamic>? queryParams,
    Map<String, String>? headers,
    int? timeoutSeconds,
  }) async {
    final uri = _buildUri(endpoint, queryParams);
    final mergedHeaders = _mergeHeaders(headers);
    final encodedBody = body != null ? jsonEncode(body) : null;

    // Performance monitoring removed

    try {
      final stopwatch = Stopwatch()..start();
      final response = await _client
          .put(uri, headers: mergedHeaders, body: encodedBody)
          .timeout(Duration(seconds: timeoutSeconds ?? _defaultTimeoutSeconds));
      stopwatch.stop();

      return _handleResponse(response);
    } on TimeoutException {
      _logError('PUT request timed out: $uri');
      throw ApiException('Request timed out', ApiErrorType.timeout);
    } on SocketException catch (e) {
      _logError('Network error during PUT request: $uri', error: e);
      throw ApiException('Network error: ${e.message}', ApiErrorType.network);
    } catch (e, stackTrace) {
      _logError('Error during PUT request: $uri', error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Performs a DELETE request to remove data at the specified endpoint
  ///
  /// Parameters:
  /// - [endpoint] The API endpoint to request (can be a path or full URL)
  /// - [queryParams] Optional query parameters to include in the URL
  /// - [headers] Optional additional headers to merge with defaults
  /// - [timeoutSeconds] Optional custom timeout (defaults to 30 seconds)
  ///
  /// Returns the decoded response body on success, or throws an exception on error
  Future<dynamic> delete(
    String endpoint, {
    Map<String, dynamic>? queryParams,
    Map<String, String>? headers,
    int? timeoutSeconds,
  }) async {
    final uri = _buildUri(endpoint, queryParams);
    final mergedHeaders = _mergeHeaders(headers);

    try {
      final response = await _client
          .delete(uri, headers: mergedHeaders)
          .timeout(Duration(seconds: timeoutSeconds ?? _defaultTimeoutSeconds));

      return _handleResponse(response);
    } on TimeoutException {
      _logError('DELETE request timed out: $uri');
      throw ApiException('Request timed out', ApiErrorType.timeout);
    } on SocketException catch (e) {
      _logError('Network error during DELETE request: $uri', error: e);
      throw ApiException('Network error: ${e.message}', ApiErrorType.network);
    } catch (e, stackTrace) {
      _logError('Error during DELETE request: $uri', error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Performs a PATCH request to partially update data at the specified endpoint
  ///
  /// Parameters:
  /// - [endpoint] The API endpoint to request (can be a path or full URL)
  /// - [body] The request body to send (will be JSON encoded)
  /// - [queryParams] Optional query parameters to include in the URL
  /// - [headers] Optional additional headers to merge with defaults
  /// - [timeoutSeconds] Optional custom timeout (defaults to 30 seconds)
  ///
  /// Returns the decoded response body on success, or throws an exception on error
  Future<dynamic> patch(
    String endpoint, {
    dynamic body,
    Map<String, dynamic>? queryParams,
    Map<String, String>? headers,
    int? timeoutSeconds,
  }) async {
    final uri = _buildUri(endpoint, queryParams);
    final mergedHeaders = _mergeHeaders(headers);
    final encodedBody = body != null ? jsonEncode(body) : null;

    try {
      final response = await _client
          .patch(uri, headers: mergedHeaders, body: encodedBody)
          .timeout(Duration(seconds: timeoutSeconds ?? _defaultTimeoutSeconds));

      return _handleResponse(response);
    } on TimeoutException {
      _logError('PATCH request timed out: $uri');
      throw ApiException('Request timed out', ApiErrorType.timeout);
    } on SocketException catch (e) {
      _logError('Network error during PATCH request: $uri', error: e);
      throw ApiException('Network error: ${e.message}', ApiErrorType.network);
    } catch (e, stackTrace) {
      _logError('Error during PATCH request: $uri', error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Builds a URI for the given endpoint and query parameters
  ///
  /// If the endpoint is a full URL, it's used directly. Otherwise, it's
  /// combined with the base API URL from app configuration.
  ///
  /// Parameters:
  /// - [endpoint] The API endpoint (full URL or path)
  /// - [queryParams] Optional query parameters to include
  ///
  /// Returns a URI object ready for use in HTTP requests
  Uri _buildUri(String endpoint, Map<String, dynamic>? queryParams) {
    Uri uri;

    // Check if the endpoint is already a full URL
    if (endpoint.startsWith('http://') || endpoint.startsWith('https://')) {
      uri = Uri.parse(endpoint);
    } else {
      // Combine with base URL from config
      final baseUrl = AppConfig.instance.apiBaseUrl;
      uri = Uri.parse('$baseUrl${endpoint.startsWith('/') ? endpoint : '/$endpoint'}');
    }

    // Add query parameters if provided
    if (queryParams != null && queryParams.isNotEmpty) {
      final queryString = queryParams.entries
          .map((e) => '${Uri.encodeComponent(e.key)}=${Uri.encodeComponent(e.value.toString())}')
          .join('&');

      final separator = uri.query.isEmpty ? '?' : '&';
      return Uri.parse('${uri.toString()}$separator$queryString');
    }

    return uri;
  }

  /// Merges default headers with request-specific headers
  ///
  /// Request-specific headers take precedence over defaults.
  ///
  /// Parameters:
  /// - [headers] Request-specific headers to merge with defaults
  ///
  /// Returns the merged headers map
  Map<String, String> _mergeHeaders(Map<String, String>? headers) {
    final result = Map<String, String>.from(_defaultHeaders);

    if (headers != null) {
      result.addAll(headers);
    }

    return result;
  }

  /// Processes the HTTP response and handles errors
  ///
  /// Success responses (2xx) return the decoded body.
  /// Error responses are transformed into ApiException objects.
  ///
  /// Parameters:
  /// - [response] The HTTP response to process
  ///
  /// Returns the decoded response body or throws an ApiException
  dynamic _handleResponse(http.Response response) {
    final statusCode = response.statusCode;
    final hasBody = response.body.isNotEmpty;

    // Handle success responses (2xx)
    if (statusCode >= 200 && statusCode < 300) {
      if (!hasBody) return null;

      try {
        return jsonDecode(response.body);
      } catch (e) {
        // If we can't parse as JSON, return the raw body
        return response.body;
      }
    }

    // Handle error responses
    ApiErrorType errorType;
    String errorMessage;
    dynamic errorData;

    if (statusCode == 401) {
      errorType = ApiErrorType.unauthorized;
      errorMessage = 'Unauthorized';
    } else if (statusCode == 403) {
      errorType = ApiErrorType.forbidden;
      errorMessage = 'Forbidden';
    } else if (statusCode == 404) {
      errorType = ApiErrorType.notFound;
      errorMessage = 'Not found';
    } else if (statusCode >= 400 && statusCode < 500) {
      errorType = ApiErrorType.badRequest;
      errorMessage = 'Bad request';
    } else if (statusCode >= 500) {
      errorType = ApiErrorType.server;
      errorMessage = 'Server error';
    } else {
      errorType = ApiErrorType.unknown;
      errorMessage = 'Unknown error';
    }

    // Try to extract error details from response body
    if (hasBody) {
      try {
        errorData = jsonDecode(response.body);
        // If there's an error message in the response, use it
        if (errorData is Map && errorData.containsKey('message')) {
          errorMessage = errorData['message'] as String;
        }
      } catch (e) {
        // If we can't parse the body, use the raw body as the error message
        errorMessage = response.body;
      }
    }

    throw ApiException(
      errorMessage,
      errorType,
      statusCode: statusCode,
      data: errorData,
    );
  }

  /// Logs an error message with optional error object and stack trace
  ///
  /// Parameters:
  /// - [message] The error message to log
  /// - [error] Optional error object
  /// - [stackTrace] Optional stack trace
  void _logError(String message, {Object? error, StackTrace? stackTrace}) {
    _logger.error(message, error: error, stackTrace: stackTrace);
  }

  /// Releases resources used by this service
  ///
  /// Should be called when the service is no longer needed
  void dispose() {
    _client.close();
  }



  /// Create a cache key for request deduplication and caching
  String _createCacheKey(String method, String url, Map<String, String> headers) {
    // Create a deterministic cache key based on method, URL, and relevant headers
    final relevantHeaders = <String, String>{};

    // Only include headers that affect the response (exclude auth tokens for caching)
    const cacheRelevantHeaders = ['accept', 'content-type', 'accept-language'];
    for (final header in cacheRelevantHeaders) {
      if (headers.containsKey(header)) {
        relevantHeaders[header] = headers[header]!;
      }
    }

    final keyData = '$method:$url:${relevantHeaders.toString()}';
    return keyData.hashCode.toString();
  }
}

/// Types of API errors that can occur during requests
///
/// Used to categorize errors for appropriate handling in the UI
enum ApiErrorType {
  /// Request was malformed or contained invalid parameters
  badRequest,

  /// Authentication credentials are missing or invalid
  unauthorized,

  /// User is authenticated but doesn't have permission
  forbidden,

  /// The requested resource doesn't exist
  notFound,

  /// An error occurred on the server
  server,

  /// Request exceeded the timeout period
  timeout,

  /// Network connectivity issues
  network,

  /// Other unexpected errors
  unknown,
}

/// Exception thrown when an API request fails
///
/// Provides detailed information about what went wrong, including
/// the type of error, status code, and any data returned by the server.
class ApiException implements Exception {
  /// Human-readable error message
  final String message;

  /// Type of API error that occurred
  final ApiErrorType type;

  /// HTTP status code (if applicable)
  final int? statusCode;

  /// Additional error data from the response
  final dynamic data;

  /// Creates a new ApiException
  ///
  /// Parameters:
  /// - [message] Human-readable error message
  /// - [type] Type of API error that occurred
  /// - [statusCode] Optional HTTP status code
  /// - [data] Optional additional error data
  ApiException(this.message, this.type, {this.statusCode, this.data});

  @override
  String toString() {
    final statusInfo = statusCode != null ? ' (Status: $statusCode)' : '';
    return 'ApiException: $message$statusInfo [${type.toString().split('.').last}]';
  }
}