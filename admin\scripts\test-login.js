#!/usr/bin/env node

// Simple test script to verify login credentials
const validCredentials = [
  { email: '<EMAIL>', password: 'admin123' },
  { email: '<EMAIL>', password: 'admin123' },
  { email: '<EMAIL>', password: 'admin123' }
]

function testCredentials(email, password) {
  console.log(`\n🔍 Testing credentials: ${email} / ${password}`)
  
  // Normalize inputs (same as in auth store)
  const normalizedEmail = email.trim().toLowerCase()
  const normalizedPassword = password.trim()
  
  console.log(`📧 Normalized email: "${normalizedEmail}"`)
  console.log(`🔑 Normalized password: "${normalizedPassword}"`)
  
  const isValid = validCredentials.some(cred => {
    const credEmailNormalized = cred.email.toLowerCase()
    const credPasswordNormalized = cred.password
    console.log(`   Comparing with: "${credEmailNormalized}" / "${credPasswordNormalized}"`)
    const emailMatch = credEmailNormalized === normalizedEmail
    const passwordMatch = credPasswordNormalized === normalizedPassword
    console.log(`   Email match: ${emailMatch}, Password match: ${passwordMatch}`)
    return emailMatch && passwordMatch
  })
  
  if (isValid) {
    console.log(`✅ VALID: Credentials are correct!`)
  } else {
    console.log(`❌ INVALID: Credentials do not match`)
  }
  
  return isValid
}

console.log('🧪 Drive-On Admin Login Credential Test')
console.log('=====================================')

console.log('\n📋 Valid Credentials:')
validCredentials.forEach((cred, index) => {
  console.log(`${index + 1}. ${cred.email} / ${cred.password}`)
})

// Test the main credential
console.log('\n🎯 Testing Main Credential:')
testCredentials('<EMAIL>', 'admin123')

// Test with different cases
console.log('\n🎯 Testing Case Variations:')
testCredentials('<EMAIL>', 'admin123')
testCredentials('<EMAIL>', 'admin123')

// Test with whitespace
console.log('\n🎯 Testing Whitespace Handling:')
testCredentials(' <EMAIL> ', 'admin123')
testCredentials('<EMAIL>', ' admin123 ')

// Test invalid credentials
console.log('\n🎯 Testing Invalid Credentials:')
testCredentials('<EMAIL>', 'admin123')
testCredentials('<EMAIL>', 'wrongpassword')

console.log('\n✅ Test completed!')
console.log('\n💡 To use in the admin panel:')
console.log('   Email: <EMAIL>')
console.log('   Password: admin123')
