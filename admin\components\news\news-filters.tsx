'use client'

import { Filter } from 'lucide-react'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'

interface NewsFiltersProps {
  filters: {
    category: string
    status: string
    author: string
  }
  onFiltersChange: (filters: any) => void
}

export function NewsFilters({ filters, onFiltersChange }: NewsFiltersProps) {
  const updateFilter = (key: string, value: string) => {
    onFiltersChange({
      ...filters,
      [key]: value
    })
  }

  return (
    <div className="flex space-x-2">
      {/* Category Filter */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" size="sm">
            <Filter className="w-4 h-4 mr-2" />
            Category: {filters.category === 'all' ? 'All' : filters.category}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          <DropdownMenuLabel>Filter by Category</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={() => updateFilter('category', 'all')}>
            All Categories
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('category', 'General')}>
            General
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('category', 'Car Prices')}>
            Car Prices
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('category', 'Industry News')}>
            Industry News
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('category', 'Fuel Prices')}>
            Fuel Prices
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('category', 'Taxes & Duties')}>
            Taxes & Duties
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('category', 'Road Safety')}>
            Road Safety
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('category', 'Traffic Updates')}>
            Traffic Updates
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('category', 'Accident Reports')}>
            Accident Reports
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Status Filter */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" size="sm">
            Status: {filters.status === 'all' ? 'All' : filters.status}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          <DropdownMenuLabel>Filter by Status</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={() => updateFilter('status', 'all')}>
            All Status
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('status', 'published')}>
            Published
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('status', 'draft')}>
            Draft
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('status', 'archived')}>
            Archived
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Author Filter */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" size="sm">
            Author: {filters.author === 'all' ? 'All' : filters.author}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          <DropdownMenuLabel>Filter by Author</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={() => updateFilter('author', 'all')}>
            All Authors
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('author', 'Admin')}>
            Admin
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('author', 'Editor')}>
            Editor
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('author', 'Content Manager')}>
            Content Manager
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  )
}
