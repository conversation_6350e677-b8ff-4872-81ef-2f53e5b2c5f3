# Drive-On Logging System Guide

## Overview

The Drive-On app uses a comprehensive, multi-level logging system to track application events, errors, and network requests. This guide explains how to use the logging system effectively for development, debugging, and production monitoring.

## Core Components

### AppLogger

The `AppLogger` class is the primary interface for logging in the application. It provides methods for different log levels and integrates with both in-app viewing and remote error tracking via Sentry.

```dart
// Create a logger for a specific component/class
final _logger = AppLogger('ComponentName');

// Log at different levels
_logger.verbose('Detailed debugging information');
_logger.debug('Debugging information');
_logger.info('General information');
_logger.warning('Warning: something might be wrong');
_logger.error('An error occurred', error: exception, stackTrace: stackTrace);
_logger.wtf('What a terrible failure: a critical error');
```

### InMemoryLogs

The `InMemoryLogs` class stores logs in memory for viewing within the app via the Log Viewer. It also handles log persistence to disk and log rotation.

```dart
// Access in-memory logs
final logs = AppLogger.inMemoryLogs;

// Get filtered logs
final errorLogs = logs.getLogsByLevel(LogLevel.error);
final networkLogs = logs.getLogsByTag('NetworkLogger');
```

### NetworkLogger

The `NetworkLogger` provides HTTP request and response logging through a custom HTTP client.

```dart
// Create a logging HTTP client
final client = NetworkLogger.createLoggingClient();

// Or use the ApiService which includes network logging
final api = ApiService(
  baseUrl: 'https://api.example.com',
  enableLogging: true,
);
```

## Log Levels

The logging system uses the following log levels, in increasing order of severity:

1. **VERBOSE**: Detailed debugging information, only useful during development
2. **DEBUG**: Debugging information useful for development
3. **INFO**: General information about app operation
4. **WARNING**: Potential issues or unexpected behaviors
5. **ERROR**: Errors and exceptions that affect functionality
6. **WTF**: Critical failures that should never happen

## In-App Log Viewer

The app includes a built-in Log Viewer for development and debugging, accessible through:

1. Settings > Developer Options > Developer Menu > Log Viewer (in debug builds only)

The Log Viewer provides the following features:
- View logs in real-time
- Filter logs by level and tag
- Search logs for specific content
- View detailed error information and stack traces
- Copy individual logs to clipboard
- Share logs with other apps
- Export all logs to a file
- Clear log history

## Log Persistence

Logs are stored in memory during app operation and also persisted to disk with the following behavior:

- Logs are stored in the application documents directory under a `logs` folder
- Log files use the naming format `app_log_YYYYMMDD_HHMMSS.txt`
- Log rotation keeps only the most recent 5 log files
- Each log file stores up to 5000 log entries
- Memory storage is limited to the most recent 1000 log entries

## Network Request Logging

All HTTP requests and responses can be logged automatically using the `NetworkLogger` or `ApiService`:

- Request method, URL, headers, and body are logged
- Response status code, duration, headers, and body are logged
- Sensitive information in headers (like authorization tokens) is automatically redacted
- Large request/response bodies are truncated to avoid excessive log sizes

## Remote Error Tracking (Sentry)

In production builds, error logs are automatically sent to Sentry for centralized error tracking:

- Error logs (ERROR and WTF levels) are sent to Sentry with stack traces
- Warning logs create breadcrumbs in Sentry for context
- User ID is included when available for tracking user-specific issues
- Tags and extra context can be added for better error categorization

## Best Practices

### When to Use Each Log Level

- **VERBOSE**: Use for loop iterations, function entries/exits, or very detailed flow tracing
- **DEBUG**: Use for values, states, and operations helpful during development
- **INFO**: Use for startup events, user actions, and major app state changes
- **WARNING**: Use for unexpected but handleable conditions
- **ERROR**: Use for exceptions and errors that affect functionality
- **WTF**: Use for critical failures that should never happen in normal operation

### Effective Logging

1. **Be specific**: Include relevant details in log messages
2. **Add context**: Include IDs, states, and values that help understand the event
3. **Use consistent tags**: Group related logs by using consistent component tags
4. **Include errors**: Always pass exceptions and stack traces to error logs
5. **Don't over-log**: Avoid excessive logging in performance-critical sections

### Structured Logging

For complex data, consider using structured formats:

```dart
_logger.info('User profile updated', {
  'userId': user.id,
  'fields': ['name', 'email'],
  'changes': {'name': 'New Name', 'previousName': 'Old Name'},
});
```

## Implementation Examples

### Basic Component Logging

```dart
class UserService {
  final AppLogger _logger = AppLogger('UserService');
  
  Future<User?> getUserProfile(String userId) async {
    _logger.debug('Getting user profile: $userId');
    try {
      final user = await _repository.fetchUser(userId);
      _logger.info('User profile retrieved: ${user.id}');
      return user;
    } catch (e, stackTrace) {
      _logger.error('Failed to get user profile', error: e, stackTrace: stackTrace);
      return null;
    }
  }
}
```

### User Action Tracking

```dart
void _onLoginButtonPressed() {
  _logger.info('Login button pressed', {
    'method': _selectedLoginMethod,
    'rememberMe': _rememberMeChecked,
  });
  
  // Continue with login logic...
}
```

### Network Request Tracking

```dart
// This is automatic when using ApiService or NetworkLogger
final api = ApiService(baseUrl: 'https://api.example.com');
final response = await api.get('/users/profile');
```

### Error Handling

```dart
try {
  await someRiskyOperation();
} catch (e, stackTrace) {
  _logger.error(
    'Failed to complete operation',
    error: e,
    stackTrace: stackTrace
  );
  
  // Show error to user, retry, or fallback behavior
}
```

## Adding Logs to Existing Code

When adding logs to existing code, follow these practices:

1. Create a logger instance at the class level with an appropriate tag
2. Log important state changes and user actions at INFO level
3. Log edge cases and potential issues at WARNING level
4. Log all exceptions with full context at ERROR level
5. Use DEBUG level for information only needed during development

## Viewing Logs

There are multiple ways to view logs:

1. **Development Console**: During development, DEBUG and higher logs appear in the console
2. **In-App Log Viewer**: Access real-time logs via the Developer Menu
3. **Exported Log Files**: Share or analyze exported log files
4. **Sentry Dashboard**: View errors and their context in the Sentry web interface 