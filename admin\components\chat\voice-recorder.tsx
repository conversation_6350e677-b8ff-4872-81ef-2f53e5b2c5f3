'use client'

import { useState, useRef, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Mi<PERSON>, MicO<PERSON>, Square, Play, Pause, Trash2, Send } from 'lucide-react'
import { Button } from '@/components/ui/button'

interface VoiceRecorderProps {
  onRecordingComplete: (audioBlob: Blob, duration: number) => void
  onCancel: () => void
  isRecording: boolean
  onStartRecording: () => void
  onStopRecording: () => void
}

export function VoiceRecorder({
  onRecordingComplete,
  onCancel,
  isRecording,
  onStartRecording,
  onStopRecording
}: VoiceRecorderProps) {
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null)
  const [duration, setDuration] = useState(0)
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentTime, setCurrentTime] = useState(0)
  const [permissionDenied, setPermissionDenied] = useState(false)
  const [microphoneStatus, setMicrophoneStatus] = useState<'checking' | 'available' | 'denied' | 'error'>('checking')

  const mediaRecorderRef = useRef<MediaRecorder | null>(null)
  const audioRef = useRef<HTMLAudioElement | null>(null)
  const chunksRef = useRef<Blob[]>([])
  const timerRef = useRef<NodeJS.Timeout | null>(null)
  const playTimerRef = useRef<NodeJS.Timeout | null>(null)

  // Check if recording is supported
  const checkRecordingSupport = (): boolean => {
    console.log('Checking recording support...')

    // Check HTTPS requirement
    if (location.protocol !== 'https:' && location.hostname !== 'localhost' && location.hostname !== '127.0.0.1') {
      alert('Microphone access requires HTTPS. Please use HTTPS or localhost.')
      return false
    }

    if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
      console.error('getUserMedia not supported')
      alert('Your browser does not support audio recording. Please use a modern browser like Chrome, Firefox, or Safari.')
      return false
    }

    if (!window.MediaRecorder) {
      console.error('MediaRecorder not supported')
      alert('Audio recording is not supported in your browser.')
      return false
    }

    console.log('Recording support check passed')
    return true
  }

  // Start recording
  const startRecording = async () => {
    console.log('Starting recording...')
    try {
      // Check if recording is supported
      if (!checkRecordingSupport()) {
        console.log('Recording not supported')
        return
      }

      console.log('Requesting microphone access...')

      // Try different audio constraints in order of preference
      let stream: MediaStream | null = null
      const audioConstraints = [
        { audio: true }, // Simple constraint
        { audio: { echoCancellation: false, noiseSuppression: false } }, // Minimal constraints
        { audio: { deviceId: 'default' } }, // Default device
      ]

      for (const constraints of audioConstraints) {
        try {
          console.log('Trying constraints:', constraints)
          stream = await navigator.mediaDevices.getUserMedia(constraints)
          console.log('Microphone access granted with constraints:', constraints)
          break
        } catch (constraintError) {
          console.warn('Failed with constraints:', constraints, constraintError)
          continue
        }
      }

      if (!stream) {
        throw new Error('Could not access microphone with any constraints')
      }

      console.log('Final stream:', stream)

      // Determine the best audio format
      let mimeType = 'audio/webm;codecs=opus'
      if (!MediaRecorder.isTypeSupported(mimeType)) {
        mimeType = 'audio/webm'
        if (!MediaRecorder.isTypeSupported(mimeType)) {
          mimeType = 'audio/mp4'
          if (!MediaRecorder.isTypeSupported(mimeType)) {
            mimeType = 'audio/wav'
          }
        }
      }

      console.log('Creating MediaRecorder with mimeType:', mimeType)
      const mediaRecorder = new MediaRecorder(stream, { mimeType })

      mediaRecorderRef.current = mediaRecorder
      chunksRef.current = []

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          chunksRef.current.push(event.data)
        }
      }

      mediaRecorder.onstop = () => {
        const blob = new Blob(chunksRef.current, { type: mimeType })
        setAudioBlob(blob)

        // Stop all tracks to release microphone
        stream.getTracks().forEach(track => track.stop())
      }

      console.log('Starting MediaRecorder...')
      mediaRecorder.start()
      onStartRecording()

      // Start timer
      setDuration(0)
      timerRef.current = setInterval(() => {
        setDuration(prev => prev + 1)
      }, 1000)

      console.log('Recording started successfully')

    } catch (error) {
      console.error('Error starting recording:', error)

      let errorMessage = 'Could not start recording. '

      if (error instanceof Error) {
        switch (error.name) {
          case 'NotAllowedError':
            setPermissionDenied(true)
            errorMessage = 'Microphone access was denied. Please click the microphone icon in your browser\'s address bar and allow access, then try again.'
            break
          case 'NotFoundError':
            errorMessage = 'No microphone found. Please connect a microphone and try again.'
            break
          case 'NotSupportedError':
            errorMessage = 'Audio recording is not supported in your browser. Please use Chrome, Firefox, or Safari.'
            break
          case 'NotReadableError':
            errorMessage = 'Microphone is being used by another application. Please close other apps using the microphone and try again.'
            break
          case 'OverconstrainedError':
            errorMessage = 'Microphone constraints could not be satisfied. Please try again.'
            break
          default:
            errorMessage = `Recording failed: ${error.message}`
        }
      } else {
        errorMessage = 'An unknown error occurred. Please check your browser permissions and try again.'
      }

      alert(errorMessage)
    }
  }

  // Stop recording
  const stopRecording = () => {
    if (mediaRecorderRef.current && mediaRecorderRef.current.state === 'recording') {
      mediaRecorderRef.current.stop()
      onStopRecording()
    }

    if (timerRef.current) {
      clearInterval(timerRef.current)
      timerRef.current = null
    }
  }

  // Play/pause audio
  const togglePlayback = () => {
    if (!audioBlob) return

    if (!audioRef.current) {
      audioRef.current = new Audio(URL.createObjectURL(audioBlob))
      audioRef.current.onended = () => {
        setIsPlaying(false)
        setCurrentTime(0)
        if (playTimerRef.current) {
          clearInterval(playTimerRef.current)
        }
      }
    }

    if (isPlaying) {
      audioRef.current.pause()
      setIsPlaying(false)
      if (playTimerRef.current) {
        clearInterval(playTimerRef.current)
      }
    } else {
      audioRef.current.play()
      setIsPlaying(true)

      // Update current time
      playTimerRef.current = setInterval(() => {
        if (audioRef.current) {
          setCurrentTime(audioRef.current.currentTime)
        }
      }, 100)
    }
  }

  // Delete recording
  const deleteRecording = () => {
    setAudioBlob(null)
    setDuration(0)
    setCurrentTime(0)
    setIsPlaying(false)

    if (audioRef.current) {
      audioRef.current.pause()
      audioRef.current = null
    }

    if (playTimerRef.current) {
      clearInterval(playTimerRef.current)
    }
  }

  // Send recording
  const sendRecording = () => {
    if (audioBlob) {
      onRecordingComplete(audioBlob, duration)
    }
  }

  // Test microphone access
  const testMicrophoneAccess = async () => {
    try {
      console.log('Testing microphone access...')
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
      console.log('Microphone test successful')
      stream.getTracks().forEach(track => track.stop()) // Clean up
      return true
    } catch (error) {
      console.error('Microphone test failed:', error)
      return false
    }
  }

  // Retry recording (reset permission state)
  const retryRecording = async () => {
    setPermissionDenied(false)
    // Test microphone access first
    const hasAccess = await testMicrophoneAccess()
    if (!hasAccess) {
      setPermissionDenied(true)
    }
  }

  // Format time
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  // Check microphone status on mount
  useEffect(() => {
    const checkInitialMicrophoneStatus = async () => {
      try {
        setMicrophoneStatus('checking')
        const hasAccess = await testMicrophoneAccess()
        setMicrophoneStatus(hasAccess ? 'available' : 'denied')
      } catch (error) {
        console.error('Initial microphone check failed:', error)
        setMicrophoneStatus('error')
      }
    }

    checkInitialMicrophoneStatus()
  }, [])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current)
      }
      if (playTimerRef.current) {
        clearInterval(playTimerRef.current)
      }
      if (audioRef.current) {
        audioRef.current.pause()
      }
    }
  }, [])

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: 20 }}
      className="bg-white border rounded-lg p-4 shadow-lg"
    >
      {permissionDenied ? (
        // Permission denied interface
        <div className="text-center space-y-3">
          <div className="text-red-600">
            <MicOff className="w-8 h-8 mx-auto mb-2" />
            <h3 className="font-medium">Microphone Access Needed</h3>
            <p className="text-sm text-gray-600 mt-1">
              Please allow microphone access in your browser to record voice messages.
            </p>
          </div>
          <div className="flex justify-center space-x-2">
            <Button
              onClick={retryRecording}
              size="sm"
              className="bg-yellow-500 hover:bg-yellow-600 text-white"
            >
              Try Again
            </Button>
            <Button
              onClick={onCancel}
              size="sm"
              variant="outline"
            >
              Cancel
            </Button>
          </div>
        </div>
      ) : !audioBlob ? (
        // Recording interface
        <div className="space-y-3">
          {/* Microphone status */}
          <div className="flex items-center space-x-2 text-xs">
            {microphoneStatus === 'checking' && (
              <>
                <div className="w-2 h-2 bg-yellow-500 rounded-full animate-pulse" />
                <span className="text-gray-600">Checking microphone...</span>
              </>
            )}
            {microphoneStatus === 'available' && (
              <>
                <div className="w-2 h-2 bg-green-500 rounded-full" />
                <span className="text-green-600">Microphone ready</span>
              </>
            )}
            {microphoneStatus === 'denied' && (
              <>
                <div className="w-2 h-2 bg-red-500 rounded-full" />
                <span className="text-red-600">Microphone access denied</span>
              </>
            )}
            {microphoneStatus === 'error' && (
              <>
                <div className="w-2 h-2 bg-red-500 rounded-full" />
                <span className="text-red-600">Microphone error</span>
              </>
            )}
          </div>

          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              {isRecording ? (
                <motion.div
                  animate={{ scale: [1, 1.1, 1] }}
                  transition={{ repeat: Infinity, duration: 1 }}
                  className="w-3 h-3 bg-red-500 rounded-full"
                />
              ) : (
                <div className="w-3 h-3 bg-gray-300 rounded-full" />
              )}
              <span className="text-sm font-medium">
                {isRecording ? 'Recording...' : 'Ready to record'}
              </span>
            </div>

            <div className="text-lg font-mono">
              {formatTime(duration)}
            </div>

            <div className="flex space-x-2">
              {!isRecording ? (
                <Button
                  onClick={startRecording}
                  size="sm"
                  className="bg-red-500 hover:bg-red-600 text-white"
                  disabled={microphoneStatus !== 'available'}
                >
                  <Mic className="w-4 h-4" />
                </Button>
              ) : (
                <Button
                  onClick={stopRecording}
                  size="sm"
                  variant="outline"
                >
                  <Square className="w-4 h-4" />
                </Button>
              )}

              <Button
                onClick={onCancel}
                size="sm"
                variant="outline"
              >
                Cancel
              </Button>
            </div>
          </div>
        </div>
      ) : (
        // Playback interface
        <div className="space-y-3">
          <div className="flex items-center space-x-4">
            <Button
              onClick={togglePlayback}
              size="sm"
              variant="outline"
            >
              {isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
            </Button>

            <div className="flex-1">
              <div className="flex justify-between text-sm text-gray-500 mb-1">
                <span>{formatTime(currentTime)}</span>
                <span>{formatTime(duration)}</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-yellow-500 h-2 rounded-full transition-all duration-100"
                  style={{ width: `${duration > 0 ? (currentTime / duration) * 100 : 0}%` }}
                />
              </div>
            </div>

            <div className="flex space-x-2">
              <Button
                onClick={deleteRecording}
                size="sm"
                variant="outline"
                className="text-red-500 hover:text-red-600"
              >
                <Trash2 className="w-4 h-4" />
              </Button>

              <Button
                onClick={sendRecording}
                size="sm"
                className="bg-yellow-500 hover:bg-yellow-600 text-white"
              >
                <Send className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>
      )}
    </motion.div>
  )
}
