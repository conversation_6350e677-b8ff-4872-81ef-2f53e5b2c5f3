import 'package:cloud_firestore/cloud_firestore.dart';

class NewsArticle {
  final String id;
  final String title;
  final String description;
  final String url;
  final String source;
  final DateTime publishedAt;
  final String imageUrl;
  final int views;
  final bool pinned;
  final String? adminComment;
  final String? adminContent; // For admin articles content
  final Map<String, int> reactionCounts;
  final DateTime createdAt;
  final String category;

  NewsArticle({
    required this.id,
    required this.title,
    required this.description,
    required this.url,
    required this.source,
    required this.publishedAt,
    required this.imageUrl,
    required this.views,
    required this.pinned,
    this.adminComment,
    this.adminContent,
    required this.reactionCounts,
    required this.createdAt,
    required this.category,
  });

  factory NewsArticle.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return NewsArticle(
      id: doc.id,
      title: data['title'] ?? '',
      description: data['description'] ?? '',
      url: data['url'] ?? '',
      source: data['source'] ?? '',
      publishedAt: (data['publishedAt'] as Timestamp).toDate(),
      imageUrl: data['imageUrl'] ?? '',
      views: data['views'] ?? 0,
      pinned: data['pinned'] ?? false,
      adminComment: data['adminComment'],
      adminContent: data['adminContent'],
      reactionCounts: Map<String, int>.from(data['reactionCounts'] ?? {}),
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      category: data['category'] ?? '',
    );
  }

  Map<String, dynamic> toFirestore() => {
    'title': title,
    'description': description,
    'url': url,
    'source': source,
    'publishedAt': Timestamp.fromDate(publishedAt),
    'imageUrl': imageUrl,
    'views': views,
    'pinned': pinned,
    'adminComment': adminComment,
    'adminContent': adminContent,
    'reactionCounts': reactionCounts,
    'createdAt': Timestamp.fromDate(createdAt),
    'category': category,
  };

  NewsArticle copyWith({
    String? title,
    String? description,
    String? url,
    String? source,
    DateTime? publishedAt,
    String? imageUrl,
    int? views,
    bool? pinned,
    String? adminComment,
    String? adminContent,
    Map<String, int>? reactionCounts,
    DateTime? createdAt,
    String? category,
  }) {
    return NewsArticle(
      id: id,
      title: title ?? this.title,
      description: description ?? this.description,
      url: url ?? this.url,
      source: source ?? this.source,
      publishedAt: publishedAt ?? this.publishedAt,
      imageUrl: imageUrl ?? this.imageUrl,
      views: views ?? this.views,
      pinned: pinned ?? this.pinned,
      adminComment: adminComment ?? this.adminComment,
      adminContent: adminContent ?? this.adminContent,
      reactionCounts: reactionCounts ?? this.reactionCounts,
      createdAt: createdAt ?? this.createdAt,
      category: category ?? this.category,
    );
  }
}