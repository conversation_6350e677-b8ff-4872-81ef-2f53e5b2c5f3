import 'package:flutter/foundation.dart';
import 'app_logger.dart';

/// A utility class to provide simple logging functions
/// This makes it easy to replace all print statements with proper logging
class Log {
  // Private constructor to prevent instantiation
  Log._();
  
  // Static logger instance
  static final AppLogger _logger = AppLogger('Log');

  /// Log a debug message
  static void d(String message, {String? tag, dynamic error, StackTrace? stackTrace}) {
    final logger = tag != null ? AppLogger(tag) : _logger;
    logger.debug(message, error: error, stackTrace: stackTrace);
  }

  /// Log an info message
  static void i(String message, {String? tag, dynamic error, StackTrace? stackTrace}) {
    final logger = tag != null ? AppLogger(tag) : _logger;
    logger.info(message, error: error, stackTrace: stackTrace);
  }

  /// Log a warning message
  static void w(String message, {String? tag, dynamic error, StackTrace? stackTrace}) {
    final logger = tag != null ? AppLogger(tag) : _logger;
    logger.warning(message, error: error, stackTrace: stackTrace);
  }

  /// Log an error message
  static void e(String message, {String? tag, dynamic error, StackTrace? stackTrace}) {
    final logger = tag != null ? AppLogger(tag) : _logger;
    logger.error(message, error: error, stackTrace: stackTrace);
  }

  /// Log a critical/WTF error message
  static void wtf(String message, {String? tag, dynamic error, StackTrace? stackTrace}) {
    final logger = tag != null ? AppLogger(tag) : _logger;
    logger.wtf(message, error: error, stackTrace: stackTrace);
  }

  /// Add a breadcrumb for tracking user actions
  static void breadcrumb(String message, {String? tag, String? category, Map<String, dynamic>? data}) {
    final logger = tag != null ? AppLogger(tag) : _logger;
    logger.addBreadcrumb(message, category: category ?? 'app.action');
  }

  /// Print a log message only in debug mode
  /// This should be used for temporary debugging only
  static void debug(String message) {
    if (kDebugMode) {
      _logger.debug('[DEBUG] $message');
    }
  }
} 