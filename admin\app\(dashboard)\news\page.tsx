'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import {
  Newspaper,
  Search,
  Filter,
  Download,
  Plus,
  Eye,
  Edit,
  Trash2,
  CheckCircle,
  XCircle,
  Clock,
  Image,
  Calendar,
  User,
} from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { DataTable } from '@/components/ui/data-table'
import { NewsModal } from '@/components/news/news-modal'
import { NewsFilters } from '@/components/news/news-filters'
import { LoadingSpinner } from '@/components/ui/loading-spinner'
import { useNews } from '@/lib/hooks/use-news'
import { formatDate, formatRelativeTime, truncateText } from '@/lib/utils'

interface NewsArticle {
  id: string
  title: string
  content: string
  excerpt: string
  category: 'Car Prices' | 'Industry News' | 'Fuel Prices' | 'Taxes & Duties' | 'Road Safety' | 'Traffic Updates' | 'Accident Reports' | 'General'
  status: 'draft' | 'published' | 'archived'
  featuredImage?: string
  author: string
  tags: string[]
  publishedAt?: Date
  createdAt: Date
  updatedAt: Date
  viewsCount: number
  likesCount: number
  isFeatured: boolean
  isBreaking: boolean
  seoTitle?: string
  seoDescription?: string
  // Main app compatible fields
  description: string
  url: string
  source: string
  imageUrl: string
  views: number
  pinned: boolean
  adminComment?: string
  reactionCounts: { [key: string]: number }
}

export default function NewsPage() {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedArticle, setSelectedArticle] = useState<NewsArticle | null>(null)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [modalMode, setModalMode] = useState<'view' | 'edit' | 'create'>('view')
  const [filters, setFilters] = useState({
    category: 'all',
    status: 'all',
    author: 'all'
  })

  const { articles, isLoading, error, updateArticle, deleteArticle, createArticle, publishArticle } = useNews()

  const filteredArticles = articles?.filter(article => {
    const matchesSearch = article.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         article.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         article.author.toLowerCase().includes(searchQuery.toLowerCase())

    const matchesCategory = filters.category === 'all' || article.category === filters.category
    const matchesStatus = filters.status === 'all' || article.status === filters.status
    const matchesAuthor = filters.author === 'all' || article.author === filters.author

    return matchesSearch && matchesCategory && matchesStatus && matchesAuthor
  }) || []

  const handleCreateArticle = () => {
    setSelectedArticle(null)
    setModalMode('create')
    setIsModalOpen(true)
  }

  const handleViewArticle = (article: NewsArticle) => {
    setSelectedArticle(article)
    setModalMode('view')
    setIsModalOpen(true)
  }

  const handleEditArticle = (article: NewsArticle) => {
    setSelectedArticle(article)
    setModalMode('edit')
    setIsModalOpen(true)
  }

  const handleDeleteArticle = async (article: NewsArticle) => {
    if (window.confirm(`Are you sure you want to delete "${article.title}"?`)) {
      try {
        await deleteArticle(article.id)
      } catch (error) {
        console.error('Error deleting article:', error)
      }
    }
  }

  const handlePublishArticle = async (article: NewsArticle) => {
    try {
      await publishArticle(article.id)
    } catch (error) {
      console.error('Error publishing article:', error)
    }
  }

  const handleToggleStatus = async (article: NewsArticle) => {
    try {
      const newStatus = article.status === 'published' ? 'draft' : 'published'
      await updateArticle(article.id, { status: newStatus })
    } catch (error) {
      console.error('Error updating article status:', error)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published':
        return 'success'
      case 'draft':
        return 'warning'
      case 'archived':
        return 'secondary'
      default:
        return 'secondary'
    }
  }

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'general':
        return 'bg-blue-100 text-blue-800'
      case 'updates':
        return 'bg-green-100 text-green-800'
      case 'announcements':
        return 'bg-purple-100 text-purple-800'
      case 'events':
        return 'bg-orange-100 text-orange-800'
      case 'promotions':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const columns = [
    {
      key: 'article',
      label: 'Article',
      render: (article: NewsArticle) => (
        <div className="flex items-center space-x-3">
          <div className="w-12 h-12 bg-gray-200 rounded-lg overflow-hidden">
            {article.featuredImage ? (
              <img src={article.featuredImage} alt={article.title} className="w-full h-full object-cover" />
            ) : (
              <div className="w-full h-full flex items-center justify-center">
                <Image className="w-6 h-6 text-gray-400" />
              </div>
            )}
          </div>
          <div className="flex-1 min-w-0">
            <p className="font-medium text-gray-900 truncate">{article.title}</p>
            <p className="text-sm text-gray-500 truncate">{truncateText(article.excerpt, 60)}</p>
            <div className="flex items-center space-x-2 mt-1">
              {article.isFeatured && (
                <Badge variant="warning" className="text-xs">Featured</Badge>
              )}
              {article.isBreaking && (
                <Badge variant="error" className="text-xs">Breaking</Badge>
              )}
            </div>
          </div>
        </div>
      )
    },
    {
      key: 'category',
      label: 'Category',
      render: (article: NewsArticle) => (
        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getCategoryColor(article.category)}`}>
          {article.category}
        </span>
      )
    },
    {
      key: 'author',
      label: 'Author',
      render: (article: NewsArticle) => (
        <div className="flex items-center space-x-1">
          <User className="w-4 h-4 text-gray-400" />
          <span className="text-sm">{article.author}</span>
        </div>
      )
    },
    {
      key: 'status',
      label: 'Status',
      render: (article: NewsArticle) => (
        <Badge variant={getStatusColor(article.status) as any}>
          {article.status}
        </Badge>
      )
    },
    {
      key: 'engagement',
      label: 'Engagement',
      render: (article: NewsArticle) => (
        <div className="text-sm">
          <div>{article.viewsCount} views</div>
          <div className="text-gray-500">{article.likesCount} likes</div>
        </div>
      )
    },
    {
      key: 'published',
      label: 'Published',
      render: (article: NewsArticle) => (
        <div className="flex items-center space-x-1">
          <Calendar className="w-4 h-4 text-gray-400" />
          <span className="text-sm">
            {article.publishedAt ? formatRelativeTime(article.publishedAt) : 'Not published'}
          </span>
        </div>
      )
    },
    {
      key: 'actions',
      label: 'Actions',
      render: (article: NewsArticle) => (
        <div className="flex items-center space-x-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleViewArticle(article)}
          >
            <Eye className="w-4 h-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleEditArticle(article)}
          >
            <Edit className="w-4 h-4" />
          </Button>
          {article.status === 'draft' && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handlePublishArticle(article)}
              className="text-green-600 hover:text-green-700"
            >
              <CheckCircle className="w-4 h-4" />
            </Button>
          )}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleToggleStatus(article)}
            className={article.status === 'published' ? 'text-red-600 hover:text-red-700' : 'text-green-600 hover:text-green-700'}
          >
            {article.status === 'published' ? <XCircle className="w-4 h-4" /> : <CheckCircle className="w-4 h-4" />}
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleDeleteArticle(article)}
            className="text-red-600 hover:text-red-700"
          >
            <Trash2 className="w-4 h-4" />
          </Button>
        </div>
      )
    }
  ]

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">News Management</h1>
          <p className="text-gray-600">Create and manage news articles and announcements</p>
        </div>
        <Button onClick={handleCreateArticle} className="gradient-primary">
          <Plus className="w-4 h-4 mr-2" />
          Create Article
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Articles</p>
                <p className="text-2xl font-bold text-gray-900">{articles?.length || 0}</p>
              </div>
              <Newspaper className="w-8 h-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Published</p>
                <p className="text-2xl font-bold text-gray-900">
                  {filteredArticles.filter(a => a.status === 'published').length}
                </p>
              </div>
              <CheckCircle className="w-8 h-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Drafts</p>
                <p className="text-2xl font-bold text-gray-900">
                  {filteredArticles.filter(a => a.status === 'draft').length}
                </p>
              </div>
              <Clock className="w-8 h-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Views</p>
                <p className="text-2xl font-bold text-gray-900">
                  {filteredArticles.reduce((sum, article) => sum + article.viewsCount, 0)}
                </p>
              </div>
              <Eye className="w-8 h-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <CardTitle>News Articles</CardTitle>
          <CardDescription>
            Manage news articles, announcements, and updates
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search articles by title, content, or author..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <NewsFilters filters={filters} onFiltersChange={setFilters} />

            <Button variant="outline">
              <Download className="w-4 h-4 mr-2" />
              Export
            </Button>
          </div>

          <DataTable
            data={filteredArticles}
            columns={columns}
            searchQuery={searchQuery}
            isLoading={isLoading}
          />
        </CardContent>
      </Card>

      {/* News Modal */}
      <NewsModal
        article={selectedArticle}
        mode={modalMode}
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onSave={async (articleData) => {
          try {
            if (modalMode === 'create') {
              await createArticle(articleData)
            } else if (modalMode === 'edit' && selectedArticle) {
              await updateArticle(selectedArticle.id, articleData)
            }
            setIsModalOpen(false)
          } catch (error) {
            console.error('Error saving article:', error)
          }
        }}
      />
    </motion.div>
  )
}
