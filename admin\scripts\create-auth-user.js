const admin = require('firebase-admin');

// Initialize Firebase Admin SDK
const serviceAccount = require('../serviceAccountKey.json');

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
  databaseURL: "https://drive-on-b2af8-default-rtdb.firebaseio.com"
});

async function createAuthUser() {
  try {
    const adminEmail = '<EMAIL>';
    const adminPassword = 'Drive@on0750##';
    
    console.log('🚀 Creating/Updating Firebase Auth User');
    console.log('=====================================\n');
    console.log(`📧 Email: ${adminEmail}`);
    
    let userRecord;
    
    try {
      // Try to get existing user
      userRecord = await admin.auth().getUserByEmail(adminEmail);
      console.log('✅ User already exists in Firebase Auth');
      console.log(`🆔 UID: ${userRecord.uid}`);
      
      // Update the user's password
      await admin.auth().updateUser(userRecord.uid, {
        password: adminPassword,
        emailVerified: true,
        disabled: false
      });
      console.log('✅ User password updated');
      
    } catch (error) {
      if (error.code === 'auth/user-not-found') {
        console.log('❌ User not found in Firebase Auth');
        console.log('💡 Creating new user...');
        
        // Create the user
        userRecord = await admin.auth().createUser({
          email: adminEmail,
          password: adminPassword,
          displayName: 'Drive-On Admin',
          emailVerified: true,
          disabled: false
        });
        console.log('✅ New user created in Firebase Auth');
        console.log(`🆔 UID: ${userRecord.uid}`);
      } else {
        throw error;
      }
    }

    // Set custom claims for super admin role
    const adminClaims = {
      admin: true,
      superAdmin: true,
      role: 'super_admin',
      permissions: [
        'all',
        'users.read',
        'users.write',
        'users.delete',
        'drivers.read',
        'drivers.write',
        'drivers.delete',
        'drivers.verify',
        'queries.read',
        'queries.write',
        'queries.delete',
        'chats.read',
        'chats.write',
        'chats.moderate',
        'analytics.read',
        'settings.read',
        'settings.write',
        'system.admin'
      ]
    };

    await admin.auth().setCustomUserClaims(userRecord.uid, adminClaims);
    console.log('✅ Super Admin claims set in Firebase Auth');

    // Update Firestore document
    const adminData = {
      email: adminEmail,
      displayName: 'Drive-On Admin',
      role: 'super_admin',
      isActive: true,
      isAdmin: true,
      superAdmin: true,
      permissions: adminClaims.permissions,
      uid: userRecord.uid,
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
      lastLoginAt: null,
      profilePicture: null,
      department: 'Administration',
      phone: null,
      timezone: 'UTC',
      language: 'en',
      twoFactorEnabled: false,
      sessionTimeout: 30,
      passwordExpiry: 90,
      loginNotifications: true,
      ipWhitelist: [],
      apiKeyRotation: 30,
      createdBy: 'auth-setup-script'
    };

    // Update admin document in Firestore using UID as document ID
    await admin.firestore().collection('admins').doc(userRecord.uid).set(adminData, { merge: true });
    console.log('✅ Admin document updated in Firestore (admins collection)');

    // Also create/update in users collection
    await admin.firestore().collection('users').doc(userRecord.uid).set({
      ...adminData,
      userType: 'admin',
      isVerified: true,
      status: 'active'
    }, { merge: true });
    console.log('✅ Admin document updated in Firestore (users collection)');

    // Create email lookup document
    const emailDocId = adminEmail.replace('@', '_').replace(/\./g, '_');
    await admin.firestore().collection('admins').doc(emailDocId).set({
      ...adminData,
      uid: userRecord.uid,
      lookupType: 'email'
    }, { merge: true });
    console.log('✅ Email lookup document created');

    console.log('\n🎉 Firebase Auth user setup completed successfully!');
    console.log('================================================');
    console.log(`📧 Email: ${adminEmail}`);
    console.log(`🔑 Password: ${adminPassword}`);
    console.log(`🆔 UID: ${userRecord.uid}`);
    console.log(`🛡️ Role: Super Admin`);
    console.log(`✨ Status: Ready for login`);
    console.log('\n📝 You can now login to the admin panel with these credentials.');
    console.log('🌐 Admin Panel URL: http://localhost:3001');
    
    // Test the user can be retrieved
    const testUser = await admin.auth().getUser(userRecord.uid);
    console.log('\n🔍 User verification:');
    console.log(`   ✓ Email: ${testUser.email}`);
    console.log(`   ✓ Email Verified: ${testUser.emailVerified}`);
    console.log(`   ✓ Disabled: ${testUser.disabled}`);
    console.log(`   ✓ Custom Claims: ${JSON.stringify(testUser.customClaims)}`);

  } catch (error) {
    console.error('\n❌ Error creating/updating auth user:', error.message);
    console.error('Full error:', error);
  } finally {
    process.exit();
  }
}

// Check if Firebase is properly initialized
console.log('🔧 Checking Firebase connection...');
admin.firestore().collection('test').limit(1).get()
  .then(() => {
    console.log('✅ Firebase connection successful\n');
    createAuthUser();
  })
  .catch((error) => {
    console.error('❌ Firebase connection failed:', error.message);
    console.log('\n💡 Please check your serviceAccountKey.json file and Firebase configuration.');
    process.exit(1);
  });
