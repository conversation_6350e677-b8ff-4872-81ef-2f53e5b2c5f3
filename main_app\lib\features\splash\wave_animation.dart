import 'dart:math' as math;
import 'package:flutter/material.dart';
import '../../core/core.dart';

class WaveAnimation extends StatefulWidget {
  final double height;
  final double width;

  const WaveAnimation({
    super.key,
    required this.height,
    required this.width,
  });

  @override
  State<WaveAnimation> createState() => _WaveAnimationState();
}

class _WaveAnimationState extends State<WaveAnimation> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 4),
    );
    _animationController.repeat();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return SizedBox(
      height: widget.height,
      width: widget.width,
      child: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return CustomPaint(
            painter: WavePainter(
              animationValue: _animationController.value,
              isDarkMode: isDarkMode,
            ),
            child: child,
          );
        },
      ),
    );
  }
}

class WavePainter extends CustomPainter {
  final double animationValue;
  final bool isDarkMode;

  WavePainter({
    required this.animationValue,
    required this.isDarkMode,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final width = size.width;
    final height = size.height;

    // The path that will draw the wave
    final path = Path();

    // Calculate the y offset for the sine wave
    final waveHeight = height * 0.05;
    final baseHeight = height * 0.75;

    // Start the path at the left edge
    path.moveTo(0, baseHeight);

    // Optimize: Use fewer points for better performance (every 4 pixels instead of every pixel)
    const step = 4.0;
    for (var x = 0.0; x <= width; x += step) {
      // Pre-calculate common values to avoid repeated calculations
      final xRatio = x / width;
      final animationPhase = animationValue * 2 * math.pi;

      // Multiple sine waves with different frequencies and phases (optimized)
      final y1 = math.sin((xRatio * 2 * math.pi) + animationPhase) * waveHeight;
      final y2 = math.sin((xRatio * 4 * math.pi) + (animationPhase * 2)) * waveHeight * 0.5;

      final combinedY = baseHeight + y1 + y2;

      path.lineTo(x, combinedY);
    }

    // Ensure we end at the exact width
    if (width % step != 0) {
      const xRatio = 1.0;
      final animationPhase = animationValue * 2 * math.pi;
      final y1 = math.sin((xRatio * 2 * math.pi) + animationPhase) * waveHeight;
      final y2 = math.sin((xRatio * 4 * math.pi) + (animationPhase * 2)) * waveHeight * 0.5;
      final combinedY = baseHeight + y1 + y2;
      path.lineTo(width, combinedY);
    }

    // Complete the path
    path.lineTo(width, height);
    path.lineTo(0, height);
    path.close();

    // Create a gradient for the wave - with different opacities for dark/light mode
    final primaryOpacity = isDarkMode ? 0.6 : 0.7;
    final secondaryOpacity = isDarkMode ? 0.25 : 0.3;

    final gradient = LinearGradient(
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
      colors: [
        AppColors.primaryYellow.withOpacity(primaryOpacity),
        AppColors.secondaryYellow.withOpacity(secondaryOpacity),
      ],
    );

    final paint = Paint()
      ..shader = gradient.createShader(Rect.fromLTWH(0, baseHeight - waveHeight, width, height))
      ..style = PaintingStyle.fill;

    canvas.drawPath(path, paint);

    // Draw a subtle line at the top of the wave for definition (optimized)
    final lineOpacity = isDarkMode ? 0.4 : 0.5;

    final linePaint = Paint()
      ..color = AppColors.primaryYellow.withOpacity(lineOpacity)
      ..style = PaintingStyle.stroke
      ..strokeWidth = isDarkMode ? 1.0 : 1.5;

    // Reuse the same path for the line to avoid duplicate calculations
    final linePath = Path();
    linePath.moveTo(0, baseHeight);

    // Use the same optimized loop as above
    for (var x = 0.0; x <= width; x += step) {
      final xRatio = x / width;
      final animationPhase = animationValue * 2 * math.pi;

      final y1 = math.sin((xRatio * 2 * math.pi) + animationPhase) * waveHeight;
      final y2 = math.sin((xRatio * 4 * math.pi) + (animationPhase * 2)) * waveHeight * 0.5;

      final combinedY = baseHeight + y1 + y2;

      linePath.lineTo(x, combinedY);
    }

    // Ensure we end at the exact width
    if (width % step != 0) {
      const xRatio = 1.0;
      final animationPhase = animationValue * 2 * math.pi;
      final y1 = math.sin((xRatio * 2 * math.pi) + animationPhase) * waveHeight;
      final y2 = math.sin((xRatio * 4 * math.pi) + (animationPhase * 2)) * waveHeight * 0.5;
      final combinedY = baseHeight + y1 + y2;
      linePath.lineTo(width, combinedY);
    }

    canvas.drawPath(linePath, linePaint);
  }

  @override
  bool shouldRepaint(WavePainter oldDelegate) {
    return oldDelegate.animationValue != animationValue;
  }
}