plugins {
    id "com.android.application"
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterRoot = localProperties.getProperty('flutter.sdk')
if (flutterRoot == null) {
    throw new FileNotFoundException("Flutter SDK not found. Define location with flutter.sdk in the local.properties file.")
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

// Load keystore properties for production signing
def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}

android {
    namespace "com.driver.drive_on"
    compileSdk 35
    ndkVersion = "26.3.11579264"

    compileOptions {
        // Enable core library desugaring for flutter_local_notifications
        coreLibraryDesugaringEnabled true
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = '17'
        freeCompilerArgs += ["-Xallow-incompatible-class-version-errors"]
    }

    lint {
        disable 'InvalidPackage'
        checkReleaseBuilds false
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    // Configure signing configurations
    signingConfigs {
        release {
            if (keystorePropertiesFile.exists()) {
                keyAlias keystoreProperties['keyAlias']
                keyPassword keystoreProperties['keyPassword']
                storeFile file(keystoreProperties['storeFile'])
                storePassword keystoreProperties['storePassword']
            }
        }
    }

    defaultConfig {
        // Application ID for Google Play Store
        applicationId "com.driver.drive_on"
        // Minimum SDK version for broad device compatibility
        minSdkVersion 23
        // Target latest Android version for optimal performance
        targetSdkVersion 35
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName

        // Enable multidex for large apps
        multiDexEnabled true

        // Optimize for Google Play Store
        vectorDrawables.useSupportLibrary = true
    }

    buildTypes {
        debug {
            debuggable true
            minifyEnabled false
            shrinkResources false
        }

        release {
            // Use production signing if keystore exists, otherwise debug for development
            signingConfig keystorePropertiesFile.exists() ? signingConfigs.release : signingConfigs.debug

            // Enable optimizations for Google Play Store
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'

            // Disable debugging in production
            debuggable false
            jniDebuggable false
            renderscriptDebuggable false

            // Enable ZIP alignment for smaller APK
            zipAlignEnabled true
        }
    }
}

flutter {
    source '../..'
}

dependencies {
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk8:$kotlin_version"
    implementation "org.jetbrains.kotlin:kotlin-reflect:$kotlin_version"

    // Core library desugaring for flutter_local_notifications
    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.0.4'

    // Firebase dependencies with fixed versions - Updated for SDK 34+ compatibility
    implementation platform('com.google.firebase:firebase-bom:33.1.2')
    implementation 'com.google.firebase:firebase-analytics'
    implementation 'com.google.firebase:firebase-auth'
    implementation 'com.google.firebase:firebase-firestore'
    implementation 'com.google.firebase:firebase-storage'

    // Google Play Services for Google Sign-In - Updated for SDK 34+ compatibility
    implementation 'com.google.android.gms:play-services-auth:21.2.0'

    // Google Play Core for split install support - Updated for SDK 35+ compatibility
    // Keep the new modular libraries
    implementation 'com.google.android.play:app-update:2.1.0'
    implementation 'com.google.android.play:app-update-ktx:2.1.0'
    implementation 'com.google.android.play:feature-delivery:2.1.0'
    implementation 'com.google.android.play:feature-delivery-ktx:2.1.0'
    implementation 'com.google.android.play:review:2.0.1'
    implementation 'com.google.android.play:review-ktx:2.0.1'

    // Google Play Core for split install support - Updated for SDK 35+ compatibility
    implementation 'com.google.android.play:app-update:2.1.0'
    implementation 'com.google.android.play:app-update-ktx:2.1.0'
    implementation 'com.google.android.play:feature-delivery:2.1.0'
    implementation 'com.google.android.play:feature-delivery-ktx:2.1.0'
    implementation 'com.google.android.play:review:2.0.1'
    implementation 'com.google.android.play:review-ktx:2.0.1'
}

apply plugin: 'com.google.gms.google-services'
