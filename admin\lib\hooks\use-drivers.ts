import { useState, useEffect } from 'react'
import { collection, query, orderBy, onSnapshot, doc, updateDoc, Timestamp, addDoc, deleteDoc, writeBatch } from 'firebase/firestore'
import { db } from '@/lib/firebase/config'

interface Driver {
  id: string
  name: string
  mobile: string
  city: string
  maritalStatus: string
  education: string
  experience: number
  isVerified: boolean
  status: 'pending' | 'verified' | 'rejected'
  documents: {
    cnic?: string
    license?: string
    photo?: string
  }
  documentUrls?: { [key: string]: string }
  createdAt: Date
  verifiedAt?: Date
  verifiedBy?: string
  rejectionReason?: string
  submittedBy?: string
  submittedAt?: Date
}

interface DriverRequest {
  id: string
  name: string
  mobile: string
  city: string
  maritalStatus: string
  education: string
  experience: number
  documentUrls: { [key: string]: string }
  status: 'pending'
  isVerified: false
  documentsVerified: false
  createdAt: Date
  submittedBy: string
  submittedAt: Date
}

export function useDrivers() {
  const [drivers, setDrivers] = useState<Driver[]>([])
  const [driverRequests, setDriverRequests] = useState<DriverRequest[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    // Fetch approved/verified drivers
    const driversQuery = query(
      collection(db, 'drivers'),
      orderBy('createdAt', 'desc')
    )

    // Fetch pending driver requests
    const requestsQuery = query(
      collection(db, 'driver_requests'),
      orderBy('createdAt', 'desc')
    )

    let loadingCount = 2

    const checkLoadingComplete = () => {
      loadingCount--
      if (loadingCount === 0) {
        setIsLoading(false)
      }
    }

    // Subscribe to drivers collection
    const driversUnsubscribe = onSnapshot(
      driversQuery,
      (snapshot) => {
        const driversData = snapshot.docs.map(doc => {
          const data = doc.data()
          return {
            id: doc.id,
            name: data.name || '',
            mobile: data.mobile || '',
            city: data.city || '',
            maritalStatus: data.maritalStatus || '',
            education: data.education || '',
            experience: data.experience || 0,
            isVerified: data.isVerified || false,
            status: data.status || 'verified',
            documents: data.documents || {},
            documentUrls: data.documentUrls || {},
            createdAt: data.createdAt?.toDate() || new Date(),
            verifiedAt: data.verifiedAt?.toDate(),
            verifiedBy: data.verifiedBy,
            rejectionReason: data.rejectionReason,
            submittedBy: data.submittedBy,
            submittedAt: data.submittedAt?.toDate(),
          } as Driver
        })

        setDrivers(driversData)
        checkLoadingComplete()
        setError(null)
      },
      (error) => {
        console.error('Error fetching drivers:', error)
        setError('Failed to fetch drivers')
        checkLoadingComplete()
      }
    )

    // Subscribe to driver requests collection
    const requestsUnsubscribe = onSnapshot(
      requestsQuery,
      (snapshot) => {
        const requestsData = snapshot.docs.map(doc => {
          const data = doc.data()
          return {
            id: doc.id,
            name: data.name || '',
            mobile: data.mobile || '',
            city: data.city || '',
            maritalStatus: data.maritalStatus || '',
            education: data.education || '',
            experience: data.experience || 0,
            documentUrls: data.documentUrls || {},
            status: 'pending',
            isVerified: false,
            documentsVerified: data.documentsVerified || false,
            createdAt: data.createdAt?.toDate() || new Date(),
            submittedBy: data.submittedBy || '',
            submittedAt: data.submittedAt?.toDate() || new Date(),
          } as DriverRequest
        })

        setDriverRequests(requestsData)
        checkLoadingComplete()
        setError(null)
      },
      (error) => {
        console.error('Error fetching driver requests:', error)
        setError('Failed to fetch driver requests')
        checkLoadingComplete()
      }
    )

    return () => {
      driversUnsubscribe?.()
      requestsUnsubscribe?.()
    }
  }, [])

  const updateDriver = async (driverId: string, updates: Partial<Driver>) => {
    try {
      const driverRef = doc(db, 'drivers', driverId)
      const updateData: any = { ...updates }

      // Remove undefined values to prevent Firestore errors
      Object.keys(updateData).forEach(key => {
        if (updateData[key] === undefined) {
          delete updateData[key]
        }
      })

      // Convert dates to Firestore timestamps
      if (updateData.createdAt instanceof Date) {
        updateData.createdAt = Timestamp.fromDate(updateData.createdAt)
      }
      if (updateData.verifiedAt instanceof Date) {
        updateData.verifiedAt = Timestamp.fromDate(updateData.verifiedAt)
      }

      updateData.updatedAt = Timestamp.now()

      await updateDoc(driverRef, updateData)
    } catch (error) {
      console.error('Error updating driver:', error)
      throw error
    }
  }

  const verifyDriver = async (driverId: string, comments?: string) => {
    try {
      const driverRef = doc(db, 'drivers', driverId)
      await updateDoc(driverRef, {
        status: 'verified',
        isVerified: true,
        verifiedAt: Timestamp.now(),
        verifiedBy: 'Admin', // TODO: Get actual admin name
        verificationComments: comments || '', // Fix: Ensure never undefined
        updatedAt: Timestamp.now()
      })
    } catch (error) {
      console.error('Error verifying driver:', error)
      throw error
    }
  }

  const rejectDriver = async (driverId: string, reason: string) => {
    try {
      const driverRef = doc(db, 'drivers', driverId)
      await updateDoc(driverRef, {
        status: 'rejected',
        isVerified: false,
        rejectionReason: reason,
        rejectedAt: Timestamp.now(),
        rejectedBy: 'Admin', // TODO: Get actual admin name
        updatedAt: Timestamp.now()
      })
    } catch (error) {
      console.error('Error rejecting driver:', error)
      throw error
    }
  }

  // Approve a driver request and move it to drivers collection
  const approveDriverRequest = async (requestId: string, comments?: string) => {
    try {
      const batch = writeBatch(db)

      // Find the request
      const request = driverRequests.find(req => req.id === requestId)
      if (!request) {
        throw new Error('Driver request not found')
      }

      // Create driver document in drivers collection
      const driverData = {
        name: request.name,
        mobile: request.mobile,
        city: request.city,
        maritalStatus: request.maritalStatus,
        education: request.education,
        experience: request.experience,
        documentUrls: request.documentUrls,
        documents: request.documentUrls, // For backward compatibility
        status: 'verified',
        isVerified: true,
        documentsVerified: true,
        createdAt: Timestamp.fromDate(request.createdAt),
        submittedBy: request.submittedBy,
        submittedAt: Timestamp.fromDate(request.submittedAt),
        verifiedAt: Timestamp.now(),
        verifiedBy: 'Admin', // TODO: Get actual admin name
        verificationComments: comments || '', // Fix: Ensure never undefined
        updatedAt: Timestamp.now()
      }

      const driverRef = doc(collection(db, 'drivers'))
      batch.set(driverRef, driverData)

      // Delete the request from driver_requests collection
      const requestRef = doc(db, 'driver_requests', requestId)
      batch.delete(requestRef)

      await batch.commit()

      // TODO: Send approval email notification
      console.log('Driver approved and moved to drivers collection')
    } catch (error) {
      console.error('Error approving driver request:', error)
      throw error
    }
  }

  // Reject a driver request
  const rejectDriverRequest = async (requestId: string, reason: string) => {
    try {
      const requestRef = doc(db, 'driver_requests', requestId)
      await updateDoc(requestRef, {
        status: 'rejected',
        rejectionReason: reason,
        rejectedAt: Timestamp.now(),
        rejectedBy: 'Admin', // TODO: Get actual admin name
        updatedAt: Timestamp.now()
      })

      // TODO: Send rejection email notification
      console.log('Driver request rejected')
    } catch (error) {
      console.error('Error rejecting driver request:', error)
      throw error
    }
  }

  return {
    drivers,
    driverRequests,
    isLoading,
    error,
    updateDriver,
    verifyDriver,
    rejectDriver,
    approveDriverRequest,
    rejectDriverRequest,
  }
}
