import 'package:get_it/get_it.dart';
import 'offline_storage_service.dart';
import 'enhanced_cache_service.dart';
import 'network_state_manager.dart';
import '../../features/news/data/repositories/news_repository.dart';
import '../../features/jobs/repositories/offline_jobs_repository.dart';
import '../../features/drivers/repositories/offline_drivers_repository.dart';
import '../utils/app_logger.dart';

/// Service to initialize all offline storage components
/// 
/// Features:
/// - Centralized initialization
/// - Error handling and recovery
/// - Performance monitoring
/// - Background sync setup
class OfflineInitializationService {
  static final OfflineInitializationService _instance = OfflineInitializationService._internal();
  factory OfflineInitializationService() => _instance;
  OfflineInitializationService._internal();

  static const String _tag = 'OfflineInitializationService';
  static final AppLogger _logger = AppLogger(_tag);

  bool _isInitialized = false;
  bool _isInitializing = false;

  /// Initialize all offline storage components
  Future<void> initialize() async {
    if (_isInitialized || _isInitializing) {
      _logger.debug('Offline storage already initialized or initializing');
      return;
    }

    _isInitializing = true;
    final stopwatch = Stopwatch()..start();

    try {
      _logger.info('Starting offline storage initialization...');

      // Step 1: Initialize core storage services
      await _initializeCoreServices();

      // Step 2: Initialize repositories
      await _initializeRepositories();

      // Step 3: Setup background sync
      await _setupBackgroundSync();

      // Step 4: Perform initial data sync if online
      await _performInitialSync();

      stopwatch.stop();
      _isInitialized = true;
      _logger.info('Offline storage initialization completed in ${stopwatch.elapsedMilliseconds}ms');

    } catch (e) {
      stopwatch.stop();
      _logger.error('Failed to initialize offline storage', error: e);
      rethrow;
    } finally {
      _isInitializing = false;
    }
  }

  /// Initialize core storage services
  Future<void> _initializeCoreServices() async {
    _logger.debug('Initializing core storage services...');

    // Core services are already initialized in service locator
    // Just verify they are available
    GetIt.instance<OfflineStorageService>();
    GetIt.instance<EnhancedCacheService>();
    GetIt.instance<NetworkStateManager>();

    _logger.debug('Core storage services initialized');
  }

  /// Initialize repositories
  Future<void> _initializeRepositories() async {
    _logger.debug('Initializing repositories...');

    try {
      // Initialize news repository offline storage
      final newsRepo = NewsRepository();
      await newsRepo.initializeOfflineStorage();

      // Other repositories are already initialized in service locator
      // Just verify they are available
      GetIt.instance<OfflineJobsRepository>();
      GetIt.instance<OfflineDriversRepository>();

      _logger.debug('Repositories initialized');
    } catch (e) {
      _logger.error('Error initializing repositories', error: e);
      rethrow;
    }
  }

  /// Setup background sync
  Future<void> _setupBackgroundSync() async {
    _logger.debug('Setting up background sync...');

    try {
      final networkManager = GetIt.instance<NetworkStateManager>();
      
      // Listen for network events and handle accordingly
      networkManager.networkEventsStream.listen((event) {
        _handleNetworkEvent(event);
      });

      _logger.debug('Background sync setup completed');
    } catch (e) {
      _logger.error('Error setting up background sync', error: e);
      rethrow;
    }
  }

  /// Handle network events
  void _handleNetworkEvent(NetworkEvent event) {
    switch (event) {
      case NetworkEvent.connectionRestored:
        _logger.info('Connection restored, triggering sync');
        _performBackgroundSync();
        break;
      case NetworkEvent.syncCompleted:
        _logger.debug('Background sync completed');
        break;
      case NetworkEvent.syncFailed:
        _logger.warning('Background sync failed');
        break;
      default:
        break;
    }
  }

  /// Perform initial data sync
  Future<void> _performInitialSync() async {
    final networkManager = GetIt.instance<NetworkStateManager>();
    
    if (!networkManager.isOnline) {
      _logger.info('Device is offline, skipping initial sync');
      return;
    }

    _logger.debug('Performing initial data sync...');

    try {
      // Sync news data
      await _syncNewsData();

      // Sync jobs data
      await _syncJobsData();

      // Sync drivers data
      await _syncDriversData();

      _logger.info('Initial data sync completed');
    } catch (e) {
      _logger.warning('Initial data sync failed, will retry later', error: e);
      // Don't rethrow - app should still work with cached data
    }
  }

  /// Perform background sync
  Future<void> _performBackgroundSync() async {
    try {
      _logger.debug('Starting background sync...');

      // Sync all data types
      await Future.wait([
        _syncNewsData(),
        _syncJobsData(),
        _syncDriversData(),
      ]);

      _logger.info('Background sync completed successfully');
    } catch (e) {
      _logger.error('Background sync failed', error: e);
    }
  }

  /// Sync news data
  Future<void> _syncNewsData() async {
    try {
      final newsRepo = NewsRepository();
      
      // Fetch fresh news and save to offline storage
      await newsRepo.getNewsFeedOfflineFirst(limit: 50);
      
      _logger.debug('News data synced');
    } catch (e) {
      _logger.error('Error syncing news data', error: e);
    }
  }

  /// Sync jobs data
  Future<void> _syncJobsData() async {
    try {
      final jobsRepo = GetIt.instance<OfflineJobsRepository>();
      
      // Fetch fresh jobs and save to offline storage
      await jobsRepo.getJobsOfflineFirst(limit: 100);
      
      _logger.debug('Jobs data synced');
    } catch (e) {
      _logger.error('Error syncing jobs data', error: e);
    }
  }

  /// Sync drivers data
  Future<void> _syncDriversData() async {
    try {
      final driversRepo = GetIt.instance<OfflineDriversRepository>();
      
      // Fetch fresh drivers and save to offline storage
      await driversRepo.getDriversOfflineFirst(limit: 100);
      
      _logger.debug('Drivers data synced');
    } catch (e) {
      _logger.error('Error syncing drivers data', error: e);
    }
  }

  /// Get initialization status
  bool get isInitialized => _isInitialized;

  /// Get offline storage statistics
  Future<Map<String, dynamic>> getOfflineStats() async {
    if (!_isInitialized) {
      return {'error': 'Offline storage not initialized'};
    }

    try {
      final newsRepo = NewsRepository();
      final jobsRepo = GetIt.instance<OfflineJobsRepository>();
      final driversRepo = GetIt.instance<OfflineDriversRepository>();
      final networkManager = GetIt.instance<NetworkStateManager>();

      final stats = {
        'initialized': _isInitialized,
        'network': networkManager.getNetworkStats(),
        'news': await newsRepo.getOfflineStats(),
        'jobs': await jobsRepo.getOfflineStats(),
        'drivers': await driversRepo.getOfflineStats(),
      };

      return stats;
    } catch (e) {
      _logger.error('Error getting offline stats', error: e);
      return {'error': e.toString()};
    }
  }

  /// Clear all offline data
  Future<void> clearAllOfflineData() async {
    if (!_isInitialized) {
      _logger.warning('Cannot clear offline data - not initialized');
      return;
    }

    try {
      _logger.info('Clearing all offline data...');

      final newsRepo = NewsRepository();
      final jobsRepo = GetIt.instance<OfflineJobsRepository>();
      final driversRepo = GetIt.instance<OfflineDriversRepository>();
      final enhancedCache = GetIt.instance<EnhancedCacheService>();

      await Future.wait([
        newsRepo.clearOfflineData(),
        jobsRepo.clearOfflineData(),
        driversRepo.clearOfflineData(),
        enhancedCache.clear(),
      ]);

      _logger.info('All offline data cleared');
    } catch (e) {
      _logger.error('Error clearing offline data', error: e);
      rethrow;
    }
  }

  /// Force sync all data
  Future<void> forceSyncAll() async {
    if (!_isInitialized) {
      _logger.warning('Cannot force sync - not initialized');
      return;
    }

    final networkManager = GetIt.instance<NetworkStateManager>();
    
    if (!networkManager.isOnline) {
      _logger.warning('Cannot force sync - device is offline');
      return;
    }

    await _performBackgroundSync();
  }
}
