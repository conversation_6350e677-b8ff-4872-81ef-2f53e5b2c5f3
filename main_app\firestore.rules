rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Helper function to check if a user is an admin
    function isAdmin() {
      return request.auth != null &&
             get(/databases/$(database)/documents/admins/$(request.auth.uid)).data.isAdmin == true;
    }

    // Allow users to read and write their own data - Temporarily more permissive
    match /users/{userId} {
      allow read: if true;
      allow write: if true; // Temporarily allow all writes for debugging
    }

    // Allow authenticated users to read driver data
    match /drivers/{driverId} {
      allow read: if request.auth != null;
      // Allow driver to update their own data
      allow write: if request.auth != null &&
                    (request.auth.uid == driverId ||
                    get(/databases/$(database)/documents/users/$(request.auth.uid)).data.isAdmin == true);
    }

    // Driver requests - Allow authenticated users to create and read their own
    // Enforce email uniqueness constraint
    match /driver_requests/{requestId} {
      allow read: if request.auth != null;
      allow create: if request.auth != null &&
                    request.auth.uid == request.resource.data.submittedBy &&
                    // Ensure email is provided for uniqueness constraint
                    'userEmail' in request.resource.data &&
                    request.resource.data.userEmail is string &&
                    request.resource.data.userEmail.size() > 0;
      allow update, delete: if request.auth != null &&
                            get(/databases/$(database)/documents/users/$(request.auth.uid)).data.isAdmin == true;
    }

    // Job rules - Enforce email uniqueness constraint
    match /jobs/{jobId} {
      // Anyone can read jobs
      allow read: if true;

      // Allow authenticated users to create jobs with email constraint
      allow create: if request.auth != null &&
                    request.auth.uid == request.resource.data.posterId &&
                    // Ensure email is provided for uniqueness constraint
                    'posterEmail' in request.resource.data &&
                    request.resource.data.posterEmail is string &&
                    request.resource.data.posterEmail.size() > 0;

      // Allow updates and deletes for job poster or admin
      allow update, delete: if request.auth != null &&
                           (request.auth.uid == resource.data.posterId ||
                            get(/databases/$(database)/documents/users/$(request.auth.uid)).data.isAdmin == true);
    }

    // Forum rules
    match /forums/{forumId} {
      // Anyone can read forums
      allow read: if true;

      // Only authenticated users can create forums
      allow create: if request.auth != null;

      // Only participants can update forum details
      allow update: if request.auth != null &&
                    (request.auth.uid in resource.data.participants ||
                     request.auth.uid == resource.data.creatorId);

      // Only the creator can delete the forum
      allow delete: if request.auth != null && request.auth.uid == resource.data.creatorId;

      // Forum messages sub-collection
      match /messages/{messageId} {
        // Anyone can read the messages
        allow read: if true;

        // Only authenticated users can create messages
        allow create: if request.auth != null;

        // Only the message author can update or delete their NON-ADMIN messages
        allow update, delete: if request.auth != null &&
                           request.auth.uid == resource.data.senderId &&
                           (!('isAdmin' in resource.data) || resource.data.isAdmin == false);
      }
    }

    // Private Queries rules
    match /queries/{userId} {
      // User can access only their own query thread
      allow read, write: if request.auth != null && request.auth.uid == userId;

      // Admins can access all query threads
      allow read, write: if isAdmin();

      // Query messages sub-collection
      match /messages/{messageId} {
        // User can read only their own messages
        allow read: if request.auth != null && request.auth.uid == userId;

        // User can create messages in their own thread
        allow create: if request.auth != null && request.auth.uid == userId;

        // User can only update or delete their own NON-ADMIN messages
        allow update, delete: if request.auth != null &&
                           request.auth.uid == userId &&
                           resource.data.senderId == request.auth.uid &&
                           (!('isAdmin' in resource.data) || resource.data.isAdmin == false);

        // Admins can read, create, update, and delete in any thread
        allow read, create: if isAdmin();

        // Admins can only update or delete their own messages
        allow update, delete: if isAdmin() && resource.data.senderId == request.auth.uid;
      }
    }

    // Admin collection
    match /admins/{userId} {
      // Only admins can read the admin collection
      allow read: if isAdmin();

      // Only admins can write to the admin collection
      allow write: if isAdmin();
    }

    // Chat room rules
    match /chats/{chatId} {
      // Only participants can read or write to a chat
      allow read, write: if request.auth != null && request.auth.uid in resource.data.participants;

      // Allow creation if the user is one of the participants
      allow create: if request.auth != null &&
                    request.auth.uid in request.resource.data.participants;

      // Chat messages sub-collection
      match /messages/{messageId} {
        // Only chat participants can read/write messages
        allow read, write: if request.auth != null &&
                         request.auth.uid in get(/databases/$(database)/documents/chats/$(chatId)).data.participants;
      }
    }

    // News articles rules
    match /news_articles/{articleId} {
      // Anyone can read news articles
      allow read: if true;

      // Only admins can create or update news articles directly
      allow create, update: if request.auth != null &&
                           get(/databases/$(database)/documents/users/$(request.auth.uid)).data.isAdmin == true;

      // Only admins can delete articles
      allow delete: if request.auth != null &&
                   get(/databases/$(database)/documents/users/$(request.auth.uid)).data.isAdmin == true;

      // Reactions subcollection
      match /reactions/{userId} {
        // Anyone can read reactions
        allow read: if true;

        // Users can only create/update/delete their own reactions
        allow create, update, delete: if request.auth != null && request.auth.uid == userId;
      }
    }

    // Default deny all other requests
    match /{document=**} {
      allow read, write: if false;
    }
  }
}