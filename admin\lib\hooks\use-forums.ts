import { useState, useEffect } from 'react'
import { collection, query, orderBy, onSnapshot, doc, updateDoc, deleteDoc, addDoc, Timestamp } from 'firebase/firestore'
import { ref, uploadBytes, getDownloadURL } from 'firebase/storage'
import { db, storage } from '@/lib/firebase/config'

interface Forum {
  id: string
  title: string
  description: string
  createdAt: Date
  creatorId: string
  creatorName: string
  imageUrl?: string
  participants: string[]
  participantNames: { [key: string]: string }
  lastMessage: string
  lastMessageTime: Date
  lastSenderId: string
  messageCount: number
  status: 'active' | 'locked' | 'archived' | 'flagged'
  isPinned: boolean
  isSticky: boolean
  category: 'general' | 'help' | 'feedback' | 'announcements' | 'off-topic'
  tags: string[]
  moderatedBy?: string
  moderationReason?: string
}

interface ForumMessage {
  id: string
  forumId: string
  senderId: string
  senderName: string
  senderAvatar: string
  text: string
  timestamp: Date
  reactions: { [emoji: string]: string[] }
  replyToId?: string
  replyToText?: string
  replyToSenderName?: string
  attachments?: string[]
  voiceNote?: string
  voiceNoteDuration?: number
  isEdited: boolean
  isPending?: boolean
  isUploading?: boolean
}

export function useForums() {
  const [forums, setForums] = useState<Forum[]>([])
  const [forumMessages, setForumMessages] = useState<{ [forumId: string]: ForumMessage[] }>({})
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    // Fetch forums
    const forumsQuery = query(
      collection(db, 'forums'),
      orderBy('lastMessageTime', 'desc')
    )

    const unsubscribe = onSnapshot(
      forumsQuery,
      (snapshot) => {
        const forumsData = snapshot.docs.map(doc => {
          const data = doc.data()
          return {
            id: doc.id,
            title: data.title || '',
            description: data.description || '',
            createdAt: data.createdAt?.toDate() || new Date(),
            creatorId: data.creatorId || '',
            creatorName: data.creatorName || '',
            imageUrl: data.imageUrl,
            participants: data.participants || [],
            participantNames: data.participantNames || {},
            lastMessage: data.lastMessage || '',
            lastMessageTime: data.lastMessageTime?.toDate() || new Date(),
            lastSenderId: data.lastSenderId || '',
            messageCount: data.messageCount || 0,
            status: data.status || 'active',
            isPinned: data.isPinned || false,
            isSticky: data.isSticky || false,
            category: data.category || 'general',
            tags: data.tags || [],
            moderatedBy: data.moderatedBy,
            moderationReason: data.moderationReason,
          } as Forum
        })

        setForums(forumsData)
        setIsLoading(false)
        setError(null)
      },
      (error) => {
        console.error('Error fetching forums:', error)
        setError('Failed to fetch forums')
        setIsLoading(false)
      }
    )

    return () => unsubscribe()
  }, [])

  // Fetch messages for a specific forum
  const fetchForumMessages = (forumId: string) => {
    console.log('Setting up forum message listener for:', forumId) // Debug log

    const messagesQuery = query(
      collection(db, 'forums', forumId, 'messages'),
      orderBy('timestamp', 'asc')
    )

    return onSnapshot(
      messagesQuery,
      (snapshot) => {
        console.log('Forum messages snapshot received:', snapshot.docs.length, 'messages') // Debug log

        const messagesData = snapshot.docs.map(doc => {
          const data = doc.data()
          console.log('Processing forum message:', doc.id, data) // Debug log

          return {
            id: doc.id,
            forumId,
            senderId: data.senderId || '',
            senderName: data.senderName || '',
            senderAvatar: data.senderAvatar || '',
            text: data.text || '',
            timestamp: data.timestamp?.toDate() || new Date(),
            isAdmin: data.isAdmin || false, // Add isAdmin field
            reactions: data.reactions || {},
            replyToId: data.replyToId,
            replyToText: data.replyToText,
            replyToSenderName: data.replyToSenderName,
            attachments: data.attachments || [],
            voiceNote: data.voiceNote,
            voiceNoteDuration: data.voiceNoteDuration,
            isEdited: data.isEdited || false,
            isPending: data.isPending || false,
            isUploading: data.isUploading || false,
          } as ForumMessage
        })

        console.log('Setting forum messages for:', forumId, messagesData) // Debug log

        setForumMessages(prev => ({
          ...prev,
          [forumId]: messagesData
        }))
      },
      (error) => {
        console.error('Error in forum message listener:', error) // Debug log
      }
    )
  }

  const updateForum = async (forumId: string, updates: Partial<Forum>) => {
    try {
      const forumRef = doc(db, 'forums', forumId)
      const updateData: any = { ...updates }

      // Remove undefined values to prevent Firestore errors
      Object.keys(updateData).forEach(key => {
        if (updateData[key] === undefined) {
          delete updateData[key]
        }
      })

      // Convert dates to Firestore timestamps
      if (updateData.createdAt instanceof Date) {
        updateData.createdAt = Timestamp.fromDate(updateData.createdAt)
      }
      if (updateData.lastMessageTime instanceof Date) {
        updateData.lastMessageTime = Timestamp.fromDate(updateData.lastMessageTime)
      }

      await updateDoc(forumRef, updateData)
    } catch (error) {
      console.error('Error updating forum:', error)
      throw error
    }
  }

  const deleteForum = async (forumId: string) => {
    try {
      const forumRef = doc(db, 'forums', forumId)
      await deleteDoc(forumRef)
    } catch (error) {
      console.error('Error deleting forum:', error)
      throw error
    }
  }

  const createForum = async (forumData: Partial<Forum>) => {
    try {
      const newForumData = {
        ...forumData,
        createdAt: Timestamp.now(),
        lastMessageTime: Timestamp.now(),
        messageCount: 0,
        participants: forumData.participants || [],
        participantNames: forumData.participantNames || {},
        isPinned: forumData.isPinned || false,
        isSticky: forumData.isSticky || false,
        status: forumData.status || 'active',
        lastMessage: '',
        lastSenderId: '',
      }

      await addDoc(collection(db, 'forums'), newForumData)
    } catch (error) {
      console.error('Error creating forum:', error)
      throw error
    }
  }

  const moderateForum = async (forumId: string, status: Forum['status'], reason?: string) => {
    try {
      const forumRef = doc(db, 'forums', forumId)
      const updateData: any = {
        status,
        moderatedBy: 'Admin', // TODO: Get actual admin name
      }

      // Only add moderationReason if it's provided
      if (reason !== undefined) {
        updateData.moderationReason = reason
      }

      await updateDoc(forumRef, updateData)
    } catch (error) {
      console.error('Error moderating forum:', error)
      throw error
    }
  }

  const sendMessage = async (messageParams: {
    forumId: string
    text: string
    replyToId?: string
    replyToText?: string
    replyToSenderName?: string
    attachments?: File[]
    voiceNote?: File
    voiceNoteDuration?: number
  }) => {
    try {
      const { forumId, text, replyToId, replyToText, replyToSenderName, attachments, voiceNote, voiceNoteDuration } = messageParams

      // Upload attachments to Firebase Storage
      const attachmentUrls: string[] = []
      if (attachments && attachments.length > 0) {
        console.log('Uploading', attachments.length, 'attachments...')
        for (let i = 0; i < attachments.length; i++) {
          const file = attachments[i]
          const timestamp = Date.now()
          const fileName = `${timestamp}_${i}_${file.name}`
          const storageRef = ref(storage, `forums/${forumId}/attachments/${fileName}`)

          try {
            const snapshot = await uploadBytes(storageRef, file)
            const downloadURL = await getDownloadURL(snapshot.ref)
            attachmentUrls.push(downloadURL)
            console.log('Uploaded attachment:', fileName, 'URL:', downloadURL)
          } catch (uploadError) {
            console.error('Error uploading attachment:', uploadError)
            throw new Error(`Failed to upload attachment: ${file.name}`)
          }
        }
      }

      // Upload voice note to Firebase Storage
      let voiceNoteUrl: string | undefined
      if (voiceNote) {
        console.log('Uploading voice note...')
        const timestamp = Date.now()
        const fileName = `voice_${timestamp}.webm`
        const storageRef = ref(storage, `forums/${forumId}/voice/${fileName}`)

        try {
          const snapshot = await uploadBytes(storageRef, voiceNote)
          voiceNoteUrl = await getDownloadURL(snapshot.ref)
          console.log('Uploaded voice note:', fileName, 'URL:', voiceNoteUrl)
        } catch (uploadError) {
          console.error('Error uploading voice note:', uploadError)
          throw new Error('Failed to upload voice note')
        }
      }

      // Build message data, excluding undefined values
      const messageData: any = {
        senderId: 'admin', // TODO: Get actual admin ID
        senderName: 'Admin', // TODO: Get actual admin name
        senderAvatar: '', // TODO: Get actual admin avatar
        text,
        timestamp: Timestamp.now(),
        isAdmin: true, // Add isAdmin field
        reactions: {},
        attachments: attachmentUrls,
        isEdited: false,
        isPending: false,
        isUploading: false,
      }

      // Only add reply fields if they have values
      if (replyToId) {
        messageData.replyToId = replyToId
      }
      if (replyToText) {
        messageData.replyToText = replyToText
      }
      if (replyToSenderName) {
        messageData.replyToSenderName = replyToSenderName
      }

      // Only add voice note fields if they have values
      if (voiceNoteUrl) {
        messageData.voiceNote = voiceNoteUrl
      }
      if (voiceNoteDuration) {
        messageData.voiceNoteDuration = voiceNoteDuration
      }

      console.log('Sending forum message:', messageData) // Debug log

      await addDoc(collection(db, 'forums', forumId, 'messages'), messageData)

      console.log('Forum message sent successfully') // Debug log

      // Update forum's last message info
      const forumRef = doc(db, 'forums', forumId)
      await updateDoc(forumRef, {
        lastMessage: text,
        lastMessageTime: Timestamp.now(),
        lastSenderId: 'admin',
        messageCount: (forums.find(f => f.id === forumId)?.messageCount || 0) + 1
      })
    } catch (error) {
      console.error('Error sending message:', error)
      throw error
    }
  }

  return {
    forums,
    forumMessages,
    isLoading,
    error,
    updateForum,
    deleteForum,
    createForum,
    moderateForum,
    fetchForumMessages,
    sendMessage,
  }
}
