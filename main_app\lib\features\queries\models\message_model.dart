import 'package:cloud_firestore/cloud_firestore.dart';

class Message {
  final String id;
  final String queryId;
  final String senderId; // userId or 'admin'
  final String senderName;
  final String message;
  final DateTime timestamp;
  final bool isRead;
  final bool isAdmin;

  Message({
    required this.id,
    required this.queryId,
    required this.senderId,
    required this.senderName,
    required this.message,
    required this.timestamp,
    this.isRead = false,
    required this.isAdmin,
  });

  factory Message.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return Message(
      id: doc.id,
      queryId: data['queryId'] ?? '',
      senderId: data['senderId'] ?? '',
      senderName: data['senderName'] ?? '',
      message: data['message'] ?? '',
      timestamp: (data['timestamp'] as Timestamp?)?.toDate() ?? DateTime.now(),
      isRead: data['isRead'] ?? false,
      isAdmin: data['isAdmin'] ?? false,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'queryId': queryId,
      'senderId': senderId,
      'senderName': senderName,
      'message': message,
      'timestamp': Timestamp.fromDate(timestamp),
      'isRead': isRead,
      'isAdmin': isAdmin,
    };
  }

  Message copyWith({
    String? id,
    String? queryId,
    String? senderId,
    String? senderName,
    String? message,
    DateTime? timestamp,
    bool? isRead,
    bool? isAdmin,
  }) {
    return Message(
      id: id ?? this.id,
      queryId: queryId ?? this.queryId,
      senderId: senderId ?? this.senderId,
      senderName: senderName ?? this.senderName,
      message: message ?? this.message,
      timestamp: timestamp ?? this.timestamp,
      isRead: isRead ?? this.isRead,
      isAdmin: isAdmin ?? this.isAdmin,
    );
  }
} 