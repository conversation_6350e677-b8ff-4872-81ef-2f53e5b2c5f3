import 'package:cloud_firestore/cloud_firestore.dart';

class Reaction {
  final String userId;
  final String reactionType;
  final DateTime timestamp;

  Reaction({
    required this.userId,
    required this.reactionType,
    required this.timestamp,
  });

  factory Reaction.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return Reaction(
      userId: data['userId'] ?? '',
      reactionType: data['reactionType'] ?? '',
      timestamp: (data['timestamp'] as Timestamp).toDate(),
    );
  }

  Map<String, dynamic> toFirestore() => {
    'userId': userId,
    'reactionType': reactionType,
    'timestamp': Timestamp.fromDate(timestamp),
  };
} 