import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:image_picker/image_picker.dart';
import 'package:device_info_plus/device_info_plus.dart';
import '../firebase/firebase_permissions_check.dart';

/// Modern image picker helper that handles Android 13+ photo picker
/// and fallbacks gracefully for older versions
class ImagePickerHelper {
  static final ImagePicker _picker = ImagePicker();
  
  /// Pick a single image with modern Android support
  static Future<File?> pickSingleImage({
    ImageSource? preferredSource,
    int imageQuality = 85,
  }) async {
    try {
      // Check permissions first
      final hasPermissions = await FirebasePermissionsCheck.checkRequiredPermissions(
        includeCamera: preferredSource == ImageSource.camera
      );
      
      if (!hasPermissions) {
        if (kDebugMode) {
          print('Permissions not granted for image picking');
        }
        return null;
      }
      
      XFile? pickedFile;
      
      if (Platform.isAndroid) {
        final androidInfo = await DeviceInfoPlugin().androidInfo;
        final sdkVersion = androidInfo.version.sdkInt;
        
        if (sdkVersion >= 33 && preferredSource != ImageSource.camera) {
          // Android 13+: Use modern approach (photo picker is handled by image_picker automatically)
          pickedFile = await _picker.pickImage(
            source: ImageSource.gallery,
            imageQuality: imageQuality,
          );
        } else {
          // Older Android or camera request
          pickedFile = await _picker.pickImage(
            source: preferredSource ?? ImageSource.gallery,
            imageQuality: imageQuality,
          );
        }
      } else {
        // iOS and other platforms
        pickedFile = await _picker.pickImage(
          source: preferredSource ?? ImageSource.gallery,
          imageQuality: imageQuality,
        );
      }
      
      if (pickedFile != null) {
        return File(pickedFile.path);
      }
      
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error picking image: $e');
      }
      return null;
    }
  }
  
  /// Pick multiple images with modern Android support
  static Future<List<File>> pickMultipleImages({
    int imageQuality = 85,
    int? limit,
  }) async {
    try {
      // Check permissions first
      final hasPermissions = await FirebasePermissionsCheck.checkRequiredPermissions(
        includeCamera: false // Multiple selection doesn't support camera
      );
      
      if (!hasPermissions) {
        if (kDebugMode) {
          print('Permissions not granted for multiple image picking');
        }
        return [];
      }
      
      List<XFile> pickedFiles;
      
      if (Platform.isAndroid) {
        final androidInfo = await DeviceInfoPlugin().androidInfo;
        final sdkVersion = androidInfo.version.sdkInt;
        
        if (sdkVersion >= 33) {
          // Android 13+: Use modern photo picker (handled automatically by image_picker)
          pickedFiles = await _picker.pickMultiImage(
            imageQuality: imageQuality,
            limit: limit,
          );
        } else {
          // Older Android: Use traditional picker
          pickedFiles = await _picker.pickMultiImage(
            imageQuality: imageQuality,
            limit: limit,
          );
        }
      } else {
        // iOS and other platforms
        pickedFiles = await _picker.pickMultiImage(
          imageQuality: imageQuality,
          limit: limit,
        );
      }
      
      // Convert XFile to File
      List<File> files = [];
      for (XFile xFile in pickedFiles) {
        files.add(File(xFile.path));
      }
      
      return files;
    } catch (e) {
      if (kDebugMode) {
        print('Error picking multiple images: $e');
      }
      return [];
    }
  }
  
  /// Show image source selection dialog
  static Future<File?> showImageSourceDialog({
    required Function(String) showDialog,
    int imageQuality = 85,
  }) async {
    // This would typically show a dialog to choose between camera and gallery
    // For now, we'll default to gallery
    return await pickSingleImage(
      preferredSource: ImageSource.gallery,
      imageQuality: imageQuality,
    );
  }
  
  /// Check if the device supports the modern photo picker
  static Future<bool> supportsModernPhotoPicker() async {
    if (Platform.isAndroid) {
      final androidInfo = await DeviceInfoPlugin().androidInfo;
      return androidInfo.version.sdkInt >= 33;
    }
    return false;
  }
  
  /// Get recommended approach for the current platform
  static Future<String> getRecommendedApproach() async {
    if (Platform.isAndroid) {
      final androidInfo = await DeviceInfoPlugin().androidInfo;
      final sdkVersion = androidInfo.version.sdkInt;
      
      if (sdkVersion >= 33) {
        return 'Using Android 13+ Photo Picker - no storage permissions needed';
      } else if (sdkVersion >= 29) {
        return 'Using scoped storage with system picker';
      } else {
        return 'Using legacy storage permissions';
      }
    } else if (Platform.isIOS) {
      return 'Using iOS photo library picker';
    }
    
    return 'Using platform default picker';
  }
}