rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    
    // Helper functions for better security and readability
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isOwner(uid) {
      return request.auth.uid == uid;
    }
    
    // Generic validator (used by other areas); remains unchanged for now
    function isValidFile() {
      return request.resource.size < 10 * 1024 * 1024 &&
             (request.resource.contentType.matches('image/.*') ||
              request.resource.contentType.matches('audio/.*') ||
              request.resource.contentType.matches('application/pdf'));
    }

    // Strict validator for driver document uploads (images only, <= 1MB)
    function isDriverDocImage() {
      return request.resource.size <= 1 * 1024 * 1024 &&
             request.resource.contentType.matches('image/.*');
    }
    
    // ✅ FIXED: Temporary uploads for driver registration
    match /temp_uploads/{uid}/documents/{allPaths=**} {
      // Only allow authenticated owner to upload images up to 1MB
      allow read, write: if isAuthenticated() && isOwner(uid) && isDriverDocImage();
    }
    
    // ✅ FIXED: Query attachments - more permissive for chat functionality
    match /queries/{queryId}/messages/{messageId}/{allPaths=**} {
      allow read: if isAuthenticated();
      allow write: if isAuthenticated() && isValidFile();
    }
    
    // ✅ FIXED: Forum attachments - specific path matching
    match /forums/{forumId}/attachments/{allPaths=**} {
      allow read: if isAuthenticated();
      allow write: if isAuthenticated() && isValidFile();
    }
    
    // ✅ FIXED: Forum voice notes - specific path matching  
    match /forums/{forumId}/voice_notes/{allPaths=**} {
      allow read: if isAuthenticated();
      allow write: if isAuthenticated() && 
                   request.resource.contentType.matches('audio/.*') &&
                   request.resource.size < 5 * 1024 * 1024; // 5MB for audio
    }
    
    // ✅ User-specific directories
    match /users/{userId}/{allPaths=**} {
      allow read, write: if isAuthenticated() && isOwner(userId) && isValidFile();
    }
    
    // ✅ Driver documents (existing - keep for backward compatibility)
    match /drivers/{driverId}/{allPaths=**} {
      allow read: if isAuthenticated();
      allow write: if isAuthenticated() && isValidFile();
    }
    
    // ✅ Public read-only files
    match /public/{allPaths=**} {
      allow read: if true;
      allow write: if false;
    }
    
    // ❌ Deny all other requests by default
    match /{allPaths=**} {
      allow read, write: if false;
    }
  }
}