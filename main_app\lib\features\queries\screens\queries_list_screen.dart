import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../services/query_service.dart';
import '../models/query_model.dart';
import 'query_detail_screen.dart';
import 'create_query_screen.dart';

class QueriesListScreen extends StatefulWidget {
  const QueriesListScreen({Key? key}) : super(key: key);

  @override
  QueriesListScreenState createState() => QueriesListScreenState();
}

class QueriesListScreenState extends State<QueriesListScreen> {
  final QueryService _queryService = QueryService();
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('My Support Queries'),
      ),
      body: StreamBuilder<List<Query>>(
        stream: _queryService.getUserQueries(),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }

          if (snapshot.hasError) {
            return Center(child: Text('Error: ${snapshot.error}'));
          }

          final queries = snapshot.data ?? [];
          
          if (queries.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Text('You have no support queries yet.'),
                  const SizedBox(height: 16),
                  ElevatedButton.icon(
                    onPressed: () => _navigateToCreateQuery(),
                    icon: const Icon(Icons.add),
                    label: const Text('Create New Query'),
                  ),
                ],
              ),
            );
          }

          return ListView.builder(
            itemCount: queries.length,
            itemBuilder: (context, index) {
              final query = queries[index];
              return _buildQueryItem(query);
            },
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _navigateToCreateQuery(),
        tooltip: 'Create New Query',
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildQueryItem(Query query) {
    final dateFormat = DateFormat('MMM d, h:mm a');
    final messageDate = dateFormat.format(query.lastMessageTime);
    
    // Status indicator color
    Color statusColor;
    switch (query.status) {
      case 'open':
        statusColor = Colors.green;
        break;
      case 'closed':
        statusColor = Colors.red;
        break;
      default:
        statusColor = Colors.grey;
    }
    
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: Theme.of(context).primaryColor,
          child: const Icon(Icons.question_answer, color: Colors.white),
        ),
        title: Row(
          children: [
            Expanded(
              child: Text(
                query.topic,
                style: const TextStyle(fontWeight: FontWeight.bold),
                overflow: TextOverflow.ellipsis,
              ),
            ),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: statusColor.withOpacity(0.2),
                borderRadius: BorderRadius.circular(10),
                border: Border.all(color: statusColor),
              ),
              child: Text(
                query.status.toUpperCase(),
                style: TextStyle(
                  fontSize: 10,
                  color: statusColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Text(
              query.lastMessage,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 2),
            Text(
              messageDate,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
        trailing: query.unreadUserCount > 0
            ? Container(
                padding: const EdgeInsets.all(6),
                decoration: const BoxDecoration(
                  color: Colors.red,
                  shape: BoxShape.circle,
                ),
                child: Text(
                  query.unreadUserCount.toString(),
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                ),
              )
            : null,
        onTap: () => _navigateToQueryDetail(query),
      ),
    );
  }

  void _navigateToCreateQuery() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const CreateQueryScreen(),
      ),
    );
  }

  void _navigateToQueryDetail(Query query) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => QueryDetailScreen(queryId: query.id),
      ),
    );
  }
} 