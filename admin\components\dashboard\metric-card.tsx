'use client'

import { motion } from 'framer-motion'
import { LucideIcon, TrendingUp, TrendingDown } from 'lucide-react'
import { Card, CardContent } from '@/components/ui/card'
import { cn, formatNumber } from '@/lib/utils'

interface MetricCardProps {
  title: string
  value: number
  change: number
  icon: LucideIcon
  description: string
  color: 'blue' | 'green' | 'purple' | 'orange' | 'red'
}

const colorClasses = {
  blue: 'bg-blue-500 text-blue-600 bg-blue-50 dark:bg-blue-900 dark:text-blue-400 dark:bg-blue-950',
  green: 'bg-green-500 text-green-600 bg-green-50 dark:bg-green-900 dark:text-green-400 dark:bg-green-950',
  purple: 'bg-purple-500 text-purple-600 bg-purple-50 dark:bg-purple-900 dark:text-purple-400 dark:bg-purple-950',
  orange: 'bg-orange-500 text-orange-600 bg-orange-50 dark:bg-orange-900 dark:text-orange-400 dark:bg-orange-950',
  red: 'bg-red-500 text-red-600 bg-red-50 dark:bg-red-900 dark:text-red-400 dark:bg-red-950',
}

export function MetricCard({ title, value, change, icon: Icon, description, color }: MetricCardProps) {
  const isPositive = change >= 0
  const [bgColor, textColor, lightBg] = colorClasses[color].split(' ')

  return (
    <motion.div
      whileHover={{ scale: 1.02 }}
      transition={{ duration: 0.2 }}
    >
      <Card className="hover:shadow-lg transition-shadow duration-200">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <p className="text-sm font-medium text-gray-600 mb-1">{title}</p>
              <div className="flex items-baseline space-x-2">
                <motion.p
                  className="text-2xl font-bold text-gray-900"
                  initial={{ opacity: 0, scale: 0.5 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5, delay: 0.2 }}
                >
                  {formatNumber(value)}
                </motion.p>
                <div className={cn(
                  'flex items-center space-x-1 text-xs font-medium px-2 py-1 rounded-full',
                  isPositive ? 'text-green-600 bg-green-50' : 'text-red-600 bg-red-50'
                )}>
                  {isPositive ? (
                    <TrendingUp className="w-3 h-3" />
                  ) : (
                    <TrendingDown className="w-3 h-3" />
                  )}
                  <span>{Math.abs(change)}%</span>
                </div>
              </div>
              <p className="text-xs text-gray-500 mt-1">{description}</p>
            </div>

            <div className={cn('p-3 rounded-lg', lightBg)}>
              <Icon className={cn('w-6 h-6', textColor)} />
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )
}
