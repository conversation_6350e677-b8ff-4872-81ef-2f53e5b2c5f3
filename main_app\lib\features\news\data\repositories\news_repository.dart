import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:sqflite/sqflite.dart';
import '../models/news_article.dart';
import '../models/reaction.dart';
import '../../../../core/services/cache_service.dart';
import '../../../../core/services/enhanced_cache_service.dart';
import '../../../../core/services/offline_storage_service.dart';
import '../sources/news_api_service.dart';
import '../../../../core/utils/app_logger.dart';

class NewsRepository {
  final FirebaseFirestore _firestore;
  final FirebaseAuth _auth;
  final CacheService _cacheService = CacheService();
  final EnhancedCacheService _enhancedCache = EnhancedCacheService();
  final OfflineStorageService _offlineStorage = OfflineStorageService();
  final NewsApiService? _newsApiService;
  final AppLogger _logger = AppLogger('NewsRepository');

  NewsRepository({
    FirebaseFirestore? firestore,
    FirebaseAuth? auth,
    NewsApiService? newsApiService,
  })  : _firestore = firestore ?? FirebaseFirestore.instance,
        _auth = auth ?? FirebaseAuth.instance,
        _newsApiService = newsApiService;

  // Collection references
  CollectionReference<Map<String, dynamic>> get _newsCollection =>
      _firestore.collection('news_articles');

  /// Save news articles to Firestore (batch operation)
  Future<void> saveNewsArticles(List<NewsArticle> articles) async {
    if (articles.isEmpty) return;

    try {
      final batch = _firestore.batch();

      for (final article in articles) {
        // Check if article already exists by URL to avoid duplicates
        final existingQuery = await _newsCollection
            .where('url', isEqualTo: article.url)
            .limit(1)
            .get();

        if (existingQuery.docs.isEmpty) {
          // Article doesn't exist, add it
          final docRef = _newsCollection.doc();
          final articleWithId = article.copyWith();
          batch.set(docRef, articleWithId.toFirestore());
        } else {
          // Article exists, update it with new data
          final existingDoc = existingQuery.docs.first;
          final existingArticle = NewsArticle.fromFirestore(existingDoc);

          // Update with new data but preserve views and reactions
          final updatedArticle = article.copyWith(
            views: existingArticle.views,
            reactionCounts: existingArticle.reactionCounts,
          );

          batch.update(existingDoc.reference, updatedArticle.toFirestore());
        }
      }

      await batch.commit();

      // Clear cache to force refresh
      await _cacheService.clear();

      _logger.info('Saved ${articles.length} articles to Firestore');
    } catch (e) {
      _logger.error('Error saving articles to Firestore', error: e);
      rethrow;
    }
  }

  /// Fetch fresh news from API and save to Firestore
  Future<List<NewsArticle>> fetchAndSaveFreshNews() async {
    if (_newsApiService == null) {
      throw Exception('NewsApiService not initialized');
    }

    try {
      final articles = await _newsApiService!.fetchAutomotiveNews();

      if (articles.isNotEmpty) {
        await saveNewsArticles(articles);
      }

      return articles;
    } catch (e) {
      _logger.error('Error fetching fresh news', error: e);
      rethrow;
    }
  }

  // Get news feed with offline-first approach
  Future<List<NewsArticle>> getNewsFeed({
    int limit = 15,
    DocumentSnapshot? lastDocument,
    String? category,
  }) async {
    final cacheKey = 'news_feed_${category ?? 'all'}_$limit';

    return await _enhancedCache.get<List<NewsArticle>>(
      key: cacheKey,
      fetcher: () => _fetchNewsFeedFromFirestore(limit, lastDocument, category),
      memoryTtl: const Duration(minutes: 10),
      hiveTtl: const Duration(hours: 2),
      sqliteTtl: const Duration(days: 1),
    ) ?? [];
  }

  // Get news feed with offline storage fallback
  Future<List<NewsArticle>> getNewsFeedOfflineFirst({
    int limit = 15,
    String? category,
  }) async {
    try {
      // First try to get from offline storage
      final offlineArticles = await _getNewsFromOfflineStorage(category: category, limit: limit);

      // If we have offline data and we're offline, return it
      if (offlineArticles.isNotEmpty && !_offlineStorage.isOnline) {
        _logger.info('Returning ${offlineArticles.length} articles from offline storage');
        return offlineArticles;
      }

      // If we're online, try to fetch fresh data
      if (_offlineStorage.isOnline) {
        try {
          final freshArticles = await _fetchNewsFeedFromFirestore(limit, null, category);
          if (freshArticles.isNotEmpty) {
            // Save to offline storage
            await _saveNewsToOfflineStorage(freshArticles);
            return freshArticles;
          }
        } catch (e) {
          _logger.warning('Failed to fetch fresh news, falling back to offline data', error: e);
        }
      }

      // Return offline data as fallback
      return offlineArticles;
    } catch (e) {
      _logger.error('Error getting news feed', error: e);
      return [];
    }
  }

  // Fetch news feed from Firestore
  Future<List<NewsArticle>> _fetchNewsFeedFromFirestore(
    int limit,
    DocumentSnapshot? lastDocument,
    String? category,
  ) async {
    try {
      Query<Map<String, dynamic>> query = _newsCollection
          .orderBy('pinned', descending: true)
          .orderBy('publishedAt', descending: true);

      if (category != null && category.isNotEmpty && category != 'All') {
        query = query.where('category', isEqualTo: category);
      }

      if (lastDocument != null) {
        query = query.startAfterDocument(lastDocument);
      }

      query = query.limit(limit);

      final QuerySnapshot<Map<String, dynamic>> snapshot = await query.get();
      return snapshot.docs.map((doc) => NewsArticle.fromFirestore(doc)).toList();
    } catch (e) {
      // Check if the error is related to missing indexes
      if (e is FirebaseException &&
          (e.message?.contains('requires an index') ?? false)) {
        // Return empty list when indexes are missing
        _logger.warning('Firebase index missing for news query, returning empty list');
        return [];
      }
      // Rethrow other errors
      rethrow;
    }
  }

  // Get trending articles with caching
  Future<List<NewsArticle>> getTrendingArticles({int limit = 10}) async {
    final cacheKey = 'trending_articles_$limit';

    return await _cacheService.get<List<NewsArticle>>(
      key: cacheKey,
      fetcher: () async {
        try {
          final QuerySnapshot<Map<String, dynamic>> snapshot = await _newsCollection
              .orderBy('views', descending: true)
              .orderBy('publishedAt', descending: true)
              .limit(limit)
              .get();

          return snapshot.docs.map((doc) => NewsArticle.fromFirestore(doc)).toList();
        } catch (e) {
          // Check if the error is related to missing indexes
          if (e is FirebaseException &&
              (e.message?.contains('requires an index') ?? false)) {
            // Return empty list when indexes are missing
            _logger.warning('Firebase index missing for trending articles, returning empty list');
            return [];
          }
          // Rethrow other errors
          rethrow;
        }
      },
      ttl: const Duration(minutes: 15), // Cache trending articles for 15 minutes
    ) ?? [];
  }

  // Get article by ID
  Future<NewsArticle?> getArticleById(String articleId) async {
    final DocumentSnapshot<Map<String, dynamic>> doc =
        await _newsCollection.doc(articleId).get();

    if (!doc.exists) return null;
    return NewsArticle.fromFirestore(doc);
  }

  // Increment view count
  Future<void> incrementViewCount(String articleId) async {
    await _newsCollection.doc(articleId).update({
      'views': FieldValue.increment(1),
    });
  }

  // Add or update reaction
  Future<void> toggleReaction(
    String articleId,
    String reactionType,
  ) async {
    final User? currentUser = _auth.currentUser;
    if (currentUser == null) throw Exception('User not authenticated');

    final String userId = currentUser.uid;
    final DocumentReference reactionDoc = _newsCollection
        .doc(articleId)
        .collection('reactions')
        .doc(userId);

    final docSnapshot = await reactionDoc.get();

    if (docSnapshot.exists) {
      final existingReaction = Reaction.fromFirestore(
          docSnapshot as DocumentSnapshot<Map<String, dynamic>>);

      if (existingReaction.reactionType == reactionType) {
        // Remove reaction if same type is clicked again
        await reactionDoc.delete();
        await _updateReactionCount(
          articleId,
          existingReaction.reactionType,
          -1,
        );
      } else {
        // Change reaction type
        await reactionDoc.set(
          Reaction(
            userId: userId,
            reactionType: reactionType,
            timestamp: DateTime.now(),
          ).toFirestore(),
        );
        await _updateReactionCount(
          articleId,
          existingReaction.reactionType,
          -1,
        );
        await _updateReactionCount(articleId, reactionType, 1);
      }
    } else {
      // Add new reaction
      await reactionDoc.set(
        Reaction(
          userId: userId,
          reactionType: reactionType,
          timestamp: DateTime.now(),
        ).toFirestore(),
      );
      await _updateReactionCount(articleId, reactionType, 1);
    }
  }

  // Update reaction count
  Future<void> _updateReactionCount(
    String articleId,
    String reactionType,
    int change,
  ) async {
    await _newsCollection.doc(articleId).update({
      'reactionCounts.$reactionType': FieldValue.increment(change),
    });
  }

  // Get user reaction for an article
  Future<String?> getUserReaction(String articleId) async {
    final User? currentUser = _auth.currentUser;
    if (currentUser == null) return null;

    final docSnapshot = await _newsCollection
        .doc(articleId)
        .collection('reactions')
        .doc(currentUser.uid)
        .get();

    if (!docSnapshot.exists) return null;

    final reaction = Reaction.fromFirestore(docSnapshot);
    return reaction.reactionType;
  }

  // For admin functions
  Future<void> togglePinArticle(String articleId, bool pinned) async {
    await _newsCollection.doc(articleId).update({
      'pinned': pinned,
    });
  }

  Future<void> updateAdminComment(String articleId, String? comment) async {
    await _newsCollection.doc(articleId).update({
      'adminComment': comment,
    });
  }

  /// Create admin article (for admin panel integration)
  Future<void> createAdminArticle({
    required String title,
    required String content,
    required String excerpt,
    required String category,
    String? featuredImage,
    List<String>? tags,
    bool isBreaking = false,
    bool isFeatured = false,
  }) async {
    try {
      final adminUrl = 'admin://article/${DateTime.now().millisecondsSinceEpoch}';

      final article = NewsArticle(
        id: '', // Will be set by Firestore
        title: title,
        description: excerpt,
        url: adminUrl,
        source: 'Admin',
        publishedAt: DateTime.now(),
        imageUrl: featuredImage ?? '',
        views: 0,
        pinned: true, // Admin articles are always pinned
        adminComment: null,
        adminContent: content, // Store the full content
        reactionCounts: {},
        createdAt: DateTime.now(),
        category: category,
      );

      await _newsCollection.add(article.toFirestore());

      // Clear cache to force refresh
      await _cacheService.clear();

      _logger.info('Admin article created successfully');
    } catch (e) {
      _logger.error('Error creating admin article', error: e);
      rethrow;
    }
  }

  /// Get news articles from offline storage (SQLite)
  Future<List<NewsArticle>> _getNewsFromOfflineStorage({
    String? category,
    int limit = 15,
  }) async {
    try {
      final db = _offlineStorage.database;
      if (db == null) return [];

      String whereClause = '';
      List<dynamic> whereArgs = [];

      if (category != null && category.isNotEmpty && category != 'All') {
        whereClause = 'WHERE category = ?';
        whereArgs.add(category);
      }

      final List<Map<String, dynamic>> maps = await db.query(
        'news_articles',
        where: whereClause.isEmpty ? null : whereClause.replaceFirst('WHERE ', ''),
        whereArgs: whereArgs.isEmpty ? null : whereArgs,
        orderBy: 'pinned DESC, published_at DESC',
        limit: limit,
      );

      return maps.map((map) => _newsArticleFromMap(map)).toList();
    } catch (e) {
      _logger.error('Error getting news from offline storage', error: e);
      return [];
    }
  }

  /// Save news articles to offline storage (SQLite)
  Future<void> _saveNewsToOfflineStorage(List<NewsArticle> articles) async {
    try {
      final db = _offlineStorage.database;
      if (db == null) return;

      final batch = db.batch();

      for (final article in articles) {
        batch.insert(
          'news_articles',
          _newsArticleToMap(article),
          conflictAlgorithm: ConflictAlgorithm.replace,
        );
      }

      await batch.commit();
      _logger.debug('Saved ${articles.length} articles to offline storage');
    } catch (e) {
      _logger.error('Error saving news to offline storage', error: e);
    }
  }

  /// Convert NewsArticle to Map for SQLite storage
  Map<String, dynamic> _newsArticleToMap(NewsArticle article) {
    return {
      'id': article.id,
      'title': article.title,
      'description': article.description,
      'url': article.url,
      'source': article.source,
      'published_at': article.publishedAt.millisecondsSinceEpoch,
      'image_url': article.imageUrl,
      'views': article.views,
      'pinned': article.pinned ? 1 : 0,
      'category': article.category,
      'content': article.adminContent,
      'created_at': article.createdAt.millisecondsSinceEpoch,
      'updated_at': DateTime.now().millisecondsSinceEpoch,
      'sync_status': 'synced',
    };
  }

  /// Convert Map from SQLite to NewsArticle
  NewsArticle _newsArticleFromMap(Map<String, dynamic> map) {
    return NewsArticle(
      id: map['id'] ?? '',
      title: map['title'] ?? '',
      description: map['description'] ?? '',
      url: map['url'] ?? '',
      source: map['source'] ?? '',
      publishedAt: DateTime.fromMillisecondsSinceEpoch(map['published_at'] ?? 0),
      imageUrl: map['image_url'] ?? '',
      views: map['views'] ?? 0,
      pinned: (map['pinned'] ?? 0) == 1,
      adminComment: null,
      adminContent: map['content'],
      reactionCounts: {},
      createdAt: map['created_at'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['created_at'])
          : DateTime.now(),
      category: map['category'],
    );
  }

  /// Initialize offline storage for news
  Future<void> initializeOfflineStorage() async {
    await _enhancedCache.initialize();
  }

  /// Clear offline news data
  Future<void> clearOfflineData() async {
    try {
      final db = _offlineStorage.database;
      if (db != null) {
        await db.delete('news_articles');
      }
      await _enhancedCache.clear();
      _logger.info('Cleared offline news data');
    } catch (e) {
      _logger.error('Error clearing offline news data', error: e);
    }
  }

  /// Get offline data statistics
  Future<Map<String, dynamic>> getOfflineStats() async {
    try {
      final db = _offlineStorage.database;
      if (db == null) return {'articles': 0, 'categories': 0};

      final articleCount = Sqflite.firstIntValue(
        await db.rawQuery('SELECT COUNT(*) FROM news_articles')
      ) ?? 0;

      final categoryCount = Sqflite.firstIntValue(
        await db.rawQuery('SELECT COUNT(DISTINCT category) FROM news_articles WHERE category IS NOT NULL')
      ) ?? 0;

      return {
        'articles': articleCount,
        'categories': categoryCount,
        'cacheStats': _enhancedCache.getStats(),
      };
    } catch (e) {
      _logger.error('Error getting offline stats', error: e);
      return {'articles': 0, 'categories': 0};
    }
  }
}