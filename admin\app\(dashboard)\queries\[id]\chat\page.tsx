'use client'

import { useEffect, useState, use } from 'react'
import { useRouter } from 'next/navigation'
import { motion } from 'framer-motion'
import { ArrowLeft, MessageSquare, User, Clock, Tag } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { LoadingSpinner } from '@/components/ui/loading-spinner'
import { ChatInterface } from '@/components/chat/chat-interface'
import { useQueries } from '@/lib/hooks/use-queries'
import { formatDate } from '@/lib/utils'

interface QueryChatPageProps {
  params: Promise<{
    id: string
  }>
}

export default function QueryChatPage({ params }: QueryChatPageProps) {
  const router = useRouter()
  const { queries, isLoading } = useQueries()
  const [query, setQuery] = useState<any>(null)
  const resolvedParams = use(params)

  // Mock current admin user - in real app, get from auth context
  const currentAdmin = {
    id: 'admin_1',
    name: 'Admin User',
    avatar: undefined
  }

  useEffect(() => {
    if (resolvedParams.id) {
      // First try to find from actual queries data
      let foundQuery = null
      if (queries && queries.length > 0) {
        foundQuery = queries.find(q => q.id === resolvedParams.id)
      }

      // If not found, create a fallback query for static export
      if (!foundQuery) {
        foundQuery = {
          id: resolvedParams.id,
          subject: `Query ${resolvedParams.id}`,
          description: `This is a sample query for ${resolvedParams.id}`,
          category: 'general',
          priority: 'medium',
          status: 'open',
          userName: 'Sample User',
          userId: 'user_1',
          userEmail: '<EMAIL>',
          userPhone: '+1234567890',
          userAvatar: null,
          createdAt: new Date().toISOString(),
          tags: ['sample', resolvedParams.id],
          assignedTo: {
            name: 'Admin User',
            id: 'admin_1'
          }
        }
      }

      setQuery(foundQuery)
    }
  }, [queries, resolvedParams.id])

  const handleBack = () => {
    router.push('/queries')
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open':
        return 'error'
      case 'in-progress':
        return 'warning'
      case 'resolved':
        return 'success'
      case 'closed':
        return 'secondary'
      default:
        return 'secondary'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'low':
        return 'bg-green-100 text-green-800'
      case 'medium':
        return 'bg-yellow-100 text-yellow-800'
      case 'high':
        return 'bg-orange-100 text-orange-800'
      case 'urgent':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'technical':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
      case 'billing':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
      case 'account':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300'
      case 'general':
        return 'bg-muted text-muted-foreground'
      case 'complaint':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
      case 'feature-request':
        return 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-300'
      default:
        return 'bg-muted text-muted-foreground'
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-96">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  if (!query) {
    return (
      <div className="flex flex-col items-center justify-center h-96 space-y-4">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Query Not Found</h2>
          <p className="text-gray-600">The query you&apos;re looking for doesn&apos;t exist or has been removed.</p>
        </div>
        <Button onClick={handleBack} variant="outline">
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Queries
        </Button>
      </div>
    )
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="h-full flex flex-col"
    >
      {/* Page Header */}
      <div className="bg-white border-b px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button
              onClick={handleBack}
              variant="ghost"
              size="sm"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Queries
            </Button>

            <div className="h-6 w-px bg-gray-300" />

            <div>
              <h1 className="text-xl font-semibold text-gray-900">Query Chat</h1>
              <p className="text-sm text-gray-600">Communicate with the user about their query</p>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Badge variant={getStatusColor(query.status) as any}>
              {query.status}
            </Badge>
            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(query.priority)}`}>
              {query.priority}
            </span>
            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getCategoryColor(query.category)}`}>
              {query.category}
            </span>
          </div>
        </div>
      </div>

      {/* Query Info Sidebar and Chat */}
      <div className="flex-1 flex overflow-hidden">
        {/* Query Info Sidebar */}
        <div className="w-80 bg-gray-50 border-r overflow-y-auto">
          <div className="p-4 space-y-4">
            {/* Query Details */}
            <Card>
              <CardHeader>
                <CardTitle className="text-sm flex items-center space-x-2">
                  <MessageSquare className="w-4 h-4" />
                  <span>Query Details</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <h3 className="font-medium text-gray-900 mb-1">{query.subject}</h3>
                  <p className="text-sm text-gray-600 whitespace-pre-wrap">{query.description}</p>
                </div>

                <div className="flex items-center space-x-2 text-sm text-gray-500">
                  <Clock className="w-4 h-4" />
                  <span>Created {formatDate(query.createdAt)}</span>
                </div>

                {query.tags && query.tags.length > 0 && (
                  <div>
                    <div className="flex items-center space-x-2 mb-2">
                      <Tag className="w-4 h-4 text-gray-400" />
                      <span className="text-sm font-medium text-gray-700">Tags</span>
                    </div>
                    <div className="flex flex-wrap gap-1">
                      {query.tags.map((tag: string, index: number) => (
                        <Badge key={index} variant="secondary" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* User Info */}
            <Card>
              <CardHeader>
                <CardTitle className="text-sm flex items-center space-x-2">
                  <User className="w-4 h-4" />
                  <span>User Information</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                    {query.userAvatar ? (
                      <img src={query.userAvatar} alt={query.userName} className="w-10 h-10 rounded-full" />
                    ) : (
                      <User className="w-5 h-5 text-blue-600" />
                    )}
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">{query.userName}</p>
                    <p className="text-sm text-gray-500">ID: {query.userId}</p>
                  </div>
                </div>

                <div className="space-y-2 text-sm">
                  <div>
                    <span className="text-gray-500">Email:</span>
                    <span className="ml-2 text-gray-900">{query.userEmail}</span>
                  </div>
                  {query.userPhone && (
                    <div>
                      <span className="text-gray-500">Phone:</span>
                      <span className="ml-2 text-gray-900">{query.userPhone}</span>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Assignment Info */}
            {query.assignedTo && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Assigned To</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center space-x-2">
                    <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                      <User className="w-4 h-4 text-green-600" />
                    </div>
                    <span className="text-sm font-medium">{query.assignedTo.name}</span>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>

        {/* Chat Interface */}
        <div className="flex-1 flex flex-col">
          <ChatInterface
            roomId={resolvedParams.id}
            roomType="query"
            currentUserId={currentAdmin.id}
            currentUserName={currentAdmin.name}
            currentUserAvatar={currentAdmin.avatar}
            showHeader={false}
          />
        </div>
      </div>
    </motion.div>
  )
}
