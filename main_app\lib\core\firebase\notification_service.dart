import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:drive_on/core/utils/app_logger.dart';
import 'package:drive_on/core/utils/error_handler.dart';
import 'package:drive_on/core/firebase/firebase_service.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  static NotificationService get instance => _instance;

  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
  final FlutterLocalNotificationsPlugin _localNotifications = FlutterLocalNotificationsPlugin();
  final AppLogger _logger = AppLogger('NotificationService');

  // Stream controllers for handling notification events
  final StreamController<RemoteMessage> _onMessageStreamController = StreamController<RemoteMessage>.broadcast();
  final StreamController<RemoteMessage> _onMessageOpenedAppStreamController = StreamController<RemoteMessage>.broadcast();
  final StreamController<String> _onTokenRefreshStreamController = StreamController<String>.broadcast();

  // Expose streams for listening to notification events
  Stream<RemoteMessage> get onMessage => _onMessageStreamController.stream;
  Stream<RemoteMessage> get onMessageOpenedApp => _onMessageOpenedAppStreamController.stream;
  Stream<String> get onTokenRefresh => _onTokenRefreshStreamController.stream;

  // Notification channels
  static const String _mainChannel = 'main_channel';
  static const String _chatChannel = 'chat_channel';
  static const String _newsChannel = 'news_channel';
  static const String _verificationChannel = 'verification_channel';
  static const String _systemChannel = 'system_channel';
  static const String _messageChannel = 'message_channel';

  // Android notification channel IDs
  AndroidNotificationChannel? _mainAndroidChannel;
  AndroidNotificationChannel? _chatAndroidChannel;
  AndroidNotificationChannel? _newsAndroidChannel;
  AndroidNotificationChannel? _verificationAndroidChannel;
  AndroidNotificationChannel? _systemAndroidChannel;
  AndroidNotificationChannel? _messageAndroidChannel;

  // Private constructor
  NotificationService._internal();

  /// Initialize the notification service
  Future<void> initialize() async {
    try {
      _logger.info('Initializing notification service');

      // Request permissions for both platforms
      if (Platform.isIOS) {
        await _requestIOSPermissions();
      } else if (Platform.isAndroid) {
        await _requestAndroidPermissions();
      }

      // Set up local notifications
      await _setupLocalNotifications();

      // Configure FCM handlers
      await _configureFCMHandlers();

      // Get FCM token and save it
      await getToken();

      _logger.info('Notification service initialized successfully');
    } catch (e, stackTrace) {
      ErrorHandler.reportHandledException(
        e,
        stackTrace,
        context: 'Notification service initialization',
        severity: ErrorSeverity.medium,
      );
      _logger.error('Error initializing notification service', error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Request notification permissions for Android
  Future<void> _requestAndroidPermissions() async {
    try {
      // For Android 13+ (API 33+), request POST_NOTIFICATIONS permission
      NotificationSettings settings = await _firebaseMessaging.requestPermission(
        alert: true,
        badge: true,
        sound: true,
        provisional: false,
      );

      _logger.info('Android notification permission status: ${settings.authorizationStatus}');

      // Additional Android-specific permission checks can be added here
      // The permission_handler package can be used for more granular control

    } catch (e, stackTrace) {
      _logger.error('Error requesting Android notification permissions',
          error: e, stackTrace: stackTrace);
      ErrorHandler.reportHandledException(
        e,
        stackTrace,
        context: 'Android notification permissions',
        severity: ErrorSeverity.low,
      );
    }
  }

  /// Request notification permissions for iOS
  Future<void> _requestIOSPermissions() async {
    try {
      NotificationSettings settings = await _firebaseMessaging.requestPermission(
        alert: true,
        badge: true,
        sound: true,
        provisional: false,
      );

      _logger.info('iOS notification permission status: ${settings.authorizationStatus}');

      // Request critical alerts permission (optional)
      await _firebaseMessaging.requestPermission(
        criticalAlert: true,
        provisional: false,
      );

      // Configure app badge settings
      await _firebaseMessaging.setForegroundNotificationPresentationOptions(
        alert: true,
        badge: true,
        sound: true,
      );
    } catch (e, stackTrace) {
      _logger.error('Error requesting iOS notification permissions',
          error: e, stackTrace: stackTrace);
      ErrorHandler.reportHandledException(
        e,
        stackTrace,
        context: 'iOS notification permissions',
        severity: ErrorSeverity.low,
      );
    }
  }

  /// Set up local notifications plugin
  Future<void> _setupLocalNotifications() async {
    try {
      // Setup Android notification channels
      if (Platform.isAndroid) {
        _mainAndroidChannel = const AndroidNotificationChannel(
          _mainChannel,
          'Main Notifications',
          description: 'General notifications from Drive-On',
          importance: Importance.high,
        );

        _chatAndroidChannel = const AndroidNotificationChannel(
          _chatChannel,
          'Chat Notifications',
          description: 'Chat and message notifications from Drive-On',
          importance: Importance.high,
        );

        _newsAndroidChannel = const AndroidNotificationChannel(
          _newsChannel,
          'News Notifications',
          description: 'News and updates from Drive-On',
          importance: Importance.defaultImportance,
        );

        _verificationAndroidChannel = const AndroidNotificationChannel(
          _verificationChannel,
          'Verification Notifications',
          description: 'Driver verification and document status updates',
          importance: Importance.high,
          enableVibration: true,
          playSound: true,
        );

        _systemAndroidChannel = const AndroidNotificationChannel(
          _systemChannel,
          'System Notifications',
          description: 'System alerts and important updates',
          importance: Importance.max,
          enableVibration: true,
          playSound: true,
        );

        _messageAndroidChannel = const AndroidNotificationChannel(
          _messageChannel,
          'Message Notifications',
          description: 'New messages in chats, forums, and queries',
          importance: Importance.high,
          enableVibration: true,
          playSound: true,
        );

        // Create notification channels individually
        final androidPlugin = _localNotifications
            .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>();

        if (androidPlugin != null) {
          await androidPlugin.createNotificationChannel(_mainAndroidChannel!);
          await androidPlugin.createNotificationChannel(_chatAndroidChannel!);
          await androidPlugin.createNotificationChannel(_newsAndroidChannel!);
          await androidPlugin.createNotificationChannel(_verificationAndroidChannel!);
          await androidPlugin.createNotificationChannel(_systemAndroidChannel!);
          await androidPlugin.createNotificationChannel(_messageAndroidChannel!);
          _logger.debug('Android notification channels created successfully');
        }
      }

      // Initialize local notifications
      const AndroidInitializationSettings androidSettings =
          AndroidInitializationSettings('@mipmap/ic_launcher');

      final DarwinInitializationSettings iosSettings = DarwinInitializationSettings(
        requestAlertPermission: false,
        requestBadgePermission: false,
        requestSoundPermission: false,
        onDidReceiveLocalNotification: _onDidReceiveLocalNotification,
      );

      final InitializationSettings initSettings = InitializationSettings(
        android: androidSettings,
        iOS: iosSettings,
      );

      await _localNotifications.initialize(
        initSettings,
        onDidReceiveNotificationResponse: _onDidReceiveNotificationResponse,
      );

      _logger.info('Local notifications setup completed');
    } catch (e, stackTrace) {
      _logger.error('Error setting up local notifications',
          error: e, stackTrace: stackTrace);
      ErrorHandler.reportHandledException(
        e,
        stackTrace,
        context: 'Local notifications setup',
        severity: ErrorSeverity.medium,
      );
    }
  }

  /// Configure handlers for FCM messages
  Future<void> _configureFCMHandlers() async {
    try {
      // Handle notifications when app is in foreground
      FirebaseMessaging.onMessage.listen((RemoteMessage message) {
        _logger.debug('Received foreground message: ${message.messageId}');
        _onMessageStreamController.add(message);
        _showLocalNotification(message);
      });

      // Handle notification click when app is in background or terminated
      FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
        _logger.debug('Notification opened from background: ${message.messageId}');
        _onMessageOpenedAppStreamController.add(message);
      });

      // Handle token refresh
      FirebaseMessaging.instance.onTokenRefresh.listen((String token) {
        _logger.debug('FCM token refreshed');
        _onTokenRefreshStreamController.add(token);
        _updateTokenInDatabase(token);
      });

      // Check if app was opened from a notification when in terminated state
      RemoteMessage? initialMessage = await FirebaseMessaging.instance.getInitialMessage();
      if (initialMessage != null) {
        _logger.debug('App opened from terminated state via notification: ${initialMessage.messageId}');
        _onMessageOpenedAppStreamController.add(initialMessage);
      }

      _logger.info('FCM handlers configured successfully');
    } catch (e, stackTrace) {
      _logger.error('Error configuring FCM handlers',
          error: e, stackTrace: stackTrace);
      ErrorHandler.reportHandledException(
        e,
        stackTrace,
        context: 'FCM handlers configuration',
        severity: ErrorSeverity.medium,
      );
    }
  }

  /// Display local notification from FCM message
  void _showLocalNotification(RemoteMessage message) {
    try {
      final notification = message.notification;
      final android = message.notification?.android;
      final data = message.data;

      if (notification == null) {
        _logger.debug('No notification payload, skipping local notification');
        return;
      }

      // Determine the channel based on notification type
      String channelId = _mainChannel;
      if (data.containsKey('type')) {
        switch (data['type']) {
          case 'chat':
          case 'message':
            channelId = _messageChannel;
            break;
          case 'news':
            channelId = _newsChannel;
            break;
          case 'verification':
          case 'driver_verification':
          case 'document_verification':
            channelId = _verificationChannel;
            break;
          case 'system':
          case 'urgent':
            channelId = _systemChannel;
            break;
          case 'forum_message':
          case 'query_message':
            channelId = _messageChannel;
            break;
          default:
            channelId = _mainChannel;
        }
      }

      // Create the notification details
      final androidDetails = AndroidNotificationDetails(
        channelId,
        'Channel Name',
        channelDescription: 'Channel Description',
        importance: Importance.high,
        priority: Priority.high,
        icon: android?.smallIcon ?? '@mipmap/ic_launcher',
      );

      const iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      final notificationDetails = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      // Show the notification
      _localNotifications.show(
        notification.hashCode,
        notification.title,
        notification.body,
        notificationDetails,
        payload: json.encode(data),
      );

      _logger.debug('Local notification displayed: ${notification.title}');
    } catch (e, stackTrace) {
      _logger.error('Error showing local notification',
          error: e, stackTrace: stackTrace);
      ErrorHandler.reportHandledException(
        e,
        stackTrace,
        context: 'Show local notification',
        severity: ErrorSeverity.low,
      );
    }
  }

  /// Handler for iOS local notification (deprecated but required for older iOS)
  void _onDidReceiveLocalNotification(int id, String? title, String? body, String? payload) {
    _logger.debug('Received iOS local notification: $title');
    // No action needed as this is only for older iOS versions
  }

  /// Handler for notification response (when user taps on notification)
  void _onDidReceiveNotificationResponse(NotificationResponse response) {
    try {
      _logger.debug('User tapped on notification: ${response.id}');

      if (response.payload != null) {
        final data = json.decode(response.payload!) as Map<String, dynamic>;
        _handleNotificationPayload(data);
      }
    } catch (e, stackTrace) {
      _logger.error('Error handling notification response',
          error: e, stackTrace: stackTrace);
      ErrorHandler.reportHandledException(
        e,
        stackTrace,
        context: 'Handle notification response',
        severity: ErrorSeverity.low,
      );
    }
  }

  /// Handle notification payload data for navigation
  void _handleNotificationPayload(Map<String, dynamic> data) {
    // This will be implemented by the notification handler in main.dart
    // We're just logging the payload here
    _logger.debug('Notification payload: $data');
  }

  /// Get FCM token
  Future<String?> getToken() async {
    try {
      final token = await _firebaseMessaging.getToken();
      _logger.info('FCM token obtained');

      if (token != null) {
        await _updateTokenInDatabase(token);
      }

      return token;
    } catch (e, stackTrace) {
      _logger.error('Error getting FCM token',
          error: e, stackTrace: stackTrace);
      ErrorHandler.reportHandledException(
        e,
        stackTrace,
        context: 'Get FCM token',
        severity: ErrorSeverity.low,
      );
      return null;
    }
  }

  /// Delete FCM token
  Future<void> deleteToken() async {
    try {
      await _firebaseMessaging.deleteToken();
      _logger.info('FCM token deleted');
    } catch (e, stackTrace) {
      _logger.error('Error deleting FCM token',
          error: e, stackTrace: stackTrace);
      ErrorHandler.reportHandledException(
        e,
        stackTrace,
        context: 'Delete FCM token',
        severity: ErrorSeverity.low,
      );
    }
  }

  /// Update FCM token in database
  Future<void> _updateTokenInDatabase(String token) async {
    try {
      // Only update token if user is logged in
      if (FirebaseService.isUserLoggedIn()) {
        final user = FirebaseService.getCurrentUser();
        if (user != null) {
          await FirebaseService.firestore
              .collection('users')
              .doc(user.uid)
              .set({
                'fcmTokens': FieldValue.arrayUnion([token]),
                'lastTokenUpdate': FieldValue.serverTimestamp(),
              }, SetOptions(merge: true));
          _logger.info('FCM token updated in database');
        }
      }
    } catch (e, stackTrace) {
      _logger.error('Error updating FCM token in database',
          error: e, stackTrace: stackTrace);
      ErrorHandler.reportHandledException(
        e,
        stackTrace,
        context: 'Update FCM token in database',
        severity: ErrorSeverity.low,
      );
    }
  }

  /// Subscribe to a topic
  Future<void> subscribeToTopic(String topic) async {
    try {
      await _firebaseMessaging.subscribeToTopic(topic);
      _logger.info('Subscribed to topic: $topic');
    } catch (e, stackTrace) {
      _logger.error('Error subscribing to topic: $topic',
          error: e, stackTrace: stackTrace);
      ErrorHandler.reportHandledException(
        e,
        stackTrace,
        context: 'Subscribe to topic',
        severity: ErrorSeverity.low,
      );
    }
  }

  /// Unsubscribe from a topic
  Future<void> unsubscribeFromTopic(String topic) async {
    try {
      await _firebaseMessaging.unsubscribeFromTopic(topic);
      _logger.info('Unsubscribed from topic: $topic');
    } catch (e, stackTrace) {
      _logger.error('Error unsubscribing from topic: $topic',
          error: e, stackTrace: stackTrace);
      ErrorHandler.reportHandledException(
        e,
        stackTrace,
        context: 'Unsubscribe from topic',
        severity: ErrorSeverity.low,
      );
    }
  }

  /// Check notification permissions
  Future<bool> checkPermission() async {
    try {
      final settings = await _firebaseMessaging.getNotificationSettings();
      return settings.authorizationStatus == AuthorizationStatus.authorized;
    } catch (e, stackTrace) {
      _logger.error('Error checking notification permission',
          error: e, stackTrace: stackTrace);
      ErrorHandler.reportHandledException(
        e,
        stackTrace,
        context: 'Check notification permission',
        severity: ErrorSeverity.low,
      );
      return false;
    }
  }

  /// Send a local test notification
  Future<void> sendTestNotification({
    String title = 'Test Notification',
    String body = 'This is a test notification from Drive-On',
    Map<String, dynamic>? payload,
  }) async {
    try {
      const androidDetails = AndroidNotificationDetails(
        _mainChannel,
        'Test Notifications',
        channelDescription: 'Channel for test notifications',
        importance: Importance.high,
        priority: Priority.high,
      );

      const iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      const details = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      await _localNotifications.show(
        0,
        title,
        body,
        details,
        payload: payload != null ? json.encode(payload) : null,
      );

      _logger.info('Test notification sent');
    } catch (e, stackTrace) {
      _logger.error('Error sending test notification',
          error: e, stackTrace: stackTrace);
      ErrorHandler.reportHandledException(
        e,
        stackTrace,
        context: 'Send test notification',
        severity: ErrorSeverity.low,
      );
    }
  }

  /// Send a message notification
  Future<void> sendMessageNotification({
    required String title,
    required String body,
    required String messageType, // 'forum', 'query', 'chat'
    String? senderId,
    String? senderName,
    String? roomId,
    Map<String, dynamic>? additionalData,
  }) async {
    try {
      final payload = {
        'type': 'message',
        'messageType': messageType,
        'senderId': senderId,
        'senderName': senderName,
        'roomId': roomId,
        'screen': messageType == 'forum' ? 'forum_detail' : 'query_detail',
        'params': roomId,
        ...?additionalData,
      };

      const androidDetails = AndroidNotificationDetails(
        _messageChannel,
        'Message Notifications',
        channelDescription: 'New messages in chats, forums, and queries',
        importance: Importance.high,
        priority: Priority.high,
        enableVibration: true,
        playSound: true,
      );

      const iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
        sound: 'default',
      );

      const details = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      await _localNotifications.show(
        DateTime.now().millisecondsSinceEpoch.remainder(100000),
        title,
        body,
        details,
        payload: json.encode(payload),
      );

      _logger.info('Message notification sent: $title');
    } catch (e, stackTrace) {
      _logger.error('Error sending message notification',
          error: e, stackTrace: stackTrace);
      ErrorHandler.reportHandledException(
        e,
        stackTrace,
        context: 'Send message notification',
        severity: ErrorSeverity.medium,
      );
    }
  }

  /// Send a verification notification
  Future<void> sendVerificationNotification({
    required String title,
    required String body,
    required String verificationType, // 'driver', 'document', 'license'
    required String status, // 'approved', 'rejected', 'pending'
    String? driverId,
    Map<String, dynamic>? additionalData,
  }) async {
    try {
      final payload = {
        'type': 'verification',
        'verificationType': verificationType,
        'status': status,
        'driverId': driverId,
        'screen': 'profile',
        ...?additionalData,
      };

      const androidDetails = AndroidNotificationDetails(
        _verificationChannel,
        'Verification Notifications',
        channelDescription: 'Driver verification and document status updates',
        importance: Importance.high,
        priority: Priority.high,
        enableVibration: true,
        playSound: true,
      );

      const iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
        sound: 'default',
      );

      const details = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      await _localNotifications.show(
        DateTime.now().millisecondsSinceEpoch.remainder(100000),
        title,
        body,
        details,
        payload: json.encode(payload),
      );

      _logger.info('Verification notification sent: $title');
    } catch (e, stackTrace) {
      _logger.error('Error sending verification notification',
          error: e, stackTrace: stackTrace);
      ErrorHandler.reportHandledException(
        e,
        stackTrace,
        context: 'Send verification notification',
        severity: ErrorSeverity.medium,
      );
    }
  }

  /// Send a system notification
  Future<void> sendSystemNotification({
    required String title,
    required String body,
    required String systemType, // 'urgent', 'maintenance', 'update'
    String? actionUrl,
    Map<String, dynamic>? additionalData,
  }) async {
    try {
      final payload = {
        'type': 'system',
        'systemType': systemType,
        'actionUrl': actionUrl,
        'screen': 'home',
        ...?additionalData,
      };

      const androidDetails = AndroidNotificationDetails(
        _systemChannel,
        'System Notifications',
        channelDescription: 'System alerts and important updates',
        importance: Importance.max,
        priority: Priority.max,
        enableVibration: true,
        playSound: true,
      );

      const iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
        sound: 'default',
      );

      const details = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      await _localNotifications.show(
        DateTime.now().millisecondsSinceEpoch.remainder(100000),
        title,
        body,
        details,
        payload: json.encode(payload),
      );

      _logger.info('System notification sent: $title');
    } catch (e, stackTrace) {
      _logger.error('Error sending system notification',
          error: e, stackTrace: stackTrace);
      ErrorHandler.reportHandledException(
        e,
        stackTrace,
        context: 'Send system notification',
        severity: ErrorSeverity.medium,
      );
    }
  }

  /// Clean up resources
  void dispose() {
    _onMessageStreamController.close();
    _onMessageOpenedAppStreamController.close();
    _onTokenRefreshStreamController.close();
    _logger.info('Notification service disposed');
  }
}