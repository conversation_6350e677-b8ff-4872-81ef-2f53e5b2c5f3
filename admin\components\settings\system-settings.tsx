'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import {
  Monitor,
  Zap,
  Shield,
  Clock,
  HardDrive,
  Activity,
  AlertTriangle,
  Settings,
  Save,
  RefreshCw,
  Power,
  Pause,
} from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { useSettings } from '@/lib/hooks/use-settings'

const logLevels = [
  { value: 'error', label: 'Error Only' },
  { value: 'warn', label: 'Warning & Error' },
  { value: 'info', label: 'Info, Warning & Error' },
  { value: 'debug', label: 'All Logs (Debug)' },
]

export function SystemSettings() {
  const { settings, updateSystemSettings } = useSettings()
  const [isLoading, setIsLoading] = useState(false)
  const [formData, setFormData] = useState(settings.system)
  const [systemStats] = useState({
    cpuUsage: 45,
    memoryUsage: 62,
    diskUsage: 78,
    uptime: '15 days, 4 hours',
    activeConnections: 1247,
    requestsPerMinute: 89
  })

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleSave = async () => {
    setIsLoading(true)
    try {
      await updateSystemSettings(formData)
      // Show success message
    } catch (error) {
      console.error('Error saving system settings:', error)
      // Show error message
    } finally {
      setIsLoading(false)
    }
  }

  const toggleMaintenanceMode = () => {
    handleInputChange('maintenanceMode', !formData.maintenanceMode)
  }

  const clearCache = async () => {
    // Simulate cache clearing
    await new Promise(resolve => setTimeout(resolve, 2000))
    // Show success message
  }

  const restartSystem = async () => {
    if (window.confirm('Are you sure you want to restart the system? This will cause temporary downtime.')) {
      // Simulate system restart
      await new Promise(resolve => setTimeout(resolve, 3000))
      // Show success message
    }
  }

  return (
    <div className="space-y-6">
      {/* System Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Monitor className="w-5 h-5" />
            <span>System Status</span>
          </CardTitle>
          <CardDescription>
            Current system performance and resource usage
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <Label>CPU Usage</Label>
                <span className="text-sm font-medium">{systemStats.cpuUsage}%</span>
              </div>
              <Progress value={systemStats.cpuUsage} className="w-full" />
            </div>

            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <Label>Memory Usage</Label>
                <span className="text-sm font-medium">{systemStats.memoryUsage}%</span>
              </div>
              <Progress value={systemStats.memoryUsage} className="w-full" />
            </div>

            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <Label>Disk Usage</Label>
                <span className="text-sm font-medium">{systemStats.diskUsage}%</span>
              </div>
              <Progress value={systemStats.diskUsage} className="w-full" />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
              <div className="w-12 h-12 bg-green-100 dark:bg-green-800 rounded-full flex items-center justify-center mx-auto mb-3">
                <Clock className="w-6 h-6 text-green-600 dark:text-green-400" />
              </div>
              <h3 className="font-medium text-gray-900 dark:text-white">Uptime</h3>
              <p className="text-sm text-green-600 dark:text-green-400">{systemStats.uptime}</p>
            </div>

            <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <div className="w-12 h-12 bg-blue-100 dark:bg-blue-800 rounded-full flex items-center justify-center mx-auto mb-3">
                <Activity className="w-6 h-6 text-blue-600 dark:text-blue-400" />
              </div>
              <h3 className="font-medium text-gray-900 dark:text-white">Active Connections</h3>
              <p className="text-sm text-blue-600 dark:text-blue-400">{systemStats.activeConnections}</p>
            </div>

            <div className="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
              <div className="w-12 h-12 bg-purple-100 dark:bg-purple-800 rounded-full flex items-center justify-center mx-auto mb-3">
                <Zap className="w-6 h-6 text-purple-600 dark:text-purple-400" />
              </div>
              <h3 className="font-medium text-gray-900 dark:text-white">Requests/Min</h3>
              <p className="text-sm text-purple-600 dark:text-purple-400">{systemStats.requestsPerMinute}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Maintenance Mode */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Settings className="w-5 h-5" />
            <span>Maintenance Mode</span>
          </CardTitle>
          <CardDescription>
            Enable maintenance mode to perform system updates
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-center justify-between p-4 border rounded-lg dark:border-gray-700">
            <div className="flex items-center space-x-4">
              <div className={`w-12 h-12 rounded-full flex items-center justify-center ${
                formData.maintenanceMode 
                  ? 'bg-orange-100 dark:bg-orange-900/20' 
                  : 'bg-green-100 dark:bg-green-900/20'
              }`}>
                {formData.maintenanceMode ? (
                  <Pause className="w-6 h-6 text-orange-600 dark:text-orange-400" />
                ) : (
                  <Power className="w-6 h-6 text-green-600 dark:text-green-400" />
                )}
              </div>
              <div>
                <h3 className="font-medium text-gray-900 dark:text-white">
                  {formData.maintenanceMode ? 'Maintenance Mode Active' : 'System Online'}
                </h3>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {formData.maintenanceMode 
                    ? 'Users will see a maintenance page' 
                    : 'All services are operational'}
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <Badge variant={formData.maintenanceMode ? 'warning' : 'success'}>
                {formData.maintenanceMode ? 'Maintenance' : 'Online'}
              </Badge>
              <Switch
                checked={formData.maintenanceMode}
                onCheckedChange={toggleMaintenanceMode}
              />
            </div>
          </div>

          {formData.maintenanceMode && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              className="p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg border border-orange-200 dark:border-orange-800"
            >
              <div className="flex items-center space-x-2 mb-2">
                <AlertTriangle className="w-4 h-4 text-orange-600 dark:text-orange-400" />
                <span className="font-medium text-orange-800 dark:text-orange-200">
                  Maintenance Mode Active
                </span>
              </div>
              <p className="text-sm text-orange-700 dark:text-orange-300">
                The system is currently in maintenance mode. Users will see a maintenance page and cannot access the application.
              </p>
            </motion.div>
          )}
        </CardContent>
      </Card>

      {/* Performance Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Zap className="w-5 h-5" />
            <span>Performance Settings</span>
          </CardTitle>
          <CardDescription>
            Configure system performance and optimization settings
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label>Enable Caching</Label>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Cache responses for better performance
                </p>
              </div>
              <Switch
                checked={formData.cacheEnabled}
                onCheckedChange={(checked) => handleInputChange('cacheEnabled', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label>Enable Compression</Label>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Compress responses to reduce bandwidth
                </p>
              </div>
              <Switch
                checked={formData.compressionEnabled}
                onCheckedChange={(checked) => handleInputChange('compressionEnabled', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label>Rate Limiting</Label>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Limit requests per user to prevent abuse
                </p>
              </div>
              <Switch
                checked={formData.rateLimiting}
                onCheckedChange={(checked) => handleInputChange('rateLimiting', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label>Debug Mode</Label>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Enable detailed error reporting
                </p>
              </div>
              <Switch
                checked={formData.debugMode}
                onCheckedChange={(checked) => handleInputChange('debugMode', checked)}
              />
            </div>
          </div>

          {formData.rateLimiting && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              className="space-y-4"
            >
              <div className="space-y-2">
                <Label htmlFor="maxRequests">Max Requests per Minute</Label>
                <Input
                  id="maxRequests"
                  type="number"
                  value={formData.maxRequestsPerMinute}
                  onChange={(e) => handleInputChange('maxRequestsPerMinute', parseInt(e.target.value))}
                  min="10"
                  max="1000"
                />
              </div>
            </motion.div>
          )}

          <div className="space-y-2">
            <Label htmlFor="sessionDuration">Session Duration (hours)</Label>
            <Input
              id="sessionDuration"
              type="number"
              value={formData.sessionDuration}
              onChange={(e) => handleInputChange('sessionDuration', parseInt(e.target.value))}
              min="1"
              max="168"
            />
          </div>
        </CardContent>
      </Card>

      {/* Logging Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <HardDrive className="w-5 h-5" />
            <span>Logging Settings</span>
          </CardTitle>
          <CardDescription>
            Configure system logging and monitoring
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-2">
            <Label htmlFor="logLevel">Log Level</Label>
            <select
              id="logLevel"
              value={formData.logLevel}
              onChange={(e) => handleInputChange('logLevel', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              {logLevels.map((level) => (
                <option key={level.value} value={level.value}>
                  {level.label}
                </option>
              ))}
            </select>
          </div>

          <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
            <div className="flex items-center space-x-2 mb-2">
              <HardDrive className="w-4 h-4 text-blue-600 dark:text-blue-400" />
              <span className="font-medium text-blue-800 dark:text-blue-200">
                Log Storage
              </span>
            </div>
            <p className="text-sm text-blue-700 dark:text-blue-300">
              Logs are automatically rotated daily and kept for 30 days. Current log size: 2.4 GB
            </p>
          </div>
        </CardContent>
      </Card>

      {/* System Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Shield className="w-5 h-5" />
            <span>System Actions</span>
          </CardTitle>
          <CardDescription>
            Perform system maintenance and administrative tasks
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button
              variant="outline"
              onClick={clearCache}
              className="h-auto p-4 flex flex-col items-center space-y-2"
            >
              <HardDrive className="w-6 h-6" />
              <span className="font-medium">Clear Cache</span>
              <span className="text-xs text-gray-500 dark:text-gray-400">Clear system cache</span>
            </Button>

            <Button
              variant="outline"
              onClick={restartSystem}
              className="h-auto p-4 flex flex-col items-center space-y-2"
            >
              <RefreshCw className="w-6 h-6" />
              <span className="font-medium">Restart System</span>
              <span className="text-xs text-gray-500 dark:text-gray-400">Restart all services</span>
            </Button>

            <Button
              variant="outline"
              className="h-auto p-4 flex flex-col items-center space-y-2"
            >
              <Activity className="w-6 h-6" />
              <span className="font-medium">View Logs</span>
              <span className="text-xs text-gray-500 dark:text-gray-400">Access system logs</span>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Save Button */}
      <div className="flex justify-end space-x-3">
        <Button
          variant="outline"
          onClick={() => setFormData(settings.system)}
        >
          Reset Changes
        </Button>
        <Button
          onClick={handleSave}
          disabled={isLoading}
          className="gradient-primary"
        >
          {isLoading ? (
            <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
          ) : (
            <Save className="w-4 h-4 mr-2" />
          )}
          {isLoading ? 'Saving...' : 'Save Changes'}
        </Button>
      </div>
    </div>
  )
}
