import 'dart:io';
import 'dart:math';
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:record/record.dart';
import 'package:path_provider/path_provider.dart';
import 'package:intl/intl.dart';
import '../../../core/core.dart';

class VoiceRecorder extends StatefulWidget {
  final Function(File, int) onStop;
  final VoidCallback onCancel;

  const VoiceRecorder({
    super.key,
    required this.onStop,
    required this.onCancel,
  });

  @override
  State<VoiceRecorder> createState() => _VoiceRecorderState();
}

class _VoiceRecorderState extends State<VoiceRecorder> {
  final _audioRecorder = AudioRecorder();
  Timer? _timer;
  int _recordDuration = 0;
  bool _isRecording = false;
  late String _filePath;
  List<double> _waveformPoints = [];
  final Random _random = Random();

  @override
  void initState() {
    super.initState();
    _startRecording();
    _generateWaveform();
  }

  @override
  void dispose() {
    _timer?.cancel();
    _stopRecording();
    super.dispose();
  }

  Future<void> _startRecording() async {
    try {
      final tempDir = await getTemporaryDirectory();
      final formattedDate = DateFormat('yyyyMMdd_HHmmss').format(DateTime.now());
      // Save as .m4a so it matches upload container/metadata
      _filePath = '${tempDir.path}/voice_note_$formattedDate.m4a';

      if (await _audioRecorder.hasPermission()) {
        await _audioRecorder.start(
          const RecordConfig(
            encoder: AudioEncoder.aacLc,
            bitRate: 128000,
            sampleRate: 44100,
          ),
          path: _filePath,
        );

        setState(() {
          _isRecording = true;
          _recordDuration = 0;
        });

        _timer = Timer.periodic(const Duration(milliseconds: 100), (Timer t) {
          setState(() {
            _recordDuration += 100;
            // Generate new waveform points periodically for animation
            if (_recordDuration % 500 == 0) {
              _generateWaveform();
            }
          });
        });
      } else {
        if (!mounted) return;
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Microphone permission is required to record voice notes'),
            backgroundColor: Colors.red,
          ),
        );
        widget.onCancel();
      }
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error starting recording: $e'),
          backgroundColor: Colors.red,
        ),
      );
      widget.onCancel();
    }
  }

  Future<void> _stopRecording() async {
    _timer?.cancel();
    if (_isRecording) {
      try {
        await _audioRecorder.stop();
        setState(() {
          _isRecording = false;
        });
      } catch (e) {
        // Ignore errors on stop
      }
    }
  }

  Future<void> _completeRecording() async {
    await _stopRecording();

    // Only complete if we've recorded for at least 1 second
    if (_recordDuration >= 1000) {
      final file = File(_filePath);
      try {
        if (await file.exists()) {
          // Verify file is readable and has content
          final fileSize = await file.length();
          if (fileSize > 0) {
            if (kDebugMode) {
              print('Voice file exists and has size: $fileSize bytes at ${file.absolute.path}');
            }
            widget.onStop(file, _recordDuration);
          } else {
            if (kDebugMode) {
              print('Voice file exists but is empty: ${file.absolute.path}');
            }
            _showErrorAndCancel('Recording failed: Empty file');
          }
        } else {
          if (kDebugMode) {
            print('Voice file does not exist at: ${file.absolute.path}');
          }
          _showErrorAndCancel('Recording failed: File not created');
        }
      } catch (e) {
        if (kDebugMode) {
          print('Error checking voice file: $e');
        }
        _showErrorAndCancel('Recording error: $e');
      }
    } else {
      _showErrorAndCancel('Recording too short');
    }
  }

  void _showErrorAndCancel(String message) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        duration: const Duration(seconds: 2),
        backgroundColor: Colors.red,
      ),
    );
    widget.onCancel();
  }

  Future<void> _cancelRecording() async {
    await _stopRecording();

    // Delete the file if it exists
    try {
      final file = File(_filePath);
      if (await file.exists()) {
        await file.delete();
      }
    } catch (e) {
      // Ignore errors during cancellation
    }

    widget.onCancel();
  }

  // Format milliseconds to mm:ss format
  String _formatDuration(int milliseconds) {
    final seconds = (milliseconds / 1000).floor();
    final minutes = (seconds / 60).floor();
    final remainingSeconds = seconds % 60;
    return '$minutes:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  // Generate random waveform for visualization
  void _generateWaveform() {
    final newPoints = List<double>.generate(
      15,
      (index) => 0.2 + _random.nextDouble() * 0.6
    );

    setState(() {
      _waveformPoints = newPoints;
    });
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: 16,
        vertical: 8,
      ),
      child: Row(
        children: [
          // Waveform visualization (simplified with animated dots)
          Expanded(
            child: Container(
              height: 40,
              decoration: BoxDecoration(
                color: isDarkMode ? Colors.grey[800] : Colors.grey[200],
                borderRadius: BorderRadius.circular(20),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Row(
                children: [
                  const Icon(
                    Icons.mic,
                    color: Colors.red,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    _formatDuration(_recordDuration),
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildWaveform(),
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(width: 8),

          // Cancel button
          IconButton(
            icon: const Icon(Icons.close),
            onPressed: _cancelRecording,
            color: Colors.red,
          ),

          // Complete button
          IconButton(
            icon: const Icon(Icons.send),
            onPressed: _completeRecording,
            color: AppColors.primaryYellow,
          ),
        ],
      ),
    );
  }

  Widget _buildWaveform() {
    // Use existing _waveformPoints for the waveform visualization
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: List.generate(_waveformPoints.length, (index) {
        // Calculate dot height based on waveform point and recording time
        const baseHeight = 5.0;
        const maxAdditionalHeight = 15.0;

        // Create animation effect based on recording time
        final time = _recordDuration / 500; // Changes over time
        final sineValue = (0.5 + 0.5 * sin(time + index)) * maxAdditionalHeight;

        // Use the stored waveform point as a base amplitude
        final amplitude = _waveformPoints[index];

        // Combine both for dynamic effect
        final height = baseHeight + (sineValue * amplitude);

        return AnimatedContainer(
          duration: const Duration(milliseconds: 100),
          width: 4,
          height: height,
          decoration: BoxDecoration(
            color: Colors.red.withOpacity(amplitude),
            borderRadius: BorderRadius.circular(2),
          ),
        );
      }),
    );
  }

  // Simple sine function
  double sin(double x) {
    // Approximation of sine function for visualization
    // Not mathematically precise, but good enough for UI animation
    const piApprox = 3.14159;
    final xNormalized = x % (2 * piApprox);

    if (xNormalized < piApprox / 2) {
      return xNormalized / (piApprox / 2);
    } else if (xNormalized < piApprox * 3 / 2) {
      return 1 - ((xNormalized - piApprox / 2) / piApprox);
    } else {
      return -1 + ((xNormalized - piApprox * 3 / 2) / (piApprox / 2));
    }
  }
}