'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import {
  <PERSON>lette,
  Sun,
  Moon,
  Monitor,
  Check,
  Paintbrush,
  Eye,
  Contrast,
  Zap,
} from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import { useTheme } from '@/lib/hooks/use-theme'

const themeOptions = [
  {
    id: 'light',
    name: 'Light',
    description: 'Clean and bright interface',
    icon: <Sun className="w-5 h-5" />,
    preview: 'bg-background border-border',
    colors: ['bg-background', 'bg-muted', 'bg-accent', 'bg-primary-500']
  },
  {
    id: 'dark',
    name: 'Dark',
    description: 'Easy on the eyes in low light',
    icon: <Moon className="w-5 h-5" />,
    preview: 'bg-background border-border',
    colors: ['bg-background', 'bg-muted', 'bg-accent', 'bg-primary-500']
  },
  {
    id: 'system',
    name: 'System',
    description: 'Follows your device preference',
    icon: <Monitor className="w-5 h-5" />,
    preview: 'bg-gradient-to-br from-background to-muted border-border',
    colors: ['bg-background', 'bg-muted', 'bg-accent', 'bg-primary-500']
  }
]

const accentColors = [
  { name: 'Yellow (Default)', value: 'yellow', class: 'bg-yellow-500' },
  { name: 'Blue', value: 'blue', class: 'bg-blue-500' },
  { name: 'Green', value: 'green', class: 'bg-green-500' },
  { name: 'Purple', value: 'purple', class: 'bg-purple-500' },
  { name: 'Red', value: 'red', class: 'bg-red-500' },
  { name: 'Orange', value: 'orange', class: 'bg-orange-500' },
  { name: 'Pink', value: 'pink', class: 'bg-pink-500' },
  { name: 'Indigo', value: 'indigo', class: 'bg-indigo-500' },
]

export function ThemeSettings() {
  const {
    theme,
    setTheme,
    accentColor,
    setAccentColor,
    fontSize,
    setFontSize,
    reducedMotion,
    setReducedMotion,
    highContrast,
    setHighContrast,
    compactMode,
    setCompactMode,
    resetTheme
  } = useTheme()

  const [previewTheme, setPreviewTheme] = useState(theme)

  const handleThemeChange = (newTheme: 'light' | 'dark' | 'system') => {
    setPreviewTheme(newTheme)
    setTheme(newTheme)
    // Force immediate update
    setTimeout(() => {
      document.documentElement.style.transition = 'all 0.3s ease'
      setTimeout(() => {
        document.documentElement.style.transition = ''
      }, 300)
    }, 0)
  }

  const handleAccentColorChange = (color: string) => {
    setAccentColor(color as any)
    // Add visual feedback
    const button = document.querySelector(`[data-color="${color}"]`)
    if (button) {
      button.classList.add('animate-pulse')
      setTimeout(() => {
        button.classList.remove('animate-pulse')
      }, 500)
    }
  }

  const handleFontSizeChange = (size: string) => {
    setFontSize(size as any)
    // Add visual feedback
    const button = document.querySelector(`[data-font-size="${size}"]`)
    if (button) {
      button.classList.add('animate-bounce')
      setTimeout(() => {
        button.classList.remove('animate-bounce')
      }, 500)
    }
  }

  const fontSizeOptions = [
    { label: 'Small', value: 'small', size: 'text-sm' },
    { label: 'Medium', value: 'medium', size: 'text-base' },
    { label: 'Large', value: 'large', size: 'text-lg' },
    { label: 'Extra Large', value: 'xl', size: 'text-xl' },
  ]

  return (
    <div className="space-y-6">
      {/* Theme Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Palette className="w-5 h-5" />
            <span>Theme Mode</span>
          </CardTitle>
          <CardDescription>
            Choose your preferred color scheme for the admin panel
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {themeOptions.map((option) => (
              <motion.div
                key={option.id}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <button
                  onClick={() => handleThemeChange(option.id as 'light' | 'dark' | 'system')}
                  className={`w-full p-4 rounded-lg border-2 transition-all ${
                    previewTheme === option.id
                      ? 'border-primary bg-primary/10 shadow-md'
                      : 'border-border hover:border-primary/50 hover:bg-accent'
                  }`}
                >
                  <div className={`w-full h-20 rounded-md mb-3 ${option.preview} flex items-center justify-center`}>
                    <div className="flex space-x-1">
                      {option.colors.map((color, index) => (
                        <div key={index} className={`w-3 h-3 rounded-full ${color}`} />
                      ))}
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      {option.icon}
                      <span className="font-medium">{option.name}</span>
                    </div>
                    {previewTheme === option.id && (
                      <Check className="w-4 h-4 text-primary" />
                    )}
                  </div>

                  <p className="text-sm text-muted-foreground mt-1 text-left">
                    {option.description}
                  </p>
                </button>
              </motion.div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Accent Color */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Paintbrush className="w-5 h-5" />
            <span>Accent Color</span>
          </CardTitle>
          <CardDescription>
            Choose the primary accent color for buttons and highlights
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-4 md:grid-cols-8 gap-3">
            {accentColors.map((color) => (
              <motion.button
                key={color.value}
                data-color={color.value}
                onClick={() => handleAccentColorChange(color.value)}
                className={`relative w-12 h-12 rounded-lg ${color.class} hover:scale-110 transition-all duration-200 ${
                  accentColor === color.value ? 'ring-2 ring-offset-2 ring-primary shadow-lg' : ''
                }`}
                title={color.name}
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.95 }}
              >
                {accentColor === color.value && (
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ type: "spring", stiffness: 500, damping: 30 }}
                  >
                    <Check className="w-4 h-4 text-white absolute inset-0 m-auto" />
                  </motion.div>
                )}
              </motion.button>
            ))}
          </div>
          <div className="text-sm text-muted-foreground mt-3">
            Current: <Badge variant="secondary">{accentColors.find(c => c.value === accentColor)?.name}</Badge>
          </div>
        </CardContent>
      </Card>

      {/* Font Size */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Eye className="w-5 h-5" />
            <span>Font Size</span>
          </CardTitle>
          <CardDescription>
            Adjust the text size for better readability
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            {fontSizeOptions.map((option) => (
              <motion.button
                key={option.value}
                data-font-size={option.value}
                onClick={() => handleFontSizeChange(option.value)}
                className={`p-3 rounded-lg border-2 transition-all duration-200 ${
                  fontSize === option.value
                    ? 'border-primary bg-primary/10 shadow-md'
                    : 'border-border hover:border-primary/50 hover:bg-accent'
                }`}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <div className={`${option.size} font-medium mb-1 text-foreground`}>Aa</div>
                <div className="text-xs text-muted-foreground">{option.label}</div>
              </motion.button>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Accessibility Options */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Contrast className="w-5 h-5" />
            <span>Accessibility</span>
          </CardTitle>
          <CardDescription>
            Customize the interface for better accessibility
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label htmlFor="high-contrast">High Contrast</Label>
              <p className="text-sm text-muted-foreground">
                Increase contrast for better visibility
              </p>
            </div>
            <Switch
              id="high-contrast"
              checked={highContrast}
              onCheckedChange={setHighContrast}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label htmlFor="reduced-motion">Reduce Motion</Label>
              <p className="text-sm text-muted-foreground">
                Minimize animations and transitions
              </p>
            </div>
            <Switch
              id="reduced-motion"
              checked={reducedMotion}
              onCheckedChange={setReducedMotion}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label htmlFor="compact-mode">Compact Mode</Label>
              <p className="text-sm text-muted-foreground">
                Reduce spacing for more content on screen
              </p>
            </div>
            <Switch
              id="compact-mode"
              checked={compactMode}
              onCheckedChange={setCompactMode}
            />
          </div>
        </CardContent>
      </Card>

      {/* Theme Preview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Eye className="w-5 h-5" />
            <span>Preview</span>
          </CardTitle>
          <CardDescription>
            See how your theme settings look in action
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="p-4 border border-border rounded-lg bg-muted/50">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="font-semibold text-foreground text-dynamic">Sample Dashboard</h3>
                <Badge variant="success">Active</Badge>
              </div>

              <div className="grid grid-cols-3 gap-3">
                <div className="p-3 bg-card rounded border border-border">
                  <div className="text-dynamic-sm text-muted-foreground">Users</div>
                  <div className="text-dynamic-xl font-bold text-foreground">1,234</div>
                </div>
                <div className="p-3 bg-card rounded border border-border">
                  <div className="text-dynamic-sm text-muted-foreground">Revenue</div>
                  <div className="text-dynamic-xl font-bold text-foreground">$12,345</div>
                </div>
                <div className="p-3 bg-card rounded border border-border">
                  <div className="text-dynamic-sm text-muted-foreground">Orders</div>
                  <div className="text-dynamic-xl font-bold text-foreground">567</div>
                </div>
              </div>

              <div className="flex space-x-2">
                <Button size="sm" className="gradient-primary text-white">
                  Primary Button
                </Button>
                <Button size="sm" variant="outline">
                  Secondary Button
                </Button>
              </div>

              <div className="text-dynamic-sm text-muted-foreground">
                Current settings: {accentColors.find(c => c.value === accentColor)?.name} theme,
                {fontSizeOptions.find(f => f.value === fontSize)?.label.toLowerCase()} font size
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Reset Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Zap className="w-5 h-5" />
            <span>Reset Settings</span>
          </CardTitle>
          <CardDescription>
            Reset all theme settings to their default values
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted-foreground">
                This will reset theme mode, accent color, font size, and all accessibility settings to defaults.
              </p>
            </div>
            <Button
              variant="outline"
              onClick={() => {
                resetTheme()
                setPreviewTheme('system')
              }}
              className="ml-4"
            >
              Reset to Defaults
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
