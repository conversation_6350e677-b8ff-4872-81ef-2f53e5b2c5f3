import 'package:cloud_firestore/cloud_firestore.dart';

class Forum {
  final String id;
  final String title;
  final String description;
  final DateTime createdAt;
  final String creatorId;
  final String creatorName;
  final String? imageUrl;
  final List<String> participants;
  final Map<String, String> participantNames;
  final String lastMessage;
  final DateTime lastMessageTime;
  final String lastSenderId;
  final int messageCount;
  
  Forum({
    required this.id,
    required this.title,
    required this.description,
    required this.createdAt,
    required this.creatorId,
    required this.creatorName,
    this.imageUrl,
    required this.participants,
    required this.participantNames,
    required this.lastMessage,
    required this.lastMessageTime,
    required this.lastSenderId,
    required this.messageCount,
  });

  factory Forum.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return Forum(
      id: doc.id,
      title: data['title'] ?? 'Untitled Forum',
      description: data['description'] ?? '',
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      creatorId: data['creatorId'] ?? '',
      creatorName: data['creatorName'] ?? 'Unknown Creator',
      imageUrl: data['imageUrl'],
      participants: List<String>.from(data['participants'] ?? []),
      participantNames: Map<String, String>.from(data['participantNames'] ?? {}),
      lastMessage: data['lastMessage'] ?? '',
      lastMessageTime: (data['lastMessageTime'] as Timestamp?)?.toDate() ?? DateTime.now(),
      lastSenderId: data['lastSenderId'] ?? '',
      messageCount: data['messageCount'] ?? 0,
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'title': title,
      'description': description,
      'createdAt': Timestamp.fromDate(createdAt),
      'creatorId': creatorId,
      'creatorName': creatorName,
      'imageUrl': imageUrl,
      'participants': participants,
      'participantNames': participantNames,
      'lastMessage': lastMessage,
      'lastMessageTime': Timestamp.fromDate(lastMessageTime),
      'lastSenderId': lastSenderId,
      'messageCount': messageCount,
    };
  }
} 