import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import '../models/query_message.dart';
import '../models/query_metadata.dart';
import '../services/query_service.dart';
import '../widgets/query_message_bubble.dart';
import '../../../core/firebase/firebase_permissions_check.dart';

class AdminQueryDetailScreen extends StatefulWidget {
  final String queryId;

  const AdminQueryDetailScreen({
    super.key,
    required this.queryId,
  });

  @override
  State<AdminQueryDetailScreen> createState() => _AdminQueryDetailScreenState();
}

class _AdminQueryDetailScreenState extends State<AdminQueryDetailScreen> {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final QueryService _queryService = QueryService();

  QueryMetadata? _queryMetadata;
  List<QueryMessage>? _messages;
  bool _isLoading = true;
  bool _isSending = false;
  bool _showAttachmentOptions = false;
  List<File> _selectedImages = [];
  QueryMessage? _editingMessage;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    try {
      setState(() {
        _isLoading = true;
      });

      // Get query metadata
      final metadata = await _queryService.getQueryMetadata(widget.queryId);

      // Get messages
      final messages = await _queryService.getMessages(widget.queryId);

      // Mark all messages as read for admin
      await _queryService.markAllAsRead(widget.queryId);

      if (mounted) {
        setState(() {
          _queryMetadata = metadata;
          _messages = messages;
          _isLoading = false;
        });

        // Scroll to bottom
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (_scrollController.hasClients) {
            _scrollController.animateTo(
              _scrollController.position.maxScrollExtent,
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeOut,
            );
          }
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading data: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _sendMessage() async {
    final text = _messageController.text.trim();

    if (text.isEmpty && _selectedImages.isEmpty) {
      return;
    }

    try {
      setState(() {
        _isSending = true;
        _showAttachmentOptions = false;
      });

      // Check permissions before attempting to upload
      bool hasPermissions = true;
      if (_selectedImages.isNotEmpty) {
        hasPermissions = await FirebasePermissionsCheck.checkRequiredPermissions();
        if (!hasPermissions && mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Storage permissions required. Please grant permissions in settings.'),
              backgroundColor: Colors.red,
              duration: Duration(seconds: 5),
            ),
          );
          setState(() {
            _isSending = false;
          });
          return;
        }
      }

      if (_editingMessage != null) {
        // Edit existing message
        await _queryService.editMessage(
          messageId: _editingMessage!.id,
          queryId: widget.queryId,
          newText: text,
        );

        _messageController.clear();
        setState(() {
          _editingMessage = null;
          _isSending = false;
        });
      } else {
        // Send new message
        await _queryService.sendMessage(
          queryId: widget.queryId,
          text: text,
          images: _selectedImages,
          isAdmin: true,
        );

        _messageController.clear();
        setState(() {
          _selectedImages = [];
          _isSending = false;
        });
      }

      // Refresh messages
      await _loadData();
    } catch (e) {
      setState(() {
        _isSending = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error sending message: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _pickImages() async {
    final picker = ImagePicker();
    final pickedFiles = await picker.pickMultiImage();

    if (pickedFiles.isNotEmpty) {
      setState(() {
        for (final pickedFile in pickedFiles) {
          _selectedImages.add(File(pickedFile.path));
        }
        _showAttachmentOptions = false;
      });
    }
  }

  Future<void> _takePhoto() async {
    final picker = ImagePicker();
    final pickedFile = await picker.pickImage(source: ImageSource.camera);

    if (pickedFile != null) {
      setState(() {
        _selectedImages.add(File(pickedFile.path));
        _showAttachmentOptions = false;
      });
    }
  }

  void _removeImage(int index) {
    setState(() {
      _selectedImages.removeAt(index);
    });
  }

  void _toggleQueryStatus() async {
    if (_queryMetadata == null) return;

    try {
      await _queryService.toggleQueryStatus(
        queryId: widget.queryId,
        isActive: !_queryMetadata!.isActive,
      );

      // Refresh data
      await _loadData();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              _queryMetadata!.isActive
                  ? 'Query marked as active'
                  : 'Query marked as closed'
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error updating query status: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }



  void _cancelEditing() {
    setState(() {
      _editingMessage = null;
      _messageController.clear();
    });
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      appBar: AppBar(
        title: _queryMetadata != null
            ? Text(_queryMetadata!.userName)
            : const Text('Query Detail'),
        actions: [
          if (_queryMetadata != null)
            IconButton(
              icon: Icon(
                _queryMetadata!.isActive
                    ? Icons.archive_outlined
                    : Icons.unarchive_outlined,
              ),
              tooltip: _queryMetadata!.isActive
                  ? 'Close query'
                  : 'Reopen query',
              onPressed: _toggleQueryStatus,
            ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                // Status bar
                if (_queryMetadata != null)
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    color: _queryMetadata!.isActive
                        ? (isDarkMode ? Colors.green[900] : Colors.green[100])
                        : (isDarkMode ? Colors.grey[800] : Colors.grey[200]),
                    child: Row(
                      children: [
                        Icon(
                          _queryMetadata!.isActive
                              ? Icons.circle
                              : Icons.archive,
                          size: 16,
                          color: _queryMetadata!.isActive
                              ? (isDarkMode ? Colors.green[300] : Colors.green[800])
                              : (isDarkMode ? Colors.grey[400] : Colors.grey[700]),
                        ),
                        const SizedBox(width: 8),
                        Text(
                          _queryMetadata!.isActive
                              ? 'Active Query'
                              : 'Closed Query',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: _queryMetadata!.isActive
                                ? (isDarkMode ? Colors.green[300] : Colors.green[800])
                                : (isDarkMode ? Colors.grey[400] : Colors.grey[700]),
                          ),
                        ),
                      ],
                    ),
                  ),

                // Messages
                Expanded(
                  child: _messages == null || _messages!.isEmpty
                      ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.question_answer_outlined,
                                size: 64,
                                color: Colors.grey[400],
                              ),
                              const SizedBox(height: 16),
                              Text(
                                'No messages yet',
                                style: TextStyle(
                                  fontSize: 18,
                                  color: Colors.grey[600],
                                ),
                              ),
                              const SizedBox(height: 8),
                              const Text(
                                'Start the conversation',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey,
                                ),
                              ),
                            ],
                          ),
                        )
                      : ListView.builder(
                          controller: _scrollController,
                          padding: const EdgeInsets.all(16),
                          itemCount: _messages!.length,
                          itemBuilder: (context, index) {
                            final message = _messages![index];
                            final isMe = message.senderId == 'admin'; // Admin message

                            // Show sender name for first message or when sender changes
                            final showSender = index == 0 ||
                                _messages![index - 1].senderId != message.senderId;

                            return Padding(
                              padding: const EdgeInsets.only(bottom: 8),
                              child: QueryMessageBubble(
                                message: message,
                                isMe: isMe,
                                showSender: showSender,
                                onEdit: isMe ? (text) {
                                  _queryService.editMessage(
                                    messageId: message.id,
                                    queryId: widget.queryId,
                                    newText: text,
                                  ).then((_) => _loadData());
                                } : null,
                                onDelete: isMe ? () {
                                  _queryService.deleteMessage(
                                    messageId: message.id,
                                    queryId: widget.queryId,
                                  ).then((_) => _loadData());
                                } : null,
                              ),
                            );
                          },
                        ),
                ),

                // Editing indicator
                if (_editingMessage != null)
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    color: isDarkMode
                        ? Colors.grey[800]
                        : Colors.grey[200],
                    child: Row(
                      children: [
                        const Icon(Icons.edit, size: 16),
                        const SizedBox(width: 8),
                        const Expanded(
                          child: Text(
                            'Editing message',
                            style: TextStyle(
                              fontWeight: FontWeight.w500,
                              fontSize: 14,
                            ),
                          ),
                        ),
                        IconButton(
                          icon: const Icon(Icons.close, size: 16),
                          padding: EdgeInsets.zero,
                          constraints: const BoxConstraints(
                            minWidth: 24,
                            minHeight: 24,
                          ),
                          onPressed: _cancelEditing,
                        ),
                      ],
                    ),
                  ),

                // Image preview
                if (_selectedImages.isNotEmpty)
                  Container(
                    height: 100,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    color: isDarkMode
                        ? Colors.grey[800]
                        : Colors.grey[200],
                    child: ListView.builder(
                      scrollDirection: Axis.horizontal,
                      itemCount: _selectedImages.length,
                      itemBuilder: (context, index) {
                        return Container(
                          width: 80,
                          height: 80,
                          margin: const EdgeInsets.only(right: 8),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: isDarkMode
                                  ? Colors.grey[700]!
                                  : Colors.grey[300]!,
                            ),
                          ),
                          child: Stack(
                            fit: StackFit.expand,
                            children: [
                              ClipRRect(
                                borderRadius: BorderRadius.circular(7),
                                child: Image.file(
                                  _selectedImages[index],
                                  fit: BoxFit.cover,
                                ),
                              ),
                              Positioned(
                                top: 0,
                                right: 0,
                                child: GestureDetector(
                                  onTap: () => _removeImage(index),
                                  child: Container(
                                    decoration: BoxDecoration(
                                      color: Colors.black.withOpacity(0.5),
                                      borderRadius: const BorderRadius.only(
                                        bottomLeft: Radius.circular(8),
                                        topRight: Radius.circular(7),
                                      ),
                                    ),
                                    child: const Icon(
                                      Icons.close,
                                      size: 16,
                                      color: Colors.white,
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        );
                      },
                    ),
                  ),



                // Message input
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: isDarkMode
                        ? Colors.grey[900]
                        : Colors.white,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.05),
                        blurRadius: 10,
                        offset: const Offset(0, -3),
                      ),
                    ],
                  ),
                  child: Row(
                    children: [
                      IconButton(
                        icon: const Icon(Icons.add),
                        onPressed: () {
                          setState(() {
                            _showAttachmentOptions = !_showAttachmentOptions;
                          });
                        },
                      ),
                      Expanded(
                        child: TextField(
                          controller: _messageController,
                          decoration: InputDecoration(
                            hintText: 'Type a message...',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(24),
                              borderSide: BorderSide.none,
                            ),
                            filled: true,
                            fillColor: isDarkMode
                                ? Colors.grey[800]
                                : Colors.grey[100],
                            contentPadding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 8,
                            ),
                          ),
                          minLines: 1,
                          maxLines: 5,
                          textCapitalization: TextCapitalization.sentences,
                          enabled: _queryMetadata?.isActive ?? true,
                        ),
                      ),
                      if (_queryMetadata?.isActive ?? true)
                        IconButton(
                          icon: const Icon(Icons.send),
                          onPressed: _isSending ? null : _sendMessage,
                        ),
                    ],
                  ),
                ),

                // Attachment options
                if (_showAttachmentOptions)
                  Container(
                    color: isDarkMode
                        ? Colors.grey[900]
                        : Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        _buildAttachmentOption(
                          icon: Icons.image,
                          label: 'Gallery',
                          onTap: _pickImages,
                        ),
                        _buildAttachmentOption(
                          icon: Icons.camera_alt,
                          label: 'Camera',
                          onTap: _takePhoto,
                        ),
                      ],
                    ),
                  ),
              ],
            ),
    );
  }

  Widget _buildAttachmentOption({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(25),
              ),
              child: Icon(
                icon,
                color: Theme.of(context).primaryColor,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: isDarkMode
                    ? Colors.grey[400]
                    : Colors.grey[800],
              ),
            ),
          ],
        ),
      ),
    );
  }
}