'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { motion } from 'framer-motion'
import {
  MessageSquare,
  Search,
  Filter,
  Download,
  Plus,
  Eye,
  Edit,
  Trash2,
  Lock,
  Unlock,
  Pin,
  Flag,
  Users,
  MessageCircle,
  Calendar,
} from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { DataTable } from '@/components/ui/data-table'
import { ForumModal } from '@/components/forums/forum-modal'
import { ForumFilters } from '@/components/forums/forum-filters'
import { LoadingSpinner } from '@/components/ui/loading-spinner'

import { useForums } from '@/lib/hooks/use-forums'
import { formatDate, formatRelativeTime } from '@/lib/utils'

interface Forum {
  id: string
  title: string
  description: string
  createdAt: Date
  creatorId: string
  creatorName: string
  imageUrl?: string
  participants: string[]
  participantNames: { [key: string]: string }
  lastMessage: string
  lastMessageTime: Date
  lastSenderId: string
  messageCount: number
  status: 'active' | 'locked' | 'archived' | 'flagged'
  isPinned: boolean
  isSticky: boolean
  category: 'general' | 'help' | 'feedback' | 'announcements' | 'off-topic'
  tags: string[]
  moderatedBy?: string
  moderationReason?: string
}

export default function ForumsPage() {
  const router = useRouter()
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedForum, setSelectedForum] = useState<Forum | null>(null)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [modalMode, setModalMode] = useState<'view' | 'edit' | 'create'>('view')

  const [filters, setFilters] = useState({
    category: 'all',
    status: 'all',
    author: 'all'
  })

  const {
    forums,
    forumMessages,
    isLoading,
    error,
    updateForum,
    deleteForum,
    createForum,
    moderateForum,
    fetchForumMessages,
    sendMessage
  } = useForums()

  const filteredForums = forums?.filter(forum => {
    const matchesSearch = forum.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         forum.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         forum.creatorName.toLowerCase().includes(searchQuery.toLowerCase())

    const matchesCategory = filters.category === 'all' || forum.category === filters.category
    const matchesStatus = filters.status === 'all' || forum.status === filters.status

    return matchesSearch && matchesCategory && matchesStatus
  }) || []

  const handleCreateForum = () => {
    setSelectedForum(null)
    setModalMode('create')
    setIsModalOpen(true)
  }

  const handleViewForum = (forum: Forum) => {
    setSelectedForum(forum)
    setModalMode('view')
    setIsModalOpen(true)
  }

  const handleEditForum = (forum: Forum) => {
    setSelectedForum(forum)
    setModalMode('edit')
    setIsModalOpen(true)
  }

  const handleDeleteForum = async (forum: Forum) => {
    if (window.confirm(`Are you sure you want to delete "${forum.title}"?`)) {
      try {
        await deleteForum(forum.id)
      } catch (error) {
        console.error('Error deleting forum:', error)
      }
    }
  }

  const handleToggleLock = async (forum: Forum) => {
    try {
      const newStatus = forum.status === 'locked' ? 'active' : 'locked'
      await moderateForum(forum.id, newStatus, newStatus === 'locked' ? 'Forum locked by admin' : undefined)
    } catch (error) {
      console.error('Error toggling forum lock:', error)
    }
  }

  const handleTogglePin = async (forum: Forum) => {
    try {
      await updateForum(forum.id, { isPinned: !forum.isPinned })
    } catch (error) {
      console.error('Error toggling forum pin:', error)
    }
  }

  const handleFlagForum = async (forum: Forum) => {
    try {
      await moderateForum(forum.id, 'flagged', 'Forum flagged for review')
    } catch (error) {
      console.error('Error flagging forum:', error)
    }
  }

  const handleOpenChat = (forum: Forum) => {
    router.push(`/forums/${forum.id}/chat`)
  }



  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'success'
      case 'locked':
        return 'warning'
      case 'archived':
        return 'secondary'
      case 'flagged':
        return 'error'
      default:
        return 'secondary'
    }
  }

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'general':
        return 'bg-blue-100 text-blue-800'
      case 'help':
        return 'bg-green-100 text-green-800'
      case 'feedback':
        return 'bg-purple-100 text-purple-800'
      case 'announcements':
        return 'bg-orange-100 text-orange-800'
      case 'off-topic':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const columns = [
    {
      key: 'forum',
      label: 'Forum',
      render: (forum: Forum) => (
        <div className="flex items-start space-x-3">
          <div className="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
            {forum.imageUrl ? (
              <img src={forum.imageUrl} alt={forum.title} className="w-10 h-10 rounded-full object-cover" />
            ) : (
              <MessageSquare className="w-5 h-5 text-primary-600" />
            )}
          </div>
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-2">
              <p className="font-medium text-gray-900 truncate">{forum.title}</p>
              {forum.isPinned && (
                <Pin className="w-4 h-4 text-orange-500" />
              )}
              {forum.isSticky && (
                <Badge variant="warning" className="text-xs">Sticky</Badge>
              )}
            </div>
            <p className="text-sm text-gray-500 truncate">{forum.description}</p>
            <p className="text-sm text-gray-500">by {forum.creatorName}</p>
            <div className="flex items-center space-x-2 mt-1">
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${getCategoryColor(forum.category)}`}>
                {forum.category}
              </span>
              {forum.tags.map((tag, index) => (
                <Badge key={index} variant="secondary" className="text-xs">
                  {tag}
                </Badge>
              ))}
            </div>
          </div>
        </div>
      )
    },
    {
      key: 'status',
      label: 'Status',
      render: (forum: Forum) => (
        <Badge variant={getStatusColor(forum.status) as any}>
          {forum.status}
        </Badge>
      )
    },
    {
      key: 'participants',
      label: 'Participants',
      render: (forum: Forum) => (
        <div className="text-sm">
          <div className="flex items-center space-x-1">
            <Users className="w-4 h-4 text-gray-400" />
            <span>{forum.participants.length} members</span>
          </div>
          <div className="flex items-center space-x-1 text-gray-500">
            <MessageCircle className="w-4 h-4" />
            <span>{forum.messageCount} messages</span>
          </div>
        </div>
      )
    },
    {
      key: 'lastActivity',
      label: 'Last Activity',
      render: (forum: Forum) => (
        <div className="text-sm">
          <div className="flex items-center space-x-1">
            <Calendar className="w-4 h-4 text-gray-400" />
            <span>{formatRelativeTime(forum.lastMessageTime)}</span>
          </div>
          <p className="text-gray-500 truncate">{forum.lastMessage || 'No messages yet'}</p>
        </div>
      )
    },
    {
      key: 'created',
      label: 'Created',
      render: (forum: Forum) => formatRelativeTime(forum.createdAt)
    },
    {
      key: 'actions',
      label: 'Actions',
      render: (forum: Forum) => (
        <div className="flex items-center space-x-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleViewForum(forum)}
          >
            <Eye className="w-4 h-4" />
          </Button>

          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleEditForum(forum)}
          >
            <Edit className="w-4 h-4" />
          </Button>

          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleOpenChat(forum)}
            className="text-blue-600 hover:text-blue-700"
            title="Open Chat"
          >
            <MessageCircle className="w-4 h-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleTogglePin(forum)}
            className={forum.isPinned ? 'text-orange-600 hover:text-orange-700' : 'text-gray-600 hover:text-gray-700'}
          >
            <Pin className="w-4 h-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleToggleLock(forum)}
            className={forum.status === 'locked' ? 'text-green-600 hover:text-green-700' : 'text-yellow-600 hover:text-yellow-700'}
          >
            {forum.status === 'locked' ? <Unlock className="w-4 h-4" /> : <Lock className="w-4 h-4" />}
          </Button>
          {forum.status !== 'flagged' && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleFlagForum(forum)}
              className="text-red-600 hover:text-red-700"
            >
              <Flag className="w-4 h-4" />
            </Button>
          )}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleDeleteForum(forum)}
            className="text-red-600 hover:text-red-700"
          >
            <Trash2 className="w-4 h-4" />
          </Button>
        </div>
      )
    }
  ]

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Forum Management</h1>
          <p className="text-gray-600">Moderate community discussions and posts</p>
        </div>
        <Button onClick={handleCreateForum} className="gradient-primary">
          <Plus className="w-4 h-4 mr-2" />
          Create Forum
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Forums</p>
                <p className="text-2xl font-bold text-gray-900">{forums?.length || 0}</p>
              </div>
              <MessageSquare className="w-8 h-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Active Forums</p>
                <p className="text-2xl font-bold text-gray-900">
                  {filteredForums.filter(f => f.status === 'active').length}
                </p>
              </div>
              <MessageCircle className="w-8 h-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Flagged Forums</p>
                <p className="text-2xl font-bold text-gray-900">
                  {filteredForums.filter(f => f.status === 'flagged').length}
                </p>
              </div>
              <Flag className="w-8 h-8 text-red-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Messages</p>
                <p className="text-2xl font-bold text-gray-900">
                  {filteredForums.reduce((sum, forum) => sum + forum.messageCount, 0)}
                </p>
              </div>
              <Users className="w-8 h-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <CardTitle>Community Forums</CardTitle>
          <CardDescription>
            Manage and moderate community forum discussions
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search forums by title, description, or creator..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <ForumFilters filters={filters} onFiltersChange={setFilters} />

            <Button variant="outline">
              <Download className="w-4 h-4 mr-2" />
              Export
            </Button>
          </div>

          <DataTable
            data={filteredForums}
            columns={columns}
            searchQuery={searchQuery}
            isLoading={isLoading}
          />
        </CardContent>
      </Card>

      {/* Forum Modal */}
      <ForumModal
        post={selectedForum ? {
          id: selectedForum.id,
          title: selectedForum.title,
          content: selectedForum.description,
          category: selectedForum.category,
          author: {
            id: selectedForum.creatorId,
            name: selectedForum.creatorName
          },
          status: selectedForum.status,
          isPinned: selectedForum.isPinned,
          isSticky: selectedForum.isSticky,
          repliesCount: selectedForum.messageCount,
          viewsCount: 0, // Forums don't track views
          lastReplyAt: selectedForum.lastMessageTime,
          lastReplyBy: selectedForum.lastSenderId ? {
            id: selectedForum.lastSenderId,
            name: 'Unknown' // We don't have the name
          } : undefined,
          createdAt: selectedForum.createdAt,
          updatedAt: selectedForum.createdAt, // Use createdAt as fallback
          tags: selectedForum.tags,
          moderatedBy: selectedForum.moderatedBy,
          moderationReason: selectedForum.moderationReason
        } : null}
        mode={modalMode}
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onSave={async (forumData) => {
          try {
            if (modalMode === 'create') {
              await createForum(forumData)
            } else if (modalMode === 'edit' && selectedForum) {
              await updateForum(selectedForum.id, forumData)
            }
            setIsModalOpen(false)
          } catch (error) {
            console.error('Error saving forum:', error)
          }
        }}
      />


    </motion.div>
  )
}
