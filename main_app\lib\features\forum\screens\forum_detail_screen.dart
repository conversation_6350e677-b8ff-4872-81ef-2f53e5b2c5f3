import 'dart:io';
import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:image_picker/image_picker.dart';
import '../../../core/core.dart';
import '../models/forum.dart';
import '../models/forum_message.dart';
import '../services/forum_service.dart';
import '../widgets/message_bubble.dart';
import '../widgets/voice_recorder.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:timeago/timeago.dart' as timeago;

class ForumDetailScreen extends StatefulWidget {
  final String forumId;

  const ForumDetailScreen({
    super.key,
    required this.forumId,
  });

  @override
  State<ForumDetailScreen> createState() => _ForumDetailScreenState();
}

class _ForumDetailScreenState extends State<ForumDetailScreen> {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  late String _currentUserId;
  late Stream<DocumentSnapshot> _forumStream;
  late Stream<QuerySnapshot> _messagesStream;
  bool _isRecording = false;
  ForumMessage? _replyToMessage;
  bool _showAttachmentOptions = false;
  List<File> _attachments = [];
  File? _voiceNote;
  int? _voiceNoteDuration;
  final List<ForumMessage> _pendingMessages = [];

  // Removed performance monitoring to reduce overhead

  @override
  void initState() {
    super.initState();

    _currentUserId = FirebaseAuth.instance.currentUser?.uid ?? '';
    _forumStream = ForumService.getForumStream(widget.forumId);
    _messagesStream = ForumService.getForumMessagesStream(widget.forumId);

    // Make sure user is in the participants list
    if (_currentUserId.isNotEmpty) {
      ForumService.joinForum(widget.forumId);
    }
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  // Send a text message
  Future<void> _sendMessage() async {
    final messageText = _messageController.text.trim();
    if (messageText.isEmpty && _attachments.isEmpty && _voiceNote == null) return;

    final currentUser = FirebaseAuth.instance.currentUser;
    if (currentUser == null) return;

    // Create pending message immediately
    final pendingMessage = ForumMessage.pending(
      senderId: currentUser.uid,
      senderName: currentUser.displayName ?? 'Anonymous',
      senderAvatar: currentUser.photoURL ?? '',
      text: messageText,
      replyToId: _replyToMessage?.id,
      replyToText: _replyToMessage?.text,
      replyToSenderName: _replyToMessage?.senderName,
      attachments: _attachments.map((f) => f.path).toList(),
      voiceNote: _voiceNote?.path,
      voiceNoteDuration: _voiceNoteDuration,
      isUploading: _attachments.isNotEmpty || _voiceNote != null,
    );

    // Add to pending messages and update UI immediately
    setState(() {
      _pendingMessages.add(pendingMessage);
      _messageController.clear();
      _replyToMessage = null;
      _attachments = [];
      _voiceNote = null;
      _voiceNoteDuration = null;
    });

    // Scroll to bottom immediately
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });

    // Send message in background
    try {
      await ForumService.sendMessage(
        forumId: widget.forumId,
        text: messageText,
        replyToId: pendingMessage.replyToId,
        replyToText: pendingMessage.replyToText,
        replyToSenderName: pendingMessage.replyToSenderName,
        attachments: _attachments,
        voiceNote: _voiceNote,
        voiceNoteDuration: pendingMessage.voiceNoteDuration,
      );

      // Remove pending message after successful send
      setState(() {
        _pendingMessages.removeWhere((msg) => msg.id == pendingMessage.id);
      });
    } catch (e) {
      // Mark pending message as failed
      setState(() {
        final index = _pendingMessages.indexWhere((msg) => msg.id == pendingMessage.id);
        if (index != -1) {
          _pendingMessages[index] = ForumMessage.pending(
            senderId: pendingMessage.senderId,
            senderName: pendingMessage.senderName,
            senderAvatar: pendingMessage.senderAvatar,
            text: pendingMessage.text,
            replyToId: pendingMessage.replyToId,
            replyToText: pendingMessage.replyToText,
            replyToSenderName: pendingMessage.replyToSenderName,
            attachments: pendingMessage.attachments,
            voiceNote: pendingMessage.voiceNote,
            voiceNoteDuration: pendingMessage.voiceNoteDuration,
            isUploading: false, // Failed upload
          );
        }
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error sending message: $e'),
            backgroundColor: Colors.red,
            action: SnackBarAction(
              label: 'Retry',
              onPressed: () {
                // Remove failed message
                setState(() {
                  _pendingMessages.removeWhere((msg) => msg.id == pendingMessage.id);
                });
              },
            ),
          ),
        );
      }
    }
  }

  // Pick image or file from gallery and send immediately
  Future<void> _pickAttachment() async {
    final picker = ImagePicker();

    final pickedFile = await picker.pickImage(
      source: ImageSource.gallery,
      imageQuality: 70,
    );

    if (pickedFile != null) {
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) return;

      // Create pending message immediately with the image
      final pendingMessage = ForumMessage.pending(
        senderId: currentUser.uid,
        senderName: currentUser.displayName ?? 'Anonymous',
        senderAvatar: currentUser.photoURL ?? '',
        text: '',
        replyToId: _replyToMessage?.id,
        replyToText: _replyToMessage?.text,
        replyToSenderName: _replyToMessage?.senderName,
        attachments: [pickedFile.path],
        isUploading: true,
      );

      // Add to pending messages and update UI immediately
      setState(() {
        _pendingMessages.add(pendingMessage);
        _replyToMessage = null;
        _showAttachmentOptions = false;
      });

      // Scroll to bottom immediately
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (_scrollController.hasClients) {
          _scrollController.animateTo(
            _scrollController.position.maxScrollExtent,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeOut,
          );
        }
      });

      // Send message in background
      try {
        await ForumService.sendMessage(
          forumId: widget.forumId,
          text: '',
          replyToId: pendingMessage.replyToId,
          replyToText: pendingMessage.replyToText,
          replyToSenderName: pendingMessage.replyToSenderName,
          attachments: [File(pickedFile.path)],
        );

        // Remove pending message after successful send
        setState(() {
          _pendingMessages.removeWhere((msg) => msg.id == pendingMessage.id);
        });
      } catch (e) {
        // Mark pending message as failed
        setState(() {
          final index = _pendingMessages.indexWhere((msg) => msg.id == pendingMessage.id);
          if (index != -1) {
            _pendingMessages[index] = ForumMessage.pending(
              senderId: pendingMessage.senderId,
              senderName: pendingMessage.senderName,
              senderAvatar: pendingMessage.senderAvatar,
              text: pendingMessage.text,
              replyToId: pendingMessage.replyToId,
              replyToText: pendingMessage.replyToText,
              replyToSenderName: pendingMessage.replyToSenderName,
              attachments: pendingMessage.attachments,
              isUploading: false, // Failed upload
            );
          }
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error sending image: $e'),
              backgroundColor: Colors.red,
              action: SnackBarAction(
                label: 'Retry',
                onPressed: () {
                  // Remove failed message
                  setState(() {
                    _pendingMessages.removeWhere((msg) => msg.id == pendingMessage.id);
                  });
                },
              ),
            ),
          );
        }
      }
    }
  }

  // Take a photo with camera and send immediately
  Future<void> _takePhoto() async {
    final picker = ImagePicker();

    final pickedFile = await picker.pickImage(
      source: ImageSource.camera,
      imageQuality: 70,
    );

    if (pickedFile != null) {
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) return;

      // Create pending message immediately with the image
      final pendingMessage = ForumMessage.pending(
        senderId: currentUser.uid,
        senderName: currentUser.displayName ?? 'Anonymous',
        senderAvatar: currentUser.photoURL ?? '',
        text: '',
        replyToId: _replyToMessage?.id,
        replyToText: _replyToMessage?.text,
        replyToSenderName: _replyToMessage?.senderName,
        attachments: [pickedFile.path],
        isUploading: true,
      );

      // Add to pending messages and update UI immediately
      setState(() {
        _pendingMessages.add(pendingMessage);
        _replyToMessage = null;
        _showAttachmentOptions = false;
      });

      // Scroll to bottom immediately
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (_scrollController.hasClients) {
          _scrollController.animateTo(
            _scrollController.position.maxScrollExtent,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeOut,
          );
        }
      });

      // Send message in background
      try {
        await ForumService.sendMessage(
          forumId: widget.forumId,
          text: '',
          replyToId: pendingMessage.replyToId,
          replyToText: pendingMessage.replyToText,
          replyToSenderName: pendingMessage.replyToSenderName,
          attachments: [File(pickedFile.path)],
        );

        // Remove pending message after successful send
        setState(() {
          _pendingMessages.removeWhere((msg) => msg.id == pendingMessage.id);
        });
      } catch (e) {
        // Mark pending message as failed
        setState(() {
          final index = _pendingMessages.indexWhere((msg) => msg.id == pendingMessage.id);
          if (index != -1) {
            _pendingMessages[index] = ForumMessage.pending(
              senderId: pendingMessage.senderId,
              senderName: pendingMessage.senderName,
              senderAvatar: pendingMessage.senderAvatar,
              text: pendingMessage.text,
              replyToId: pendingMessage.replyToId,
              replyToText: pendingMessage.replyToText,
              replyToSenderName: pendingMessage.replyToSenderName,
              attachments: pendingMessage.attachments,
              isUploading: false, // Failed upload
            );
          }
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error sending image: $e'),
              backgroundColor: Colors.red,
              action: SnackBarAction(
                label: 'Retry',
                onPressed: () {
                  // Remove failed message
                  setState(() {
                    _pendingMessages.removeWhere((msg) => msg.id == pendingMessage.id);
                  });
                },
              ),
            ),
          );
        }
      }
    }
  }

  // Handle voice recording complete and send immediately with optimistic UI
  Future<void> _onRecordingComplete(File audioFile, int durationInMilliseconds) async {
    final currentUser = FirebaseAuth.instance.currentUser;
    if (currentUser == null) return;

    // Create pending message immediately
    final pendingMessage = ForumMessage.pending(
      senderId: currentUser.uid,
      senderName: currentUser.displayName ?? 'Anonymous',
      senderAvatar: currentUser.photoURL ?? '',
      text: '',
      replyToId: _replyToMessage?.id,
      replyToText: _replyToMessage?.text,
      replyToSenderName: _replyToMessage?.senderName,
      attachments: _attachments.map((f) => f.path).toList(),
      voiceNote: audioFile.path, // Use local path for pending
      voiceNoteDuration: (durationInMilliseconds / 1000).round(),
      isUploading: true,
    );

    // Add to pending messages and update UI immediately
    setState(() {
      _isRecording = false;
      _pendingMessages.add(pendingMessage);
      _replyToMessage = null;
      _attachments = [];
      _voiceNote = null;
      _voiceNoteDuration = null;
    });

    // Scroll to bottom immediately
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });

    // Send message in background
    try {
      await ForumService.sendMessage(
        forumId: widget.forumId,
        text: '',
        replyToId: pendingMessage.replyToId,
        replyToText: pendingMessage.replyToText,
        replyToSenderName: pendingMessage.replyToSenderName,
        attachments: _attachments,
        voiceNote: audioFile,
        voiceNoteDuration: pendingMessage.voiceNoteDuration,
      );

      // Remove pending message after successful send
      setState(() {
        _pendingMessages.removeWhere((msg) => msg.id == pendingMessage.id);
      });
    } catch (e) {
      // Mark pending message as failed
      setState(() {
        final index = _pendingMessages.indexWhere((msg) => msg.id == pendingMessage.id);
        if (index != -1) {
          _pendingMessages[index] = ForumMessage.pending(
            senderId: pendingMessage.senderId,
            senderName: pendingMessage.senderName,
            senderAvatar: pendingMessage.senderAvatar,
            text: pendingMessage.text,
            replyToId: pendingMessage.replyToId,
            replyToText: pendingMessage.replyToText,
            replyToSenderName: pendingMessage.replyToSenderName,
            attachments: pendingMessage.attachments,
            voiceNote: pendingMessage.voiceNote,
            voiceNoteDuration: pendingMessage.voiceNoteDuration,
            isUploading: false, // Failed upload
          );
        }
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error sending voice note: $e'),
            backgroundColor: Colors.red,
            action: SnackBarAction(
              label: 'Retry',
              onPressed: () {
                // Remove failed message
                setState(() {
                  _pendingMessages.removeWhere((msg) => msg.id == pendingMessage.id);
                });
              },
            ),
          ),
        );
      }
    }
  }

  // Cancel reply
  void _cancelReply() {
    setState(() {
      _replyToMessage = null;
    });
  }

  // Remove attachment
  void _removeAttachment(int index) {
    setState(() {
      _attachments.removeAt(index);
    });
  }

  // Remove voice note
  void _removeVoiceNote() {
    setState(() {
      _voiceNote = null;
      _voiceNoteDuration = null;
    });
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return StreamBuilder<DocumentSnapshot>(
      stream: _forumStream,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Scaffold(
            body: Center(child: CircularProgressIndicator()),
          );
        }

        if (snapshot.hasError) {
          return Scaffold(
            appBar: AppBar(title: const Text('Error')),
            body: Center(
              child: Text('Error loading forum: ${snapshot.error}'),
            ),
          );
        }

        if (!snapshot.hasData || !snapshot.data!.exists) {
          return Scaffold(
            appBar: AppBar(title: const Text('Forum Not Found')),
            body: const Center(
              child: Text('The forum you\'re looking for doesn\'t exist.'),
            ),
          );
        }

        final forum = Forum.fromFirestore(snapshot.data!);

        return Scaffold(
          appBar: AppBar(
            title: Text(forum.title),
            backgroundColor: AppColors.primaryYellow,
            foregroundColor: Colors.black,
            actions: [
              IconButton(
                icon: const Icon(Icons.info_outline),
                onPressed: () {
                  showModalBottomSheet(
                    context: context,
                    isScrollControlled: true,
                    shape: const RoundedRectangleBorder(
                      borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
                    ),
                    builder: (context) => _buildForumInfoSheet(context, forum, isDarkMode),
                  );
                },
              ),
            ],
          ),
          body: Column(
            children: [
              // Messages area
              Expanded(
                child: StreamBuilder<QuerySnapshot>(
                  stream: _messagesStream,
                  builder: (context, snapshot) {
                    if (snapshot.connectionState == ConnectionState.waiting) {
                      return const Center(child: CircularProgressIndicator());
                    }

                    if (snapshot.hasError) {
                      return Center(
                        child: Text('Error loading messages: ${snapshot.error}'),
                      );
                    }

                    // Now handle the UI display with loaded messages
                    final firestoreMessages = snapshot.data!.docs
                        .map((doc) => ForumMessage.fromFirestore(doc))
                        .toList();

                    // Combine Firestore messages with pending messages
                    final allMessages = [...firestoreMessages, ..._pendingMessages];

                    if (allMessages.isEmpty) {
                      return Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.forum_outlined,
                              size: 64,
                              color: isDarkMode ? Colors.white24 : Colors.black12,
                            ),
                            const SizedBox(height: 16),
                            Text(
                              'No messages yet',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: isDarkMode ? AppColors.darkTextSecondary : AppColors.lightTextSecondary,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Be the first to start the conversation!',
                              style: TextStyle(
                                fontSize: 14,
                                color: isDarkMode ? AppColors.darkTextSecondary : AppColors.lightTextSecondary,
                              ),
                            ),
                          ],
                        ),
                      );
                    }

                    // Scroll to bottom on new messages
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      if (_scrollController.hasClients) {
                        _scrollController.animateTo(
                          _scrollController.position.maxScrollExtent,
                          duration: const Duration(milliseconds: 300),
                          curve: Curves.easeOut,
                        );
                      }
                    });

                    return ListView.builder(
                      controller: _scrollController,
                      padding: const EdgeInsets.all(16),
                      itemCount: allMessages.length,
                      itemBuilder: (context, index) {
                        final message = allMessages[index];
                        final isMe = message.senderId == _currentUserId;

                        return MessageBubble(
                          message: message,
                          isMe: isMe,
                          currentUserId: _currentUserId,
                          forumId: widget.forumId,
                          onReply: (message) {
                            setState(() {
                              _replyToMessage = message;
                            });
                            FocusScope.of(context).requestFocus(FocusNode());
                            Future.delayed(const Duration(milliseconds: 100), () {
                              FocusScope.of(context).unfocus();
                            });
                          },
                        );
                      },
                    );
                  },
                ),
              ),

              // Reply indicator
              if (_replyToMessage != null)
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  color: isDarkMode ? Colors.grey.shade800 : Colors.grey.shade200,
                  child: Row(
                    children: [
                      const Icon(Icons.reply, size: 16),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              'Replying to ${_replyToMessage!.senderName}',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 12,
                              ),
                            ),
                            Text(
                              _replyToMessage!.text,
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                              style: const TextStyle(fontSize: 12),
                            ),
                          ],
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.close, size: 16),
                        constraints: const BoxConstraints(
                          minWidth: 24,
                          minHeight: 24,
                        ),
                        padding: EdgeInsets.zero,
                        onPressed: _cancelReply,
                      ),
                    ],
                  ),
                ),

              // Attachment preview
              if (_attachments.isNotEmpty)
                Container(
                  height: 80,
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  color: isDarkMode ? Colors.grey.shade800 : Colors.grey.shade200,
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: _attachments.length,
                    itemBuilder: (context, index) {
                      return Container(
                        width: 60,
                        height: 60,
                        margin: const EdgeInsets.only(right: 8),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: isDarkMode ? Colors.grey.shade700 : Colors.grey.shade300,
                          ),
                        ),
                        child: Stack(
                          fit: StackFit.expand,
                          children: [
                            ClipRRect(
                              borderRadius: BorderRadius.circular(7),
                              child: Image.file(
                                _attachments[index],
                                fit: BoxFit.cover,
                              ),
                            ),
                            Positioned(
                              top: 0,
                              right: 0,
                              child: GestureDetector(
                                onTap: () => _removeAttachment(index),
                                child: Container(
                                  decoration: BoxDecoration(
                                    color: Colors.black.withOpacity(0.5),
                                    borderRadius: const BorderRadius.only(
                                      bottomLeft: Radius.circular(8),
                                      topRight: Radius.circular(7),
                                    ),
                                  ),
                                  child: const Icon(
                                    Icons.close,
                                    size: 16,
                                    color: Colors.white,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                ),

              // Voice note preview
              if (_voiceNote != null)
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  color: isDarkMode ? Colors.grey.shade800 : Colors.grey.shade200,
                  child: Row(
                    children: [
                      const Icon(Icons.mic, color: AppColors.primaryYellow),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'Voice note (${_voiceNoteDuration ?? 0}s)',
                          style: const TextStyle(fontSize: 14),
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.close, size: 18),
                        onPressed: _removeVoiceNote,
                      ),
                    ],
                  ),
                ),

              // Message input
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: isDarkMode ? AppColors.darkSurface : Colors.white,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.05),
                      blurRadius: 10,
                      offset: const Offset(0, -3),
                    ),
                  ],
                ),
                child: _isRecording
                    ? VoiceRecorder(
                        onStop: _onRecordingComplete,
                        onCancel: () {
                          setState(() {
                            _isRecording = false;
                          });
                        },
                      )
                    : Row(
                        children: [
                          IconButton(
                            icon: const Icon(Icons.add),
                            onPressed: () {
                              setState(() {
                                _showAttachmentOptions = !_showAttachmentOptions;
                              });
                            },
                          ),
                          Expanded(
                            child: TextField(
                              controller: _messageController,
                              decoration: InputDecoration(
                                hintText: 'Type a message...',
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(24),
                                  borderSide: BorderSide.none,
                                ),
                                filled: true,
                                fillColor: isDarkMode
                                    ? Colors.grey.shade800
                                    : Colors.grey.shade100,
                                contentPadding: const EdgeInsets.symmetric(
                                  horizontal: 16,
                                  vertical: 10,
                                ),
                              ),
                              minLines: 1,
                              maxLines: 5,
                            ),
                          ),
                          IconButton(
                            icon: const Icon(Icons.mic),
                            onPressed: () {
                              setState(() {
                                _isRecording = true;
                              });
                            },
                          ),
                          IconButton(
                            icon: const Icon(Icons.send),
                            onPressed: _sendMessage,
                          ),
                        ],
                      ),
              ),

              // Attachment options
              if (_showAttachmentOptions)
                Container(
                  color: isDarkMode ? AppColors.darkSurface : Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      _buildAttachmentOption(
                        icon: Icons.image,
                        label: 'Gallery',
                        onTap: _pickAttachment,
                        isDarkMode: isDarkMode,
                      ),
                      _buildAttachmentOption(
                        icon: Icons.camera_alt,
                        label: 'Camera',
                        onTap: _takePhoto,
                        isDarkMode: isDarkMode,
                      ),
                    ],
                  ),
                ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildAttachmentOption({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
    required bool isDarkMode,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                color: AppColors.primaryYellow.withOpacity(0.2),
                borderRadius: BorderRadius.circular(25),
              ),
              child: Icon(
                icon,
                color: AppColors.primaryYellow,
                size: 24,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: isDarkMode ? AppColors.darkTextSecondary : AppColors.lightTextSecondary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildForumInfoSheet(BuildContext context, Forum forum, bool isDarkMode) {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              if (forum.imageUrl != null)
                ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: CachedNetworkImage(
                    imageUrl: forum.imageUrl!,
                    width: 80,
                    height: 80,
                    fit: BoxFit.cover,
                    placeholder: (context, url) => Container(
                      color: AppColors.primaryYellow.withOpacity(0.2),
                      child: const Center(child: CircularProgressIndicator()),
                    ),
                    errorWidget: (context, url, error) => Container(
                      color: AppColors.primaryYellow.withOpacity(0.2),
                      child: const Icon(Icons.forum, color: AppColors.primaryYellow),
                    ),
                  ),
                )
              else
                Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    color: AppColors.primaryYellow.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(Icons.forum, size: 40, color: AppColors.primaryYellow),
                ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      forum.title,
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Created by ${forum.creatorName}',
                      style: TextStyle(
                        fontSize: 14,
                        color: isDarkMode ? AppColors.darkTextSecondary : AppColors.lightTextSecondary,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      timeago.format(forum.createdAt),
                      style: TextStyle(
                        fontSize: 12,
                        color: isDarkMode ? AppColors.darkTextSecondary : AppColors.lightTextSecondary,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          Text(
            'Description',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: isDarkMode ? AppColors.darkText : AppColors.lightText,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            forum.description,
            style: TextStyle(
              fontSize: 14,
              color: isDarkMode ? AppColors.darkText : AppColors.lightText,
            ),
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                child: _buildInfoItem(
                  icon: Icons.people,
                  label: 'Participants',
                  value: forum.participants.length.toString(),
                  isDarkMode: isDarkMode,
                ),
              ),
              Expanded(
                child: _buildInfoItem(
                  icon: Icons.message,
                  label: 'Messages',
                  value: forum.messageCount.toString(),
                  isDarkMode: isDarkMode,
                ),
              ),
            ],
          ),
          const SizedBox(height: 30),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () {
                ForumService.leaveForum(forum.id);
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Left forum')),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
              child: const Text('Leave Forum'),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoItem({
    required IconData icon,
    required String label,
    required String value,
    required bool isDarkMode,
  }) {
    return Column(
      children: [
        Icon(
          icon,
          size: 28,
          color: AppColors.primaryYellow,
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: isDarkMode ? AppColors.darkTextSecondary : AppColors.lightTextSecondary,
          ),
        ),
      ],
    );
  }
}