# Chat Implementation for Admin Panel

This document describes the comprehensive chat system implemented for the admin panel, allowing administrators to communicate with users through queries and participate in forum discussions.

## Overview

The chat system provides real-time messaging capabilities with support for:
- Text messages
- Voice notes
- Image sharing
- Message replies
- Real-time updates
- Message history
- Typing indicators

## Architecture

### Components

1. **ChatInterface** (`components/chat/chat-interface.tsx`)
   - Main chat component that orchestrates the entire chat experience
   - Handles message display, user interactions, and real-time updates
   - Supports both query and forum chat modes

2. **MessageBubble** (`components/chat/message-bubble.tsx`)
   - Individual message display component
   - Supports different message types (text, voice, image)
   - Handles message actions like reply and download

3. **MessageInput** (`components/chat/message-input.tsx`)
   - Input component for composing messages
   - Integrates voice recording and image upload
   - Supports message replies and typing indicators

4. **VoiceRecorder** (`components/chat/voice-recorder.tsx`)
   - Voice recording functionality
   - Audio playback controls
   - Recording duration tracking

5. **ImageUploader** (`components/chat/image-uploader.tsx`)
   - Drag-and-drop image upload
   - Multiple image selection
   - Image preview and management

### Services

1. **ChatService** (`lib/services/chat-service.ts`)
   - Firebase integration for real-time messaging
   - File upload handling (voice notes, images)
   - Message CRUD operations
   - Room management

### Hooks

1. **useChat** (`lib/hooks/use-chat.ts`)
   - React hook for chat functionality
   - Real-time message subscription
   - Message sending operations
   - State management

## Features

### Text Messaging
- Real-time text message exchange
- Message timestamps
- Read receipts
- Message editing indicators

### Voice Messages
- Record voice notes using browser's MediaRecorder API
- Playback controls with progress tracking
- Voice note duration display
- Audio file compression and upload

### Image Sharing
- Multiple image upload support
- Drag-and-drop interface
- Image preview before sending
- Automatic image compression
- Caption support

### Message Replies
- Reply to specific messages
- Visual reply indicators
- Context preservation

### Real-time Updates
- Live message synchronization
- Typing indicators
- Online status tracking
- Auto-scroll to new messages

## Usage

### Query Chat
Navigate to a query and click the chat button to open the chat interface:
```
/queries/[queryId]/chat
```

### Forum Chat
Navigate to a forum and click the chat button to participate in discussions:
```
/forums/[forumId]/chat
```

## Firebase Structure

### Messages Collection
```
queries/[queryId]/messages/[messageId]
forums/[forumId]/messages/[messageId]
```

### Message Document Structure
```typescript
{
  id: string
  text: string
  senderId: string
  senderName: string
  senderAvatar?: string
  timestamp: Date
  type: 'text' | 'voice' | 'image'
  voiceNote?: {
    url: string
    duration: number
  }
  imageUrls?: string[]
  isAdmin: boolean
  isEdited: boolean
  replyTo?: {
    id: string
    text: string
    senderName: string
  }
}
```

### Storage Structure
```
queries/[queryId]/voice/[filename]
queries/[queryId]/images/[filename]
forums/[forumId]/voice/[filename]
forums/[forumId]/images/[filename]
```

## Security

- Admin authentication required
- File upload validation
- Message content sanitization
- Rate limiting on message sending
- Secure file storage with Firebase Storage

## Performance Optimizations

- Lazy loading of message history
- Image compression before upload
- Efficient real-time subscriptions
- Memory management for audio/video elements
- Optimized re-renders with React.memo

## Browser Compatibility

- Modern browsers with MediaRecorder API support
- WebRTC for voice recording
- File API for image uploads
- WebSocket for real-time updates

## Future Enhancements

- Message search functionality
- File attachment support
- Message reactions/emojis
- Message threading
- Video call integration
- Message encryption
- Offline message queuing
- Push notifications

## Troubleshooting

### Common Issues

1. **Microphone Access Denied**
   - Ensure browser permissions are granted
   - Check HTTPS requirement for microphone access

2. **Images Not Uploading**
   - Verify Firebase Storage configuration
   - Check file size limits
   - Ensure proper authentication

3. **Messages Not Syncing**
   - Check Firebase Firestore rules
   - Verify network connectivity
   - Check browser console for errors

### Debug Mode
Enable debug logging by setting:
```javascript
localStorage.setItem('chat-debug', 'true')
```

## Testing

The chat system can be tested by:
- Creating actual queries and using the chat functionality
- Creating forums and participating in discussions
- Testing with multiple browser tabs for real-time sync
- Verifying voice recording and image upload features
