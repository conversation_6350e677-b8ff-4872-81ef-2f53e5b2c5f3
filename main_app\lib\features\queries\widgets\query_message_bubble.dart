import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../../core/core.dart';
import '../models/query_message.dart';
import 'package:timeago/timeago.dart' as timeago;

class QueryMessageBubble extends StatefulWidget {
  final QueryMessage message;
  final bool isMe;
  final bool showSender;
  final Function(String)? onEdit;
  final VoidCallback? onDelete;

  const QueryMessageBubble({
    super.key,
    required this.message,
    required this.isMe,
    this.showSender = true,
    this.onEdit,
    this.onDelete,
  });

  @override
  State<QueryMessageBubble> createState() => _QueryMessageBubbleState();
}

class _QueryMessageBubbleState extends State<QueryMessageBubble> {
  bool _isEditing = false;
  bool _showOptions = false;
  final TextEditingController _editingController = TextEditingController();

  @override
  void dispose() {
    _editingController.dispose();
    super.dispose();
  }

  void _startEditing() {
    _editingController.text = widget.message.text;
    setState(() {
      _isEditing = true;
      _showOptions = false;
    });
  }

  void _saveEdit() {
    if (_editingController.text.trim().isNotEmpty) {
      widget.onEdit?.call(_editingController.text.trim());
      setState(() {
        _isEditing = false;
      });
    }
  }

  void _cancelEditing() {
    setState(() {
      _isEditing = false;
    });
  }

  void _confirmDelete() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Message'),
        content: const Text('Are you sure you want to delete this message? This cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              widget.onDelete?.call();
            },
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final isSystemMessage = widget.message.isSystemMessage;

    // In main app: user messages on right, admin messages on left
    final isOnRight = widget.message.isAdmin ? false : widget.isMe;

    // Define colors based on who sent the message
    final bubbleColor = isSystemMessage
        ? (isDarkMode ? Colors.grey[800] : Colors.grey[200])
        : widget.message.isAdmin
            ? (isDarkMode ? AppColors.primaryYellow.withOpacity(0.8) : AppColors.primaryYellow.withOpacity(0.2))
            : widget.isMe
                ? AppColors.primaryYellow.withOpacity(0.9)
                : (isDarkMode ? AppColors.darkSurface : Colors.white);

    final textColor = isSystemMessage
        ? (isDarkMode ? Colors.grey[400] : Colors.grey[700])
        : widget.message.isAdmin
            ? (isDarkMode ? Colors.black : Colors.black)
            : widget.isMe
                ? Colors.black
                : (isDarkMode ? Colors.white : Colors.black);

    final bubbleBorder = isSystemMessage
        ? null
        : isOnRight
            ? BorderRadius.only(
                topLeft: const Radius.circular(16),
                topRight: Radius.circular(widget.showSender ? 16 : 4),
                bottomLeft: const Radius.circular(16),
                bottomRight: const Radius.circular(4),
              )
            : BorderRadius.only(
                topLeft: Radius.circular(widget.showSender ? 16 : 4),
                topRight: const Radius.circular(16),
                bottomLeft: const Radius.circular(4),
                bottomRight: const Radius.circular(16),
              );

    return Stack(
      children: [
        Row(
          mainAxisAlignment: isOnRight ? MainAxisAlignment.end : MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
        // Avatar for messages on left side (not shown for right side messages)
        if (!isOnRight && widget.showSender && !isSystemMessage)
          Container(
            width: 32,
            height: 32,
            margin: const EdgeInsets.only(right: 8),
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: Colors.grey[300],
            ),
            child: widget.message.senderPhotoUrl != null
                ? ClipRRect(
                    borderRadius: BorderRadius.circular(16),
                    child: CachedNetworkImage(
                      imageUrl: widget.message.senderPhotoUrl!,
                      fit: BoxFit.cover,
                      placeholder: (context, url) => Container(
                        color: Colors.grey[300],
                        child: const Icon(Icons.person, color: Colors.white),
                      ),
                      errorWidget: (context, url, error) => const Icon(
                        Icons.person,
                        color: Colors.white,
                      ),
                    ),
                  )
                : const Icon(Icons.person, color: Colors.white),
          ),

        // Message content
        Flexible(
          child: Column(
            crossAxisAlignment: isOnRight ? CrossAxisAlignment.end : CrossAxisAlignment.start,
            children: [
              // Sender name with admin badge
              if (widget.showSender && !isOnRight && !isSystemMessage)
                Padding(
                  padding: const EdgeInsets.only(left: 4, bottom: 2),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        widget.message.senderName,
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: isDarkMode ? Colors.grey[400] : Colors.grey[700],
                        ),
                      ),
                      if (widget.message.isAdmin) ...[
                        const SizedBox(width: 4),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: AppColors.primaryYellow,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: const Text(
                            'Admin',
                            style: TextStyle(
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                              color: Colors.black,
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                ),

              // Message bubble
              GestureDetector(
                onLongPress: () {
                  if (widget.isMe && !isSystemMessage && !widget.message.isAdmin && !_isEditing) {
                    setState(() {
                      _showOptions = true;
                    });
                  }
                },
                onTap: () {
                  if (_showOptions) {
                    setState(() {
                      _showOptions = false;
                    });
                  }
                },
                child: Container(
                  constraints: BoxConstraints(
                    maxWidth: MediaQuery.of(context).size.width * 0.75,
                  ),
                  padding: EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: isSystemMessage ? 8 : 10,
                  ),
                  decoration: BoxDecoration(
                    color: bubbleColor,
                    borderRadius: bubbleBorder ?? BorderRadius.circular(16),
                    border: isSystemMessage
                        ? Border.all(
                            color: isDarkMode ? Colors.grey[700]! : Colors.grey[300]!,
                            width: 1,
                          )
                        : null,
                  ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Text content
                    if (_isEditing)
                      // Editing field
                      TextField(
                        controller: _editingController,
                        autofocus: true,
                        decoration: InputDecoration(
                          contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(
                              color: isDarkMode ? Colors.grey[700]! : Colors.grey[300]!,
                            ),
                          ),
                          suffixIcon: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              IconButton(
                                icon: const Icon(Icons.check, size: 18),
                                onPressed: _saveEdit,
                              ),
                              IconButton(
                                icon: const Icon(Icons.close, size: 18),
                                onPressed: _cancelEditing,
                              ),
                            ],
                          ),
                        ),
                        maxLines: 3,
                        minLines: 1,
                      )
                    else if (widget.message.text.isNotEmpty)
                      Text(
                        widget.message.text,
                        style: TextStyle(
                          color: textColor,
                          fontSize: 15,
                        ),
                      ),

                    // Images
                    if (widget.message.imageUrls.isNotEmpty)
                      Padding(
                        padding: EdgeInsets.only(
                          top: widget.message.text.isNotEmpty ? 8 : 0,
                        ),
                        child: widget.message.imageUrls.length == 1
                            ? ClipRRect(
                                borderRadius: BorderRadius.circular(12),
                                child: CachedNetworkImage(
                                  imageUrl: widget.message.imageUrls.first,
                                  fit: BoxFit.cover,
                                  maxHeightDiskCache: 800,
                                  placeholder: (context, url) => Container(
                                    height: 150,
                                    color: Colors.grey[300],
                                    child: const Center(
                                      child: CircularProgressIndicator(),
                                    ),
                                  ),
                                  errorWidget: (context, url, error) => Container(
                                    height: 150,
                                    color: Colors.grey[300],
                                    child: const Icon(
                                      Icons.error,
                                      color: Colors.white,
                                    ),
                                  ),
                                ),
                              )
                            : Wrap(
                                spacing: 5,
                                runSpacing: 5,
                                children: widget.message.imageUrls.map((url) {
                                  return ClipRRect(
                                    borderRadius: BorderRadius.circular(8),
                                    child: CachedNetworkImage(
                                      imageUrl: url,
                                      fit: BoxFit.cover,
                                      width: 100,
                                      height: 100,
                                      placeholder: (context, url) => Container(
                                        color: Colors.grey[300],
                                        width: 100,
                                        height: 100,
                                        child: const Center(
                                          child: CircularProgressIndicator(
                                            strokeWidth: 2,
                                          ),
                                        ),
                                      ),
                                      errorWidget: (context, url, error) =>
                                          Container(
                                        color: Colors.grey[300],
                                        width: 100,
                                        height: 100,
                                        child: const Icon(
                                          Icons.error,
                                          color: Colors.white,
                                        ),
                                      ),
                                    ),
                                  );
                                }).toList(),
                              ),
                      ),



                    // Timestamp & edited indicator
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        if (widget.message.isEdited)
                          Text(
                            'Edited • ',
                            style: TextStyle(
                              fontSize: 10,
                              color: textColor?.withOpacity(0.6),
                            ),
                          ),
                        Text(
                          timeago.format(widget.message.timestamp, locale: 'en_short'),
                          style: TextStyle(
                            fontSize: 10,
                            color: textColor?.withOpacity(0.6),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
                ),
              ),
            ],
          ),
        ),
        ],
        ),

        // Hold-to-show options for my messages (but not admin messages)
        if (_showOptions && widget.isMe && !isSystemMessage && !widget.message.isAdmin && !_isEditing)
          Positioned(
            bottom: 0,
            right: isOnRight ? 0 : null,
            left: isOnRight ? null : 0,
            child: Container(
              margin: const EdgeInsets.only(top: 8),
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: isDarkMode ? Colors.grey[800] : Colors.white,
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Edit option
                  if (widget.onEdit != null)
                    IconButton(
                      icon: const Icon(
                        Icons.edit,
                        size: 20,
                        color: AppColors.primaryYellow,
                      ),
                      onPressed: () {
                        _startEditing();
                      },
                      tooltip: 'Edit',
                    ),

                  // Delete option
                  if (widget.onDelete != null)
                    IconButton(
                      icon: const Icon(
                        Icons.delete,
                        size: 20,
                        color: Colors.red,
                      ),
                      onPressed: () {
                        setState(() {
                          _showOptions = false;
                        });
                        _confirmDelete();
                      },
                      tooltip: 'Delete',
                    ),
                ],
              ),
            ),
          ),
      ],
    );
  }
}