import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';

import 'secure_storage.dart';
import 'encryption_service.dart';

/// Central manager for security-related functions
class SecurityManager {
  static final SecurityManager _instance = SecurityManager._internal();
  
  /// Singleton instance of SecurityManager
  factory SecurityManager() => _instance;
  
  SecurityManager._internal();
  
  /// The secure storage service
  final SecureStorage _secureStorage = SecureStorage();
  
  /// The encryption service
  final EncryptionService _encryptionService = EncryptionService();
  
  /// Initialize security services
  Future<void> initialize() async {
    await _encryptionService.initializeEncryptionKey();
    
    // Register security services with GetIt if using dependency injection
    final getIt = GetIt.instance;
    if (!getIt.isRegistered<SecureStorage>()) {
      getIt.registerSingleton<SecureStorage>(_secureStorage);
    }
    if (!getIt.isRegistered<EncryptionService>()) {
      getIt.registerSingleton<EncryptionService>(_encryptionService);
    }
  }
  
  /// Get secure storage service
  SecureStorage get secureStorage => _secureStorage;
  
  /// Get encryption service
  EncryptionService get encryptionService => _encryptionService;
  
  /// Store authentication token securely
  Future<void> storeAuthToken(String token) async {
    await _secureStorage.storeAuthToken(token);
  }
  
  /// Retrieve authentication token
  Future<String?> getAuthToken() async {
    return await _secureStorage.getAuthToken();
  }
  
  /// Clear authentication data
  Future<void> clearAuthData() async {
    await _secureStorage.clearAuthData();
  }
  
  /// Securely store sensitive data with encryption
  Future<void> storeSensitiveData(String key, String value) async {
    final encryptedValue = await _encryptionService.encryptData(value);
    await _secureStorage.storeValue(key, encryptedValue);
  }
  
  /// Retrieve and decrypt sensitive data
  Future<String?> getSensitiveData(String key) async {
    final encryptedValue = await _secureStorage.getValue(key);
    if (encryptedValue == null) return null;
    
    try {
      return await _encryptionService.decrypt(encryptedValue);
    } catch (e) {
      if (kDebugMode) {
        print('Error decrypting data: $e');
      }
      return null;
    }
  }
  
  /// Securely store a map of sensitive data with encryption
  Future<void> storeSensitiveMap(String key, Map<String, dynamic> data) async {
    final encryptedData = await _encryptionService.encryptMap(data);
    await _secureStorage.storeValue(key, encryptedData);
  }
  
  /// Retrieve and decrypt a map of sensitive data
  Future<Map<String, dynamic>?> getSensitiveMap(String key) async {
    final encryptedData = await _secureStorage.getValue(key);
    if (encryptedData == null) return null;
    
    try {
      return await _encryptionService.decryptMap(encryptedData);
    } catch (e) {
      if (kDebugMode) {
        print('Error decrypting map data: $e');
      }
      return null;
    }
  }
  
  /// Hash a string using SHA-256
  String hashData(String input) {
    return _encryptionService.hashString(input);
  }
  
  /// Verify if a plaintext matches a hashed value
  bool verifyHash(String plaintext, String hashedValue) {
    return _encryptionService.verifyPassword(plaintext, hashedValue);
  }
  
  /// Delete sensitive data
  Future<void> deleteSensitiveData(String key) async {
    await _secureStorage.deleteValue(key);
  }
} 