import { useState, useEffect } from 'react'
import { collection, query, orderBy, onSnapshot, doc, updateDoc, deleteDoc, addDoc, Timestamp } from 'firebase/firestore'
import { db } from '@/lib/firebase/config'

interface NewsArticle {
  id: string
  title: string
  content: string
  excerpt: string
  category: 'Car Prices' | 'Industry News' | 'Fuel Prices' | 'Taxes & Duties' | 'Road Safety' | 'Traffic Updates' | 'Accident Reports' | 'General'
  status: 'draft' | 'published' | 'archived'
  featuredImage?: string
  author: string
  tags: string[]
  publishedAt?: Date
  createdAt: Date
  updatedAt: Date
  viewsCount: number
  likesCount: number
  isFeatured: boolean
  isBreaking: boolean
  seoTitle?: string
  seoDescription?: string
  // Main app compatible fields
  description: string
  url: string
  source: string
  imageUrl: string
  views: number
  pinned: boolean
  adminComment?: string
  reactionCounts: { [key: string]: number }
}

export function useNews() {
  const [articles, setArticles] = useState<NewsArticle[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const articlesQuery = query(
      collection(db, 'news_articles'),
      orderBy('createdAt', 'desc')
    )

    const unsubscribe = onSnapshot(
      articlesQuery,
      (snapshot) => {
        const articlesData = snapshot.docs.map(doc => {
          const data = doc.data()
          return {
            id: doc.id,
            title: data.title || '',
            content: data.content || '',
            excerpt: data.excerpt || '',
            category: data.category || 'general',
            status: data.status || 'draft',
            featuredImage: data.featuredImage,
            author: data.author || 'Admin',
            tags: data.tags || [],
            publishedAt: data.publishedAt?.toDate(),
            createdAt: data.createdAt?.toDate() || new Date(),
            updatedAt: data.updatedAt?.toDate() || new Date(),
            viewsCount: data.viewsCount || 0,
            likesCount: data.likesCount || 0,
            isFeatured: data.isFeatured || false,
            isBreaking: data.isBreaking || false,
            seoTitle: data.seoTitle,
            seoDescription: data.seoDescription,
          } as NewsArticle
        })

        setArticles(articlesData)
        setIsLoading(false)
        setError(null)
      },
      (error) => {
        console.error('Error fetching articles:', error)
        setError('Failed to fetch articles')
        setIsLoading(false)
      }
    )

    return () => unsubscribe()
  }, [])

  const updateArticle = async (articleId: string, updates: Partial<NewsArticle>) => {
    try {
      const articleRef = doc(db, 'news_articles', articleId)
      const updateData: any = { ...updates }

      // Convert dates to Firestore timestamps
      if (updateData.createdAt instanceof Date) {
        updateData.createdAt = Timestamp.fromDate(updateData.createdAt)
      }
      if (updateData.updatedAt instanceof Date) {
        updateData.updatedAt = Timestamp.fromDate(updateData.updatedAt)
      }
      if (updateData.publishedAt instanceof Date) {
        updateData.publishedAt = Timestamp.fromDate(updateData.publishedAt)
      }

      updateData.updatedAt = Timestamp.now()

      await updateDoc(articleRef, updateData)
    } catch (error) {
      console.error('Error updating article:', error)
      throw error
    }
  }

  const deleteArticle = async (articleId: string) => {
    try {
      const articleRef = doc(db, 'news_articles', articleId)
      await deleteDoc(articleRef)
    } catch (error) {
      console.error('Error deleting article:', error)
      throw error
    }
  }

  const createArticle = async (articleData: Partial<NewsArticle>) => {
    try {
      // Generate a unique URL for admin articles
      const adminUrl = `admin://article/${Date.now()}`

      const newArticleData = {
        ...articleData,
        // Main app compatible fields
        description: articleData.excerpt || '',
        url: adminUrl,
        source: 'Admin',
        imageUrl: articleData.featuredImage || '',
        views: 0,
        pinned: true, // Admin articles are always pinned to top
        adminComment: null,
        reactionCounts: {},
        // Store content for admin articles
        adminContent: articleData.content || '',
        // Admin specific fields
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now(),
        viewsCount: 0,
        likesCount: 0,
        publishedAt: articleData.status === 'published' ? Timestamp.now() : null,
      }

      await addDoc(collection(db, 'news_articles'), newArticleData)
    } catch (error) {
      console.error('Error creating article:', error)
      throw error
    }
  }

  const publishArticle = async (articleId: string) => {
    try {
      const articleRef = doc(db, 'news_articles', articleId)
      await updateDoc(articleRef, {
        status: 'published',
        publishedAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      })
    } catch (error) {
      console.error('Error publishing article:', error)
      throw error
    }
  }

  return {
    articles,
    isLoading,
    error,
    updateArticle,
    deleteArticle,
    createArticle,
    publishArticle,
  }
}
