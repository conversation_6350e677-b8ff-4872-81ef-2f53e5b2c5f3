#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function checkFile(filePath, description) {
  const fullPath = path.join(__dirname, '..', filePath);
  if (fs.existsSync(fullPath)) {
    log(`${colors.green}✅ ${description}${colors.reset}`);
    return true;
  } else {
    log(`${colors.red}❌ ${description} - File not found: ${filePath}${colors.reset}`);
    return false;
  }
}

function runCommand(command, description) {
  try {
    execSync(command, { stdio: 'pipe', cwd: path.join(__dirname, '..') });
    log(`${colors.green}✅ ${description}${colors.reset}`);
    return true;
  } catch (error) {
    log(`${colors.red}❌ ${description} - Failed${colors.reset}`);
    return false;
  }
}

function checkEnvironmentVariables() {
  log(`${colors.blue}🔍 Checking environment variables...${colors.reset}`);
  
  const envFile = path.join(__dirname, '..', '.env.production');
  if (!fs.existsSync(envFile)) {
    log(`${colors.red}❌ .env.production file not found${colors.reset}`);
    return false;
  }
  
  const envContent = fs.readFileSync(envFile, 'utf8');
  const requiredVars = [
    'NEXT_PUBLIC_FIREBASE_API_KEY',
    'NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN',
    'NEXT_PUBLIC_FIREBASE_PROJECT_ID',
    'NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET',
    'NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID',
    'NEXT_PUBLIC_FIREBASE_APP_ID'
  ];
  
  let allVarsPresent = true;
  requiredVars.forEach(varName => {
    if (envContent.includes(`${varName}=`) && !envContent.includes(`${varName}=\n`)) {
      log(`${colors.green}✅ ${varName} is set${colors.reset}`);
    } else {
      log(`${colors.red}❌ ${varName} is missing or empty${colors.reset}`);
      allVarsPresent = false;
    }
  });
  
  return allVarsPresent;
}

function checkFirebaseConfig() {
  log(`${colors.blue}🔍 Checking Firebase configuration...${colors.reset}`);
  
  const configFile = path.join(__dirname, '..', 'lib', 'firebase', 'config.ts');
  if (!fs.existsSync(configFile)) {
    log(`${colors.red}❌ Firebase config file not found${colors.reset}`);
    return false;
  }
  
  const configContent = fs.readFileSync(configFile, 'utf8');
  if (configContent.includes('drive-on-b2af8')) {
    log(`${colors.green}✅ Firebase project ID is correct${colors.reset}`);
    return true;
  } else {
    log(`${colors.red}❌ Firebase project ID mismatch${colors.reset}`);
    return false;
  }
}

function checkPackageJson() {
  log(`${colors.blue}🔍 Checking package.json...${colors.reset}`);
  
  const packagePath = path.join(__dirname, '..', 'package.json');
  if (!fs.existsSync(packagePath)) {
    log(`${colors.red}❌ package.json not found${colors.reset}`);
    return false;
  }
  
  const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
  
  // Check required scripts
  const requiredScripts = ['build', 'deploy', 'deploy:staging', 'deploy:prod'];
  let allScriptsPresent = true;
  
  requiredScripts.forEach(script => {
    if (packageJson.scripts && packageJson.scripts[script]) {
      log(`${colors.green}✅ Script '${script}' is defined${colors.reset}`);
    } else {
      log(`${colors.red}❌ Script '${script}' is missing${colors.reset}`);
      allScriptsPresent = false;
    }
  });
  
  return allScriptsPresent;
}

function main() {
  log(`${colors.bright}${colors.magenta}🔍 Pre-Deployment Checklist${colors.reset}`);
  log(`${colors.cyan}================================${colors.reset}`);
  
  let allChecksPass = true;
  
  // File existence checks
  log(`\n${colors.yellow}📁 File Existence Checks${colors.reset}`);
  allChecksPass &= checkFile('package.json', 'package.json exists');
  allChecksPass &= checkFile('firebase.json', 'firebase.json exists');
  allChecksPass &= checkFile('.firebaserc', '.firebaserc exists');
  allChecksPass &= checkFile('next.config.js', 'next.config.js exists');
  allChecksPass &= checkFile('.env.production', '.env.production exists');
  allChecksPass &= checkFile('lib/firebase/config.ts', 'Firebase config exists');
  
  // Environment variables check
  log(`\n${colors.yellow}🌍 Environment Variables${colors.reset}`);
  allChecksPass &= checkEnvironmentVariables();
  
  // Firebase configuration check
  log(`\n${colors.yellow}🔥 Firebase Configuration${colors.reset}`);
  allChecksPass &= checkFirebaseConfig();
  
  // Package.json check
  log(`\n${colors.yellow}📦 Package Configuration${colors.reset}`);
  allChecksPass &= checkPackageJson();
  
  // Build test
  log(`\n${colors.yellow}🏗️ Build Test${colors.reset}`);
  allChecksPass &= runCommand('npm run type-check', 'TypeScript compilation');
  allChecksPass &= runCommand('npm run lint', 'ESLint checks');
  
  // Firebase CLI check
  log(`\n${colors.yellow}🔧 Firebase CLI${colors.reset}`);
  allChecksPass &= runCommand('firebase --version', 'Firebase CLI installed');
  allChecksPass &= runCommand('firebase projects:list', 'Firebase authentication');
  
  // Final result
  log(`\n${colors.cyan}================================${colors.reset}`);
  if (allChecksPass) {
    log(`${colors.bright}${colors.green}🎉 All checks passed! Ready for deployment.${colors.reset}`);
    log(`${colors.yellow}Next steps:${colors.reset}`);
    log(`${colors.cyan}  • npm run deploy:staging (for staging)${colors.reset}`);
    log(`${colors.cyan}  • npm run deploy:prod (for production)${colors.reset}`);
    process.exit(0);
  } else {
    log(`${colors.bright}${colors.red}❌ Some checks failed. Please fix the issues before deploying.${colors.reset}`);
    process.exit(1);
  }
}

main();
