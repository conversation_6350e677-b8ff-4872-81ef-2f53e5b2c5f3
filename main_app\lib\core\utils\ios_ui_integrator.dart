import 'package:flutter/material.dart';
import '../theme/ios_theme.dart';
import '../theme/colors.dart';

/// A utility class to help integrate iOS-style UI with the existing app
class IOSUIIntegrator {
  /// Apply iOS-style theme to the existing app theme
  static ThemeData applyIOSTheme(ThemeData baseTheme) {
    // Check if we're in dark mode
    final isDarkMode = baseTheme.brightness == Brightness.dark;

    // Use appropriate colors based on the theme
    final backgroundColor = isDarkMode ? AppColors.darkBackground : IOSTheme.backgroundColor;
    final cardColor = isDarkMode ? AppColors.darkSurface : IOSTheme.cardColor;
    final textColor = isDarkMode ? AppColors.darkText : IOSTheme.textColor;
    final dividerColor = isDarkMode ? AppColors.darkDivider : IOSTheme.dividerColor;
    // Always use yellow theme color for consistency
    const primaryColor = AppColors.primaryYellow;

    return baseTheme.copyWith(
      // Colors - preserve dark mode colors
      primaryColor: primaryColor,
      scaffoldBackgroundColor: backgroundColor,
      cardColor: cardColor,
      dividerColor: dividerColor,

      // Text Theme
      textTheme: baseTheme.textTheme.copyWith(
        bodyLarge: TextStyle(
          color: textColor,
          fontSize: 17,
          fontWeight: FontWeight.normal,
          fontFamily: IOSTheme.fontFamily,
        ),
        bodyMedium: TextStyle(
          color: textColor,
          fontSize: 15,
          fontWeight: FontWeight.normal,
          fontFamily: IOSTheme.fontFamily,
        ),
        titleLarge: TextStyle(
          color: textColor,
          fontSize: 20,
          fontWeight: FontWeight.w600,
          fontFamily: IOSTheme.fontFamily,
        ),
        titleMedium: TextStyle(
          color: textColor,
          fontSize: 17,
          fontWeight: FontWeight.w600,
          fontFamily: IOSTheme.fontFamily,
        ),
      ),

      // App Bar Theme
      appBarTheme: AppBarTheme(
        backgroundColor: isDarkMode ? AppColors.primaryYellow : Colors.white,
        foregroundColor: isDarkMode ? AppColors.darkBackground : primaryColor,
        elevation: 0,
        centerTitle: true,
        titleTextStyle: TextStyle(
          color: isDarkMode ? AppColors.darkBackground : textColor,
          fontSize: 17,
          fontWeight: FontWeight.w600,
          fontFamily: IOSTheme.fontFamily,
        ),
        iconTheme: IconThemeData(
          color: isDarkMode ? AppColors.darkBackground : primaryColor,
        ),
      ),

      // Button Themes
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: primaryColor,
          foregroundColor: Colors.white,
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
          textStyle: const TextStyle(
            fontSize: 17,
            fontWeight: FontWeight.w400,
            fontFamily: IOSTheme.fontFamily,
          ),
        ),
      ),

      // Input Decoration
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: isDarkMode ? AppColors.darkSurface : Colors.white,
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: dividerColor, width: 0.5),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: dividerColor, width: 0.5),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppColors.primaryYellow, width: 1.0),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: isDarkMode ? AppColors.error : IOSTheme.errorColor, width: 1.0),
        ),
      ),

      // Card Theme
      cardTheme: CardThemeData(
        elevation: 0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        color: cardColor,
      ),

      // Dialog Theme
      dialogTheme: DialogThemeData(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(14),
        ),
        titleTextStyle: TextStyle(
          color: textColor,
          fontSize: 17,
          fontWeight: FontWeight.w600,
          fontFamily: IOSTheme.fontFamily,
        ),
        contentTextStyle: TextStyle(
          color: textColor,
          fontSize: 13,
          fontWeight: FontWeight.normal,
          fontFamily: IOSTheme.fontFamily,
        ),
      ),

      // Bottom Navigation Bar Theme
      bottomNavigationBarTheme: BottomNavigationBarThemeData(
        backgroundColor: isDarkMode ? AppColors.darkBackground : Colors.white,
        selectedItemColor: AppColors.primaryYellow,
        unselectedItemColor: isDarkMode ? AppColors.darkTextSecondary : IOSTheme.secondaryTextColor,
        type: BottomNavigationBarType.fixed,
        elevation: 8,
      ),
    );
  }

  /// Apply iOS-style scrolling physics to the app
  static ScrollBehavior getIOSScrollBehavior() {
    return const ScrollBehavior().copyWith(
      physics: const BouncingScrollPhysics(
        parent: AlwaysScrollableScrollPhysics(),
      ),
      scrollbars: false,
      overscroll: true,
    );
  }
}
