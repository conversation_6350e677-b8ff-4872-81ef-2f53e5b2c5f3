'use client'

import { useState } from 'react'
import { collection, addDoc, getDocs, Timestamp } from 'firebase/firestore'
import { db } from '@/lib/firebase/config'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

export function FirebaseTest() {
  const [testMessage, setTestMessage] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [result, setResult] = useState<string>('')
  const [messages, setMessages] = useState<any[]>([])

  const testFirebaseWrite = async () => {
    if (!testMessage.trim()) return

    try {
      setIsLoading(true)
      setResult('Testing Firebase write...')

      const testData = {
        text: testMessage,
        senderId: 'admin-test',
        senderName: 'Admin Test',
        timestamp: Timestamp.now(),
        isAdmin: true,
        isTest: true
      }

      console.log('Attempting to write to Firebase:', testData)

      const docRef = await addDoc(collection(db, 'test_messages'), testData)
      
      console.log('Document written with ID:', docRef.id)
      setResult(`✅ Success! Document written with ID: ${docRef.id}`)
      setTestMessage('')
    } catch (error) {
      console.error('Firebase write error:', error)
      setResult(`❌ Error: ${error}`)
    } finally {
      setIsLoading(false)
    }
  }

  const testFirebaseRead = async () => {
    try {
      setIsLoading(true)
      setResult('Testing Firebase read...')

      console.log('Attempting to read from Firebase...')

      const querySnapshot = await getDocs(collection(db, 'test_messages'))
      const docs = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }))

      console.log('Documents read:', docs)
      setMessages(docs)
      setResult(`✅ Success! Read ${docs.length} documents`)
    } catch (error) {
      console.error('Firebase read error:', error)
      setResult(`❌ Error: ${error}`)
    } finally {
      setIsLoading(false)
    }
  }

  const testQueryWrite = async () => {
    try {
      setIsLoading(true)
      setResult('Testing query message write...')

      const queryId = 'test-query-123'
      const messageData = {
        queryId: queryId,
        senderId: 'admin',
        senderName: 'Admin',
        senderAvatar: '',
        text: testMessage || 'Test admin message',
        timestamp: Timestamp.now(),
        isAdmin: true,
        attachments: [],
        isEdited: false,
        isPending: false,
        isUploading: false,
      }

      console.log('Attempting to write query message:', messageData)

      const docRef = await addDoc(collection(db, 'queries', queryId, 'messages'), messageData)
      
      console.log('Query message written with ID:', docRef.id)
      setResult(`✅ Success! Query message written with ID: ${docRef.id}`)
      setTestMessage('')
    } catch (error) {
      console.error('Query message write error:', error)
      setResult(`❌ Error: ${error}`)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>Firebase Connection Test</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex space-x-2">
          <Input
            value={testMessage}
            onChange={(e) => setTestMessage(e.target.value)}
            placeholder="Enter test message..."
            disabled={isLoading}
          />
        </div>

        <div className="flex space-x-2">
          <Button 
            onClick={testFirebaseWrite} 
            disabled={isLoading || !testMessage.trim()}
          >
            Test Write
          </Button>
          <Button 
            onClick={testFirebaseRead} 
            disabled={isLoading}
            variant="outline"
          >
            Test Read
          </Button>
          <Button 
            onClick={testQueryWrite} 
            disabled={isLoading}
            variant="secondary"
          >
            Test Query Message
          </Button>
        </div>

        {result && (
          <div className="p-3 bg-gray-100 rounded-md">
            <p className="text-sm">{result}</p>
          </div>
        )}

        {messages.length > 0 && (
          <div className="space-y-2">
            <h4 className="font-medium">Test Messages:</h4>
            {messages.map((msg) => (
              <div key={msg.id} className="p-2 bg-blue-50 rounded text-sm">
                <p><strong>ID:</strong> {msg.id}</p>
                <p><strong>Text:</strong> {msg.text}</p>
                <p><strong>Sender:</strong> {msg.senderName}</p>
                <p><strong>Timestamp:</strong> {msg.timestamp?.toDate?.()?.toLocaleString() || 'N/A'}</p>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
