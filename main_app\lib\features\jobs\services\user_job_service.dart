import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/job_model.dart';
import '../../../core/services/email_uniqueness_service.dart';
import '../../../core/utils/app_logger.dart';

class UserJobService {
  static final UserJobService _instance = UserJobService._internal();
  factory UserJobService() => _instance;
  UserJobService._internal();

  static UserJobService get instance => _instance;

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final AppLogger _logger = AppLogger('UserJobService');

  /// Check if current user has posted any jobs (by email) - Simple version
  Future<bool> hasUserPostedJob() async {
    try {
      // Get user's email using the same service as job creation
      final userEmail = await EmailUniquenessService.instance.getCurrentUserEmail();
      if (userEmail == null) {
        _logger.debug('No email found');
        return false;
      }

      _logger.debug('Checking for jobs with email: $userEmail');

      final querySnapshot = await _firestore
          .collection('jobs')
          .where('posterEmail', isEqualTo: userEmail)
          .limit(1)
          .get();

      final hasJob = querySnapshot.docs.isNotEmpty;
      _logger.debug('Email $userEmail has job: $hasJob');

      return hasJob;
    } catch (e) {
      _logger.error('Error checking user job', error: e);
      return false;
    }
  }

  /// Get current user's job if exists
  Future<JobModel?> getCurrentUserJob() async {
    try {
      final userEmail = await EmailUniquenessService.instance.getCurrentUserEmail();
      if (userEmail == null) return null;

      final querySnapshot = await _firestore
          .collection('jobs')
          .where('posterEmail', isEqualTo: userEmail)
          .limit(1)
          .get();

      if (querySnapshot.docs.isNotEmpty) {
        // Return the user's job regardless of status (active or suspended)
        // This allows the button to show for both active and suspended jobs
        final doc = querySnapshot.docs.first;
        final data = doc.data();
        final status = data['status'] as String?;

        // Don't return deleted jobs, but return active and suspended jobs
        if (status != 'deleted') {
          final job = JobModel.fromDocument(doc);
          _logger.info('Found job "${job.title}" with status "$status" for email $userEmail');
          return job;
        }
      }

      _logger.debug('No job found for email $userEmail');
      return null;
    } catch (e) {
      _logger.error('Error getting current user job', error: e);
      return null;
    }
  }

  /// Get current user's job as a stream for real-time updates
  Stream<JobModel?> getCurrentUserJobStream() {
    return Stream.fromFuture(_getUserEmail()).asyncExpand((email) {
      if (email == null) {
        _logger.debug('No user email found');
        return Stream.value(null);
      }

      _logger.debug('Querying jobs for email: $email');
      return _firestore
          .collection('jobs')
          .where('posterEmail', isEqualTo: email)
          .limit(1)
          .snapshots()
          .map((snapshot) {
            if (snapshot.docs.isNotEmpty) {
              final doc = snapshot.docs.first;
              final data = doc.data();
              final status = data['status'] as String?;

              // Don't return deleted jobs, but return active and suspended jobs
              if (status != 'deleted') {
                final job = JobModel.fromDocument(doc);
                _logger.info('Found job "${job.title}" with status "$status" for email $email');
                return job;
              }
            }
            _logger.debug('No job found for email $email');
            return null;
          });
    });
  }

  /// Get the user's most recent job (by email)
  Future<JobModel?> getUserLatestJob() async {
    try {
      // Get user's email using the same service as job creation
      final userEmail = await EmailUniquenessService.instance.getCurrentUserEmail();
      if (userEmail == null) return null;

      final querySnapshot = await _firestore
          .collection('jobs')
          .where('posterEmail', isEqualTo: userEmail)
          .orderBy('createdAt', descending: true)
          .limit(1)
          .get();

      if (querySnapshot.docs.isNotEmpty) {
        _logger.info('Found job for email $userEmail: ${querySnapshot.docs.first.data()['title']}');
        return JobModel.fromDocument(querySnapshot.docs.first);
      }
      _logger.debug('No job found for email $userEmail');
      return null;
    } catch (e) {
      _logger.error('Error getting user latest job', error: e);
      return null;
    }
  }

  /// Get all jobs posted by current user (by email)
  Stream<List<JobModel>> getUserJobsStream() {
    return Stream.fromFuture(_getUserEmail()).asyncExpand((email) {
      if (email == null) {
        _logger.debug('No user email found');
        return Stream.value([]);
      }

      _logger.debug('Querying jobs for email: $email');
      return _firestore
          .collection('jobs')
          .where('posterEmail', isEqualTo: email)
          .orderBy('createdAt', descending: true)
          .snapshots()
          .map((snapshot) {
            _logger.debug('Found ${snapshot.docs.length} jobs');
            return snapshot.docs
                .map((doc) => JobModel.fromDocument(doc))
                .toList();
          });
    });
  }

  /// Helper method to get user email consistently
  Future<String?> _getUserEmail() async {
    try {
      return await EmailUniquenessService.instance.getCurrentUserEmail();
    } catch (e) {
      _logger.error('Error getting user email', error: e);
      return null;
    }
  }

  /// Get count of unread messages for user's job
  Future<int> getUnreadMessagesCount(String jobId) async {
    try {
      final user = _auth.currentUser;
      if (user == null) return 0;

      // Get all chats where user is the job poster
      final chatsSnapshot = await _firestore
          .collection('chats')
          .where('participants', arrayContains: user.uid)
          .get();

      int unreadCount = 0;

      for (final chatDoc in chatsSnapshot.docs) {
        final chatData = chatDoc.data();
        final participants = List<String>.from(chatData['participants'] ?? []);

        // Check if this chat is related to the job (other participant contacted about this job)
        if (participants.length == 2) {
          // Count unread messages in this chat
          final messagesSnapshot = await _firestore
              .collection('chats')
              .doc(chatDoc.id)
              .collection('messages')
              .where('senderId', isNotEqualTo: user.uid)
              .where('isRead', isEqualTo: false)
              .get();

          unreadCount += messagesSnapshot.docs.length;
        }
      }

      return unreadCount;
    } catch (e) {
      _logger.error('Error getting unread messages count', error: e);
      return 0;
    }
  }

  /// Stream of unread messages count for real-time updates
  Stream<int> getUnreadMessagesCountStream(String jobId) {
    final user = _auth.currentUser;
    if (user == null) {
      return Stream.value(0);
    }

    return _firestore
        .collection('chats')
        .where('participants', arrayContains: user.uid)
        .snapshots()
        .asyncMap((chatsSnapshot) async {
      int unreadCount = 0;

      for (final chatDoc in chatsSnapshot.docs) {
        final chatData = chatDoc.data();
        final participants = List<String>.from(chatData['participants'] ?? []);

        if (participants.length == 2) {
          final messagesSnapshot = await _firestore
              .collection('chats')
              .doc(chatDoc.id)
              .collection('messages')
              .where('senderId', isNotEqualTo: user.uid)
              .where('isRead', isEqualTo: false)
              .get();

          unreadCount += messagesSnapshot.docs.length;
        }
      }

      return unreadCount;
    });
  }
}
