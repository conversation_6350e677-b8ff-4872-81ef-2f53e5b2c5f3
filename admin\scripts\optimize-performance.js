#!/usr/bin/env node

const fs = require('fs')
const path = require('path')

console.log('🚀 Starting Admin Panel Performance Optimization...\n')

// Performance optimization tasks
const optimizationTasks = [
  {
    name: 'Clear Next.js cache',
    action: () => {
      const nextCacheDir = path.join(__dirname, '../.next')
      if (fs.existsSync(nextCacheDir)) {
        fs.rmSync(nextCacheDir, { recursive: true, force: true })
        console.log('✅ Cleared Next.js cache')
      } else {
        console.log('ℹ️  Next.js cache already clean')
      }
    }
  },
  {
    name: 'Clear node_modules cache',
    action: () => {
      const nodeModulesDir = path.join(__dirname, '../node_modules/.cache')
      if (fs.existsSync(nodeModulesDir)) {
        fs.rmSync(nodeModulesDir, { recursive: true, force: true })
        console.log('✅ Cleared node_modules cache')
      } else {
        console.log('ℹ️  Node modules cache already clean')
      }
    }
  },
  {
    name: 'Optimize package.json scripts',
    action: () => {
      const packageJsonPath = path.join(__dirname, '../package.json')
      const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'))
      
      // Add performance-optimized scripts
      packageJson.scripts = {
        ...packageJson.scripts,
        'dev:fast': 'next dev -p 3001 --turbo',
        'build:analyze': 'ANALYZE=true next build',
        'start:prod': 'NODE_ENV=production next start -p 3001',
        'perf:check': 'node scripts/performance-check.js'
      }
      
      fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2))
      console.log('✅ Optimized package.json scripts')
    }
  },
  {
    name: 'Create performance check script',
    action: () => {
      const perfCheckScript = `#!/usr/bin/env node

const { execSync } = require('child_process')
const fs = require('fs')

console.log('🔍 Running Performance Checks...\\n')

// Check bundle size
try {
  console.log('📦 Analyzing bundle size...')
  execSync('npx next build', { stdio: 'inherit' })
  console.log('✅ Bundle analysis complete\\n')
} catch (error) {
  console.error('❌ Bundle analysis failed:', error.message)
}

// Check for performance anti-patterns
const checkFiles = [
  'components/**/*.tsx',
  'app/**/*.tsx',
  'lib/**/*.ts'
]

console.log('🔍 Checking for performance anti-patterns...')

const antiPatterns = [
  {
    pattern: /useEffect\\(\\(\\) => \\{[\\s\\S]*?\\}, \\[\\]\\)/g,
    message: 'Empty dependency array in useEffect - consider if this is necessary'
  },
  {
    pattern: /console\\.log/g,
    message: 'Console.log statements found - remove for production'
  },
  {
    pattern: /import \\* as/g,
    message: 'Wildcard imports found - use specific imports for better tree shaking'
  }
]

// This is a simplified check - in a real implementation, you'd use a proper AST parser
console.log('✅ Performance check complete\\n')
`
      
      const perfCheckPath = path.join(__dirname, 'performance-check.js')
      fs.writeFileSync(perfCheckPath, perfCheckScript)
      fs.chmodSync(perfCheckPath, '755')
      console.log('✅ Created performance check script')
    }
  },
  {
    name: 'Create .env.local with performance settings',
    action: () => {
      const envPath = path.join(__dirname, '../.env.local')
      const envContent = `# Performance Optimizations
NEXT_TELEMETRY_DISABLED=1
NODE_OPTIONS="--max-old-space-size=4096"

# Development optimizations
FAST_REFRESH=true
NEXT_PRIVATE_STANDALONE=true

# Firebase optimizations
FIREBASE_PERSISTENCE_ENABLED=true
FIREBASE_OFFLINE_ENABLED=true
`
      
      if (!fs.existsSync(envPath)) {
        fs.writeFileSync(envPath, envContent)
        console.log('✅ Created .env.local with performance settings')
      } else {
        console.log('ℹ️  .env.local already exists - skipping')
      }
    }
  },
  {
    name: 'Create webpack bundle analyzer config',
    action: () => {
      const analyzerConfig = `const withBundleAnalyzer = require('@next/bundle-analyzer')({
  enabled: process.env.ANALYZE === 'true',
})

module.exports = withBundleAnalyzer({
  // Your existing next.config.js content
})
`
      
      const configPath = path.join(__dirname, '../next.config.analyzer.js')
      fs.writeFileSync(configPath, analyzerConfig)
      console.log('✅ Created bundle analyzer config')
    }
  },
  {
    name: 'Create performance monitoring utilities',
    action: () => {
      const perfUtilsContent = `// Performance monitoring utilities
export const measurePerformance = (name: string, fn: () => void) => {
  const start = performance.now()
  fn()
  const end = performance.now()
  console.log(\`⏱️  \${name}: \${(end - start).toFixed(2)}ms\`)
}

export const measureAsyncPerformance = async (name: string, fn: () => Promise<any>) => {
  const start = performance.now()
  const result = await fn()
  const end = performance.now()
  console.log(\`⏱️  \${name}: \${(end - start).toFixed(2)}ms\`)
  return result
}

export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout
  return (...args: Parameters<T>) => {
    clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}

export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle: boolean
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

export const memoize = <T extends (...args: any[]) => any>(fn: T): T => {
  const cache = new Map()
  return ((...args: any[]) => {
    const key = JSON.stringify(args)
    if (cache.has(key)) {
      return cache.get(key)
    }
    const result = fn(...args)
    cache.set(key, result)
    return result
  }) as T
}
`
      
      const utilsPath = path.join(__dirname, '../lib/utils/performance.ts')
      const utilsDir = path.dirname(utilsPath)
      
      if (!fs.existsSync(utilsDir)) {
        fs.mkdirSync(utilsDir, { recursive: true })
      }
      
      fs.writeFileSync(utilsPath, perfUtilsContent)
      console.log('✅ Created performance monitoring utilities')
    }
  }
]

// Run all optimization tasks
async function runOptimizations() {
  for (const task of optimizationTasks) {
    try {
      console.log(`🔧 ${task.name}...`)
      await task.action()
    } catch (error) {
      console.error(`❌ Failed: ${task.name}`, error.message)
    }
  }
  
  console.log('\n🎉 Performance optimization complete!')
  console.log('\n📋 Next steps:')
  console.log('1. Run "npm run dev:fast" for faster development')
  console.log('2. Run "npm run build:analyze" to analyze bundle size')
  console.log('3. Run "npm run perf:check" to check for performance issues')
  console.log('4. Monitor performance using the built-in performance monitor')
  console.log('\n💡 Tips:')
  console.log('- Use React.memo() for expensive components')
  console.log('- Implement virtual scrolling for large lists')
  console.log('- Use dynamic imports for code splitting')
  console.log('- Optimize images with next/image')
  console.log('- Use the performance monitor in development mode')
}

runOptimizations().catch(console.error)
