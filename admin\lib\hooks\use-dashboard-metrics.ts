import { useState, useEffect } from 'react'
import { useUsers } from './use-users'
import { useDrivers } from './use-drivers'
import { usePartners } from './use-partners'
import { useJobs } from './use-jobs'
import { useQueries } from './use-queries'

interface DashboardMetrics {
  totalUsers: number
  activeUsers: number
  totalDrivers: number
  pendingDrivers: number
  verifiedDrivers: number
  rejectedDrivers: number
  totalPartners: number
  pendingPartners: number
  approvedPartners: number
  rejectedPartners: number
  totalJobs: number
  activeJobs: number
  pendingJobs: number
  totalQueries: number
  openQueries: number
  resolvedQueries: number
  userGrowth: number
  driverGrowth: number
  partnerGrowth: number
  jobGrowth: number
}

interface GrowthData {
  current: number
  previous: number
  growth: number
}

// Cache for dashboard metrics
let metricsCache: DashboardMetrics | null = null
let metricsCacheTimestamp = 0
const METRICS_CACHE_DURATION = 5 * 60 * 1000 // 5 minutes

export function useDashboardMetrics() {
  const [metrics, setMetrics] = useState<DashboardMetrics | null>(metricsCache)
  const [isLoading, setIsLoading] = useState(!metricsCache)
  const [error, setError] = useState<string | null>(null)

  // Only load data hooks when needed, with lazy loading
  const { users, isLoading: usersLoading } = useUsers()
  const { drivers, driverRequests, isLoading: driversLoading } = useDrivers()
  const { partners, isLoading: partnersLoading } = usePartners()
  const { jobs, isLoading: jobsLoading } = useJobs()
  const { queries, isLoading: queriesLoading } = useQueries()

  // Calculate growth percentage
  const calculateGrowth = (current: number, previous: number): number => {
    if (previous === 0) return current > 0 ? 100 : 0
    return ((current - previous) / previous) * 100
  }

  // Calculate growth data for the last 30 days
  const calculateGrowthData = (data: any[], dateField: string = 'createdAt'): GrowthData => {
    const now = new Date()
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
    const sixtyDaysAgo = new Date(now.getTime() - 60 * 24 * 60 * 60 * 1000)

    const currentPeriod = data.filter(item => {
      const itemDate = item[dateField]
      return itemDate >= thirtyDaysAgo && itemDate <= now
    }).length

    const previousPeriod = data.filter(item => {
      const itemDate = item[dateField]
      return itemDate >= sixtyDaysAgo && itemDate < thirtyDaysAgo
    }).length

    return {
      current: currentPeriod,
      previous: previousPeriod,
      growth: calculateGrowth(currentPeriod, previousPeriod)
    }
  }

  useEffect(() => {
    // Check if we have fresh cached metrics
    const now = Date.now()
    if (metricsCache && (now - metricsCacheTimestamp) < METRICS_CACHE_DURATION) {
      setMetrics(metricsCache)
      setIsLoading(false)
      return
    }

    // Wait for all data to load
    if (usersLoading || driversLoading || partnersLoading || jobsLoading || queriesLoading) {
      return
    }

    try {
      // Calculate user metrics
      const activeUsers = users?.filter(user => user.isActive).length || 0
      const userGrowthData = calculateGrowthData(users || [])

      // Calculate driver metrics
      const allDrivers = [...(drivers || []), ...(driverRequests || [])]
      const verifiedDrivers = drivers?.filter(driver => driver.status === 'verified').length || 0
      const pendingDrivers = driverRequests?.length || 0
      const rejectedDrivers = drivers?.filter(driver => driver.status === 'rejected').length || 0
      const driverGrowthData = calculateGrowthData(allDrivers)

      // Calculate partner metrics
      const approvedPartners = partners?.filter(partner => partner.status === 'approved').length || 0
      const pendingPartners = partners?.filter(partner => partner.status === 'pending').length || 0
      const rejectedPartners = partners?.filter(partner => partner.status === 'rejected').length || 0
      const partnerGrowthData = calculateGrowthData(partners || [])

      // Calculate job metrics
      const activeJobs = jobs?.filter(job => job.status === 'active' && job.isApproved).length || 0
      const pendingJobs = jobs?.filter(job => !job.isApproved).length || 0
      const jobGrowthData = calculateGrowthData(jobs || [])

      // Calculate query metrics
      const openQueries = queries?.filter(query =>
        query.status === 'open' || query.status === 'in-progress'
      ).length || 0
      const resolvedQueries = queries?.filter(query =>
        query.status === 'resolved' || query.status === 'closed'
      ).length || 0

      const dashboardMetrics: DashboardMetrics = {
        totalUsers: users?.length || 0,
        activeUsers,
        totalDrivers: allDrivers.length,
        pendingDrivers,
        verifiedDrivers,
        rejectedDrivers,
        totalPartners: partners?.length || 0,
        pendingPartners,
        approvedPartners,
        rejectedPartners,
        totalJobs: jobs?.length || 0,
        activeJobs,
        pendingJobs,
        totalQueries: queries?.length || 0,
        openQueries,
        resolvedQueries,
        userGrowth: userGrowthData.growth,
        driverGrowth: driverGrowthData.growth,
        partnerGrowth: partnerGrowthData.growth,
        jobGrowth: jobGrowthData.growth,
      }

      // Cache the metrics
      metricsCache = dashboardMetrics
      metricsCacheTimestamp = Date.now()

      setMetrics(dashboardMetrics)
      setIsLoading(false)
      setError(null)
    } catch (err) {
      console.error('Error calculating dashboard metrics:', err)
      setError('Failed to calculate metrics')
      setIsLoading(false)
    }
  }, [
    users, usersLoading,
    drivers, driverRequests, driversLoading,
    partners, partnersLoading,
    jobs, jobsLoading,
    queries, queriesLoading
  ])

  return {
    metrics,
    isLoading,
    error
  }
}
