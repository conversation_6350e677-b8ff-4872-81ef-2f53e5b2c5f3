import {
  collection,
  query,
  onSnapshot,
  orderBy,
  limit,
  where,
  Timestamp,
  addDoc,
  updateDoc,
  doc,
  getDocs
} from 'firebase/firestore'
import { db } from '@/lib/firebase/config'

export interface AdminNotification {
  id: string
  type: 'driver_request' | 'partner_request' | 'forum_message' | 'query' | 'user_registration' | 'system' | 'urgent'
  title: string
  message: string
  data?: any
  isRead: boolean
  priority: 'low' | 'medium' | 'high' | 'urgent'
  createdAt: Date
  relatedId?: string
  relatedCollection?: string
  actionUrl?: string
}

export interface NotificationSettings {
  soundEnabled: boolean
  browserNotifications: boolean
  emailNotifications: boolean
  driverRequestAlerts: boolean
  partnerRequestAlerts: boolean
  forumMessageAlerts: boolean
  queryAlerts: boolean
  userRegistrationAlerts: boolean
  systemAlerts: boolean
  urgentAlertsOnly: boolean
  quietHoursEnabled: boolean
  quietHoursStart: string
  quietHoursEnd: string
}

class AdminNotificationService {
  private static instance: AdminNotificationService
  private notifications: AdminNotification[] = []
  private unreadCount = 0
  private listeners: ((notifications: AdminNotification[]) => void)[] = []
  private unreadCountListeners: ((count: number) => void)[] = []
  private settings: NotificationSettings = {
    soundEnabled: true,
    browserNotifications: true,
    emailNotifications: true,
    driverRequestAlerts: true,
    partnerRequestAlerts: true,
    forumMessageAlerts: true,
    queryAlerts: true,
    userRegistrationAlerts: true,
    systemAlerts: true,
    urgentAlertsOnly: false,
    quietHoursEnabled: false,
    quietHoursStart: '22:00',
    quietHoursEnd: '08:00'
  }
  private unsubscribers: (() => void)[] = []
  private audio: HTMLAudioElement | null = null

  private constructor() {
    this.initializeAudio()
    this.requestBrowserPermission()
  }

  static getInstance(): AdminNotificationService {
    if (!AdminNotificationService.instance) {
      AdminNotificationService.instance = new AdminNotificationService()
    }
    return AdminNotificationService.instance
  }

  private initializeAudio() {
    try {
      // Create a simple notification sound using Web Audio API
      this.createNotificationSound()
    } catch (error) {
      console.warn('Could not initialize notification audio:', error)
    }
  }

  private createNotificationSound() {
    try {
      if (typeof window === 'undefined') return

      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()

      const playNotificationTone = () => {
        const oscillator = audioContext.createOscillator()
        const gainNode = audioContext.createGain()

        oscillator.connect(gainNode)
        gainNode.connect(audioContext.destination)

        oscillator.frequency.setValueAtTime(800, audioContext.currentTime)
        oscillator.frequency.setValueAtTime(600, audioContext.currentTime + 0.1)

        gainNode.gain.setValueAtTime(0, audioContext.currentTime)
        gainNode.gain.linearRampToValueAtTime(0.3, audioContext.currentTime + 0.01)
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3)

        oscillator.start(audioContext.currentTime)
        oscillator.stop(audioContext.currentTime + 0.3)
      }

      // Store the function for later use
      this.audio = { play: playNotificationTone } as any
    } catch (error) {
      console.warn('Could not create notification sound:', error)
    }
  }

  private async requestBrowserPermission() {
    if (typeof window === 'undefined') return

    if ('Notification' in window && Notification.permission === 'default') {
      await Notification.requestPermission()
    }
  }

  private playNotificationSound() {
    if (this.settings.soundEnabled && this.audio) {
      try {
        this.audio.play()
      } catch (error) {
        console.warn('Error playing notification sound:', error)
      }
    }
  }

  private showBrowserNotification(notification: AdminNotification) {
    if (typeof window === 'undefined') return

    if (!this.settings.browserNotifications || Notification.permission !== 'granted') {
      return
    }

    if (this.isQuietHours()) {
      return
    }

    const browserNotification = new Notification(notification.title, {
      body: notification.message,
      icon: '/icons/admin-icon.svg',
      badge: '/icons/admin-badge.svg',
      tag: notification.id,
      requireInteraction: notification.priority === 'urgent',
      silent: !this.settings.soundEnabled
    })

    browserNotification.onclick = () => {
      window.focus()
      if (notification.actionUrl) {
        window.location.href = notification.actionUrl
      }
      browserNotification.close()
    }

    // Auto close after 5 seconds for non-urgent notifications
    if (notification.priority !== 'urgent') {
      setTimeout(() => {
        browserNotification.close()
      }, 5000)
    }
  }

  private isQuietHours(): boolean {
    if (!this.settings.quietHoursEnabled) return false

    const now = new Date()
    const currentTime = now.getHours() * 60 + now.getMinutes()

    const [startHour, startMin] = this.settings.quietHoursStart.split(':').map(Number)
    const [endHour, endMin] = this.settings.quietHoursEnd.split(':').map(Number)

    const startTime = startHour * 60 + startMin
    const endTime = endHour * 60 + endMin

    if (startTime <= endTime) {
      return currentTime >= startTime && currentTime <= endTime
    } else {
      // Quiet hours span midnight
      return currentTime >= startTime || currentTime <= endTime
    }
  }

  private async saveNotificationToFirestore(notification: Omit<AdminNotification, 'id'>) {
    try {
      await addDoc(collection(db, 'admin_notifications'), {
        ...notification,
        createdAt: Timestamp.fromDate(notification.createdAt)
      })
    } catch (error) {
      console.error('Error saving notification to Firestore:', error)
    }
  }

  async initialize() {
    try {
      // Cleanup any existing listeners first
      this.cleanup()

      // Load settings
      await this.loadSettings()

      // Set up real-time listeners for different collections with throttling
      this.setupDriverRequestListener()
      this.setupPartnerRequestListener()
      this.setupForumMessageListener()
      this.setupQueryListener()
      this.setupUserRegistrationListener()

      // Load existing notifications
      await this.loadExistingNotifications()

      console.log('Admin notification service initialized')
    } catch (error) {
      console.error('Error initializing notification service:', error)
    }
  }

  private setupDriverRequestListener() {
    if (!this.settings.driverRequestAlerts) return

    const driverRequestsQuery = query(
      collection(db, 'driver_requests'),
      orderBy('createdAt', 'desc'),
      limit(1)
    )

    const unsubscribe = onSnapshot(driverRequestsQuery, (snapshot) => {
      snapshot.docChanges().forEach((change) => {
        if (change.type === 'added') {
          const data = change.doc.data()
          const notification: Omit<AdminNotification, 'id'> = {
            type: 'driver_request',
            title: 'New Driver Application',
            message: `${data.name} has submitted a driver application from ${data.city}`,
            data: { driverId: change.doc.id, ...data },
            isRead: false,
            priority: 'high',
            createdAt: data.createdAt?.toDate() || new Date(),
            relatedId: change.doc.id,
            relatedCollection: 'driver_requests',
            actionUrl: `/drivers?tab=requests&highlight=${change.doc.id}`
          }
          this.addNotification(notification)
        }
      })
    })

    this.unsubscribers.push(unsubscribe)
  }

  private setupPartnerRequestListener() {
    if (!this.settings.partnerRequestAlerts) return

    const partnerRequestsQuery = query(
      collection(db, 'partners'),
      orderBy('createdAt', 'desc'),
      limit(1)
    )

    const unsubscribe = onSnapshot(partnerRequestsQuery, (snapshot) => {
      snapshot.docChanges().forEach((change) => {
        if (change.type === 'added') {
          const data = change.doc.data()
          // Only notify for pending requests
          if (data.status === 'pending') {
            const notification: Omit<AdminNotification, 'id'> = {
              type: 'partner_request',
              title: 'New Partner Request',
              message: `${data.companyName} has submitted a partnership request`,
              data: { partnerId: change.doc.id, ...data },
              isRead: false,
              priority: 'high',
              createdAt: data.createdAt?.toDate() || new Date(),
              relatedId: change.doc.id,
              relatedCollection: 'partners',
              actionUrl: `/partners?highlight=${change.doc.id}`
            }
            this.addNotification(notification)
          }
        }
      })
    })

    this.unsubscribers.push(unsubscribe)
  }

  private setupForumMessageListener() {
    if (!this.settings.forumMessageAlerts) return

    // Listen to all forums for new messages
    const forumsQuery = query(collection(db, 'forums'))

    const unsubscribe = onSnapshot(forumsQuery, (snapshot) => {
      snapshot.docChanges().forEach((change) => {
        if (change.type === 'modified') {
          const data = change.doc.data()
          const now = new Date()
          const lastMessageTime = data.lastMessageTime?.toDate()

          // Check if this is a recent message (within last 30 seconds)
          if (lastMessageTime && (now.getTime() - lastMessageTime.getTime()) < 30000) {
            const notification: Omit<AdminNotification, 'id'> = {
              type: 'forum_message',
              title: 'New Forum Message',
              message: `New message in "${data.title}" by ${data.lastSenderId}`,
              data: { forumId: change.doc.id, ...data },
              isRead: false,
              priority: 'medium',
              createdAt: lastMessageTime,
              relatedId: change.doc.id,
              relatedCollection: 'forums',
              actionUrl: `/forums/${change.doc.id}`
            }
            this.addNotification(notification)
          }
        }
      })
    })

    this.unsubscribers.push(unsubscribe)
  }

  private setupQueryListener() {
    if (!this.settings.queryAlerts) return

    const queriesQuery = query(
      collection(db, 'queries'),
      orderBy('createdAt', 'desc'),
      limit(5)
    )

    const unsubscribe = onSnapshot(queriesQuery, (snapshot) => {
      snapshot.docChanges().forEach((change) => {
        if (change.type === 'added') {
          const data = change.doc.data()
          const priority = data.priority === 'urgent' ? 'urgent' : 'high'

          const notification: Omit<AdminNotification, 'id'> = {
            type: 'query',
            title: 'New Support Query',
            message: `${data.userName} submitted: "${data.subject}" (${data.priority} priority)`,
            data: { queryId: change.doc.id, ...data },
            isRead: false,
            priority,
            createdAt: data.createdAt?.toDate() || new Date(),
            relatedId: change.doc.id,
            relatedCollection: 'queries',
            actionUrl: `/queries/${change.doc.id}`
          }
          this.addNotification(notification)
        }
      })
    })

    this.unsubscribers.push(unsubscribe)
  }

  private setupUserRegistrationListener() {
    if (!this.settings.userRegistrationAlerts) return

    const usersQuery = query(
      collection(db, 'users'),
      orderBy('createdAt', 'desc'),
      limit(1)
    )

    const unsubscribe = onSnapshot(usersQuery, (snapshot) => {
      snapshot.docChanges().forEach((change) => {
        if (change.type === 'added') {
          const data = change.doc.data()
          const notification: Omit<AdminNotification, 'id'> = {
            type: 'user_registration',
            title: 'New User Registration',
            message: `${data.displayName || data.email} has registered from ${data.city || 'Unknown location'}`,
            data: { userId: change.doc.id, ...data },
            isRead: false,
            priority: 'low',
            createdAt: data.createdAt?.toDate() || new Date(),
            relatedId: change.doc.id,
            relatedCollection: 'users',
            actionUrl: `/users?highlight=${change.doc.id}`
          }
          this.addNotification(notification)
        }
      })
    })

    this.unsubscribers.push(unsubscribe)
  }

  private async loadExistingNotifications() {
    try {
      const notificationsQuery = query(
        collection(db, 'admin_notifications'),
        orderBy('createdAt', 'desc'),
        limit(50)
      )

      const snapshot = await getDocs(notificationsQuery)
      const notifications: AdminNotification[] = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate() || new Date()
      } as AdminNotification))

      this.notifications = notifications
      this.updateUnreadCount()
      this.notifyListeners()
    } catch (error) {
      console.error('Error loading existing notifications:', error)
    }
  }

  private addNotification(notification: Omit<AdminNotification, 'id'>) {
    // Skip if in quiet hours and not urgent
    if (this.isQuietHours() && notification.priority !== 'urgent') {
      return
    }

    // Skip if urgent alerts only and not urgent
    if (this.settings.urgentAlertsOnly && notification.priority !== 'urgent') {
      return
    }

    const newNotification: AdminNotification = {
      ...notification,
      id: Date.now().toString()
    }

    this.notifications.unshift(newNotification)
    this.updateUnreadCount()
    this.notifyListeners()

    // Save to Firestore
    this.saveNotificationToFirestore(notification)

    // Show browser notification
    this.showBrowserNotification(newNotification)

    // Play sound
    this.playNotificationSound()
  }

  private updateUnreadCount() {
    this.unreadCount = this.notifications.filter(n => !n.isRead).length
    this.unreadCountListeners.forEach(listener => listener(this.unreadCount))
  }

  private notifyListeners() {
    this.listeners.forEach(listener => listener([...this.notifications]))
  }

  // Public methods
  subscribe(listener: (notifications: AdminNotification[]) => void) {
    this.listeners.push(listener)
    listener([...this.notifications])

    return () => {
      this.listeners = this.listeners.filter(l => l !== listener)
    }
  }

  subscribeToUnreadCount(listener: (count: number) => void) {
    this.unreadCountListeners.push(listener)
    listener(this.unreadCount)

    return () => {
      this.unreadCountListeners = this.unreadCountListeners.filter(l => l !== listener)
    }
  }

  async markAsRead(notificationId: string) {
    const notification = this.notifications.find(n => n.id === notificationId)
    if (notification && !notification.isRead) {
      notification.isRead = true
      this.updateUnreadCount()
      this.notifyListeners()

      // Update in Firestore
      try {
        await updateDoc(doc(db, 'admin_notifications', notificationId), {
          isRead: true
        })
      } catch (error) {
        console.error('Error marking notification as read:', error)
      }
    }
  }

  async markAllAsRead() {
    const unreadNotifications = this.notifications.filter(n => !n.isRead)

    unreadNotifications.forEach(notification => {
      notification.isRead = true
    })

    this.updateUnreadCount()
    this.notifyListeners()

    // Update in Firestore (batch update would be better for many notifications)
    for (const notification of unreadNotifications) {
      try {
        await updateDoc(doc(db, 'admin_notifications', notification.id), {
          isRead: true
        })
      } catch (error) {
        console.error('Error marking notification as read:', error)
      }
    }
  }

  async updateSettings(newSettings: Partial<NotificationSettings>) {
    this.settings = { ...this.settings, ...newSettings }

    // Save to localStorage
    localStorage.setItem('admin_notification_settings', JSON.stringify(this.settings))

    // Restart listeners with new settings
    this.cleanup()
    this.setupDriverRequestListener()
    this.setupPartnerRequestListener()
    this.setupForumMessageListener()
    this.setupQueryListener()
    this.setupUserRegistrationListener()
  }

  private async loadSettings() {
    try {
      const saved = localStorage.getItem('admin_notification_settings')
      if (saved) {
        this.settings = { ...this.settings, ...JSON.parse(saved) }
      }
    } catch (error) {
      console.error('Error loading notification settings:', error)
    }
  }

  getSettings(): NotificationSettings {
    return { ...this.settings }
  }

  getNotifications(): AdminNotification[] {
    return [...this.notifications]
  }

  getUnreadCount(): number {
    return this.unreadCount
  }

  cleanup() {
    this.unsubscribers.forEach(unsubscribe => unsubscribe())
    this.unsubscribers = []
  }

  // Test notification
  async sendTestNotification() {
    const notification: Omit<AdminNotification, 'id'> = {
      type: 'system',
      title: 'Test Notification',
      message: 'This is a test notification from the admin panel',
      isRead: false,
      priority: 'medium',
      createdAt: new Date()
    }
    this.addNotification(notification)
  }
}

export const notificationService = AdminNotificationService.getInstance()
