class Forum {
  final String id;
  final String title;
  final String? description;
  final DateTime createdAt;
  final int postCount;
  final String? imageUrl;

  Forum({
    required this.id,
    required this.title,
    this.description,
    required this.createdAt,
    this.postCount = 0,
    this.imageUrl,
  });

  // Create from Firestore document
  factory Forum.fromMap(Map<String, dynamic> map, String documentId) {
    return Forum(
      id: documentId,
      title: map['title'] ?? '',
      description: map['description'],
      createdAt: (map['createdAt'] as DateTime?) ?? DateTime.now(),
      postCount: map['postCount'] ?? 0,
      imageUrl: map['imageUrl'],
    );
  }

  // Convert to Map for Firestore
  Map<String, dynamic> toMap() {
    return {
      'title': title,
      'description': description,
      'createdAt': createdAt,
      'postCount': postCount,
      'imageUrl': imageUrl,
    };
  }
} 