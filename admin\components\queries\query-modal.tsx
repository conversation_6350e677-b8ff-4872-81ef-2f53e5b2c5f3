'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import {
  X,
  User,
  Mail,
  Phone,
  Calendar,
  MessageSquare,
  Paperclip,
  Send,
  CheckCircle,
  Clock,
  Star,
  AlertTriangle,
  FileText
} from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { formatDate, formatRelativeTime } from '@/lib/utils'

export interface ModalQuery {
  id: string
  subject: string
  description: string
  category: 'technical' | 'billing' | 'account' | 'general' | 'complaint' | 'feature-request'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  status: 'open' | 'in-progress' | 'resolved' | 'closed'
  user: {
    id: string
    name: string
    email: string
    phone?: string
    avatar?: string
  }
  assignedTo?: {
    id: string
    name: string
  }
  responses: {
    id: string
    message: string
    author: string
    isAdmin: boolean
    createdAt: Date
    attachments?: string[]
  }[]
  attachments: string[]
  createdAt: Date
  updatedAt: Date
  resolvedAt?: Date
  resolvedBy?: string
  resolutionNote?: string
  satisfactionRating?: number
  tags: string[]
}

interface QueryModalProps {
  query: ModalQuery | null
  mode: 'view' | 'respond'
  isOpen: boolean
  onClose: () => void
  onRespond: (response: string) => Promise<void>
  onResolve: (resolutionNote: string) => Promise<void>
  onAssign: (assigneeId: string) => Promise<void>
}

export function QueryModal({ query, mode, isOpen, onClose, onRespond, onResolve, onAssign }: QueryModalProps) {
  const [responseMessage, setResponseMessage] = useState('')
  const [resolutionNote, setResolutionNote] = useState('')
  const [selectedAssignee, setSelectedAssignee] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [showResolveForm, setShowResolveForm] = useState(false)
  const [showAssignForm, setShowAssignForm] = useState(false)

  // Mock assignees - in real app, fetch from API
  const assignees = [
    { id: '1', name: 'John Admin' },
    { id: '2', name: 'Sarah Support' },
    { id: '3', name: 'Mike Manager' },
  ]

  useEffect(() => {
    if (mode === 'respond') {
      setResponseMessage('')
    }
  }, [mode, query])

  const handleSendResponse = async () => {
    if (!responseMessage.trim()) return

    setIsLoading(true)
    try {
      await onRespond(responseMessage)
      setResponseMessage('')
    } catch (error) {
      console.error('Error sending response:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleResolve = async () => {
    if (!resolutionNote.trim()) return

    setIsLoading(true)
    try {
      await onResolve(resolutionNote)
      setShowResolveForm(false)
      setResolutionNote('')
    } catch (error) {
      console.error('Error resolving query:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleAssign = async () => {
    if (!selectedAssignee) return

    setIsLoading(true)
    try {
      await onAssign(selectedAssignee)
      setShowAssignForm(false)
      setSelectedAssignee('')
    } catch (error) {
      console.error('Error assigning query:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open':
        return 'error'
      case 'in-progress':
        return 'warning'
      case 'resolved':
        return 'success'
      case 'closed':
        return 'secondary'
      default:
        return 'secondary'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'low':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
      case 'high':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300'
      case 'urgent':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
      default:
        return 'bg-muted text-muted-foreground'
    }
  }

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'technical':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
      case 'billing':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
      case 'account':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300'
      case 'general':
        return 'bg-muted text-muted-foreground'
      case 'complaint':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
      case 'feature-request':
        return 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-300'
      default:
        return 'bg-muted text-muted-foreground'
    }
  }

  if (!isOpen || !query) return null

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="absolute inset-0 bg-black/50"
        onClick={onClose}
      />

      {/* Modal */}
      <motion.div
        initial={{ opacity: 0, scale: 0.95, y: 20 }}
        animate={{ opacity: 1, scale: 1, y: 0 }}
        exit={{ opacity: 0, scale: 0.95, y: 20 }}
        className="relative w-full max-w-4xl mx-4 bg-background rounded-lg shadow-xl max-h-[90vh] overflow-y-auto"
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-border">
          <div className="flex items-center space-x-3">
            <h2 className="text-xl font-semibold text-foreground">
              {mode === 'respond' ? 'Respond to Query' : 'Query Details'}
            </h2>
            <div className="flex space-x-2">
              <Badge variant={getStatusColor(query.status) as any}>
                {query.status}
              </Badge>
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(query.priority)}`}>
                {query.priority}
              </span>
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${getCategoryColor(query.category)}`}>
                {query.category}
              </span>
            </div>
          </div>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="w-4 h-4" />
          </Button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Query Header */}
          <div className="space-y-4">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">{query.subject}</h3>
              <p className="text-gray-700 whitespace-pre-wrap">{query.description}</p>
            </div>

            {/* User Info and Query Details */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">User Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
                      {query.user.avatar ? (
                        <img src={query.user.avatar} alt={query.user.name} className="w-10 h-10 rounded-full" />
                      ) : (
                        <User className="w-5 h-5 text-primary-600" />
                      )}
                    </div>
                    <div>
                      <p className="font-medium text-gray-900">{query.user.name}</p>
                      <p className="text-sm text-gray-500">User ID: {query.user.id}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Mail className="w-4 h-4 text-gray-400" />
                    <span className="text-sm">{query.user.email}</span>
                  </div>
                  {query.user.phone && (
                    <div className="flex items-center space-x-2">
                      <Phone className="w-4 h-4 text-gray-400" />
                      <span className="text-sm">{query.user.phone}</span>
                    </div>
                  )}
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Query Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <Calendar className="w-4 h-4 text-gray-400" />
                    <span className="text-sm">Created {formatDate(query.createdAt)}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Clock className="w-4 h-4 text-gray-400" />
                    <span className="text-sm">Last updated {formatRelativeTime(query.updatedAt)}</span>
                  </div>
                  {query.assignedTo && (
                    <div className="flex items-center space-x-2">
                      <User className="w-4 h-4 text-gray-400" />
                      <span className="text-sm">Assigned to {query.assignedTo.name}</span>
                    </div>
                  )}
                  {query.resolvedAt && (
                    <div className="flex items-center space-x-2">
                      <CheckCircle className="w-4 h-4 text-gray-400" />
                      <span className="text-sm">Resolved {formatDate(query.resolvedAt)}</span>
                    </div>
                  )}
                  {query.satisfactionRating && (
                    <div className="flex items-center space-x-2">
                      <Star className="w-4 h-4 text-gray-400" />
                      <span className="text-sm">Rating: {query.satisfactionRating}/5</span>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>

            {/* Attachments */}
            {query.attachments.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm flex items-center space-x-2">
                    <Paperclip className="w-4 h-4" />
                    <span>Attachments</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                    {query.attachments.map((attachment, index) => (
                      <div key={index} className="flex items-center space-x-2 p-2 border rounded">
                        <FileText className="w-4 h-4 text-gray-400" />
                        <span className="text-sm truncate">Attachment {index + 1}</span>
                        <Button size="sm" variant="ghost">Download</Button>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Tags */}
            {query.tags.length > 0 && (
              <div className="flex items-center space-x-2">
                <span className="text-sm font-medium text-gray-700">Tags:</span>
                <div className="flex flex-wrap gap-2">
                  {query.tags.map((tag, index) => (
                    <Badge key={index} variant="secondary">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Conversation History */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm flex items-center space-x-2">
                <MessageSquare className="w-4 h-4" />
                <span>Conversation History</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4 max-h-64 overflow-y-auto">
                {query.responses.map((response) => (
                  <div
                    key={response.id}
                    className={`flex ${response.isAdmin ? 'justify-end' : 'justify-start'}`}
                  >
                    <div
                      className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                        response.isAdmin
                          ? 'bg-primary-600 text-white'
                          : 'bg-gray-100 text-gray-900'
                      }`}
                    >
                      <p className="text-sm">{response.message}</p>
                      <div className="flex items-center justify-between mt-2">
                        <span className={`text-xs ${response.isAdmin ? 'text-white/80' : 'text-gray-500'}`}>
                          {response.author}
                        </span>
                        <span className={`text-xs ${response.isAdmin ? 'text-white/80' : 'text-gray-500'}`}>
                          {formatRelativeTime(response.createdAt)}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Response Form */}
          {mode === 'respond' && (
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Send Response</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="response">Your Response</Label>
                  <textarea
                    id="response"
                    value={responseMessage}
                    onChange={(e) => setResponseMessage(e.target.value)}
                    rows={4}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                    placeholder="Type your response here..."
                  />
                </div>
                <div className="flex justify-end space-x-3">
                  <Button
                    onClick={handleSendResponse}
                    disabled={!responseMessage.trim() || isLoading}
                    className="gradient-primary"
                  >
                    <Send className="w-4 h-4 mr-2" />
                    {isLoading ? 'Sending...' : 'Send Response'}
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Resolution Form */}
          {showResolveForm && (
            <Card>
              <CardHeader>
                <CardTitle className="text-sm text-green-600">Resolve Query</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="resolutionNote">Resolution Note</Label>
                  <textarea
                    id="resolutionNote"
                    value={resolutionNote}
                    onChange={(e) => setResolutionNote(e.target.value)}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                    placeholder="Describe how the query was resolved..."
                  />
                </div>
                <div className="flex space-x-3">
                  <Button
                    onClick={handleResolve}
                    disabled={!resolutionNote.trim() || isLoading}
                    className="bg-green-600 hover:bg-green-700"
                  >
                    {isLoading ? 'Resolving...' : 'Mark as Resolved'}
                  </Button>
                  <Button
                    onClick={() => setShowResolveForm(false)}
                    variant="outline"
                  >
                    Cancel
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Assignment Form */}
          {showAssignForm && (
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Assign Query</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="assignee">Assign to</Label>
                  <select
                    id="assignee"
                    value={selectedAssignee}
                    onChange={(e) => setSelectedAssignee(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                  >
                    <option value="">Select assignee</option>
                    {assignees.map((assignee) => (
                      <option key={assignee.id} value={assignee.id}>
                        {assignee.name}
                      </option>
                    ))}
                  </select>
                </div>
                <div className="flex space-x-3">
                  <Button
                    onClick={handleAssign}
                    disabled={!selectedAssignee || isLoading}
                    className="gradient-primary"
                  >
                    {isLoading ? 'Assigning...' : 'Assign Query'}
                  </Button>
                  <Button
                    onClick={() => setShowAssignForm(false)}
                    variant="outline"
                  >
                    Cancel
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Actions */}
          {mode === 'view' && query.status !== 'resolved' && query.status !== 'closed' && (
            <div className="flex justify-end space-x-3 pt-6 border-t">
              {!query.assignedTo && (
                <Button
                  onClick={() => setShowAssignForm(true)}
                  variant="outline"
                >
                  Assign Query
                </Button>
              )}
              <Button
                onClick={() => setShowResolveForm(true)}
                className="bg-green-600 hover:bg-green-700"
              >
                <CheckCircle className="w-4 h-4 mr-2" />
                Resolve Query
              </Button>
            </div>
          )}
        </div>
      </motion.div>
    </div>
  )
}
