# 🚀 CI/CD Pipeline Documentation

## Overview

This document describes the comprehensive CI/CD pipeline for the Drive-On main application, designed for automated testing, building, and deployment across multiple environments.

## 🏗️ Pipeline Architecture

### Workflows

1. **Continuous Integration** (`.github/workflows/ci.yml`)
   - Triggered on all pushes and pull requests
   - Runs code analysis, tests, and build verification
   - Includes security scanning and performance checks

2. **Android Deployment** (`.github/workflows/android-deploy.yml`)
   - Builds APK and App Bundle
   - Deploys to Firebase App Distribution
   - Supports staging and production environments

3. **Web Deployment** (`.github/workflows/web-deploy.yml`)
   - Builds web application
   - Deploys to Firebase Hosting
   - Creates preview deployments for PRs

4. **Release Management** (`.github/workflows/release.yml`)
   - Automated versioning and tagging
   - Creates GitHub releases
   - Deploys to production environments

## 🌍 Environments

### Development
- **Branch**: `develop`, feature branches
- **Purpose**: Active development and testing
- **Deployment**: Manual or on push to develop
- **URL**: Development Firebase channel

### Staging
- **Branch**: `develop` (auto), manual trigger
- **Purpose**: Pre-production testing
- **Deployment**: Automatic on develop branch
- **URL**: Staging Firebase channel

### Production
- **Branch**: `main`/`master`
- **Purpose**: Live application
- **Deployment**: Automatic on main branch push
- **URL**: https://drive-on-b2af8.web.app

## 🔧 Setup Instructions

### 1. GitHub Secrets Configuration

Add the following secrets to your GitHub repository:

```
FIREBASE_SERVICE_ACCOUNT_DRIVE_ON_B2AF8
FIREBASE_APP_ID
CODECOV_TOKEN (optional)
```

### 2. Firebase Configuration

Ensure your Firebase project (`drive-on-b2af8`) has:
- Hosting enabled
- App Distribution configured
- Service account with appropriate permissions

### 3. Environment Files

Create environment-specific configuration files:
- `.env.development`
- `.env.staging`
- `.env.production`

## 🚦 Pipeline Stages

### Stage 1: Code Quality
- **Formatting**: Dart format verification
- **Analysis**: Flutter analyze with no fatal warnings
- **Security**: Trivy vulnerability scanning
- **Dependencies**: Unused dependency detection

### Stage 2: Testing
- **Unit Tests**: Core business logic testing
- **Widget Tests**: UI component testing
- **Integration Tests**: End-to-end functionality
- **Coverage**: Code coverage reporting

### Stage 3: Building
- **Android**: APK and App Bundle generation
- **Web**: Optimized web build with PWA support
- **Optimization**: Code obfuscation and tree shaking
- **Artifacts**: Build artifact storage

### Stage 4: Deployment
- **Preview**: PR preview deployments
- **Staging**: Staging environment deployment
- **Production**: Live environment deployment
- **Distribution**: Firebase App Distribution

## 📱 Android Deployment

### Build Types
- **Debug**: Development builds with debugging enabled
- **Release**: Production-ready builds with optimizations

### Distribution
- **Firebase App Distribution**: Automatic distribution to testers
- **Tester Groups**: 
  - `staging-testers`: Staging environment testers
  - `production-testers`: Production release testers

### Build Configuration
```bash
flutter build apk --release \
  --build-number=$BUILD_NUMBER \
  --dart-define=ENVIRONMENT=production \
  --obfuscate \
  --split-debug-info=build/debug-info
```

## 🌐 Web Deployment

### Build Configuration
```bash
flutter build web --release \
  --web-renderer html \
  --dart-define=ENVIRONMENT=production \
  --source-maps \
  --pwa-strategy=offline-first
```

### Hosting Features
- **PWA Support**: Progressive Web App capabilities
- **Caching**: Optimized caching strategies
- **Security Headers**: XSS protection, CSP, etc.
- **Performance**: Optimized asset delivery

## 🔄 Automated Workflows

### Pull Request Workflow
1. Code analysis and formatting check
2. Run all tests with coverage
3. Build verification for all platforms
4. Security scanning
5. Create preview deployment
6. Performance analysis

### Main Branch Workflow
1. All PR checks pass
2. Build production artifacts
3. Deploy to production environments
4. Create GitHub release
5. Notify stakeholders
6. Update documentation

### Release Workflow
1. Version bump (manual or automatic)
2. Generate release notes
3. Build and test all platforms
4. Deploy to production
5. Create GitHub release with artifacts
6. Tag repository

## 📊 Monitoring and Notifications

### Build Status
- GitHub status checks on PRs
- Slack/Discord notifications (configurable)
- Email notifications for failures

### Performance Monitoring
- Bundle size tracking
- Build time optimization
- Test execution time monitoring

### Security Monitoring
- Vulnerability scanning results
- Dependency audit reports
- Security advisory notifications

## 🛠️ Manual Deployment

### Local Deployment Scripts

**Web Deployment:**
```bash
# Production deployment
scripts\deploy.bat --env production

# Staging deployment
scripts\deploy.bat --env staging --channel staging

# Skip tests for quick deployment
scripts\deploy.bat --env staging --skip-tests
```

**Android Deployment:**
```bash
# Deploy to Firebase App Distribution
scripts\deploy-android.bat
```

### Manual Triggers

Use GitHub Actions workflow dispatch for manual deployments:
1. Go to Actions tab in GitHub
2. Select the desired workflow
3. Click "Run workflow"
4. Choose environment and options

## 🔍 Troubleshooting

### Common Issues

1. **Build Failures**
   - Check Flutter version compatibility
   - Verify environment configuration
   - Review dependency conflicts

2. **Deployment Failures**
   - Verify Firebase permissions
   - Check service account configuration
   - Validate environment variables

3. **Test Failures**
   - Review test logs in GitHub Actions
   - Run tests locally for debugging
   - Check for environment-specific issues

### Debug Commands

```bash
# Local build verification
flutter clean && flutter pub get && flutter build web --release

# Test execution
flutter test --coverage

# Firebase deployment test
firebase deploy --only hosting --debug
```

## 📈 Performance Optimization

### Build Optimization
- Code obfuscation for release builds
- Tree shaking for unused code removal
- Asset optimization and compression
- Bundle size monitoring

### Deployment Optimization
- Parallel job execution
- Artifact caching
- Incremental builds
- Optimized Docker images

## 🔐 Security Considerations

### Secret Management
- GitHub Secrets for sensitive data
- Environment-specific configurations
- Service account key rotation

### Security Scanning
- Dependency vulnerability scanning
- Code security analysis
- Container security scanning
- License compliance checking

## 📚 Additional Resources

- [Firebase Hosting Documentation](https://firebase.google.com/docs/hosting)
- [GitHub Actions Documentation](https://docs.github.com/en/actions)
- [Flutter CI/CD Best Practices](https://docs.flutter.dev/deployment/cd)
- [Firebase App Distribution](https://firebase.google.com/docs/app-distribution)
