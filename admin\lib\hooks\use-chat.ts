import { useState, useEffect, useCallback, useRef } from 'react'
import { chatService, ChatMessage, ChatRoom } from '@/lib/services/chat-service'

interface UseChatProps {
  roomId: string
  roomType: 'query' | 'forum'
  currentUserId: string
  currentUserName: string
  currentUserAvatar?: string
}

export function useChat({ 
  roomId, 
  roomType, 
  currentUserId, 
  currentUserName, 
  currentUserAvatar 
}: UseChatProps) {
  const [messages, setMessages] = useState<ChatMessage[]>([])
  const [roomInfo, setRoomInfo] = useState<ChatRoom | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isSending, setIsSending] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [isTyping, setIsTyping] = useState(false)
  const [replyTo, setReplyTo] = useState<ChatMessage | null>(null)
  
  const unsubscribeRef = useRef<(() => void) | null>(null)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  // Scroll to bottom of messages
  const scrollToBottom = useCallback(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [])

  // Load room info
  useEffect(() => {
    const loadRoomInfo = async () => {
      try {
        const info = await chatService.getRoomInfo(roomId, roomType)
        setRoomInfo(info)
      } catch (err) {
        console.error('Error loading room info:', err)
        setError('Failed to load room information')
      }
    }

    loadRoomInfo()
  }, [roomId, roomType])

  // Subscribe to messages
  useEffect(() => {
    setIsLoading(true)
    setError(null)

    try {
      const unsubscribe = chatService.getMessages(roomId, roomType, (newMessages) => {
        setMessages(newMessages)
        setIsLoading(false)
        
        // Auto-scroll to bottom when new messages arrive
        setTimeout(scrollToBottom, 100)
      })

      unsubscribeRef.current = unsubscribe

      return () => {
        if (unsubscribeRef.current) {
          unsubscribeRef.current()
        }
      }
    } catch (err) {
      console.error('Error subscribing to messages:', err)
      setError('Failed to load messages')
      setIsLoading(false)
    }
  }, [roomId, roomType, scrollToBottom])

  // Mark messages as read when component mounts or messages change
  useEffect(() => {
    if (messages.length > 0 && currentUserId) {
      chatService.markAsRead(roomId, roomType, currentUserId)
    }
  }, [messages, roomId, roomType, currentUserId])

  // Send text message
  const sendMessage = useCallback(async (text: string) => {
    if (!text.trim() || isSending) return

    setIsSending(true)
    setError(null)

    try {
      await chatService.sendMessage(
        roomId,
        roomType,
        text,
        currentUserId,
        currentUserName,
        currentUserAvatar,
        true, // isAdmin
        replyTo ? {
          id: replyTo.id,
          text: replyTo.text,
          senderName: replyTo.senderName
        } : undefined
      )
      
      setReplyTo(null)
    } catch (err) {
      console.error('Error sending message:', err)
      setError('Failed to send message')
    } finally {
      setIsSending(false)
    }
  }, [roomId, roomType, currentUserId, currentUserName, currentUserAvatar, isSending, replyTo])

  // Send voice message
  const sendVoiceMessage = useCallback(async (audioBlob: Blob, duration: number) => {
    if (isSending) return

    setIsSending(true)
    setError(null)

    try {
      await chatService.sendVoiceMessage(
        roomId,
        roomType,
        audioBlob,
        duration,
        currentUserId,
        currentUserName,
        currentUserAvatar,
        true // isAdmin
      )
    } catch (err) {
      console.error('Error sending voice message:', err)
      setError('Failed to send voice message')
    } finally {
      setIsSending(false)
    }
  }, [roomId, roomType, currentUserId, currentUserName, currentUserAvatar, isSending])

  // Send image message
  const sendImageMessage = useCallback(async (images: File[], text?: string) => {
    if (images.length === 0 || isSending) return

    setIsSending(true)
    setError(null)

    try {
      await chatService.sendImageMessage(
        roomId,
        roomType,
        images,
        text || '',
        currentUserId,
        currentUserName,
        currentUserAvatar,
        true // isAdmin
      )
    } catch (err) {
      console.error('Error sending image message:', err)
      setError('Failed to send image message')
    } finally {
      setIsSending(false)
    }
  }, [roomId, roomType, currentUserId, currentUserName, currentUserAvatar, isSending])

  // Set typing indicator
  const setTypingIndicator = useCallback((typing: boolean) => {
    setIsTyping(typing)
  }, [])

  // Set reply message
  const setReplyMessage = useCallback((message: ChatMessage | null) => {
    setReplyTo(message)
  }, [])

  // Clear error
  const clearError = useCallback(() => {
    setError(null)
  }, [])

  return {
    messages,
    roomInfo,
    isLoading,
    isSending,
    error,
    isTyping,
    replyTo,
    messagesEndRef,
    sendMessage,
    sendVoiceMessage,
    sendImageMessage,
    setTypingIndicator,
    setReplyMessage,
    clearError,
    scrollToBottom
  }
}
