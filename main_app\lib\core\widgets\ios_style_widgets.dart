import 'package:flutter/material.dart';
import '../theme/ios_theme.dart';
import '../theme/colors.dart';

/// iOS-style button with rounded corners and no elevation
class IOSButton extends StatelessWidget {
  final String text;
  final VoidCallback onPressed;
  final bool isPrimary;
  final bool isDestructive;
  final bool isSmall;

  const IOSButton({
    Key? key,
    required this.text,
    required this.onPressed,
    this.isPrimary = true,
    this.isDestructive = false,
    this.isSmall = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    Color textColor;
    Color backgroundColor;

    if (isDestructive) {
      textColor = Colors.white;
      backgroundColor = IOSTheme.errorColor;
    } else if (isPrimary) {
      textColor = Colors.white;
      backgroundColor = AppColors.primaryYellow;
    } else {
      textColor = AppColors.primaryYellow;
      backgroundColor = Colors.transparent;
    }

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onPressed,
        borderRadius: BorderRadius.circular(8),
        child: Ink(
          decoration: BoxDecoration(
            color: backgroundColor,
            borderRadius: BorderRadius.circular(8),
            border: !isPrimary && !isDestructive
                ? Border.all(color: AppColors.primaryYellow)
                : null,
          ),
          padding: EdgeInsets.symmetric(
            horizontal: isSmall ? 12 : 16,
            vertical: isSmall ? 8 : 12,
          ),
          child: Text(
            text,
            style: TextStyle(
              color: textColor,
              fontSize: isSmall ? 15 : 17,
              fontWeight: FontWeight.w400,
              fontFamily: IOSTheme.fontFamily,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
  }
}

/// iOS-style text field with rounded corners
class IOSTextField extends StatelessWidget {
  final TextEditingController controller;
  final String? placeholder;
  final TextInputType? keyboardType;
  final bool obscureText;
  final Widget? prefix;
  final Widget? suffix;
  final int? maxLines;
  final int? minLines;
  final Function(String)? onChanged;
  final String? Function(String?)? validator;
  final bool autofocus;
  final FocusNode? focusNode;

  const IOSTextField({
    Key? key,
    required this.controller,
    this.placeholder,
    this.keyboardType,
    this.obscureText = false,
    this.prefix,
    this.suffix,
    this.maxLines = 1,
    this.minLines,
    this.onChanged,
    this.validator,
    this.autofocus = false,
    this.focusNode,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: controller,
      keyboardType: keyboardType,
      obscureText: obscureText,
      maxLines: maxLines,
      minLines: minLines,
      onChanged: onChanged,
      validator: validator,
      autofocus: autofocus,
      focusNode: focusNode,
      style: const TextStyle(
        fontSize: 17,
        fontFamily: IOSTheme.fontFamily,
      ),
      decoration: InputDecoration(
        hintText: placeholder,
        prefixIcon: prefix,
        suffixIcon: suffix,
        filled: true,
        fillColor: Colors.white,
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: IOSTheme.dividerColor, width: 0.5),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: IOSTheme.dividerColor, width: 0.5),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppColors.primaryYellow, width: 1.0),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: IOSTheme.errorColor, width: 1.0),
        ),
      ),
    );
  }
}

/// iOS-style switch with custom colors
class IOSSwitch extends StatelessWidget {
  final bool value;
  final ValueChanged<bool> onChanged;

  const IOSSwitch({
    Key? key,
    required this.value,
    required this.onChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Switch(
      value: value,
      onChanged: onChanged,
      activeColor: AppColors.primaryYellow,
      activeTrackColor: AppColors.primaryYellow.withOpacity(0.5),
      inactiveThumbColor: Colors.white,
      inactiveTrackColor: const Color(0xFFE5E5EA),
    );
  }
}

/// iOS-style app bar with centered title
class IOSAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final List<Widget>? actions;
  final Widget? leading;
  final bool automaticallyImplyLeading;
  final double elevation;

  const IOSAppBar({
    Key? key,
    required this.title,
    this.actions,
    this.leading,
    this.automaticallyImplyLeading = true,
    this.elevation = 0,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: Text(
        title,
        style: const TextStyle(
          color: Colors.black,
          fontSize: 17,
          fontWeight: FontWeight.w600,
          fontFamily: IOSTheme.fontFamily,
        ),
      ),
      centerTitle: true,
      backgroundColor: Colors.white,
      elevation: elevation,
      leading: leading,
      automaticallyImplyLeading: automaticallyImplyLeading,
      actions: actions,
      iconTheme: const IconThemeData(
        color: AppColors.primaryYellow,
      ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

/// iOS-style bottom navigation bar
class IOSBottomNavBar extends StatelessWidget {
  final int currentIndex;
  final ValueChanged<int> onTap;
  final List<BottomNavigationBarItem> items;

  const IOSBottomNavBar({
    Key? key,
    required this.currentIndex,
    required this.onTap,
    required this.items,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BottomNavigationBar(
      currentIndex: currentIndex,
      onTap: onTap,
      items: items,
      type: BottomNavigationBarType.fixed,
      selectedItemColor: AppColors.primaryYellow,
      unselectedItemColor: IOSTheme.secondaryTextColor,
      backgroundColor: Colors.white,
      elevation: 8,
      selectedFontSize: 12,
      unselectedFontSize: 12,
      selectedLabelStyle: const TextStyle(
        fontFamily: IOSTheme.fontFamily,
        fontWeight: FontWeight.w500,
      ),
      unselectedLabelStyle: const TextStyle(
        fontFamily: IOSTheme.fontFamily,
        fontWeight: FontWeight.w500,
      ),
    );
  }
}

/// iOS-style segmented control
class IOSSegmentedControl extends StatelessWidget {
  final List<String> segments;
  final int selectedIndex;
  final ValueChanged<int> onSegmentTapped;

  const IOSSegmentedControl({
    Key? key,
    required this.segments,
    required this.selectedIndex,
    required this.onSegmentTapped,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(2),
      decoration: BoxDecoration(
        color: const Color(0xFFE5E5EA),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: List.generate(segments.length, (index) {
          final isSelected = index == selectedIndex;
          return Expanded(
            child: GestureDetector(
              onTap: () => onSegmentTapped(index),
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 8),
                decoration: BoxDecoration(
                  color: isSelected ? Colors.white : Colors.transparent,
                  borderRadius: BorderRadius.circular(6),
                  boxShadow: isSelected
                      ? [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.1),
                            blurRadius: 2,
                            offset: const Offset(0, 1),
                          ),
                        ]
                      : null,
                ),
                child: Text(
                  segments[index],
                  style: TextStyle(
                    color: isSelected ? Colors.black : Colors.black87,
                    fontSize: 13,
                    fontWeight: isSelected ? FontWeight.w500 : FontWeight.w400,
                    fontFamily: IOSTheme.fontFamily,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          );
        }),
      ),
    );
  }
}

/// iOS-style card with rounded corners and subtle shadow
class IOSCard extends StatelessWidget {
  final Widget child;
  final EdgeInsetsGeometry padding;
  final VoidCallback? onTap;

  const IOSCard({
    Key? key,
    required this.child,
    this.padding = const EdgeInsets.all(16),
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: padding,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(10),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: child,
      ),
    );
  }
}

/// iOS-style list tile with chevron
class IOSListTile extends StatelessWidget {
  final String title;
  final String? subtitle;
  final Widget? leading;
  final Widget? trailing;
  final VoidCallback? onTap;
  final bool showChevron;
  final bool hasDivider;

  const IOSListTile({
    Key? key,
    required this.title,
    this.subtitle,
    this.leading,
    this.trailing,
    this.onTap,
    this.showChevron = true,
    this.hasDivider = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        InkWell(
          onTap: onTap,
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
            child: Row(
              children: [
                if (leading != null) ...[
                  leading!,
                  const SizedBox(width: 16),
                ],
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        title,
                        style: const TextStyle(
                          fontSize: 17,
                          fontFamily: IOSTheme.fontFamily,
                        ),
                      ),
                      if (subtitle != null) ...[
                        const SizedBox(height: 4),
                        Text(
                          subtitle!,
                          style: const TextStyle(
                            fontSize: 14,
                            color: IOSTheme.secondaryTextColor,
                            fontFamily: IOSTheme.fontFamily,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                if (trailing != null) ...[
                  trailing!,
                ] else if (showChevron && onTap != null) ...[
                  const Icon(
                    Icons.chevron_right,
                    color: IOSTheme.secondaryTextColor,
                    size: 20,
                  ),
                ],
              ],
            ),
          ),
        ),
        if (hasDivider)
          const Divider(
            height: 1,
            thickness: 0.5,
            color: IOSTheme.dividerColor,
            indent: 16,
          ),
      ],
    );
  }
}

/// iOS-style search bar
class IOSSearchBar extends StatelessWidget {
  final TextEditingController controller;
  final String placeholder;
  final Function(String)? onChanged;
  final VoidCallback? onClear;
  final FocusNode? focusNode;

  const IOSSearchBar({
    Key? key,
    required this.controller,
    this.placeholder = 'Search',
    this.onChanged,
    this.onClear,
    this.focusNode,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 36,
      decoration: BoxDecoration(
        color: const Color(0xFFE5E5EA),
        borderRadius: BorderRadius.circular(10),
      ),
      child: TextField(
        controller: controller,
        focusNode: focusNode,
        onChanged: onChanged,
        style: const TextStyle(
          fontSize: 16,
          fontFamily: IOSTheme.fontFamily,
        ),
        decoration: InputDecoration(
          hintText: placeholder,
          hintStyle: const TextStyle(
            color: IOSTheme.secondaryTextColor,
            fontSize: 16,
            fontFamily: IOSTheme.fontFamily,
          ),
          prefixIcon: const Icon(
            Icons.search,
            color: IOSTheme.secondaryTextColor,
            size: 20,
          ),
          suffixIcon: controller.text.isNotEmpty
              ? GestureDetector(
                  onTap: () {
                    controller.clear();
                    if (onClear != null) {
                      onClear!();
                    }
                    if (onChanged != null) {
                      onChanged!('');
                    }
                  },
                  child: const Icon(
                    Icons.cancel,
                    color: IOSTheme.secondaryTextColor,
                    size: 20,
                  ),
                )
              : null,
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(vertical: 8),
        ),
      ),
    );
  }
}

/// iOS-style alert dialog
Future<T?> showIOSDialog<T>({
  required BuildContext context,
  required String title,
  required String message,
  String? cancelText,
  String? confirmText,
  VoidCallback? onCancel,
  VoidCallback? onConfirm,
  bool barrierDismissible = true,
}) {
  return showDialog<T>(
    context: context,
    barrierDismissible: barrierDismissible,
    builder: (context) => AlertDialog(
      title: Text(
        title,
        style: const TextStyle(
          fontSize: 17,
          fontWeight: FontWeight.w600,
          fontFamily: IOSTheme.fontFamily,
        ),
        textAlign: TextAlign.center,
      ),
      content: Text(
        message,
        style: const TextStyle(
          fontSize: 13,
          fontFamily: IOSTheme.fontFamily,
        ),
        textAlign: TextAlign.center,
      ),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(14),
      ),
      actions: [
        if (cancelText != null)
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              if (onCancel != null) {
                onCancel();
              }
            },
            child: Text(
              cancelText,
              style: TextStyle(
                color: onConfirm != null ? IOSTheme.secondaryTextColor : AppColors.primaryYellow,
                fontSize: 17,
                fontFamily: IOSTheme.fontFamily,
              ),
            ),
          ),
        if (confirmText != null)
          TextButton(
            onPressed: () {
              Navigator.of(context).pop(true);
              if (onConfirm != null) {
                onConfirm();
              }
            },
            child: Text(
              confirmText,
              style: const TextStyle(
                color: AppColors.primaryYellow,
                fontSize: 17,
                fontWeight: FontWeight.w600,
                fontFamily: IOSTheme.fontFamily,
              ),
            ),
          ),
      ],
      actionsPadding: const EdgeInsets.symmetric(horizontal: 8),
      buttonPadding: EdgeInsets.zero,
      actionsAlignment: MainAxisAlignment.spaceEvenly,
    ),
  );
}
