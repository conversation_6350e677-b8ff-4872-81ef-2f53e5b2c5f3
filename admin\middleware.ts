import { NextRequest, NextResponse } from 'next/server'

// Define protected routes
const protectedRoutes = [
  '/dashboard',
  '/users',
  '/drivers',
  '/partners',
  '/jobs',
  '/news',
  '/forums',
  '/queries',
  '/chats',
  '/analytics',
  '/settings',
  '/notifications',
  '/test',
]

// Define public routes
const publicRoutes = [
  '/login',
  '/api/auth/login',
  '/api/auth/logout',
]

export async function middleware(request: NextRequest) {
  // Skip middleware in static export mode (production build)
  if (process.env.NODE_ENV === 'production') {
    return NextResponse.next()
  }

  const { pathname } = request.nextUrl

  // Allow public routes
  if (publicRoutes.some(route => pathname.startsWith(route))) {
    return NextResponse.next()
  }

  // Allow static files and API routes (except auth)
  if (
    pathname.startsWith('/_next') ||
    pathname.startsWith('/favicon') ||
    pathname.startsWith('/api/') && !pathname.startsWith('/api/auth')
  ) {
    return NextResponse.next()
  }

  // Check if route is protected
  const isProtectedRoute = protectedRoutes.some(route =>
    pathname.startsWith(route) || pathname === '/'
  )

  if (isProtectedRoute) {
    // Get token from cookies
    const token = request.cookies.get('admin-token')?.value

    if (!token || token !== 'valid-token') {
      // Redirect to login if no valid token
      const loginUrl = new URL('/login', request.url)
      loginUrl.searchParams.set('redirect', pathname)
      return NextResponse.redirect(loginUrl)
    }

    // Allow access if valid token exists
    return NextResponse.next()
  }

  return NextResponse.next()
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
}
