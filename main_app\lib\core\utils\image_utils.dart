import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';

/// Utility class for optimized image loading and caching
class ImageUtils {
  /// Returns an optimized CachedNetworkImage with standard memory caching
  /// and error handling. Uses proper sizing constraints to avoid OOM issues.
  static Widget optimizedNetworkImage({
    required String imageUrl,
    double? width,
    double? height,
    BoxFit fit = BoxFit.cover,
    Widget? placeholder,
    Widget? errorWidget,
    bool useProgressIndicator = true,
    int? memCacheWidth,
    int? memCacheHeight,
    BorderRadius? borderRadius,
  }) {
    // Default memory cache sizing for better performance
    memCacheWidth ??= width != null ? (width * 2).toInt() : 800;
    memCacheHeight ??= height != null ? (height * 2).toInt() : 800;
    
    final image = CachedNetworkImage(
      imageUrl: imageUrl,
      width: width,
      height: height,
      fit: fit,
      memCacheWidth: memCacheWidth,
      memCacheHeight: memCacheHeight,
      maxWidthDiskCache: 1200, // Cap disk cache images at 1200px width
      placeholder: useProgressIndicator
          ? (context, url) => placeholder ?? _defaultPlaceholder(context)
          : null,
      errorWidget: (context, url, error) => 
          errorWidget ?? _defaultErrorWidget(context),
    );
    
    if (borderRadius != null) {
      return ClipRRect(
        borderRadius: borderRadius,
        child: image,
      );
    }
    
    return image;
  }
  
  /// Default placeholder widget when loading images
  static Widget _defaultPlaceholder(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return Container(
      color: isDarkMode ? Colors.grey[800] : Colors.grey[200],
      child: Center(
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(
            Theme.of(context).primaryColor.withOpacity(0.7),
          ),
        ),
      ),
    );
  }
  
  /// Default error widget when image loading fails
  static Widget _defaultErrorWidget(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return Container(
      color: isDarkMode ? Colors.grey[800] : Colors.grey[200],
      child: Center(
        child: Icon(
          Icons.broken_image,
          color: isDarkMode ? Colors.grey[600] : Colors.grey[400],
          size: 32,
        ),
      ),
    );
  }
} 