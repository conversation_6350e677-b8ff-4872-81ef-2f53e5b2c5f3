import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../firebase/notification_service.dart';
import '../utils/app_logger.dart';
import '../utils/error_handler.dart';

/// Service to handle verification status notifications
class VerificationNotificationService {
  static final VerificationNotificationService _instance = VerificationNotificationService._internal();
  static VerificationNotificationService get instance => _instance;
  VerificationNotificationService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final AppLogger _logger = AppLogger('VerificationNotificationService');

  final List<StreamSubscription> _subscriptions = [];
  bool _isInitialized = false;
  String? _currentUserId;

  /// Initialize the verification notification service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      _currentUserId = _auth.currentUser?.uid;
      if (_currentUserId == null) {
        _logger.warning('User not authenticated, skipping verification notification setup');
        return;
      }

      // Listen for auth state changes
      _auth.authStateChanges().listen((User? user) {
        if (user != null) {
          _currentUserId = user.uid;
          _setupVerificationListeners();
        } else {
          _currentUserId = null;
          _cleanup();
        }
      });

      await _setupVerificationListeners();
      _isInitialized = true;
      _logger.info('Verification notification service initialized');
    } catch (e, stackTrace) {
      _logger.error('Error initializing verification notification service',
          error: e, stackTrace: stackTrace);
      ErrorHandler.reportHandledException(
        e,
        stackTrace,
        context: 'Initialize verification notification service',
        severity: ErrorSeverity.medium,
      );
    }
  }

  /// Setup listeners for verification status changes
  Future<void> _setupVerificationListeners() async {
    if (_currentUserId == null) return;

    try {
      // Cleanup existing listeners
      _cleanup();

      // Listen for driver verification status changes
      await _setupDriverVerificationListener();

      // Listen for document verification status changes
      await _setupDocumentVerificationListener();

      _logger.info('Verification listeners setup completed');
    } catch (e, stackTrace) {
      _logger.error('Error setting up verification listeners',
          error: e, stackTrace: stackTrace);
    }
  }

  /// Setup driver verification listener
  Future<void> _setupDriverVerificationListener() async {
    try {
      // Listen for changes in driver requests for current user with optimized query
      final driverQuery = _firestore.collection('driver_requests')
          .where('userId', isEqualTo: _currentUserId);

      // Use includeMetadataChanges: false for better performance and real-time updates
      final subscription = driverQuery.snapshots(includeMetadataChanges: false).listen(
        (snapshot) {
          // Process only document changes for efficiency
          for (final change in snapshot.docChanges) {
            if (change.type == DocumentChangeType.modified || change.type == DocumentChangeType.added) {
              _handleDriverVerificationUpdate(change.doc);
            }
          }
        },
        onError: (error) {
          _logger.error('Driver verification listener error', error: error);
        },
      );

      _subscriptions.add(subscription);
      _logger.debug('Driver verification listener setup with real-time optimization');
    } catch (e, stackTrace) {
      _logger.error('Error setting up driver verification listener',
          error: e, stackTrace: stackTrace);
    }
  }

  /// Setup document verification listener
  Future<void> _setupDocumentVerificationListener() async {
    try {
      // Listen for changes in user's documents with optimized configuration
      final userDocRef = _firestore.collection('users').doc(_currentUserId);

      // Use includeMetadataChanges: false for better performance and real-time updates
      final subscription = userDocRef.snapshots(includeMetadataChanges: false).listen(
        (snapshot) {
          if (snapshot.exists) {
            _handleDocumentVerificationUpdate(snapshot);
          }
        },
        onError: (error) {
          _logger.error('Document verification listener error', error: error);
        },
      );

      _subscriptions.add(subscription);
      _logger.debug('Document verification listener setup with real-time optimization');
    } catch (e, stackTrace) {
      _logger.error('Error setting up document verification listener',
          error: e, stackTrace: stackTrace);
    }
  }

  /// Handle driver verification status update
  void _handleDriverVerificationUpdate(DocumentSnapshot doc) {
    try {
      final data = doc.data() as Map<String, dynamic>?;
      if (data == null) return;

      final status = data['status'] as String?;
      final verifiedAt = data['verifiedAt'] as Timestamp?;
      final rejectionReason = data['rejectionReason'] as String?;

      // Check if this is a recent verification (within last 30 seconds to avoid initial load spam)
      if (verifiedAt != null) {
        final verificationTime = verifiedAt.toDate();
        final now = DateTime.now();
        final difference = now.difference(verificationTime).inSeconds;

        // Only skip very old verifications (more than 30 seconds) to avoid initial load notifications
        // but allow real-time verification updates to come through immediately
        if (difference > 30) return; // Skip old verifications from initial load
      }

      String title = '';
      String body = '';

      switch (status) {
        case 'verified':
        case 'approved':
          title = '🎉 Driver Application Approved!';
          body = 'Congratulations! Your driver application has been approved. You can now start accepting rides.';
          break;
        case 'rejected':
          title = '❌ Driver Application Rejected';
          body = rejectionReason ?? 'Your driver application has been rejected. Please contact support for more information.';
          break;
        case 'pending_review':
          title = '⏳ Application Under Review';
          body = 'Your driver application is now under review. We\'ll notify you once it\'s processed.';
          break;
        case 'pending_documents':
          title = '📄 Documents Required';
          body = 'Additional documents are required for your driver application. Please upload the missing documents.';
          break;
        default:
          return; // Don't notify for unknown statuses
      }

      if (title.isNotEmpty && body.isNotEmpty) {
        NotificationService.instance.sendVerificationNotification(
          title: title,
          body: body,
          verificationType: 'driver',
          status: status ?? 'unknown',
          driverId: doc.id,
          additionalData: {
            'rejectionReason': rejectionReason,
            'verifiedAt': verifiedAt?.toDate().toIso8601String(),
          },
        );
      }
    } catch (e, stackTrace) {
      _logger.error('Error handling driver verification update',
          error: e, stackTrace: stackTrace);
    }
  }

  /// Handle document verification status update
  void _handleDocumentVerificationUpdate(DocumentSnapshot doc) {
    try {
      final data = doc.data() as Map<String, dynamic>?;
      if (data == null) return;

      final documents = data['documents'] as Map<String, dynamic>?;
      if (documents == null) return;

      // Check for recent document status changes
      documents.forEach((docType, docData) {
        if (docData is Map<String, dynamic>) {
          final status = docData['status'] as String?;
          final verifiedAt = docData['verifiedAt'] as Timestamp?;
          final rejectionReason = docData['rejectionReason'] as String?;

          // Check if this is a recent verification (within last 30 seconds to avoid initial load spam)
          if (verifiedAt != null) {
            final verificationTime = verifiedAt.toDate();
            final now = DateTime.now();
            final difference = now.difference(verificationTime).inSeconds;

            // Only skip very old verifications (more than 30 seconds) to avoid initial load notifications
            // but allow real-time verification updates to come through immediately
            if (difference > 30) return; // Skip old verifications from initial load
          }

          String title = '';
          String body = '';
          String documentName = _getDocumentDisplayName(docType);

          switch (status) {
            case 'verified':
            case 'approved':
              title = '✅ $documentName Verified';
              body = 'Your $documentName has been successfully verified.';
              break;
            case 'rejected':
              title = '❌ $documentName Rejected';
              body = rejectionReason ?? 'Your $documentName was rejected. Please upload a new document.';
              break;
            case 'pending':
              title = '⏳ $documentName Under Review';
              body = 'Your $documentName is being reviewed. We\'ll notify you once it\'s processed.';
              break;
            default:
              return; // Don't notify for unknown statuses
          }

          if (title.isNotEmpty && body.isNotEmpty) {
            NotificationService.instance.sendVerificationNotification(
              title: title,
              body: body,
              verificationType: 'document',
              status: status ?? 'unknown',
              additionalData: {
                'documentType': docType,
                'documentName': documentName,
                'rejectionReason': rejectionReason,
                'verifiedAt': verifiedAt?.toDate().toIso8601String(),
              },
            );
          }
        }
      });
    } catch (e, stackTrace) {
      _logger.error('Error handling document verification update',
          error: e, stackTrace: stackTrace);
    }
  }

  /// Get display name for document type
  String _getDocumentDisplayName(String docType) {
    switch (docType.toLowerCase()) {
      case 'cnic':
        return 'CNIC';
      case 'license':
      case 'driving_license':
        return 'Driving License';
      case 'vehicle_registration':
        return 'Vehicle Registration';
      case 'insurance':
        return 'Insurance Document';
      case 'photo':
      case 'profile_photo':
        return 'Profile Photo';
      default:
        return docType.replaceAll('_', ' ').split(' ')
            .map((word) => word.isNotEmpty ? word[0].toUpperCase() + word.substring(1) : '')
            .join(' ');
    }
  }

  /// Send a manual verification notification
  Future<void> sendManualVerificationNotification({
    required String title,
    required String body,
    required String verificationType,
    required String status,
    Map<String, dynamic>? additionalData,
  }) async {
    try {
      await NotificationService.instance.sendVerificationNotification(
        title: title,
        body: body,
        verificationType: verificationType,
        status: status,
        additionalData: additionalData,
      );
      _logger.info('Manual verification notification sent: $title');
    } catch (e, stackTrace) {
      _logger.error('Error sending manual verification notification',
          error: e, stackTrace: stackTrace);
    }
  }

  /// Cleanup all subscriptions
  void _cleanup() {
    for (final subscription in _subscriptions) {
      subscription.cancel();
    }
    _subscriptions.clear();
    _logger.debug('Verification notification listeners cleaned up');
  }

  /// Dispose the service
  void dispose() {
    _cleanup();
    _isInitialized = false;
    _logger.info('Verification notification service disposed');
  }
}
