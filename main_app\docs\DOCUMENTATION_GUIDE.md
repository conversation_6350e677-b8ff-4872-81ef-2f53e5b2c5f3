# Documentation Guide

This guide outlines the documentation practices for the Drive-On application.

## Table of Contents

- [Code Documentation](#code-documentation)
- [API Documentation Generation](#api-documentation-generation)
- [Project Documentation](#project-documentation)
- [Documentation Maintenance](#documentation-maintenance)

## Code Documentation

### Inline Comments

Use inline comments to explain complex logic, business rules, or non-obvious implementation details. Follow these guidelines:

#### Class Documentation

Document classes with a brief description of their purpose and responsibility:

```dart
/// A service that manages authentication-related operations.
/// 
/// This service handles user login, registration, logout, and 
/// maintains the current authentication state.
class AuthService extends ChangeNotifier {
  // ...
}
```

#### Method Documentation

Document public methods with descriptions, parameters, return values, and exceptions:

```dart
/// Attempts to log in a user with email and password.
/// 
/// Returns a [bool] indicating success or failure.
/// 
/// Parameters:
/// - [email] The user's email address
/// - [password] The user's password
/// 
/// Throws [FirebaseAuthException] if authentication fails
Future<bool> login(String email, String password) async {
  // ...
}
```

#### Complex Logic

Add comments before complex logic to explain the reasoning:

```dart
// Apply exponential backoff for retries with a maximum of 5 attempts
// This helps prevent overwhelming the server during outages
for (int attempt = 0; attempt < 5; attempt++) {
  try {
    final delay = Duration(milliseconds: pow(2, attempt) * 100);
    await Future.delayed(delay);
    final result = await _apiClient.fetchData();
    return result;
  } catch (e) {
    if (attempt == 4) rethrow;
  }
}
```

### Documentation Best Practices

1. **Be Concise**: Write clear, concise comments that explain "why" not just "what."
2. **Keep Updated**: Update documentation when you change the code.
3. **Avoid Obvious Comments**: Don't document obvious operations.
4. **Use Markdown**: Dartdoc supports markdown formatting.
5. **Link Related Elements**: Reference related classes or methods using square brackets: `[AuthService]`.

## API Documentation Generation

The project uses `dartdoc` to generate API documentation. To generate documentation:

### Installation

Dartdoc is included as a dev dependency in the project:

```bash
flutter pub add dartdoc --dev
```

### Generating Documentation

Run the following command to generate documentation:

```bash
dart doc .
```

Documentation will be generated in the `doc/api` directory.

### Viewing Documentation

You can view the generated documentation by opening `doc/api/index.html` in a web browser.

### Customizing Documentation

You can customize the documentation generation by creating a `dartdoc_options.yaml` file in the root of your project:

```yaml
dartdoc:
  categories:
    "Core":
      - "lib/core/**"
    "Features":
      - "lib/features/**"
  categoryOrder: ["Core", "Features"]
  examplePathPrefix: "examples"
  includeExternal: ["lib/core/external/**"]
  nodoc: ["lib/generated/**"]
```

## Project Documentation

The project contains several documentation files:

### README.md

The main project documentation located at the root of the repository. It includes:
- Project overview
- Setup instructions
- Basic usage examples
- Architecture overview
- Links to more detailed documentation

### Feature Documentation

Each major feature should have its own documentation file in the `docs` directory:

- `docs/CONFIGURATION.md`: Configuration system documentation
- `docs/LOGGING.md`: Logging system documentation
- `docs/TESTING.md`: Testing strategy and guidelines

### Architecture Documentation

Document the application architecture in `docs/ARCHITECTURE.md`, including:
- System components
- Design patterns used
- Data flow diagrams
- Dependency management

## Documentation Maintenance

### Review Process

Documentation should be reviewed as part of the PR process:
1. Ensure new code is properly documented
2. Update existing documentation affected by changes
3. Add new documentation files as needed

### Documentation Standards

- Use a consistent style across all documentation
- Keep documentation concise and focused
- Use examples to illustrate complex concepts
- Include diagrams where appropriate (use tools like [mermaid](https://mermaid-js.github.io/))

### Documentation Updates

When to update documentation:
1. When adding new features
2. When changing existing functionality
3. When deprecating or removing features
4. When fixing bugs with behavioral impact

## Documentation Checklist

Use this checklist when working on code to ensure proper documentation:

- [ ] Added class documentation
- [ ] Added method documentation
- [ ] Documented complex logic
- [ ] Updated affected documentation files
- [ ] Generated and reviewed API documentation
- [ ] Added examples for new functionality

## Examples

### Example Class Documentation

```dart
/// A service that manages the caching of API responses.
/// 
/// This service provides:
/// - Time-based cache expiration
/// - Size-limited caching
/// - Forced cache refresh
/// 
/// Usage:
/// ```dart
/// final cacheService = CacheService();
/// final data = await cacheService.get('key', () => fetchDataFromApi());
/// ```
class CacheService {
  // Implementation...
}
```

### Example Method Documentation

```dart
/// Fetches data from cache if available and valid, otherwise from the provided function.
/// 
/// Parameters:
/// - [key] The cache key to use
/// - [fetcher] A function that returns a Future with the data to cache
/// - [maxAge] Optional maximum age of the cache entry (defaults to 1 hour)
/// 
/// Returns a Future with the data either from cache or freshly fetched.
/// 
/// Throws:
/// - [CacheException] if there's an error with the cache
/// - Any exception thrown by the [fetcher] function
Future<T> get<T>(String key, Future<T> Function() fetcher, {Duration? maxAge}) async {
  // Implementation...
}
``` 