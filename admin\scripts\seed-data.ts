import { collection, addDoc, Timestamp } from 'firebase/firestore'
import { db } from '../lib/firebase/config'

// Sample data seeding script for testing analytics
export async function seedSampleData() {
  try {
    console.log('Starting data seeding...')

    // Seed sample users
    const users = [
      {
        email: '<EMAIL>',
        displayName: '<PERSON>',
        role: 'user',
        isActive: true,
        city: 'Karachi',
        phoneNumber: '+***********',
        createdAt: Timestamp.fromDate(new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)), // 30 days ago
        lastLoginAt: Timestamp.fromDate(new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)), // 2 days ago
      },
      {
        email: '<EMAIL>',
        displayName: '<PERSON>',
        role: 'user',
        isActive: true,
        city: 'Lahore',
        phoneNumber: '+***********',
        createdAt: Timestamp.fromDate(new Date(Date.now() - 25 * 24 * 60 * 60 * 1000)), // 25 days ago
        lastLoginAt: Timestamp.fromDate(new Date(Date.now() - 1 * 24 * 60 * 60 * 1000)), // 1 day ago
      },
      {
        email: '<EMAIL>',
        displayName: '<PERSON> <PERSON>',
        role: 'user',
        isActive: true,
        city: 'Islamabad',
        phoneNumber: '+92302345678',
        createdAt: Timestamp.fromDate(new Date(Date.now() - 20 * 24 * 60 * 60 * 1000)), // 20 days ago
        lastLoginAt: Timestamp.fromDate(new Date(Date.now() - 3 * 60 * 60 * 1000)), // 3 hours ago
      },
      {
        email: '<EMAIL>',
        displayName: 'Fatima Khan',
        role: 'user',
        isActive: true,
        city: 'Karachi',
        phoneNumber: '+92303456789',
        createdAt: Timestamp.fromDate(new Date(Date.now() - 15 * 24 * 60 * 60 * 1000)), // 15 days ago
        lastLoginAt: Timestamp.fromDate(new Date(Date.now() - 30 * 60 * 1000)), // 30 minutes ago
      },
      {
        email: '<EMAIL>',
        displayName: 'Hassan Shah',
        role: 'user',
        isActive: true,
        city: 'Lahore',
        phoneNumber: '+92304567890',
        createdAt: Timestamp.fromDate(new Date(Date.now() - 10 * 24 * 60 * 60 * 1000)), // 10 days ago
        lastLoginAt: Timestamp.fromDate(new Date(Date.now() - 5 * 60 * 1000)), // 5 minutes ago
      }
    ]

    for (const user of users) {
      await addDoc(collection(db, 'users'), user)
    }
    console.log('✅ Users seeded successfully')

    // Seed sample drivers
    const drivers = [
      {
        name: 'Muhammad Asif',
        mobile: '+92305123456',
        city: 'Karachi',
        maritalStatus: 'Married',
        education: 'Intermediate',
        experience: 5,
        status: 'verified',
        isVerified: true,
        documentsVerified: true,
        documentUrls: {
          cnic: 'https://example.com/cnic1.jpg',
          license: 'https://example.com/license1.jpg',
          photo: 'https://example.com/photo1.jpg'
        },
        createdAt: Timestamp.fromDate(new Date(Date.now() - 28 * 24 * 60 * 60 * 1000)),
        verifiedAt: Timestamp.fromDate(new Date(Date.now() - 25 * 24 * 60 * 60 * 1000)),
        verifiedBy: 'Admin'
      },
      {
        name: 'Ali Raza',
        mobile: '+92306234567',
        city: 'Lahore',
        maritalStatus: 'Single',
        education: 'Matric',
        experience: 3,
        status: 'verified',
        isVerified: true,
        documentsVerified: true,
        documentUrls: {
          cnic: 'https://example.com/cnic2.jpg',
          license: 'https://example.com/license2.jpg',
          photo: 'https://example.com/photo2.jpg'
        },
        createdAt: Timestamp.fromDate(new Date(Date.now() - 22 * 24 * 60 * 60 * 1000)),
        verifiedAt: Timestamp.fromDate(new Date(Date.now() - 20 * 24 * 60 * 60 * 1000)),
        verifiedBy: 'Admin'
      },
      {
        name: 'Usman Ahmed',
        mobile: '+92307345678',
        city: 'Islamabad',
        maritalStatus: 'Married',
        education: 'Graduate',
        experience: 7,
        status: 'verified',
        isVerified: true,
        documentsVerified: true,
        documentUrls: {
          cnic: 'https://example.com/cnic3.jpg',
          license: 'https://example.com/license3.jpg',
          photo: 'https://example.com/photo3.jpg'
        },
        createdAt: Timestamp.fromDate(new Date(Date.now() - 18 * 24 * 60 * 60 * 1000)),
        verifiedAt: Timestamp.fromDate(new Date(Date.now() - 15 * 24 * 60 * 60 * 1000)),
        verifiedBy: 'Admin'
      }
    ]

    for (const driver of drivers) {
      await addDoc(collection(db, 'drivers'), driver)
    }
    console.log('✅ Drivers seeded successfully')

    // Seed sample driver requests (pending)
    const driverRequests = [
      {
        name: 'Tariq Mahmood',
        mobile: '+92308456789',
        city: 'Karachi',
        maritalStatus: 'Married',
        education: 'Intermediate',
        experience: 4,
        status: 'pending',
        isVerified: false,
        documentsVerified: false,
        documentUrls: {
          cnic: 'https://example.com/cnic4.jpg',
          license: 'https://example.com/license4.jpg',
          photo: 'https://example.com/photo4.jpg'
        },
        createdAt: Timestamp.fromDate(new Date(Date.now() - 5 * 24 * 60 * 60 * 1000)),
        submittedBy: 'user123',
        submittedAt: Timestamp.fromDate(new Date(Date.now() - 5 * 24 * 60 * 60 * 1000))
      },
      {
        name: 'Shahid Iqbal',
        mobile: '+92309567890',
        city: 'Lahore',
        maritalStatus: 'Single',
        education: 'Matric',
        experience: 2,
        status: 'pending',
        isVerified: false,
        documentsVerified: false,
        documentUrls: {
          cnic: 'https://example.com/cnic5.jpg',
          license: 'https://example.com/license5.jpg',
          photo: 'https://example.com/photo5.jpg'
        },
        createdAt: Timestamp.fromDate(new Date(Date.now() - 3 * 24 * 60 * 60 * 1000)),
        submittedBy: 'user456',
        submittedAt: Timestamp.fromDate(new Date(Date.now() - 3 * 24 * 60 * 60 * 1000))
      }
    ]

    for (const request of driverRequests) {
      await addDoc(collection(db, 'driver_requests'), request)
    }
    console.log('✅ Driver requests seeded successfully')

    // Seed sample forums
    const forums = [
      {
        title: 'General Discussion',
        description: 'A place for general discussions about the platform',
        category: 'general',
        status: 'active',
        isPinned: true,
        isSticky: false,
        creatorId: 'user1',
        creatorName: 'John Doe',
        participants: ['user1', 'user2', 'user3'],
        participantNames: { user1: 'John Doe', user2: 'Jane Smith', user3: 'Ahmed Ali' },
        lastMessage: 'Welcome to the general discussion forum!',
        lastMessageTime: Timestamp.fromDate(new Date(Date.now() - 2 * 60 * 60 * 1000)),
        lastSenderId: 'user1',
        messageCount: 15,
        tags: ['general', 'discussion'],
        createdAt: Timestamp.fromDate(new Date(Date.now() - 20 * 24 * 60 * 60 * 1000))
      },
      {
        title: 'Help & Support',
        description: 'Get help with platform features and issues',
        category: 'help',
        status: 'active',
        isPinned: false,
        isSticky: true,
        creatorId: 'admin',
        creatorName: 'Admin',
        participants: ['user1', 'user2', 'user4', 'admin'],
        participantNames: { user1: 'John Doe', user2: 'Jane Smith', user4: 'Fatima Khan', admin: 'Admin' },
        lastMessage: 'How can I update my profile?',
        lastMessageTime: Timestamp.fromDate(new Date(Date.now() - 4 * 60 * 60 * 1000)),
        lastSenderId: 'user4',
        messageCount: 8,
        tags: ['help', 'support'],
        createdAt: Timestamp.fromDate(new Date(Date.now() - 15 * 24 * 60 * 60 * 1000))
      },
      {
        title: 'Feature Requests',
        description: 'Suggest new features for the platform',
        category: 'feedback',
        status: 'active',
        isPinned: false,
        isSticky: false,
        creatorId: 'user3',
        creatorName: 'Ahmed Ali',
        participants: ['user3', 'user5'],
        participantNames: { user3: 'Ahmed Ali', user5: 'Hassan Shah' },
        lastMessage: 'It would be great to have dark mode!',
        lastMessageTime: Timestamp.fromDate(new Date(Date.now() - 6 * 60 * 60 * 1000)),
        lastSenderId: 'user5',
        messageCount: 12,
        tags: ['features', 'feedback'],
        createdAt: Timestamp.fromDate(new Date(Date.now() - 12 * 24 * 60 * 60 * 1000))
      }
    ]

    for (const forum of forums) {
      await addDoc(collection(db, 'forums'), forum)
    }
    console.log('✅ Forums seeded successfully')

    // Seed sample queries
    const queries = [
      {
        subject: 'Account Verification Issue',
        description: 'I am having trouble verifying my account. The verification email is not arriving.',
        category: 'account',
        priority: 'high',
        status: 'open',
        userId: 'user1',
        userName: 'John Doe',
        userEmail: '<EMAIL>',
        userPhone: '+***********',
        lastMessage: 'I am having trouble verifying my account. The verification email is not arriving.',
        lastMessageTime: Timestamp.fromDate(new Date(Date.now() - 30 * 60 * 1000)),
        lastSenderId: 'user1',
        messageCount: 1,
        tags: ['verification', 'email'],
        createdAt: Timestamp.fromDate(new Date(Date.now() - 30 * 60 * 1000)),
        updatedAt: Timestamp.fromDate(new Date(Date.now() - 30 * 60 * 1000))
      },
      {
        subject: 'Payment Method Update',
        description: 'How can I update my payment method in the app?',
        category: 'billing',
        priority: 'medium',
        status: 'resolved',
        userId: 'user2',
        userName: 'Jane Smith',
        userEmail: '<EMAIL>',
        userPhone: '+***********',
        lastMessage: 'Thank you for the help! Issue resolved.',
        lastMessageTime: Timestamp.fromDate(new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)),
        lastSenderId: 'admin',
        messageCount: 4,
        tags: ['payment', 'billing'],
        createdAt: Timestamp.fromDate(new Date(Date.now() - 3 * 24 * 60 * 60 * 1000)),
        updatedAt: Timestamp.fromDate(new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)),
        resolvedAt: Timestamp.fromDate(new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)),
        resolvedBy: 'Admin',
        resolutionNote: 'Guided user through payment method update process',
        satisfactionRating: 5
      },
      {
        subject: 'App Crashes on Startup',
        description: 'The app keeps crashing when I try to open it on my Android device.',
        category: 'technical',
        priority: 'urgent',
        status: 'in-progress',
        userId: 'user3',
        userName: 'Ahmed Ali',
        userEmail: '<EMAIL>',
        userPhone: '+92302345678',
        assignedTo: { id: 'admin1', name: 'Tech Support' },
        lastMessage: 'We are investigating this issue. Please try clearing the app cache.',
        lastMessageTime: Timestamp.fromDate(new Date(Date.now() - 4 * 60 * 60 * 1000)),
        lastSenderId: 'admin1',
        messageCount: 3,
        tags: ['crash', 'android', 'technical'],
        createdAt: Timestamp.fromDate(new Date(Date.now() - 8 * 60 * 60 * 1000)),
        updatedAt: Timestamp.fromDate(new Date(Date.now() - 4 * 60 * 60 * 1000))
      }
    ]

    for (const query of queries) {
      await addDoc(collection(db, 'queries'), query)
    }
    console.log('✅ Queries seeded successfully')

    console.log('🎉 All sample data seeded successfully!')
    
  } catch (error) {
    console.error('❌ Error seeding data:', error)
    throw error
  }
}

// Uncomment the line below to run the seeding script
// seedSampleData()
