'use client'

import { useState, useEffect, useRef } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Activity, Zap, Clock, Database, Wifi } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'

interface PerformanceMetrics {
  fps: number
  memoryUsage: number
  renderTime: number
  networkLatency: number
  firebaseConnections: number
  componentRenders: number
}

export function PerformanceMonitor() {
  const [isVisible, setIsVisible] = useState(false)
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    fps: 0,
    memoryUsage: 0,
    renderTime: 0,
    networkLatency: 0,
    firebaseConnections: 0,
    componentRenders: 0
  })

  const frameCount = useRef(0)
  const lastTime = useRef(performance.now())
  const renderTimes = useRef<number[]>([])
  const animationFrame = useRef<number>()

  // FPS calculation
  const calculateFPS = () => {
    frameCount.current++
    const currentTime = performance.now()
    
    if (currentTime - lastTime.current >= 1000) {
      setMetrics(prev => ({
        ...prev,
        fps: Math.round((frameCount.current * 1000) / (currentTime - lastTime.current))
      }))
      frameCount.current = 0
      lastTime.current = currentTime
    }
    
    animationFrame.current = requestAnimationFrame(calculateFPS)
  }

  // Memory usage monitoring
  const getMemoryUsage = () => {
    if ('memory' in performance) {
      const memory = (performance as any).memory
      return Math.round(memory.usedJSHeapSize / 1024 / 1024) // MB
    }
    return 0
  }

  // Network latency test
  const testNetworkLatency = async () => {
    const start = performance.now()
    try {
      await fetch('/api/ping', { method: 'HEAD' })
      const latency = performance.now() - start
      setMetrics(prev => ({ ...prev, networkLatency: Math.round(latency) }))
    } catch (error) {
      console.warn('Network latency test failed:', error)
    }
  }

  // Component render tracking
  const trackRender = () => {
    const renderStart = performance.now()
    
    // Simulate render measurement
    requestAnimationFrame(() => {
      const renderEnd = performance.now()
      const renderTime = renderEnd - renderStart
      
      renderTimes.current.push(renderTime)
      if (renderTimes.current.length > 10) {
        renderTimes.current.shift()
      }
      
      const avgRenderTime = renderTimes.current.reduce((a, b) => a + b, 0) / renderTimes.current.length
      
      setMetrics(prev => ({
        ...prev,
        renderTime: Math.round(avgRenderTime * 100) / 100,
        componentRenders: prev.componentRenders + 1
      }))
    })
  }

  // Firebase connections monitoring
  const monitorFirebaseConnections = () => {
    // This would need to be implemented based on your Firebase usage
    // For now, we'll simulate it
    const connections = Math.floor(Math.random() * 5) + 1
    setMetrics(prev => ({ ...prev, firebaseConnections: connections }))
  }

  useEffect(() => {
    if (isVisible) {
      // Start FPS monitoring
      animationFrame.current = requestAnimationFrame(calculateFPS)
      
      // Update metrics every second
      const interval = setInterval(() => {
        setMetrics(prev => ({
          ...prev,
          memoryUsage: getMemoryUsage()
        }))
        trackRender()
        monitorFirebaseConnections()
      }, 1000)

      // Test network latency every 5 seconds
      const networkInterval = setInterval(testNetworkLatency, 5000)
      testNetworkLatency() // Initial test

      return () => {
        if (animationFrame.current) {
          cancelAnimationFrame(animationFrame.current)
        }
        clearInterval(interval)
        clearInterval(networkInterval)
      }
    }
  }, [isVisible])

  const getPerformanceColor = (value: number, thresholds: { good: number; warning: number }) => {
    if (value <= thresholds.good) return 'text-green-600'
    if (value <= thresholds.warning) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getMemoryColor = (mb: number) => getPerformanceColor(mb, { good: 50, warning: 100 })
  const getFPSColor = (fps: number) => fps >= 55 ? 'text-green-600' : fps >= 30 ? 'text-yellow-600' : 'text-red-600'
  const getLatencyColor = (ms: number) => getPerformanceColor(ms, { good: 100, warning: 300 })
  const getRenderColor = (ms: number) => getPerformanceColor(ms, { good: 16, warning: 33 })

  if (process.env.NODE_ENV === 'production') {
    return null
  }

  return (
    <>
      {/* Toggle Button */}
      <Button
        onClick={() => setIsVisible(!isVisible)}
        className="fixed bottom-4 right-4 z-50 bg-yellow-500 hover:bg-yellow-600 text-white shadow-lg"
        size="sm"
      >
        <Activity className="w-4 h-4 mr-2" />
        Perf
      </Button>

      {/* Performance Monitor Panel */}
      <AnimatePresence>
        {isVisible && (
          <motion.div
            initial={{ opacity: 0, x: 300 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 300 }}
            className="fixed top-4 right-4 z-40 w-80"
          >
            <Card className="p-4 bg-white/95 backdrop-blur-sm border shadow-xl">
              <div className="flex items-center justify-between mb-4">
                <h3 className="font-semibold text-gray-900 flex items-center">
                  <Zap className="w-4 h-4 mr-2 text-yellow-500" />
                  Performance Monitor
                </h3>
                <Button
                  onClick={() => setIsVisible(false)}
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0"
                >
                  ×
                </Button>
              </div>

              <div className="space-y-3">
                {/* FPS */}
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600 flex items-center">
                    <Activity className="w-3 h-3 mr-1" />
                    FPS
                  </span>
                  <span className={`text-sm font-mono ${getFPSColor(metrics.fps)}`}>
                    {metrics.fps}
                  </span>
                </div>

                {/* Memory Usage */}
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600 flex items-center">
                    <Database className="w-3 h-3 mr-1" />
                    Memory
                  </span>
                  <span className={`text-sm font-mono ${getMemoryColor(metrics.memoryUsage)}`}>
                    {metrics.memoryUsage} MB
                  </span>
                </div>

                {/* Render Time */}
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600 flex items-center">
                    <Clock className="w-3 h-3 mr-1" />
                    Render
                  </span>
                  <span className={`text-sm font-mono ${getRenderColor(metrics.renderTime)}`}>
                    {metrics.renderTime} ms
                  </span>
                </div>

                {/* Network Latency */}
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600 flex items-center">
                    <Wifi className="w-3 h-3 mr-1" />
                    Latency
                  </span>
                  <span className={`text-sm font-mono ${getLatencyColor(metrics.networkLatency)}`}>
                    {metrics.networkLatency} ms
                  </span>
                </div>

                {/* Firebase Connections */}
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Firebase</span>
                  <span className="text-sm font-mono text-blue-600">
                    {metrics.firebaseConnections} conn
                  </span>
                </div>

                {/* Component Renders */}
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Renders</span>
                  <span className="text-sm font-mono text-purple-600">
                    {metrics.componentRenders}
                  </span>
                </div>
              </div>

              {/* Performance Tips */}
              <div className="mt-4 pt-3 border-t border-gray-200">
                <div className="text-xs text-gray-500 space-y-1">
                  {metrics.fps < 30 && (
                    <div className="text-red-600">⚠ Low FPS detected</div>
                  )}
                  {metrics.memoryUsage > 100 && (
                    <div className="text-red-600">⚠ High memory usage</div>
                  )}
                  {metrics.networkLatency > 300 && (
                    <div className="text-red-600">⚠ High network latency</div>
                  )}
                  {metrics.renderTime > 33 && (
                    <div className="text-red-600">⚠ Slow render times</div>
                  )}
                </div>
              </div>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  )
}
