name: drive_on
description: Admin portal for Drive-On application
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 2.1.0+3

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: '>=3.24.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  cupertino_icons: ^1.0.6
  google_fonts: ^6.1.0
  flutter_svg: ^2.0.9
  cached_network_image: ^3.3.0
  flutter_staggered_animations: ^1.1.1
  visibility_detector: ^0.4.0+2
  flutter_displaymode: ^0.6.0
  provider: ^6.1.1
  flutter_riverpod: ^2.4.9
  get_it: ^7.6.7
  firebase_core: ^2.24.2
  firebase_auth: ^4.15.3
  cloud_firestore: ^4.13.6
  firebase_storage: ^11.5.6
  firebase_messaging: ^14.7.9
  firebase_analytics: ^10.8.8
  firebase_crashlytics: ^3.4.9
  firebase_performance: ^0.9.4+7
  google_sign_in: ^6.2.1
  shared_preferences: ^2.2.2
  flutter_secure_storage: ^9.0.0
  path_provider: ^2.1.1
  path: ^1.8.3
  intl: ^0.19.0
  uuid: ^4.2.2
  http: ^1.1.2
  url_launcher: ^6.2.4
  share_plus: ^7.2.1
  flutter_local_notifications: ^17.2.2
  timeago: ^3.7.0
  encrypt: ^5.0.3
  crypto: ^3.0.3
  package_info_plus: ^8.0.2
  flutter_dotenv: ^5.1.0
  image_picker: ^1.0.7
  file_picker: ^6.1.1
  record: ^5.2.0
  just_audio: ^0.9.42
  webview_flutter: ^4.8.0
  pdf: ^3.10.7
  printing: ^5.11.1
  flutter_email_sender: ^6.0.2
  mailer: ^6.0.0
  logger: ^2.0.2+1
  sentry_flutter: ^7.16.0
  permission_handler: ^11.3.1
  # youtube_player_flutter: ^8.1.2

  # Offline storage dependencies
  sqflite: ^2.3.0
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  connectivity_plus: ^5.0.2
  drift: ^2.14.1
  sqlite3_flutter_libs: ^0.5.0

dev_dependencies:
  build_runner: ^2.4.7
  flutter_launcher_icons: ^0.13.1
  flutter_lints: ^2.0.0
  hive_generator: ^2.0.1
  drift_dev: ^2.14.1

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/icons/
    - assets/fonts/
    - assets/sounds/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages

# Comprehensive dependency overrides to fix all v1 embedding issues
dependency_overrides:
  win32: ^5.14.0
  web: ^1.1.1
  material_color_utilities: ^0.13.0
  file_picker: ^8.1.6
  # Core Flutter plugins with v2 embedding support
  flutter_plugin_android_lifecycle: ^2.0.22
  # Image picker plugins
  image_picker_android: ^0.8.12+24
  image_picker: ^1.1.2
  # Google Sign-In plugins
  google_sign_in_android: ^6.1.28
  google_sign_in: ^6.2.2
  # Path provider plugins
  path_provider_android: ^2.2.17
  path_provider: ^2.1.5
  # Other common plugins that may have v1 embedding issues
  shared_preferences_android: ^2.4.10
  shared_preferences: ^2.5.3
  url_launcher_android: ^6.3.16
  url_launcher: ^6.3.2
  permission_handler_android: ^13.0.1
  permission_handler: ^12.0.1
  device_info_plus: ^10.1.2
  webview_flutter_android: ^4.8.2
  webview_flutter: ^4.13.0
