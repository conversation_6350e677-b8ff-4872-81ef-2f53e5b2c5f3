import 'package:flutter/material.dart';

class AppErrorWidget extends StatelessWidget {
  final String message;
  final String? title;
  final VoidCallback? onRetry;
  final IconData? icon;
  final bool showHomeButton;

  const AppErrorWidget({
    Key? key,
    required this.message,
    this.title,
    this.onRetry,
    this.icon,
    this.showHomeButton = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Error icon
            Icon(
              icon ?? Icons.error_outline_rounded,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),
            
            // Error title
            if (title != null) ...[
              Text(
                title!,
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
            ],
            
            // Error message
            Text(
              message,
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            
            // Retry button if provided
            if (onRetry != null)
              ElevatedButton.icon(
                onPressed: onRetry,
                icon: const Icon(Icons.refresh),
                label: const Text('Try Again'),
              ),
              
            // Home button option
            if (showHomeButton) ...[
              const SizedBox(height: 12),
              TextButton.icon(
                onPressed: () {
                  // Navigate to home screen
                  Navigator.of(context).popUntil((route) => route.isFirst);
                },
                icon: const Icon(Icons.home),
                label: const Text('Go to Home'),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

/// Error widget for failed network requests
class NetworkErrorWidget extends StatelessWidget {
  final VoidCallback onRetry;

  const NetworkErrorWidget({
    Key? key,
    required this.onRetry,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AppErrorWidget(
      title: 'Connection Error',
      message: 'We couldn\'t connect to the server. Please check your internet connection and try again.',
      icon: Icons.wifi_off_rounded,
      onRetry: onRetry,
    );
  }
}

/// Error widget for when content is not found
class NotFoundErrorWidget extends StatelessWidget {
  final String? message;
  final VoidCallback? onRetry;

  const NotFoundErrorWidget({
    Key? key,
    this.message,
    this.onRetry,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AppErrorWidget(
      title: 'Not Found',
      message: message ?? 'The content you are looking for could not be found.',
      icon: Icons.search_off_rounded,
      onRetry: onRetry,
      showHomeButton: true,
    );
  }
}

/// Error widget for permission denied errors
class PermissionErrorWidget extends StatelessWidget {
  final String permission;
  final VoidCallback onRequestPermission;

  const PermissionErrorWidget({
    Key? key,
    required this.permission,
    required this.onRequestPermission,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AppErrorWidget(
      title: 'Permission Required',
      message: 'This feature requires $permission permission to work properly.',
      icon: Icons.no_encryption_rounded,
      onRetry: onRequestPermission,
    );
  }
} 