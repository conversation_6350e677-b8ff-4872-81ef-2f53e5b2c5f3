// Simple Node.js script to run the data seeding
// Run with: node scripts/run-seed.js

const { seedSampleData } = require('./seed-data.ts')

async function runSeed() {
  try {
    console.log('🌱 Starting data seeding process...')
    await seedSampleData()
    console.log('✅ Data seeding completed successfully!')
    process.exit(0)
  } catch (error) {
    console.error('❌ Data seeding failed:', error)
    process.exit(1)
  }
}

runSeed()
