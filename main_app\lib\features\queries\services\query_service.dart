import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:path/path.dart' as path;
import '../models/query_message.dart';
import '../models/query_metadata.dart';
import '../../../core/utils/app_logger.dart';
import '../../../core/firebase/firebase_permissions_check.dart';

import 'package:flutter/foundation.dart';
import '../models/query_model.dart' as models;
import '../models/message_model.dart';

class QueryService {
  // Singleton pattern
  static final QueryService _instance = QueryService._internal();
  factory QueryService() => _instance;
  QueryService._internal();

  // Firebase instances
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseStorage _storage = FirebaseStorage.instance;

  // Logger
  static final AppLogger _logger = AppLogger('QueryService');

  // Collection references
  final CollectionReference _queriesCollection =
      FirebaseFirestore.instance.collection('queries');

  // Current user info
  String? get currentUserId => _auth.currentUser?.uid;
  String? get currentUserName => _auth.currentUser?.displayName ?? 'User';
  String? get currentUserEmail => _auth.currentUser?.email;
  String? get _currentUserPhotoUrl => _auth.currentUser?.photoURL;

  // ====== User & Admin Status ======

  Future<bool> isUserAdmin() async {
    try {
      if (currentUserId == null) return false;

      final docSnapshot = await _firestore
          .collection('admins')
          .doc(currentUserId)
          .get();

      return docSnapshot.exists;
    } catch (e) {
      _logger.error('Error checking admin status: $e');
      return false;
    }
  }

  // ====== Query Management ======

  Future<QueryMetadata> getUserQuery() async {
    try {
      if (currentUserId == null) {
        throw Exception('User not authenticated');
      }

      // Look for an existing query for this user
      final querySnapshot = await _queriesCollection
          .where('userId', isEqualTo: currentUserId)
          .limit(1)
          .get();

      // If query exists, return it
      if (querySnapshot.docs.isNotEmpty) {
        final queryDoc = querySnapshot.docs.first;
        return QueryMetadata.fromMap(queryDoc.data() as Map<String, dynamic>, queryDoc.id);
      }

      // If no query exists, create a new one
      return _createNewQuery();
    } catch (e) {
      _logger.error('Error getting user query: $e');
      rethrow;
    }
  }

  Future<QueryMetadata> _createNewQuery() async {
    try {
      // Create a new query document reference
      final newQueryRef = _queriesCollection.doc();

      // Create initial metadata
      final newQuery = QueryMetadata(
        id: newQueryRef.id,
        userId: currentUserId!,
        userName: currentUserName!,
        userPhotoUrl: _currentUserPhotoUrl,
        lastMessage: 'New support query created',
        lastMessageTime: DateTime.now(),
        unreadCount: 0,
        isActive: true,
      );

      // Save to Firestore
      await newQueryRef.set(newQuery.toMap());

      // Create initial system message
      await _sendSystemMessage(
        queryId: newQuery.id,
        text: 'Welcome to support! An admin will assist you soon.',
      );

      return newQuery;
    } catch (e) {
      _logger.error('Error creating new query: $e');
      rethrow;
    }
  }

  Future<List<QueryMetadata>> getAllQueries() async {
    try {
      final querySnapshot = await _queriesCollection.get();

      return querySnapshot.docs
          .map((doc) => QueryMetadata.fromMap(doc.data() as Map<String, dynamic>, doc.id))
          .toList();
    } catch (e) {
      _logger.error('Error getting all queries: $e');
      rethrow;
    }
  }

  Future<QueryMetadata> getQueryMetadata(String queryId) async {
    try {
      final docSnapshot = await _queriesCollection.doc(queryId).get();

      if (!docSnapshot.exists) {
        throw Exception('Query not found');
      }

      return QueryMetadata.fromMap(docSnapshot.data() as Map<String, dynamic>, docSnapshot.id);
    } catch (e) {
      _logger.error('Error getting query metadata: $e');
      rethrow;
    }
  }

  Future<void> toggleQueryStatus({
    required String queryId,
    required bool isActive,
  }) async {
    try {
      await _queriesCollection.doc(queryId).update({
        'isActive': isActive,
      });

      // Send a system message about the status change
      final message = isActive
          ? 'This query has been reopened.'
          : 'This query has been closed.';

      await _sendSystemMessage(
        queryId: queryId,
        text: message,
      );
    } catch (e) {
      _logger.error('Error toggling query status: $e');
      rethrow;
    }
  }

  // ====== Messages Management ======

  Stream<List<QueryMessage>> getMessagesStream(String queryId) {
    return _queriesCollection
        .doc(queryId)
        .collection('messages')
        .orderBy('timestamp', descending: false)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => QueryMessage.fromMap(doc.data(), doc.id))
            .toList());
  }

  Future<List<QueryMessage>> getMessages(String queryId) async {
    try {
      final querySnapshot = await _queriesCollection
          .doc(queryId)
          .collection('messages')
          .orderBy('timestamp', descending: false)
          .get();

      return querySnapshot.docs
          .map((doc) => QueryMessage.fromMap(doc.data(), doc.id))
          .toList();
    } catch (e) {
      _logger.error('Error getting messages: $e');
      rethrow;
    }
  }

  Future<void> sendMessage({
    required String queryId,
    required String text,
    List<File>? images,
    bool isAdmin = false,
  }) async {
    try {
      // Get message reference
      final messageRef = _queriesCollection
          .doc(queryId)
          .collection('messages')
          .doc();

      // Upload images if any
      List<String> imageUrls = [];
      if (images != null && images.isNotEmpty) {
        imageUrls = await _uploadFiles(
          queryId: queryId,
          messageId: messageRef.id,
          files: images,
          type: 'images',
        );
      }

      // Create message
      final message = QueryMessage(
        id: messageRef.id,
        text: text,
        senderId: isAdmin ? 'admin' : currentUserId!,
        senderName: isAdmin ? 'Support Admin' : currentUserName!,
        senderPhotoUrl: isAdmin ? null : _currentUserPhotoUrl,
        timestamp: DateTime.now(),
        imageUrls: imageUrls,
        voiceUrl: null,
        voiceDuration: null,
        isEdited: false,
        isSystemMessage: false,
        isAdmin: isAdmin,
      );

      // Save message
      await messageRef.set(message.toMap());

      // Update query metadata
      await _updateQueryAfterNewMessage(
        queryId: queryId,
        message: message,
        sentByAdmin: isAdmin,
      );

      // Note: Notifications are handled by MessageNotificationService via Firebase listeners
      // This ensures real-time delivery without duplicate notifications
      _logger.debug('Message sent successfully, notification will be handled by MessageNotificationService');
    } catch (e) {
      _logger.error('Error sending message: $e');
      rethrow;
    }
  }

  Future<void> _sendSystemMessage({
    required String queryId,
    required String text,
  }) async {
    try {
      // Get message reference
      final messageRef = _queriesCollection
          .doc(queryId)
          .collection('messages')
          .doc();

      // Create system message
      final message = QueryMessage(
        id: messageRef.id,
        text: text,
        senderId: 'system',
        senderName: 'System',
        senderPhotoUrl: null,
        timestamp: DateTime.now(),
        imageUrls: [],
        voiceUrl: null,
        voiceDuration: null,
        isEdited: false,
        isSystemMessage: true,
      );

      // Save message
      await messageRef.set(message.toMap());

      // Update query metadata (without incrementing unread count)
      await _queriesCollection.doc(queryId).update({
        'lastMessage': text,
        'lastMessageTime': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      _logger.error('Error sending system message: $e');
      // Don't rethrow, as this is a non-critical operation
    }
  }

  Future<void> editMessage({
    required String messageId,
    required String queryId,
    required String newText,
  }) async {
    try {
      // Get message document reference
      final messageRef = _queriesCollection
          .doc(queryId)
          .collection('messages')
          .doc(messageId);

      // Update message
      await messageRef.update({
        'text': newText,
        'isEdited': true,
      });

      // Get the updated message
      final messageDoc = await messageRef.get();
      if (messageDoc.exists) {
        final message = QueryMessage.fromMap(messageDoc.data() as Map<String, dynamic>, messageDoc.id);

        // If this was the last message, update query metadata
        final queryDoc = await _queriesCollection.doc(queryId).get();
        final query = QueryMetadata.fromMap(queryDoc.data() as Map<String, dynamic>, queryDoc.id);

        if (query.lastMessageTime.isAtSameMomentAs(message.timestamp)) {
          await _queriesCollection.doc(queryId).update({
            'lastMessage': newText,
          });
        }
      }
    } catch (e) {
      _logger.error('Error editing message: $e');
      rethrow;
    }
  }

  Future<void> deleteMessage({
    required String messageId,
    required String queryId,
  }) async {
    try {
      // Get message before deleting
      final messageDoc = await _queriesCollection
          .doc(queryId)
          .collection('messages')
          .doc(messageId)
          .get();

      if (!messageDoc.exists) {
        return; // Nothing to delete
      }

      final message = QueryMessage.fromMap(messageDoc.data() as Map<String, dynamic>, messageDoc.id);

      // Delete associated files if any
      if (message.imageUrls.isNotEmpty) {
        for (final url in message.imageUrls) {
          await _deleteFileFromUrl(url);
        }
      }

      if (message.voiceUrl != null) {
        await _deleteFileFromUrl(message.voiceUrl!);
      }

      // Delete the message
      await _queriesCollection
          .doc(queryId)
          .collection('messages')
          .doc(messageId)
          .delete();

      // If this was the last message, update query metadata with previous message
      final queryDoc = await _queriesCollection.doc(queryId).get();
      final query = QueryMetadata.fromMap(queryDoc.data() as Map<String, dynamic>, queryDoc.id);

      if (query.lastMessageTime.isAtSameMomentAs(message.timestamp)) {
        await _updateLastMessageAfterDeletion(queryId);
      }
    } catch (e) {
      _logger.error('Error deleting message: $e');
      rethrow;
    }
  }

  Future<void> markAsRead({
    required String queryId,
    required String messageId,
  }) async {
    try {
      // Only mark messages as read if the current user is not the sender
      final messageDoc = await _queriesCollection
          .doc(queryId)
          .collection('messages')
          .doc(messageId)
          .get();

      if (!messageDoc.exists) return;

      final message = QueryMessage.fromMap(messageDoc.data() as Map<String, dynamic>, messageDoc.id);

      if (message.senderId == currentUserId) return; // Don't mark own messages

      // Mark as read
      await messageDoc.reference.update({
        'isRead': true,
      });
    } catch (e) {
      _logger.error('Error marking message as read: $e');
      // Don't throw as this is not critical
    }
  }

  Future<void> markAllAsRead(String queryId) async {
    try {
      // Get all unread messages not sent by current user
      final querySnapshot = await _queriesCollection
          .doc(queryId)
          .collection('messages')
          .where('isRead', isEqualTo: false)
          .get();

      // Check if current user is admin or regular user
      final isAdmin = await isUserAdmin();
      final checkSenderId = isAdmin ? 'admin' : currentUserId!;

      // Calculate how many messages were read
      int unreadCount = 0;

      // Mark all messages as read
      for (final doc in querySnapshot.docs) {
        final message = QueryMessage.fromMap(doc.data(), doc.id);

        // Only mark as read if the message was not sent by this user
        if (message.senderId != checkSenderId) {
          await doc.reference.update({'isRead': true});
          unreadCount++;
        }
      }

      // Update unread count in metadata if any messages were read
      if (unreadCount > 0) {
        await _queriesCollection.doc(queryId).update({
          'unreadCount': 0, // Reset unread count for this user
        });
      }
    } catch (e) {
      _logger.error('Error marking all messages as read: $e');
      // Don't throw as this is not critical
    }
  }

  // ====== Helper Methods ======

  Future<void> _updateQueryAfterNewMessage({
    required String queryId,
    required QueryMessage message,
    required bool sentByAdmin,
  }) async {
    try {
      // Get query document
      final queryDoc = await _queriesCollection.doc(queryId).get();

      if (!queryDoc.exists) return;

      final query = QueryMetadata.fromMap(queryDoc.data() as Map<String, dynamic>, queryDoc.id);

      // Update query metadata
      String messageText = 'New message';
      if (message.text.isNotEmpty) {
        messageText = message.text;
      } else if (message.imageUrls.isNotEmpty) {
        messageText = '📷 Image';
      } else if (message.voiceUrl != null) {
        messageText = '🎤 Voice note';
      }

      final updates = <String, dynamic>{
        'lastMessage': messageText,
        'lastMessageTime': FieldValue.serverTimestamp(),
      };

      // Increment unread count for recipient
      if (sentByAdmin && query.userId != currentUserId) {
        // Admin sent to user
        updates['unreadCount'] = FieldValue.increment(1);
      } else if (!sentByAdmin) {
        // User sent to admin
        updates['unreadCount'] = FieldValue.increment(1);
      }

      await _queriesCollection.doc(queryId).update(updates);
    } catch (e) {
      _logger.error('Error updating query after new message: $e');
      // Don't throw as this is not critical
    }
  }

  Future<void> _updateLastMessageAfterDeletion(String queryId) async {
    try {
      // Get the most recent message
      final messagesSnapshot = await _queriesCollection
          .doc(queryId)
          .collection('messages')
          .orderBy('timestamp', descending: true)
          .limit(1)
          .get();

      if (messagesSnapshot.docs.isEmpty) {
        // No messages left, update with default message
        await _queriesCollection.doc(queryId).update({
          'lastMessage': 'No messages',
          'lastMessageTime': FieldValue.serverTimestamp(),
        });
        return;
      }

      // Update with the most recent message
      final lastMessage = QueryMessage.fromMap(
        messagesSnapshot.docs.first.data(),
        messagesSnapshot.docs.first.id
      );

      String messageText = 'Message';
      if (lastMessage.text.isNotEmpty) {
        messageText = lastMessage.text;
      } else if (lastMessage.imageUrls.isNotEmpty) {
        messageText = '📷 Image';
      } else if (lastMessage.voiceUrl != null) {
        messageText = '🎤 Voice note';
      }

      await _queriesCollection.doc(queryId).update({
        'lastMessage': messageText,
        'lastMessageTime': lastMessage.timestamp,
      });
    } catch (e) {
      _logger.error('Error updating last message after deletion: $e');
      // Don't throw as this is not critical
    }
  }

  Future<List<String>> _uploadFiles({
    required String queryId,
    required String messageId,
    required List<File> files,
    required String type,
  }) async {
    final urls = <String>[];
    const int maxRetries = 3;

    try {
      // Check permissions first
      if (!await FirebasePermissionsCheck.checkRequiredPermissions()) {
        _logger.error('Required permissions not granted for file uploads');
        return urls;
      }

      for (var i = 0; i < files.length; i++) {
        final file = files[i];
        if (!await file.exists()) {
          _logger.error('File does not exist: ${file.path}');
          continue; // Skip this file and try the next one
        }

        // Verify file is readable
        try {
          await file.length();
        } catch (e) {
          _logger.error('File exists but is not readable: ${file.path} - $e');
          continue;
        }

        final fileName = path.basename(file.path);
        final extension = path.extension(fileName);

        // Create storage reference with more specific path
        final timestamp = DateTime.now().millisecondsSinceEpoch;
        final userId = currentUserId?.isEmpty ?? true ? 'anonymous' : currentUserId!;
        final fileNameWithTimestamp = '${type}_${timestamp}_$i$extension';
        final storagePath = 'queries/$queryId/messages/$messageId/$fileNameWithTimestamp';

        _logger.info('Attempting to upload file to path: $storagePath');
        final storageRef = _storage.ref().child(storagePath);

        // Try upload with retries
        String? downloadUrl;
        int retryCount = 0;
        bool uploadSuccess = false;
        Exception? lastError;

        while (!uploadSuccess && retryCount < maxRetries) {
          try {
            retryCount++;
            _logger.info('Attempting file upload (attempt $retryCount of $maxRetries)');

            // Verify file exists and is readable
            if (!await file.exists()) {
              throw Exception('File does not exist: ${file.path}');
            }

            final fileSize = await file.length();
            if (fileSize <= 0) {
              throw Exception('File is empty: ${file.path}');
            }

            _logger.info('File exists and has size: $fileSize bytes at ${file.absolute.path}');

            // Set content type based on file extension
            final contentType = type == 'voice'
                ? 'audio/aac'
                : (extension.toLowerCase() == '.jpg' || extension.toLowerCase() == '.jpeg'
                    ? 'image/jpeg'
                    : extension.toLowerCase() == '.png'
                        ? 'image/png'
                        : 'application/octet-stream');

            final metadata = SettableMetadata(
              contentType: contentType,
              customMetadata: {
                'uploaded_by': userId,
                'uploaded_at': timestamp.toString(),
              }
            );

            // Upload file with metadata
            final uploadTask = storageRef.putFile(file, metadata);

            // Add progress logging
            uploadTask.snapshotEvents.listen((TaskSnapshot snapshot) {
              final progress = snapshot.bytesTransferred / snapshot.totalBytes;
              _logger.info('Upload progress: ${(progress * 100).toStringAsFixed(1)}%');
            }, onError: (e) {
              _logger.error('Upload stream error: $e');
            });

            // Wait for upload to complete with timeout
            final snapshot = await uploadTask.timeout(const Duration(seconds: 120));

            // Double check state is success before proceeding
            if (snapshot.state != TaskState.success) {
              throw Exception('Upload task completed but not successful. State: ${snapshot.state}');
            }

            // Get download URL
            _logger.info('Upload successful, retrieving download URL');
            downloadUrl = await snapshot.ref.getDownloadURL();
            if (downloadUrl.isNotEmpty) {
              urls.add(downloadUrl);
              uploadSuccess = true;
              _logger.info('File uploaded successfully: $downloadUrl');
            } else {
              throw Exception('Empty download URL received');
            }
          } catch (e) {
            lastError = e is Exception ? e : Exception(e.toString());
            if (retryCount >= maxRetries) {
              _logger.error('Error uploading file after $maxRetries attempts: $e');
            } else {
              _logger.warning('Upload attempt $retryCount failed: $e. Retrying...');
              await Future.delayed(Duration(seconds: retryCount * 2)); // Exponential backoff
            }
          }
        }

        // If all retries failed for this file, log the error but continue with other files
        if (!uploadSuccess) {
          _logger.error('Failed to upload file after all retries: ${lastError?.toString() ?? "Unknown error"}');
        }
      }

      // Return whatever URLs we managed to get
      return urls;
    } catch (e) {
      _logger.error('Error in _uploadFiles method: $e');
      // Return any URLs we managed to collect before the error
      return urls;
    }
  }

  Future<void> _deleteFileFromUrl(String url) async {
    try {
      // Get reference from URL
      final ref = _storage.refFromURL(url);
      // Delete file
      await ref.delete();
    } catch (e) {
      _logger.error('Error deleting file: $e');
      // Don't throw as this is not critical
    }
  }

  // ====== New Query Management ======

  Future<String> createQuery(String topic, String initialMessage) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      final now = DateTime.now();

      final queryData = {
        'userId': user.uid,
        'userDisplayName': user.displayName ?? 'User',
        'userEmail': user.email ?? '',
        'userPhotoUrl': user.photoURL ?? '',
        'topic': topic,
        'lastMessage': initialMessage,
        'createdAt': Timestamp.fromDate(now),
        'lastMessageTime': Timestamp.fromDate(now),
        'unreadCount': 1, // Admin has 1 unread message initially
        'unreadUserCount': 0,
        'status': 'open',
      };

      // Add query document
      final queryRef = await _queriesCollection.add(queryData);

      // Add initial message
      await queryRef.collection('messages').add({
        'queryId': queryRef.id,
        'senderId': user.uid,
        'senderName': user.displayName ?? 'User',
        'message': initialMessage,
        'timestamp': Timestamp.fromDate(now),
        'isRead': false,
        'isAdmin': false,
      });

      return queryRef.id;
    } catch (e) {
      debugPrint('Error creating query: $e');
      rethrow;
    }
  }

  // Get queries for current user (realtime)
  Stream<List<models.Query>> getUserQueries() {
    final user = _auth.currentUser;
    if (user == null) {
      // Return empty stream if not authenticated
      return Stream.value([]);
    }

    return _queriesCollection
        .where('userId', isEqualTo: user.uid)
        .orderBy('lastMessageTime', descending: true)
        .snapshots()
        .map((snapshot) {
          return snapshot.docs
              .map((doc) => models.Query.fromFirestore(doc))
              .toList();
        });
  }

  // Get all queries for admin (realtime)
  Stream<List<models.Query>> getAdminQueries() {
    return _queriesCollection
        .orderBy('lastMessageTime', descending: true)
        .snapshots()
        .map((snapshot) {
          return snapshot.docs
              .map((doc) => models.Query.fromFirestore(doc))
              .toList();
        });
  }

  // Get a specific query by ID
  Future<models.Query?> getQueryById(String queryId) async {
    try {
      final docSnapshot = await _queriesCollection.doc(queryId).get();
      if (docSnapshot.exists) {
        return models.Query.fromFirestore(docSnapshot);
      }
      return null;
    } catch (e) {
      debugPrint('Error getting query: $e');
      return null;
    }
  }

  // Get messages for a query (realtime)
  Stream<List<Message>> getQueryMessages(String queryId) {
    return _queriesCollection
        .doc(queryId)
        .collection('messages')
        .orderBy('timestamp', descending: false)
        .snapshots()
        .map((snapshot) {
          return snapshot.docs
              .map((doc) => Message.fromFirestore(doc))
              .toList();
        });
  }

  // Mark messages as read
  Future<void> markMessagesAsRead(String queryId, {bool isAdmin = false}) async {
    try {
      final batch = _firestore.batch();

      // Get all unread messages from the other party
      final messagesSnapshot = await _queriesCollection
          .doc(queryId)
          .collection('messages')
          .where('isRead', isEqualTo: false)
          .where('isAdmin', isEqualTo: !isAdmin) // If admin is reading, get user messages
          .get();

      // Mark all messages as read
      for (final doc in messagesSnapshot.docs) {
        batch.update(doc.reference, {'isRead': true});
      }

      // Reset the unread counter for the reader
      if (isAdmin) {
        batch.update(_queriesCollection.doc(queryId), {'unreadCount': 0});
      } else {
        batch.update(_queriesCollection.doc(queryId), {'unreadUserCount': 0});
      }

      await batch.commit();
    } catch (e) {
      debugPrint('Error marking messages as read: $e');
    }
  }

  // Close a query
  Future<void> closeQuery(String queryId) async {
    try {
      await _queriesCollection.doc(queryId).update({
        'status': 'closed',
      });
    } catch (e) {
      debugPrint('Error closing query: $e');
      rethrow;
    }
  }

  // Reopen a query
  Future<void> reopenQuery(String queryId) async {
    try {
      await _queriesCollection.doc(queryId).update({
        'status': 'open',
      });
    } catch (e) {
      debugPrint('Error reopening query: $e');
      rethrow;
    }
  }

  // Get unread queries count for user
  Stream<int> getUserUnreadCount() {
    final user = _auth.currentUser;
    if (user == null) {
      return Stream.value(0);
    }

    return _queriesCollection
        .where('userId', isEqualTo: user.uid)
        .where('unreadUserCount', isGreaterThan: 0)
        .snapshots()
        .map((snapshot) => snapshot.docs.length);
  }

  // Get total unread messages count for admin
  Stream<int> getAdminUnreadCount() {
    return _queriesCollection
        .where('unreadCount', isGreaterThan: 0)
        .snapshots()
        .map((snapshot) => snapshot.docs.length);
  }

}