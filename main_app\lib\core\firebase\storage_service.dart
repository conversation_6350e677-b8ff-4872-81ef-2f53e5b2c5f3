import 'dart:io';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/foundation.dart';
import '../utils/app_logger.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class StorageService {
  // Use FirebaseStorage.instance directly to avoid bucket configuration issues
  static final FirebaseStorage _storage = FirebaseStorage.instance;
  static final AppLogger _logger = AppLogger('StorageService');

  // Getter for the storage instance
  static FirebaseStorage get storage => _storage;

  /// Upload a file to Firebase Storage (consolidated method)
  static Future<String> uploadFile(File file, String path, {Function(double)? onProgress}) async {
    try {
      // Convert file to bytes and use the optimized uploadBytes method
      final bytes = await compute(_readFileBytes, file.path);
      return await uploadBytes(bytes, path, onProgress: onProgress);
    } catch (e) {
      _logger.error('Error uploading file', error: e);
      rethrow;
    }
  }

  /// Static method to read file bytes in isolate
  static Future<Uint8List> _readFileBytes(String filePath) async {
    final file = File(filePath);
    return await file.readAsBytes();
  }

  /// Upload bytes to Firebase Storage with automatic directory creation and error handling
  static Future<String> uploadBytes(List<int> bytes, String path, {Function(double)? onProgress}) async {
    // First check if user is authenticated
    final currentUser = FirebaseAuth.instance.currentUser;
    if (currentUser == null) {
      _logger.error('User not authenticated for upload');
      throw Exception('User must be authenticated to upload files');
    }
    
    int retryCount = 0;
    const maxRetries = 3;
    Exception? lastError;

    while (retryCount < maxRetries) {
      try {
        _logger.info('Uploading bytes to path: $path (attempt ${retryCount + 1}/$maxRetries)');
        _logger.debug('Using storage bucket: ${_storage.bucket}');

        // First ensure the parent directory structure exists
        final dirPath = path.contains('/') ? path.substring(0, path.lastIndexOf('/')) : '';
        // Skip placeholder directory creation for temp driver documents to avoid rule conflicts
        if (dirPath.isNotEmpty && !dirPath.startsWith('temp_uploads/')) {
          await validateStoragePath(dirPath);
        }

        // Create reference after validating path
        final ref = _storage.ref().child(path);

        // Determine content type
        final contentType = _getContentTypeFromPath(path);
        final metadata = SettableMetadata(contentType: contentType);

        // Start upload task with Uint8List to ensure compatibility
        final uploadTask = ref.putData(Uint8List.fromList(bytes), metadata);

        // Monitor progress
        uploadTask.snapshotEvents.listen((TaskSnapshot snapshot) {
          final progress = snapshot.bytesTransferred / snapshot.totalBytes;
          _logger.debug('Upload progress: ${(progress * 100).toStringAsFixed(1)}%');
          onProgress?.call(progress);
        }, onError: (e) {
          _logger.error('Error in progress stream', error: e);
        });

        // Wait for completion
        final snapshot = await uploadTask.whenComplete(() {
          _logger.info('Upload task completed successfully');
        });

        // Get download URL after completion
        final url = await snapshot.ref.getDownloadURL();
        _logger.info('Upload successful, got download URL: $url');
        return url;
      } catch (e) {
        lastError = e as Exception;
        _logger.error('Upload failed (attempt ${retryCount + 1}/$maxRetries)', error: e);

        // Handle specific Firebase errors
        if (e is FirebaseException) {
          if (e.code == 'object-not-found') {
            _logger.warning('Object not found error, trying fallback upload');
            return await _fallbackUpload(bytes, path, onProgress: onProgress);
          } else if (e.code == 'unauthorized') {
            _logger.error('Unauthorized access to Firebase Storage');
            // No retry for auth errors
            rethrow;
          }
        }

        // Increment retry counter and wait before retrying
        retryCount++;
        if (retryCount < maxRetries) {
          final delay = Duration(seconds: retryCount * 2); // Exponential backoff
          _logger.info('Retrying upload after ${delay.inSeconds} seconds...');
          await Future.delayed(delay);
        }
      }
    }

    // If we've exhausted all retries, try the fallback method
    _logger.warning('All $maxRetries upload attempts failed, using fallback upload as last resort');
    try {
      return await _fallbackUpload(bytes, path, onProgress: onProgress);
    } catch (e) {
      _logger.error('Both normal and fallback uploads failed', error: e);
      throw lastError ?? Exception('Upload failed after multiple attempts');
    }
  }

  /// Determine content type from file path
  static String _getContentTypeFromPath(String path) {
    final ext = path.split('.').last.toLowerCase();
    switch (ext) {
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      case 'png':
        return 'image/png';
      case 'pdf':
        return 'application/pdf';
      case 'doc':
      case 'docx':
        return 'application/msword';
      default:
        return 'application/octet-stream';
    }
  }

  /// Upload data from memory to Firebase Storage (redirects to uploadBytes for consistency)
  static Future<String> uploadData(Uint8List data, String path, String contentType, {Function(double)? onProgress}) async {
    // Use the consolidated uploadBytes method for consistency
    return await uploadBytes(data, path, onProgress: onProgress);
  }

  /// Delete a file from Firebase Storage
  static Future<void> deleteFile(String path) async {
    try {
      final ref = _storage.ref().child(path);
      await ref.delete();
    } catch (e) {
      if (kDebugMode) {
        print('Error deleting file: $e');
      }
      rethrow;
    }
  }

  /// Get download URL for a file
  static Future<String> getDownloadURL(String path) async {
    int attempts = 0;
    int maxRetries = 3;

    while (attempts < maxRetries) {
      attempts++;
      try {
        _logger.info('GetDownloadURL attempt $attempts/$maxRetries for path: $path');

        final ref = _storage.ref().child(path);
        final url = await ref.getDownloadURL();

        _logger.info('Download URL fetched successfully');
        return url;
      } on FirebaseException catch (e) {
        _logger.error('Firebase error on attempt $attempts: ${e.code} - ${e.message}', error: e);

        if (e.code == 'object-not-found') {
          _logger.error('File not found at path: $path');
          // This is an expected error when the file doesn't exist, no retries
          throw Exception('File not found. The file may have been deleted or the path is incorrect.');
        } else if (attempts < maxRetries) {
          // For other errors, retry with delay
          _logger.info('Will retry getDownloadURL after delay');
          await Future.delayed(const Duration(seconds: 1));
          continue;
        } else {
          rethrow;
        }
      } catch (e) {
        _logger.error('Unexpected error on attempt $attempts', error: e);
        if (attempts < maxRetries) {
          await Future.delayed(const Duration(seconds: 1));
          continue;
        } else {
          rethrow;
        }
      }
    }

    // This should never be reached due to returns and throws above
    throw Exception('Failed to get download URL after $maxRetries attempts');
  }

  /// Get a reference to a file in Firebase Storage
  static Reference getReference(String path) {
    return _storage.ref().child(path);
  }

  /// Get metadata for a file
  static Future<FullMetadata> getMetadata(String path) async {
    try {
      final ref = _storage.ref().child(path);
      return await ref.getMetadata();
    } catch (e) {
      if (kDebugMode) {
        print('Error getting metadata: $e');
      }
      rethrow;
    }
  }

  /// List all files in a directory
  static Future<ListResult> listFiles(String path) async {
    try {
      final ref = _storage.ref().child(path);
      return await ref.listAll();
    } catch (e) {
      if (kDebugMode) {
        print('Error listing files: $e');
      }
      rethrow;
    }
  }

  /// Generate a unique file path for uploads
  static String generateFilePath(String userId, String folder, String fileName) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return '$userId/$folder/$timestamp-$fileName';
  }

  /// Get the Firebase Storage bucket name
  static String getBucketName() {
    final bucket = _storage.bucket;
    _logger.debug('Current Firebase Storage bucket: $bucket');
    return bucket;
  }

  /// Check if we're using the correct storage bucket
  static bool isUsingCorrectBucket() {
    final bucket = getBucketName();
    return bucket.contains('appspot.com');
  }

  /// Fix storage bucket format if it's incorrect
  static bool fixStorageBucket() {
    final bucket = getBucketName();

    if (bucket.contains('firebasestorage.app')) {
      _logger.warning('Detected incorrect storage bucket format: $bucket');
      _logger.info('Correct bucket format should contain appspot.com');
      return false;
    }

    _logger.info('Storage bucket is correct: $bucket');
    return true;
  }

  /// Validate a storage path by creating empty placeholders for each directory
  static Future<bool> validateStoragePath(String path) async {
    _logger.info('Validating storage path: $path');

    try {
      // Normalize the path - remove any leading slashes
      final normalizedPath = path.startsWith('/') ? path.substring(1) : path;

      // Split the path into directories
      final parts = normalizedPath.split('/').where((part) => part.isNotEmpty).toList();

      // Ensure the structure exists by creating placeholders at each level
      if (parts.isEmpty) {
        return true; // Root path is always valid
      }

      String currentPath = '';

      for (int i = 0; i < parts.length; i++) {
        currentPath += (currentPath.isEmpty ? '' : '/') + parts[i];

        // Skip creating placeholder for the last part if it might be a file
        if (i == parts.length - 1 && parts[i].contains('.')) {
          continue;
        }

        try {
          final placeholderPath = '$currentPath/.placeholder';
          _logger.debug('Creating placeholder at: $placeholderPath');

          // Create an empty file to ensure the directory exists
          final placeholderRef = _storage.ref().child(placeholderPath);
          await placeholderRef.putData(
              Uint8List.fromList([]),
              SettableMetadata(contentType: 'application/octet-stream')
          );

          _logger.debug('Created placeholder successfully at $placeholderPath');
        } catch (e) {
          // Log the error but continue - most placeholder errors can be safely ignored
          _logger.debug('Note when creating placeholder: ${e.toString()}');
        }
      }

      _logger.info('Path validation successful: $path');
      return true; // Return true to continue with the upload
    } catch (e) {
      _logger.error('Path validation error', error: e);
      return true; // Return true to continue with the upload despite errors
    }
  }

  /// Create a simplified path for more reliable uploads
  static String createSimplifiedPath(String userId, String documentType, String extension) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    // Use a flat directory structure to avoid nested directory issues
    return 'direct_uploads/user_${userId}_${documentType.replaceAll(' ', '_')}_$timestamp$extension';
  }

  /// Initialize method to call during app startup
  static void initialize() {
    try {
      _logger.info('StorageService initialized with bucket: ${_storage.bucket}');

      // If bucket is in wrong format, log a warning but continue
      if (!isUsingCorrectBucket()) {
        _logger.warning('Storage bucket format may be incorrect. This could cause upload issues.');
        _logger.info('Expected bucket format should contain appspot.com');
      }
    } catch (e) {
      _logger.error('Error during StorageService initialization', error: e);
    }
  }

  /// Create a simplified path for more reliable uploads
  static String _createSimplifiedPath(String userId, String documentType, String extension) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    // Use a flat directory structure to avoid nested directory issues
    return 'direct_uploads/user_${userId}_${documentType.replaceAll(' ', '_')}_$timestamp$extension';
  }

  /// Fallback upload method that uses a simplified path structure
  static Future<String> _fallbackUpload(List<int> bytes, String path, {Function(double)? onProgress}) async {
    try {
      _logger.info('Using fallback upload method for path: $path');

      // Extract information from the original path
      final fileName = path.split('/').last;
      final extension = fileName.contains('.') ? '.${fileName.split('.').last}' : '';

      // Get current user ID
      final user = FirebaseAuth.instance.currentUser;
      final userId = user?.uid ?? 'anonymous';

      // Create a simplified path in a flat directory structure
      final simplePath = _createSimplifiedPath(userId, 'fallback', extension);
      _logger.info('Using simplified path: $simplePath');

      // Ensure the direct_uploads directory exists - but don't nest directories
      // This is a direct reference to a top-level directory
      final directUploadsRef = _storage.ref().child('direct_uploads');

      try {
        // Create a placeholder to ensure the directory exists
        await directUploadsRef.child('.placeholder').putData(
          Uint8List.fromList([]),
          SettableMetadata(contentType: 'application/octet-stream')
        );
        _logger.debug('Created direct_uploads directory placeholder');
      } catch (e) {
        // Ignore errors here - the directory might already exist
        _logger.debug('Note when creating direct_uploads directory: $e');
      }

      // Create reference directly (no parent validation needed for flat structure)
      final ref = _storage.ref().child(simplePath);

      // Determine content type
      final contentType = _getContentTypeFromPath(simplePath);
      final metadata = SettableMetadata(
        contentType: contentType,
        customMetadata: {
          'originalPath': path,
          'uploadTimestamp': DateTime.now().millisecondsSinceEpoch.toString(),
          'uploadType': 'fallback',
          'userId': userId,
        }
      );

      // Start upload task
      final uploadTask = ref.putData(Uint8List.fromList(bytes), metadata);

      // Monitor progress
      uploadTask.snapshotEvents.listen((TaskSnapshot snapshot) {
        final progress = snapshot.bytesTransferred / snapshot.totalBytes;
        _logger.debug('Fallback upload progress: ${(progress * 100).toStringAsFixed(1)}%');
        onProgress?.call(progress);
      }, onError: (e) {
        _logger.error('Error in fallback upload stream', error: e);
      });

      // Wait for completion
      final snapshot = await uploadTask.whenComplete(() {
        _logger.info('Fallback upload task completed successfully');
      });

      // Get download URL after completion
      final url = await snapshot.ref.getDownloadURL();
      _logger.info('Fallback upload successful, got download URL: $url');

      // Store a mapping of the original path to the fallback path in Firestore for reference
      try {
        if (user != null) {
          await FirebaseFirestore.instance
              .collection('storage_fallbacks')
              .doc(user.uid)
              .collection('paths')
              .add({
                'originalPath': path,
                'fallbackPath': simplePath,
                'downloadUrl': url,
                'timestamp': FieldValue.serverTimestamp(),
              });
          _logger.info('Stored fallback path mapping in Firestore');
        }
      } catch (e) {
        // Don't fail the upload if we can't store the mapping
        _logger.warning('Could not store fallback path mapping: $e');
      }

      return url;
    } catch (e) {
      _logger.error('Fallback upload failed', error: e);
      rethrow;
    }
  }

  /// Debug utility to help identify Firebase Storage issues
  static Future<Map<String, String>> debugStorageConnection() async {
    final Map<String, String> results = {};

    try {
      // Check authentication status
      final user = FirebaseAuth.instance.currentUser;
      results['Auth Status'] = user != null ? 'Authenticated (${user.uid})' : 'Not authenticated';

      // Check storage bucket
      final bucket = getBucketName();
      results['Storage Bucket'] = bucket;
      results['Bucket Format'] = isUsingCorrectBucket() ? 'Valid (contains appspot.com)' : 'Invalid';

      // Try to write a test file
      try {
        final testPath = 'debug/test_${DateTime.now().millisecondsSinceEpoch}.txt';
        final ref = _storage.ref().child(testPath);

        // Upload a small test file
        final testData = 'Test connection at ${DateTime.now()}';
        final task = ref.putString(testData);

        // Wait for upload
        await task.whenComplete(() => null);

        // Try to get URL
        final url = await ref.getDownloadURL();
        results['Write Test'] = 'Success: wrote test file and got URL: ${url.substring(0, 50)}...';

        // Clean up
        await ref.delete();
        results['Delete Test'] = 'Success: deleted test file';
      } catch (e) {
        results['Storage Write Test'] = 'Failed: $e';
      }

      // Try to list files in the root directory
      try {
        final list = await _storage.ref().listAll();
        results['List Operation'] = 'Success: found ${list.items.length} files and ${list.prefixes.length} directories';
      } catch (e) {
        results['List Operation'] = 'Failed: $e';
      }

      results['Summary'] = 'Debug completed at ${DateTime.now()}';

    } catch (e) {
      results['Error'] = 'Error running diagnostic tests: $e';
    }

    return results;
  }
}