name: Continuous Integration

on:
  push:
    branches: [ main, master, develop, feature/*, hotfix/* ]
    paths:
      - 'main_app/**'
  pull_request:
    branches: [ main, master, develop ]
    paths:
      - 'main_app/**'

env:
  FLUTTER_VERSION: '3.19.0'
  JAVA_VERSION: '17'

jobs:
  analyze:
    name: Code Analysis
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./main_app
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: ${{ env.FLUTTER_VERSION }}
          channel: 'stable'
          cache: true
          
      - name: Get Flutter dependencies
        run: flutter pub get
        
      - name: Verify formatting
        run: dart format --output=none --set-exit-if-changed .
        
      - name: Analyze project source
        run: flutter analyze --no-fatal-infos
        
      - name: Check for unused dependencies
        run: |
          flutter pub deps --json > deps.json
          # Add custom script to check for unused dependencies
          
      - name: Security audit
        run: |
          # Check for known security vulnerabilities
          flutter pub audit || true

  test:
    name: Unit & Widget Tests
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./main_app
    
    strategy:
      matrix:
        test_type: [unit, widget, integration]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: ${{ env.FLUTTER_VERSION }}
          channel: 'stable'
          cache: true
          
      - name: Get Flutter dependencies
        run: flutter pub get
        
      - name: Run unit tests
        if: matrix.test_type == 'unit'
        run: flutter test test/unit/ --coverage
        
      - name: Run widget tests
        if: matrix.test_type == 'widget'
        run: flutter test test/widget/ --coverage
        
      - name: Run integration tests
        if: matrix.test_type == 'integration'
        run: flutter test integration_test/ --coverage
        
      - name: Upload coverage reports
        if: matrix.test_type == 'unit'
        uses: codecov/codecov-action@v3
        with:
          file: ./main_app/coverage/lcov.info
          fail_ci_if_error: false
          flags: unittests
          name: codecov-umbrella

  build_check:
    name: Build Verification
    runs-on: ubuntu-latest
    needs: [analyze, test]
    defaults:
      run:
        working-directory: ./main_app
    
    strategy:
      matrix:
        platform: [android, web]
        build_mode: [debug, release]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Java (Android only)
        if: matrix.platform == 'android'
        uses: actions/setup-java@v4
        with:
          distribution: 'zulu'
          java-version: ${{ env.JAVA_VERSION }}
          
      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: ${{ env.FLUTTER_VERSION }}
          channel: 'stable'
          cache: true
          
      - name: Get Flutter dependencies
        run: flutter pub get
        
      - name: Build Android APK (Debug)
        if: matrix.platform == 'android' && matrix.build_mode == 'debug'
        run: flutter build apk --debug
        
      - name: Build Android APK (Release)
        if: matrix.platform == 'android' && matrix.build_mode == 'release'
        run: flutter build apk --release --dart-define=ENVIRONMENT=staging
        
      - name: Build Web (Debug)
        if: matrix.platform == 'web' && matrix.build_mode == 'debug'
        run: flutter build web --debug
        
      - name: Build Web (Release)
        if: matrix.platform == 'web' && matrix.build_mode == 'release'
        run: flutter build web --release --dart-define=ENVIRONMENT=staging

  performance:
    name: Performance Tests
    runs-on: ubuntu-latest
    needs: build_check
    defaults:
      run:
        working-directory: ./main_app
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: ${{ env.FLUTTER_VERSION }}
          channel: 'stable'
          cache: true
          
      - name: Get Flutter dependencies
        run: flutter pub get
        
      - name: Run performance tests
        run: |
          # Add performance testing commands here
          echo "Running performance tests..."
          # flutter drive --target=test_driver/perf_test.dart
          
      - name: Bundle size analysis
        run: |
          flutter build web --release --dart-define=ENVIRONMENT=staging
          du -sh build/web/
          echo "Web bundle size:" >> $GITHUB_STEP_SUMMARY
          du -sh build/web/ >> $GITHUB_STEP_SUMMARY

  security:
    name: Security Scan
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./main_app
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: './main_app'
          format: 'sarif'
          output: 'trivy-results.sarif'
          
      - name: Upload Trivy scan results to GitHub Security tab
        uses: github/codeql-action/upload-sarif@v2
        if: always()
        with:
          sarif_file: 'trivy-results.sarif'

  notify:
    name: Notification
    runs-on: ubuntu-latest
    needs: [analyze, test, build_check, performance, security]
    if: always()
    
    steps:
      - name: Notify on success
        if: ${{ needs.analyze.result == 'success' && needs.test.result == 'success' && needs.build_check.result == 'success' }}
        run: |
          echo "✅ All CI checks passed successfully!"
          echo "Ready for deployment 🚀"
          
      - name: Notify on failure
        if: ${{ needs.analyze.result == 'failure' || needs.test.result == 'failure' || needs.build_check.result == 'failure' }}
        run: |
          echo "❌ CI checks failed!"
          echo "Please review the failed jobs and fix the issues."
          exit 1
