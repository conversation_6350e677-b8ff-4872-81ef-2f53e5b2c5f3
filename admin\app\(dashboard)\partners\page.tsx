'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import {
  Building2,
  Search,
  Filter,
  Download,
  Plus,
  Eye,
  Edit,
  CheckCircle,
  XCircle,
  Clock,
  Mail,
  Phone,
  MapPin,
} from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { DataTable } from '@/components/ui/data-table'
import { PartnerModal } from '@/components/partners/partner-modal'
import { ContactInfoModal } from '@/components/partners/contact-info-modal'

import { LoadingSpinner } from '@/components/ui/loading-spinner'
import { usePartners } from '@/lib/hooks/use-partners'
import { formatDate, formatRelativeTime } from '@/lib/utils'

interface Partner {
  id: string
  companyName: string
  email: string
  phone: string
  address: string
  description: string
  status: 'pending' | 'approved' | 'rejected'
  createdAt: Date
  approvedAt?: Date
  approvedBy?: string
  rejectionReason?: string
  welcomeEmailSent?: boolean
}

export default function PartnersPage() {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedPartner, setSelectedPartner] = useState<Partner | null>(null)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [modalMode, setModalMode] = useState<'view' | 'edit' | 'create'>('view')
  const [filters, setFilters] = useState({
    status: 'all'
  })
  const [contactModalOpen, setContactModalOpen] = useState(false)
  const [contactType, setContactType] = useState<'email' | 'phone'>('email')

  const { partners, isLoading, error, updatePartner, approvePartner, rejectPartner, createPartner } = usePartners()

  const filteredPartners = partners?.filter(partner => {
    const matchesSearch = partner.companyName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         partner.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         partner.phone.includes(searchQuery) ||
                         partner.address.toLowerCase().includes(searchQuery.toLowerCase())

    const matchesStatus = filters.status === 'all' || partner.status === filters.status

    return matchesSearch && matchesStatus
  }) || []

  const handleCreatePartner = () => {
    setSelectedPartner(null)
    setModalMode('create')
    setIsModalOpen(true)
  }

  const handleViewPartner = (partner: Partner) => {
    setSelectedPartner(partner)
    setModalMode('view')
    setIsModalOpen(true)
  }

  const handleEditPartner = (partner: Partner) => {
    setSelectedPartner(partner)
    setModalMode('edit')
    setIsModalOpen(true)
  }

  const handleShowEmail = (partner: Partner) => {
    setSelectedPartner(partner)
    setContactType('email')
    setContactModalOpen(true)
  }

  const handleShowPhone = (partner: Partner) => {
    setSelectedPartner(partner)
    setContactType('phone')
    setContactModalOpen(true)
  }



  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return 'success'
      case 'rejected':
        return 'error'
      case 'suspended':
        return 'warning'
      case 'pending':
        return 'info'
      default:
        return 'secondary'
    }
  }

  const columns = [
    {
      key: 'company',
      label: 'Company',
      render: (partner: Partner) => (
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
            <Building2 className="w-5 h-5 text-primary-600" />
          </div>
          <div>
            <p className="font-medium text-gray-900">{partner.companyName}</p>
            <p className="text-sm text-gray-500">{partner.email}</p>
          </div>
        </div>
      )
    },
    {
      key: 'contact',
      label: 'Contact',
      render: (partner: Partner) => (
        <div>
          <p className="text-sm">{partner.phone}</p>
          <p className="text-sm text-gray-500">{partner.address}</p>
        </div>
      )
    },
    {
      key: 'description',
      label: 'Description',
      render: (partner: Partner) => (
        <div className="max-w-xs">
          <p className="text-sm text-gray-600 truncate">{partner.description}</p>
        </div>
      )
    },
    {
      key: 'status',
      label: 'Status',
      render: (partner: Partner) => (
        <Badge variant={getStatusColor(partner.status) as any}>
          {partner.status}
        </Badge>
      )
    },
    {
      key: 'appliedDate',
      label: 'Applied',
      render: (partner: Partner) => formatRelativeTime(partner.createdAt)
    },
    {
      key: 'actions',
      label: 'Actions',
      render: (partner: Partner) => (
        <div className="flex items-center space-x-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleViewPartner(partner)}
            title="View Details"
          >
            <Eye className="w-4 h-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleShowEmail(partner)}
            className="text-blue-600 hover:text-blue-700"
            title="Show Partner Email"
          >
            <Mail className="w-4 h-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleShowPhone(partner)}
            className="text-green-600 hover:text-green-700"
            title="Show Partner Phone"
          >
            <Phone className="w-4 h-4" />
          </Button>

        </div>
      )
    }
  ]

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Partner Management</h1>
          <p className="text-gray-600">Manage business partnerships and approvals</p>
        </div>
        <Button onClick={handleCreatePartner} className="gradient-primary">
          <Plus className="w-4 h-4 mr-2" />
          Add Partner
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Partners</p>
                <p className="text-2xl font-bold text-gray-900">{partners?.length || 0}</p>
              </div>
              <Building2 className="w-8 h-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Pending Approval</p>
                <p className="text-2xl font-bold text-gray-900">
                  {filteredPartners.filter(p => p.status === 'pending').length}
                </p>
              </div>
              <Clock className="w-8 h-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Approved Partners</p>
                <p className="text-2xl font-bold text-gray-900">
                  {filteredPartners.filter(p => p.status === 'approved').length}
                </p>
              </div>
              <CheckCircle className="w-8 h-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Rejected</p>
                <p className="text-2xl font-bold text-gray-900">
                  {filteredPartners.filter(p => p.status === 'rejected').length}
                </p>
              </div>
              <XCircle className="w-8 h-8 text-red-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <CardTitle>Business Partners</CardTitle>
          <CardDescription>
            Manage partnership applications and contracts
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search partners by company name, email, phone, or address..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <select
              value={filters.status}
              onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              <option value="all">All Status</option>
              <option value="pending">Pending</option>
              <option value="approved">Approved</option>
              <option value="rejected">Rejected</option>
            </select>

            <Button variant="outline">
              <Download className="w-4 h-4 mr-2" />
              Export
            </Button>
          </div>

          <DataTable
            data={filteredPartners}
            columns={columns}
            searchQuery={searchQuery}
            isLoading={isLoading}
          />
        </CardContent>
      </Card>

      {/* Partner Modal */}
      <PartnerModal
        partner={selectedPartner}
        mode={modalMode}
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onSave={async (partnerData) => {
          try {
            if (modalMode === 'create') {
              await createPartner(partnerData)
            } else if (modalMode === 'edit' && selectedPartner) {
              await updatePartner(selectedPartner.id, partnerData)
            }
            setIsModalOpen(false)
          } catch (error) {
            console.error('Error saving partner:', error)
          }
        }}
      />

      {/* Contact Info Modal */}
      <ContactInfoModal
        partner={selectedPartner}
        type={contactType}
        isOpen={contactModalOpen}
        onClose={() => setContactModalOpen(false)}
      />
    </motion.div>
  )
}
