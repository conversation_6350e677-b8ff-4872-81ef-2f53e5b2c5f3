@echo off
REM Android build script for Drive-On Main App (Windows)
REM This script builds APK and App Bundle for Android

echo 🤖 Starting Android build process...

REM Check if Flutter is installed
flutter --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Flutter is not installed. Please install it first:
    echo https://docs.flutter.dev/get-started/install
    pause
    exit /b 1
)

REM Check if Java is installed
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Java is not installed. Please install Java 17 or later.
    pause
    exit /b 1
)

REM Get Flutter dependencies
echo 📦 Getting Flutter dependencies...
flutter pub get
if %errorlevel% neq 0 (
    echo ❌ Failed to get Flutter dependencies
    pause
    exit /b 1
)

REM Clean previous builds
echo 🧹 Cleaning previous builds...
flutter clean
flutter pub get

REM Check Flutter doctor for Android setup
echo 🩺 Checking Flutter doctor...
flutter doctor

REM Build APK
echo 📱 Building APK (release)...
flutter build apk --release
if %errorlevel% neq 0 (
    echo ❌ APK build failed
    pause
    exit /b 1
)

REM Build App Bundle
echo 📦 Building App Bundle (release)...
flutter build appbundle --release
if %errorlevel% neq 0 (
    echo ❌ App Bundle build failed
    pause
    exit /b 1
)

REM Display build information
echo.
echo ✅ Android build completed successfully!
echo.
echo 📊 Build Information:
echo ====================

if exist "build\app\outputs\flutter-apk\app-release.apk" (
    echo 📱 APK: build\app\outputs\flutter-apk\app-release.apk
) else (
    echo ❌ APK build failed
)

if exist "build\app\outputs\bundle\release\app-release.aab" (
    echo 📦 App Bundle: build\app\outputs\bundle\release\app-release.aab
) else (
    echo ❌ App Bundle build failed
)

echo.
echo 🚀 Next Steps:
echo 1. Test the APK: flutter install
echo 2. Upload to Play Store: Use the App Bundle (.aab file)
echo 3. Distribute to testers: Use Firebase App Distribution
pause
