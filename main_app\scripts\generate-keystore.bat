@echo off
REM Generate Production Keystore for Drive-On App
REM This script creates a production keystore for Google Play Store deployment

setlocal enabledelayedexpansion

echo 🔐 Drive-On Production Keystore Generator
echo ========================================

REM Check if Java keytool is available
set KEYTOOL_PATH=""
where keytool >nul 2>nul
if %errorlevel% equ 0 (
    set KEYTOOL_PATH=keytool
) else (
    REM Try Android Studio JBR
    if exist "C:\Program Files\Android\Android Studio\jbr\bin\keytool.exe" (
        set KEYTOOL_PATH="C:\Program Files\Android\Android Studio\jbr\bin\keytool.exe"
    ) else (
        echo ❌ Error: Java keytool not found in PATH or Android Studio
        echo Please install Java JDK or Android Studio
        pause
        exit /b 1
    )
)

REM Set keystore details
set KEYSTORE_NAME=drive-on-release-key.jks
set KEY_ALIAS=drive-on-key
set KEYSTORE_PATH=android\app\%KEYSTORE_NAME%
set KEY_PROPERTIES_PATH=android\key.properties

echo 📝 Keystore Configuration:
echo - Keystore file: %KEYSTORE_PATH%
echo - Key alias: %KEY_ALIAS%
echo - Algorithm: RSA
echo - Key size: 2048
echo - Validity: 25 years
echo.

REM Check if keystore already exists
if exist "%KEYSTORE_PATH%" (
    echo ⚠️  Warning: Keystore file already exists at %KEYSTORE_PATH%
    set /p OVERWRITE="Do you want to overwrite it? (y/N): "
    if /i not "!OVERWRITE!"=="y" (
        echo Operation cancelled.
        pause
        exit /b 0
    )
    del "%KEYSTORE_PATH%"
)

echo 🔑 Generating production keystore...
echo.
echo Please provide the following information when prompted:
echo - Use strong passwords (minimum 8 characters)
echo - Remember these passwords - you'll need them for deployment
echo - Store passwords securely (consider using a password manager)
echo.

REM Generate the keystore
%KEYTOOL_PATH% -genkey -v -keystore "%KEYSTORE_PATH%" -alias %KEY_ALIAS% -keyalg RSA -keysize 2048 -validity 9125 -storetype JKS

if %errorlevel% neq 0 (
    echo ❌ Error: Failed to generate keystore
    pause
    exit /b 1
)

echo.
echo ✅ Keystore generated successfully!
echo.

REM Prompt for passwords to create key.properties
echo 📝 Creating key.properties file...
echo.
echo Please enter the passwords you just used:
set /p STORE_PASSWORD="Store password: "
set /p KEY_PASSWORD="Key password: "

REM Create key.properties file
echo # Drive-On Production Keystore Configuration > "%KEY_PROPERTIES_PATH%"
echo # Generated on %date% %time% >> "%KEY_PROPERTIES_PATH%"
echo # >> "%KEY_PROPERTIES_PATH%"
echo # IMPORTANT: Keep this file secure and do not commit to version control >> "%KEY_PROPERTIES_PATH%"
echo # Add key.properties to .gitignore >> "%KEY_PROPERTIES_PATH%"
echo. >> "%KEY_PROPERTIES_PATH%"
echo storePassword=%STORE_PASSWORD% >> "%KEY_PROPERTIES_PATH%"
echo keyPassword=%KEY_PASSWORD% >> "%KEY_PROPERTIES_PATH%"
echo keyAlias=%KEY_ALIAS% >> "%KEY_PROPERTIES_PATH%"
echo storeFile=%KEYSTORE_NAME% >> "%KEY_PROPERTIES_PATH%"

echo ✅ key.properties file created successfully!
echo.

REM Get certificate fingerprint for Google Play Console
echo 🔍 Getting certificate fingerprint for Google Play Console...
%KEYTOOL_PATH% -list -v -keystore "%KEYSTORE_PATH%" -alias %KEY_ALIAS% -storepass %STORE_PASSWORD% | findstr "SHA256"

echo.
echo 📋 Next Steps:
echo 1. ✅ Keystore and key.properties created
echo 2. 🔒 Add key.properties to .gitignore (if not already)
echo 3. 🔐 Store passwords securely in password manager
echo 4. 📱 Update build.gradle to use production signing
echo 5. 🌐 Add SHA256 fingerprint to Google Play Console
echo 6. 🚀 Test production build generation
echo.

REM Add to .gitignore if not already present
if exist ".gitignore" (
    findstr /c:"key.properties" .gitignore >nul
    if !errorlevel! neq 0 (
        echo. >> .gitignore
        echo # Android keystore >> .gitignore
        echo android/key.properties >> .gitignore
        echo android/app/*.jks >> .gitignore
        echo ✅ Added keystore files to .gitignore
    )
) else (
    echo # Android keystore > .gitignore
    echo android/key.properties >> .gitignore
    echo android/app/*.jks >> .gitignore
    echo ✅ Created .gitignore with keystore exclusions
)

echo.
echo 🎉 Production keystore setup complete!
echo.
echo ⚠️  IMPORTANT SECURITY NOTES:
echo - Never commit key.properties or .jks files to version control
echo - Store passwords in a secure password manager
echo - Keep backup copies of keystore file in secure location
echo - If you lose the keystore, you cannot update your app on Play Store
echo.

pause
