@echo off
echo ========================================
echo SHA-1 FINGERPRINT VERIFICATION
echo ========================================
echo.

echo 🔍 CHECKING CURRENT SHA-1 FINGERPRINTS...
echo.

echo 📋 DEBUG KEYSTORE SHA-1:
echo.
keytool -list -v -keystore "%USERPROFILE%\.android\debug.keystore" -alias androiddebugkey -storepass android -keypass android | findstr "SHA1:"
if %errorlevel% neq 0 (
    echo ❌ Could not read debug keystore
    echo Make sure Android SDK is installed and debug.keystore exists
)

echo.
echo 📋 PRODUCTION KEYSTORE SHA-1:
echo.
if exist "android\drive-on-release-key.jks" (
    keytool -list -v -keystore "android\drive-on-release-key.jks" -alias drive-on-key -storepass DriveOn123! -keypass DriveOn123! | findstr "SHA1:"
    if %errorlevel% neq 0 (
        echo ❌ Could not read production keystore
        echo Check if drive-on-release-key.jks exists and password is correct
    )
) else (
    echo ❌ Production keystore not found: android\drive-on-release-key.jks
)

echo.
echo 📋 FIREBASE CONFIGURATION CHECK:
echo.
echo Current google-services.json contains:
echo.
echo DEBUG SHA-1: 9a8e9068d497c6e25aefb05175bccbb5ccbd55dc
echo PRODUCTION SHA-1: 85c3078a88353d1d15887693844f8d71741a94ba3
echo.

echo 🔍 COMPARISON:
echo.
echo If the SHA-1 fingerprints above don't match the Firebase configuration,
echo that's the cause of your Google Sign-In issue.
echo.

echo 📞 NEXT STEPS:
echo.
echo 1. If fingerprints don't match:
echo    - Update Firebase Console with correct SHA-1
echo    - Download updated google-services.json
echo    - Rebuild app
echo.
echo 2. If fingerprints match:
echo    - Clear device cache manually
echo    - Restart device
echo    - Test Google Sign-In
echo.

pause 