# Auth Module Refactoring

## Overview

The Authentication module has been refactored to use a repository pattern and dependency injection, replacing the static `FirebaseService` approach. This improves testability, maintainability, and separation of concerns.

## Key Components

### 1. Repository Interfaces and Implementations

- **`AuthRepository` (Interface)**: Defines the contract for authentication operations.
- **`FirebaseAuthRepository` (Implementation)**: Concrete implementation using Firebase.
- **`UserRepository`**: Manages user data operations in Firestore.

### 2. Service Layer

- **`AuthService`**: Uses repositories through dependency injection instead of static calls.

### 3. Dependency Injection

- **`service_locator.dart`**: Centralizes dependency registration and resolution using GetIt.

## Benefits of the New Approach

1. **Testability**: Easily mock dependencies for unit testing.
2. **Separation of Concerns**: Clear boundaries between authentication, user data, and service logic.
3. **Maintainability**: Easier to modify, extend or swap implementations.
4. **Code Reuse**: Repositories can be used by multiple services.

## Component Relationships

```
AuthService
    │
    ├── AuthRepository (Interface)
    │       └── FirebaseAuthRepository (Implementation)
    │
    └── UserRepository
```

## Testing Improvements

- Tests now use mock repositories instead of trying to mock static methods.
- Better isolation of units being tested.
- More reliable and comprehensive test coverage.

## Usage Examples

### Getting an instance via dependency injection:

```dart
// Get the auth service from the service locator
final authService = serviceLocator<AuthService>();

// Use the service
await authService.login(email, password);
```

### Using repositories directly (when needed):

```dart
final authRepo = serviceLocator<AuthRepository>();
final user = await authRepo.getCurrentUser();
```

## Migration Path

This refactoring is part of an incremental improvement approach:

1. ✅ Create repository interfaces and implementations
2. ✅ Refactor AuthService to use repositories
3. ✅ Set up dependency injection using GetIt
4. ✅ Update tests to use the new pattern
5. ⬜ Gradually refactor other static services to follow this pattern

## Future Improvements

- Continue replacing static services with injectable classes
- Add more repositories for other data types (e.g., Driver, Job, Forum)
- Consider adding a BLoC or ViewModel layer later if UI complexity increases 