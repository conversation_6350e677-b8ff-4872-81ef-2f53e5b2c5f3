import 'dart:async';
import 'dart:io';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'app_logger.dart';

enum ErrorSeverity {
  low,    // Non-critical errors that don't affect app functionality
  medium, // Errors that affect some features but allow the app to continue
  high,   // Critical errors that require user action or restart
  fatal   // Errors that crash the app
}

class ErrorHandler {
  static final AppLogger _logger = AppLogger('ErrorHandler');
  
  // Use lazy getters instead of static finals to prevent early access
  static FirebaseAnalytics get _analytics => FirebaseAnalytics.instance;
  static FirebaseCrashlytics get _crashlytics => FirebaseCrashlytics.instance;
  
  /// Initialize error handling for the entire app
  static Future<void> init() async {
    _logger.info('Initializing error handler');
    
    // Only set up Crashlytics handlers if Firebase is initialized
    if (Firebase.apps.isNotEmpty) {
      try {
        // Enable Crashlytics data collection
        await _crashlytics.setCrashlyticsCollectionEnabled(!kDebugMode);

        // Pass all uncaught errors from the framework to Crashlytics
        FlutterError.onError = _handleFlutterError;
        
        // Catch async errors that aren't caught by FlutterError.onError
        PlatformDispatcher.instance.onError = _handlePlatformError;
        
        if (kDebugMode) {
          _logger.info('Error handler initialized with Crashlytics');
        }
      } catch (e, stackTrace) {
        _logger.error('Failed to initialize error handler', error: e, stackTrace: stackTrace);
      }
    } else {
      _logger.warning('Firebase not initialized, skipping Crashlytics setup');
    }
  }
  
  /// Handle Flutter framework errors
  static void _handleFlutterError(FlutterErrorDetails details) {
    _logger.error(
      'Flutter error',
      error: details.exception,
      stackTrace: details.stack,
    );
    
    // Only report to Crashlytics if Firebase is initialized
    if (Firebase.apps.isNotEmpty) {
      _crashlytics.recordFlutterError(details);
    }
  }
  
  /// Handle platform errors
  static bool _handlePlatformError(Object error, StackTrace stack) {
    _logger.error(
      'Platform error',
      error: error,
      stackTrace: stack,
    );
    
    // Only report to Crashlytics if Firebase is initialized
    if (Firebase.apps.isNotEmpty) {
      _crashlytics.recordError(error, stack, reason: 'Platform error');
    }
    
    // Return true to prevent the error from being handled by the platform
    return true;
  }
  
  /// Set user identifier for better error tracking
  static Future<void> setUserIdentifier(String userId) async {
    await _crashlytics.setUserIdentifier(userId);
  }
  
  /// Clear user identifier when user logs out
  static Future<void> clearUserIdentifier() async {
    await _crashlytics.setUserIdentifier('');
  }
  
  /// Report a non-fatal error
  static void reportError(
    String message,
    Object error,
    StackTrace stackTrace, {
    Map<String, dynamic>? additionalData,
  }) {
    _logger.error(
      message,
      error: error,
      stackTrace: stackTrace,
    );
    
    // Only report to Crashlytics if Firebase is initialized
    if (Firebase.apps.isNotEmpty) {
      // Add custom keys for better debugging
      if (additionalData != null) {
        for (final entry in additionalData.entries) {
          _crashlytics.setCustomKey(entry.key, entry.value.toString());
        }
      }
      
      _crashlytics.recordError(
        error,
        stackTrace,
        reason: message,
        printDetails: kDebugMode,
      );
    }
  }

  
  /// Handle network errors specifically
  static String handleNetworkError(Object error) {
    if (error is SocketException || error is TimeoutException) {
      return 'Network connection error. Please check your internet connection and try again.';
    } else {
      return 'An unexpected error occurred. Please try again later.';
    }
  }
  
  /// Handle authentication errors
  static String handleAuthError(Object error) {
    String message = 'Authentication failed. Please try again.';
    
    // You can add more specific error handling here based on FirebaseAuth error codes
    if (error.toString().contains('wrong-password')) {
      message = 'Incorrect password. Please try again.';
    } else if (error.toString().contains('user-not-found')) {
      message = 'No account found with this email. Please sign up.';
    } else if (error.toString().contains('invalid-email')) {
      message = 'Invalid email address format.';
    } else if (error.toString().contains('email-already-in-use')) {
      message = 'This email is already registered. Please log in or use another email.';
    }
    
    return message;
  }
  
  /// Public method to report handled exceptions
  static void reportHandledException(
    Object exception,
    StackTrace? stackTrace, {
    ErrorSeverity severity = ErrorSeverity.medium,
    String? context,
  }) {
    // Log locally in debug mode
    if (kDebugMode) {
      _logger.error('Handled exception in $context: $exception');
      if (stackTrace != null) {
        _logger.error('Stack trace: $stackTrace');
      }
    }
    
    // Only report to analytics and crashlytics if not in debug mode
    if (!kDebugMode) {
      // Log to Firebase Analytics
      _analytics.logEvent(
        name: 'handled_exception',
        parameters: {
          'error': exception.toString(),
          'context': context ?? 'unknown',
          'severity': severity.toString(),
        },
      );
      
      // Report to Firebase Crashlytics as non-fatal
      _crashlytics.recordError(
        exception,
        stackTrace,
        reason: context ?? 'Handled exception',
        fatal: false,
      );
    }
  }
  
  /// Add custom log message to crash reports
  static void addLog(String message) {
    _crashlytics.log(message);
  }
  
  /// Add custom key-value data to crash reports
  static Future<void> setCustomKey(String key, dynamic value) async {
    if (value is String) {
      await _crashlytics.setCustomKey(key, value);
    } else if (value is bool) {
      await _crashlytics.setCustomKey(key, value);
    } else if (value is int) {
      await _crashlytics.setCustomKey(key, value);
    } else if (value is double) {
      await _crashlytics.setCustomKey(key, value);
    } else {
      await _crashlytics.setCustomKey(key, value.toString());
    }
  }
  
  /// Display a user-friendly error dialog
  static Future<void> showErrorDialog(
    BuildContext context,
    String title,
    String message, {
    String buttonText = 'OK',
    VoidCallback? onPressed,
  }) async {
    return showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(title),
          content: Text(message),
          actions: <Widget>[
            TextButton(
              onPressed: onPressed ?? () => Navigator.of(context).pop(),
              child: Text(buttonText),
            ),
          ],
        );
      },
    );
  }
}