import 'package:flutter/material.dart';
import '../../../core/core.dart';

/// A widget that displays a driver's profile picture with fallback to initials
class DriverAvatar extends StatelessWidget {
  final String? profilePictureUrl;
  final String driverName;
  final double radius;
  final String? heroTag;

  const DriverAvatar({
    Key? key,
    required this.profilePictureUrl,
    required this.driverName,
    this.radius = 30,
    this.heroTag,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final avatar = CircleAvatar(
      radius: radius,
      backgroundColor: AppColors.primaryYellow.withOpacity(0.2),
      backgroundImage: profilePictureUrl != null && profilePictureUrl!.isNotEmpty
          ? NetworkImage(profilePictureUrl!)
          : null,
      onBackgroundImageError: profilePictureUrl != null && profilePictureUrl!.isNotEmpty
          ? (exception, stackTrace) {
              // Handle image loading error silently and show fallback
            }
          : null,
      child: profilePictureUrl == null || profilePictureUrl!.isEmpty
          ? Text(
              driverName.isNotEmpty ? driverName[0].toUpperCase() : '?',
              style: TextStyle(
                fontSize: radius * 0.8, // Scale font size with radius
                fontWeight: FontWeight.bold,
                color: AppColors.primaryYellow,
              ),
            )
          : null,
    );

    // Wrap with Hero if heroTag is provided
    if (heroTag != null) {
      return Hero(
        tag: heroTag!,
        child: avatar,
      );
    }

    return avatar;
  }
}
