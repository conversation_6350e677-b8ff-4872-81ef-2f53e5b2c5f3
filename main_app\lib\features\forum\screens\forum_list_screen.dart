import 'package:flutter/material.dart';
import 'package:drive_on/core/utils/error_handler.dart';
import 'package:drive_on/core/widgets/app_error_widget.dart';
import '../models/forum_model.dart';
import '../services/forum_service.dart';

class ForumListScreen extends StatefulWidget {
  const ForumListScreen({Key? key}) : super(key: key);

  @override
  State<ForumListScreen> createState() => _ForumListScreenState();
}

class _ForumListScreenState extends State<ForumListScreen> {
  // Data state
  List<Forum>? _forums;

  // Error state
  bool _isLoading = true;
  bool _hasError = false;
  String _errorMessage = '';
  bool _isNetworkError = false;

  @override
  void initState() {
    super.initState();
    _loadForums();
  }

  // Load forum data with error handling
  Future<void> _loadForums() async {
    setState(() {
      _isLoading = true;
      _hasError = false;
    });

    try {
      // Log start of operation
      ErrorHandler.addLog('Loading forums list');

      // Simulate a delay for demo purposes
      await Future.delayed(const Duration(seconds: 1));

      final forums = await ForumService.getForums();

      // Only update state if the widget is still mounted
      if (mounted) {
        setState(() {
          _forums = forums;
          _isLoading = false;
        });
      }
    } catch (e, stackTrace) {
      // Report the error
      ErrorHandler.reportHandledException(
        e,
        stackTrace,
        context: 'Loading forums list',
        severity: ErrorSeverity.medium,
      );

      // Check if it's a network error
      final isNetworkError = e.toString().contains('SocketException') ||
                            e.toString().contains('TimeoutException');

      // Only update state if the widget is still mounted
      if (mounted) {
        setState(() {
          _hasError = true;
          _isLoading = false;
          _isNetworkError = isNetworkError;
          _errorMessage = isNetworkError
              ? 'Network connection error. Please check your internet connection and try again.'
              : 'Failed to load forums. Please try again.';
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Forums'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadForums,
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    // Show loading indicator
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    // Show error state
    if (_hasError) {
      return _isNetworkError
          ? NetworkErrorWidget(onRetry: _loadForums)
          : AppErrorWidget(
              title: 'Something Went Wrong',
              message: _errorMessage,
              onRetry: _loadForums,
            );
    }

    // Show empty state
    if (_forums == null || _forums!.isEmpty) {
      return const Center(
        child: Text('No forums available'),
      );
    }

    // Show content with optimized ListView
    return ListView.separated(
      itemCount: _forums!.length,
      separatorBuilder: (context, index) => const Divider(height: 1),
      itemBuilder: (context, index) {
        final forum = _forums![index];
        return ListTile(
          title: Text(forum.title),
          subtitle: Text(forum.description ?? 'No description'),
          leading: CircleAvatar(
            child: Text(forum.title.substring(0, 1)),
          ),
          onTap: () {
            // Navigate to forum detail
            Navigator.of(context).push(
              MaterialPageRoute(
                builder: (context) => ForumDetailScreen(forumId: forum.id),
              ),
            );
          },
        );
      },
      // Add performance optimizations
      physics: const BouncingScrollPhysics(
        parent: AlwaysScrollableScrollPhysics(),
      ),
      cacheExtent: 500, // Cache 500 pixels worth of items
    );
  }
}

/// Placeholder for the forum detail screen
class ForumDetailScreen extends StatelessWidget {
  final String forumId;

  const ForumDetailScreen({
    Key? key,
    required this.forumId,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Forum Details'),
      ),
      body: Center(
        child: Text('Forum ID: $forumId'),
      ),
    );
  }
}