import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../core/core.dart';
import '../../core/security/input_validator.dart';

class RegistrationRequestForm extends StatefulWidget {
  const RegistrationRequestForm({super.key});

  @override
  State<RegistrationRequestForm> createState() => _RegistrationRequestFormState();
}

class _RegistrationRequestFormState extends State<RegistrationRequestForm> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _mobileController = TextEditingController();
  final _emailController = TextEditingController();
  final _idCardController = TextEditingController();
  final _numberOfCarsController = TextEditingController();
  
  String? _selectedCity;
  bool _isLoading = false;
  
  // List of major Pakistani cities
  final List<String> _pakistanCities = [
    'Karachi', 'Lahore', 'Islamabad', 'Rawalpindi', 'Faisalabad', 
    'Multan', 'Peshawar', 'Quetta', 'Sialkot', 'Gujranwala', 
    'Hyderabad', 'Abbottabad', 'Bahawalpur', 'Dera Ghazi Khan', 
    'Gujrat', 'Jhang', 'Kasur', 'Mardan', 'Mingora', 'Nawabshah',
    'Okara', 'Sahiwal', 'Sargodha', 'Sukkur', 'Wah Cantonment'
  ];

  @override
  void dispose() {
    _nameController.dispose();
    _mobileController.dispose();
    _emailController.dispose();
    _idCardController.dispose();
    _numberOfCarsController.dispose();
    super.dispose();
  }

  // Handle form submission and Firebase upload
  Future<void> _submitRequest() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isLoading = true;
      });
      
      try {
        // Create data map for Firestore that matches the admin portal Partner model
        final partnerRequestData = {
          'companyName': _nameController.text.trim(),
          'email': _emailController.text.trim(),
          'phone': _mobileController.text.trim(),
          'address': _selectedCity,
          'description': 'Number of cars: ${_numberOfCarsController.text.trim()}',
          'status': 'pending',
          'createdAt': FieldValue.serverTimestamp(),
          'welcomeEmailSent': false,
        };
        
        // Upload to Firestore under 'partners' collection (instead of 'partner_requests')
        await FirebaseFirestore.instance
            .collection('partners')
            .add(partnerRequestData);
        
        if (mounted) {
          // Show success dialog
          showDialog(
            context: context,
            barrierDismissible: false,
            builder: (context) => AlertDialog(
              title: const Row(
                children: [
                  Icon(
                    Icons.check_circle,
                    color: AppColors.success,
                  ),
                  SizedBox(width: 8),
                  Text('Congratulations'),
                ],
              ),
              content: const Text('Your partner request has been submitted. We will reach out to you shortly.'),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop(); // Close dialog
                    Navigator.of(context).pop(); // Return to Partners Section
                  },
                  child: const Text('OK'),
                ),
              ],
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Failed to submit request: $e')),
          );
        }
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Register Your Request'),
        backgroundColor: AppColors.primaryYellow,
        foregroundColor: Colors.black,
      ),
      body: _isLoading 
          ? const Center(child: CircularProgressIndicator())
          : Form(
              key: _formKey,
              child: ListView(
                padding: const EdgeInsets.all(16.0),
                children: [
                  // Form description
                  Text(
                    'Partner Registration',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: isDarkMode ? AppColors.darkText : AppColors.lightText,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Fill in your details to apply as our partner',
                    style: TextStyle(
                      fontSize: 14,
                      color: isDarkMode ? AppColors.darkTextSecondary : AppColors.lightTextSecondary,
                    ),
                  ),
                  const SizedBox(height: 24),
                  
                  // Full Name field
                  TextFormField(
                    controller: _nameController,
                    decoration: InputDecoration(
                      labelText: 'Full Name',
                      hintText: 'Enter your full name',
                      prefixIcon: const Icon(Icons.person),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      filled: true,
                      fillColor: isDarkMode ? Colors.grey.shade800 : Colors.grey.shade50,
                    ),
                    validator: (value) => InputValidator.validateRequired(value, 'full name'),
                  ),
                  const SizedBox(height: 16),
                  
                  // Mobile Number field
                  TextFormField(
                    controller: _mobileController,
                    decoration: InputDecoration(
                      labelText: 'Mobile Number',
                      hintText: 'e.g., 03XX-XXXXXXX',
                      prefixIcon: const Icon(Icons.phone),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      filled: true,
                      fillColor: isDarkMode ? Colors.grey.shade800 : Colors.grey.shade50,
                    ),
                    keyboardType: TextInputType.phone,
                    validator: InputValidator.validatePakistanPhone,
                  ),
                  const SizedBox(height: 16),
                  
                  // Email field
                  TextFormField(
                    controller: _emailController,
                    decoration: InputDecoration(
                      labelText: 'Email ID',
                      hintText: 'Enter your email address',
                      prefixIcon: const Icon(Icons.email),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      filled: true,
                      fillColor: isDarkMode ? Colors.grey.shade800 : Colors.grey.shade50,
                    ),
                    keyboardType: TextInputType.emailAddress,
                    validator: InputValidator.validateEmail,
                  ),
                  const SizedBox(height: 16),
                  
                  // ID Card Number field
                  TextFormField(
                    controller: _idCardController,
                    decoration: InputDecoration(
                      labelText: 'ID Card Number',
                      hintText: 'Enter your CNIC (without dashes)',
                      prefixIcon: const Icon(Icons.badge),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      filled: true,
                      fillColor: isDarkMode ? Colors.grey.shade800 : Colors.grey.shade50,
                    ),
                    keyboardType: TextInputType.number,
                    validator: InputValidator.validateCnic,
                  ),
                  const SizedBox(height: 16),
                  
                  // City dropdown
                  DropdownButtonFormField<String>(
                    value: _selectedCity,
                    decoration: InputDecoration(
                      labelText: 'City',
                      hintText: 'Select your city',
                      prefixIcon: const Icon(Icons.location_city),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      filled: true,
                      fillColor: isDarkMode ? Colors.grey.shade800 : Colors.grey.shade50,
                    ),
                    items: _pakistanCities.map((city) {
                      return DropdownMenuItem<String>(
                        value: city,
                        child: Text(city),
                      );
                    }).toList(),
                    onChanged: (String? value) {
                      setState(() {
                        _selectedCity = value;
                      });
                    },
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please select your city';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  
                  // Number of Cars field
                  TextFormField(
                    controller: _numberOfCarsController,
                    decoration: InputDecoration(
                      labelText: 'Number of Cars',
                      hintText: 'How many cars do you want to register?',
                      prefixIcon: const Icon(Icons.directions_car),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      filled: true,
                      fillColor: isDarkMode ? Colors.grey.shade800 : Colors.grey.shade50,
                    ),
                    keyboardType: TextInputType.number,
                    validator: (value) => InputValidator.validateNumeric(value, 'number of cars'),
                  ),
                  const SizedBox(height: 32),
                  
                  // Submit button
                  SizedBox(
                    height: 50,
                    child: ElevatedButton.icon(
                      onPressed: _submitRequest,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primaryYellow,
                        foregroundColor: Colors.black,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      icon: const Icon(Icons.send),
                      label: const Text(
                        'Register your request',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
    );
  }
} 