# 🚀 Drive-On Admin Panel Deployment Guide

This guide covers deploying the Drive-On Admin Panel to Firebase Hosting with CI/CD automation.

## 📋 Prerequisites

### 1. Install Firebase CLI
```bash
npm install -g firebase-tools
```

### 2. Login to Firebase
```bash
firebase login
```

### 3. Verify Project Access
```bash
firebase projects:list
```

## 🏗️ Initial Setup

### 1. Install Dependencies
```bash
cd admin
npm install
```

### 2. Install Firebase Tools (if not already installed)
```bash
npm install firebase-tools --save-dev
```

### 3. Initialize Firebase (if needed)
```bash
npm run firebase:init
```

## 🌍 Environment Configuration

### 1. Copy Environment Template
```bash
cp .env.example .env.local
cp .env.example .env.production
```

### 2. Update Environment Variables
Edit `.env.production` with your production values:
- Firebase configuration
- API endpoints
- Feature flags
- Security keys

## 🚀 Deployment Methods

### Method 1: Manual Deployment

#### Deploy to Staging
```bash
npm run deploy:staging
```

#### Deploy to Production
```bash
npm run deploy:prod
```

#### Using the Deployment Script
```bash
# Deploy to staging
node scripts/deploy.js staging

# Deploy to production
node scripts/deploy.js production
```

### Method 2: Automated CI/CD

#### Setup GitHub Secrets
1. Go to your GitHub repository
2. Navigate to Settings > Secrets and variables > Actions
3. Add the following secrets:

```
FIREBASE_SERVICE_ACCOUNT_DRIVE_ON_B2AF8
```

To get the service account key:
```bash
firebase projects:list
firebase service-accounts:create github-actions --project drive-on-b2af8
firebase service-accounts:keys:create key.json --iam-account <EMAIL>
```

#### Automatic Deployment Triggers
- **Staging**: Push to `develop` branch
- **Production**: Push to `main` branch
- **Manual**: Use GitHub Actions workflow dispatch

## 🔧 Build Configuration

### Production Build
```bash
npm run build
```

### Build with Analysis
```bash
npm run build:analyze
```

### Local Testing
```bash
npm run firebase:serve
```

## 📊 Monitoring & Analytics

### Firebase Console
- Visit [Firebase Console](https://console.firebase.google.com/)
- Select your project: `drive-on-b2af8`
- Navigate to Hosting section

### Performance Monitoring
```bash
npm run perf:check
```

## 🔒 Security Configuration

### Headers
The following security headers are automatically configured:
- `X-Frame-Options: DENY`
- `X-Content-Type-Options: nosniff`
- `Referrer-Policy: strict-origin-when-cross-origin`
- `X-XSS-Protection: 1; mode=block`
- `Strict-Transport-Security: max-age=********; includeSubDomains`

### HTTPS
- All traffic is automatically redirected to HTTPS
- SSL certificates are managed by Firebase

## 🌐 Custom Domain Setup

### 1. Add Custom Domain in Firebase Console
1. Go to Firebase Console > Hosting
2. Click "Add custom domain"
3. Enter your domain (e.g., `admin.driveon.com`)
4. Follow verification steps

### 2. Update DNS Records
Add the provided DNS records to your domain provider:
```
Type: A
Name: admin
Value: [Firebase IP addresses]
```

### 3. Update Environment Variables
```bash
NEXTAUTH_URL=https://admin.driveon.com
```

## 🔄 Rollback Strategy

### Quick Rollback
```bash
firebase hosting:clone SOURCE_SITE_ID:SOURCE_VERSION_ID TARGET_SITE_ID
```

### Version Management
- All deployments create new versions
- Previous versions remain accessible
- Easy rollback through Firebase Console

## 📈 Performance Optimization

### Caching Strategy
- Static assets: 1 year cache
- HTML/JSON: No cache (always fresh)
- Service Worker enabled

### Build Optimization
- Code splitting enabled
- Bundle analysis available
- Image optimization configured

## 🐛 Troubleshooting

### Common Issues

#### Build Failures
```bash
# Clear cache and rebuild
rm -rf .next node_modules
npm install
npm run build
```

#### Firebase Authentication
```bash
# Re-login to Firebase
firebase logout
firebase login
```

#### Environment Variables
- Ensure all required variables are set
- Check `.env.production` file
- Verify Firebase configuration

### Debug Mode
```bash
# Enable debug logging
DEBUG=* npm run deploy
```

## 📞 Support

### Resources
- [Firebase Hosting Documentation](https://firebase.google.com/docs/hosting)
- [Next.js Deployment Guide](https://nextjs.org/docs/deployment)
- [GitHub Actions Documentation](https://docs.github.com/en/actions)

### Contact
- Technical Issues: Create GitHub issue
- Deployment Support: Contact DevOps team

---

## 🎯 Quick Commands Reference

```bash
# Development
npm run dev                 # Start development server
npm run build              # Build for production
npm run start              # Start production server

# Deployment
npm run deploy             # Deploy to default environment
npm run deploy:staging     # Deploy to staging
npm run deploy:prod        # Deploy to production

# Firebase
npm run firebase:login     # Login to Firebase
npm run firebase:serve     # Serve locally with Firebase
npm run firebase:init      # Initialize Firebase project

# Quality Assurance
npm run lint               # Run linting
npm run type-check         # Run TypeScript checks
npm run format             # Format code
npm run perf:check         # Performance check
```
