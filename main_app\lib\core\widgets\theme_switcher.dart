import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../theme/theme_provider.dart';
import '../theme/app_icons.dart';
import '../theme/colors.dart';

class ThemeSwitcher extends StatelessWidget {
  final double size;
  final bool showText;

  const ThemeSwitcher({
    super.key,
    this.size = 24,
    this.showText = false,
  });

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final isDarkMode = themeProvider.isDarkMode;

    return InkWell(
      onTap: () => themeProvider.toggleTheme(),
      borderRadius: BorderRadius.circular(50),
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              isDarkMode ? AppIcons.lightMode : AppIcons.darkMode,
              size: size,
              color: isDarkMode 
                  ? AppColors.primaryYellow 
                  : AppColors.lightText,
            ),
            if (showText) ...[
              const SizedBox(width: 8),
              Text(
                isDarkMode ? 'Light Mode' : 'Dark Mode',
                style: TextStyle(
                  fontSize: size * 0.7,
                  color: isDarkMode 
                      ? AppColors.darkText 
                      : AppColors.lightText,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
} 