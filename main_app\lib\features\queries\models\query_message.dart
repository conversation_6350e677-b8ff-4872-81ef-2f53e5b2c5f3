import 'package:cloud_firestore/cloud_firestore.dart';

class QueryMessage {
  final String id;
  final String text;
  final String senderId;
  final String senderName;
  final String? senderPhotoUrl;
  final DateTime timestamp;
  final List<String> imageUrls;
  final String? voiceUrl;
  final int? voiceDuration;
  final bool isEdited;
  final bool isSystemMessage;
  final bool isAdmin;
  final bool isPending;
  final bool isUploading;

  QueryMessage({
    required this.id,
    required this.text,
    required this.senderId,
    required this.senderName,
    this.senderPhotoUrl,
    required this.timestamp,
    this.imageUrls = const [],
    this.voiceUrl,
    this.voiceDuration,
    this.isEdited = false,
    this.isSystemMessage = false,
    this.isAdmin = false,
    this.isPending = false,
    this.isUploading = false,
  });

  factory QueryMessage.fromMap(Map<String, dynamic> map, String documentId) {
    return QueryMessage(
      id: documentId,
      text: map['text'] ?? '',
      senderId: map['senderId'] ?? '',
      senderName: map['senderName'] ?? 'Unknown',
      senderPhotoUrl: map['senderPhotoUrl'],
      timestamp: (map['timestamp'] is Timestamp)
          ? (map['timestamp'] as Timestamp).toDate()
          : DateTime.now(),
      imageUrls: List<String>.from(map['imageUrls'] ?? []),
      voiceUrl: map['voiceUrl'],
      voiceDuration: map['voiceDuration'],
      isEdited: map['isEdited'] ?? false,
      isSystemMessage: map['isSystemMessage'] ?? false,
      isAdmin: map['isAdmin'] ?? false,
      isPending: false, // Firestore messages are never pending
      isUploading: false, // Firestore messages are never uploading
    );
  }

  // Factory for creating pending messages
  factory QueryMessage.pending({
    required String senderId,
    required String senderName,
    String? senderPhotoUrl,
    String text = '',
    List<String> imageUrls = const [],
    String? voiceUrl,
    int? voiceDuration,
    bool isAdmin = false,
    bool isUploading = false,
  }) {
    return QueryMessage(
      id: 'pending_${DateTime.now().millisecondsSinceEpoch}',
      text: text,
      senderId: senderId,
      senderName: senderName,
      senderPhotoUrl: senderPhotoUrl,
      timestamp: DateTime.now(),
      imageUrls: imageUrls,
      voiceUrl: voiceUrl,
      voiceDuration: voiceDuration,
      isEdited: false,
      isSystemMessage: false,
      isAdmin: isAdmin,
      isPending: true,
      isUploading: isUploading,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'text': text,
      'senderId': senderId,
      'senderName': senderName,
      'senderPhotoUrl': senderPhotoUrl,
      'timestamp': timestamp,
      'imageUrls': imageUrls,
      'voiceUrl': voiceUrl,
      'voiceDuration': voiceDuration,
      'isEdited': isEdited,
      'isSystemMessage': isSystemMessage,
      'isAdmin': isAdmin,
    };
  }

  QueryMessage copyWith({
    String? id,
    String? text,
    String? senderId,
    String? senderName,
    String? senderPhotoUrl,
    DateTime? timestamp,
    List<String>? imageUrls,
    String? voiceUrl,
    int? voiceDuration,
    bool? isEdited,
    bool? isSystemMessage,
    bool? isAdmin,
  }) {
    return QueryMessage(
      id: id ?? this.id,
      text: text ?? this.text,
      senderId: senderId ?? this.senderId,
      senderName: senderName ?? this.senderName,
      senderPhotoUrl: senderPhotoUrl ?? this.senderPhotoUrl,
      timestamp: timestamp ?? this.timestamp,
      imageUrls: imageUrls ?? this.imageUrls,
      voiceUrl: voiceUrl ?? this.voiceUrl,
      voiceDuration: voiceDuration ?? this.voiceDuration,
      isEdited: isEdited ?? this.isEdited,
      isSystemMessage: isSystemMessage ?? this.isSystemMessage,
      isAdmin: isAdmin ?? this.isAdmin,
    );
  }
}