import 'package:cloud_firestore/cloud_firestore.dart';
import '../../../core/utils/app_logger.dart';

/// Repository for user-related data operations
class UserRepository {
  final FirebaseFirestore _firestore;
  final AppLogger _logger = AppLogger('UserRepository');

  /// Creates a new user repository
  /// 
  /// Accepts optional firestore parameter for testing
  UserRepository({FirebaseFirestore? firestore}) 
    : _firestore = firestore ?? FirebaseFirestore.instance;

  /// Save user data to Firestore
  Future<void> saveUserData(String userId, Map<String, dynamic> userData) async {
    try {
      await _firestore.collection('users').doc(userId).set(
        userData,
        SetOptions(merge: true),
      );
      _logger.info('User data saved for ID: $userId');
    } catch (e) {
      _logger.error('Error saving user data', error: e);
      rethrow;
    }
  }

  /// Get user data from Firestore
  Future<Map<String, dynamic>?> getUserData(String userId) async {
    try {
      final doc = await _firestore.collection('users').doc(userId).get();
      if (doc.exists) {
        return doc.data();
      }
      return null;
    } catch (e) {
      _logger.error('Error getting user data', error: e);
      rethrow;
    }
  }

  /// Update specific user fields
  Future<void> updateUserFields(String userId, Map<String, dynamic> fields) async {
    try {
      await _firestore.collection('users').doc(userId).update(fields);
      _logger.info('User fields updated for ID: $userId');
    } catch (e) {
      _logger.error('Error updating user fields', error: e);
      rethrow;
    }
  }

  /// Check if a user exists in Firestore
  Future<bool> userExists(String userId) async {
    try {
      final doc = await _firestore.collection('users').doc(userId).get();
      return doc.exists;
    } catch (e) {
      _logger.error('Error checking if user exists', error: e);
      return false;
    }
  }
} 