import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../core/utils/app_logger.dart';
import 'repositories/auth_repository.dart';
import 'repositories/user_repository.dart';
import '../../core/security/security_manager.dart';
import '../../core/services/email_service.dart';

/// Service class for handling authentication-related operations
class AuthService extends ChangeNotifier {
  final AuthRepository _authRepository;
  final UserRepository _userRepository;
  final AppLogger _logger = AppLogger('AuthService');
  SecurityManager? _securityManager;
  EmailService? _emailService;

  User? _firebaseUser;
  bool _isLoggedIn = false;
  String? _userEmail;

  /// Constructor that takes repositories through dependency injection
  AuthService({
    required AuthRepository authRepository,
    required UserRepository userRepository,
  }) :
    _authRepository = authRepository,
    _userRepository = userRepository;

  /// Lazy initialization of security manager
  SecurityManager get securityManager {
    _securityManager ??= SecurityManager();
    return _securityManager!;
  }

  /// Lazy initialization of email service
  EmailService get emailService {
    _emailService ??= EmailService();
    return _emailService!;
  }

  /// Initialize auth state
  Future<void> init() async {
    // Check if user is already logged in
    _firebaseUser = await _authRepository.getCurrentUser();

    if (_firebaseUser != null) {
      _isLoggedIn = true;
      _userEmail = _firebaseUser?.email;
    } else {
      // Try getting login status from secure storage
      try {
        final token = await securityManager.getAuthToken();
        final email = await securityManager.secureStorage.getUserEmail();

        _isLoggedIn = token != null;
        _userEmail = email;
      } catch (e) {
        // If security manager fails, assume not logged in
        _isLoggedIn = false;
        _userEmail = null;
      }
    }

    notifyListeners();
  }

  /// Get current user
  User? get currentUser => _firebaseUser;

  /// Check if user is logged in
  bool get isLoggedIn => _isLoggedIn;

  /// Get user email
  String? get userEmail => _userEmail;

  /// Login with email and password
  Future<bool> login(String email, String password) async {
    try {
      // Login with Firebase
      final userCredential = await _authRepository.signInWithEmailPassword(
        email,
        password,
      );

      _firebaseUser = userCredential.user;
      _isLoggedIn = _firebaseUser != null;
      _userEmail = _firebaseUser?.email;

      if (_firebaseUser != null) {
        // Get token and save it securely
        final idToken = await _firebaseUser!.getIdToken();
        if (idToken != null) {
          await securityManager.storeAuthToken(idToken);
        }

        // Save user ID and email securely
        await securityManager.secureStorage.storeUserId(_firebaseUser!.uid);
        await securityManager.secureStorage.storeUserEmail(email);
      }

      notifyListeners();
      return true;
    } on FirebaseAuthException catch (e) {
      _logger.error('Login error', error: e);
      // Handle specific errors
      if (e.code == 'network-request-failed') {
        // Network error, check internet connection
        // You could retry or show a specific message
      } else if (e.code == 'user-not-found' || e.code == 'wrong-password') {
        // Invalid credentials
      }
      return false;
    } catch (e) {
      _logger.error('Login error', error: e);
      return false;
    }
  }

  /// Register with email and password
  Future<bool> register(String email, String password) async {
    try {
      // Create user with Firebase
      final userCredential = await _authRepository.createUserWithEmailPassword(
        email,
        password,
      );

      _firebaseUser = userCredential.user;
      _isLoggedIn = _firebaseUser != null;
      _userEmail = _firebaseUser?.email;

      if (_firebaseUser != null) {
        // Get token and save it securely
        final idToken = await _firebaseUser!.getIdToken();
        if (idToken != null) {
          await securityManager.storeAuthToken(idToken);
        }

        // Save user ID and email securely
        await securityManager.secureStorage.storeUserId(_firebaseUser!.uid);
        await securityManager.secureStorage.storeUserEmail(email);

        // Save user data to Firestore
        await _userRepository.saveUserData(_firebaseUser!.uid, {
          'email': email,
          'createdAt': FieldValue.serverTimestamp(),
        });

        // Send welcome email
        try {
          await emailService.sendEmail(
            to: email,
            subject: 'Welcome to Drive-On!',
            body: '''Dear User,

Welcome to Drive-On! We're excited to have you join our community of professional drivers.

Here's what you can do with your Drive-On account:
- Create and manage your driver profile
- Apply for driving jobs
- Track your applications
- Connect with potential employers
- Access exclusive driver resources

To get started, please complete your profile and upload your documents for verification.

If you have any questions, feel free to contact our support team.

Best regards,
The Drive-On Team''',
          );
        } catch (e) {
          _logger.error('Error sending welcome email', error: e);
          // Don't fail registration if email fails
        }
      }

      notifyListeners();
      return true;
    } on FirebaseAuthException catch (e) {
      _logger.error('Registration error', error: e);

      // Don't rethrow, just return false so the UI can handle it
      return false;
    } catch (e) {
      _logger.error('Registration error', error: e);
      return false;
    }
  }

  /// Sign in with Google
  Future<bool> signInWithGoogle() async {
    try {
      // Sign in with Google
      final userCredential = await _authRepository.signInWithGoogle();

      // If user cancels the sign-in process
      if (userCredential == null) {
        return false;
      }

      _firebaseUser = userCredential.user;
      _isLoggedIn = _firebaseUser != null;
      _userEmail = _firebaseUser?.email;

      // Save to preferences for quick checks
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('isLoggedIn', true);
      await prefs.setString('userEmail', _userEmail ?? '');

      // Check if this is a new user and save to Firestore if needed
      if (_firebaseUser != null) {
        final exists = await _userRepository.userExists(_firebaseUser!.uid);
        if (!exists) {
          await _userRepository.saveUserData(_firebaseUser!.uid, {
            'email': _userEmail,
            'displayName': _firebaseUser?.displayName,
            'photoURL': _firebaseUser?.photoURL,
            'createdAt': FieldValue.serverTimestamp(),
          });
        }
      }

      notifyListeners();
      return true;
    } catch (e) {
      _logger.error('Google sign-in error', error: e);
      return false;
    }
  }

  /// Logout user
  Future<void> logout() async {
    try {
      await _authRepository.signOut();

      _firebaseUser = null;
      _isLoggedIn = false;
      _userEmail = null;

      // Clear secure storage
      await securityManager.clearAuthData();

      notifyListeners();
    } catch (e) {
      _logger.error('Logout error', error: e);
    }
  }

  /// Reset password
  Future<bool> resetPassword(String email) async {
    try {
      await _authRepository.sendPasswordResetEmail(email);
      return true;
    } catch (e) {
      _logger.error('Password reset error', error: e);
      return false;
    }
  }

  /// Verify token is still valid
  Future<bool> isTokenValid() async {
    try {
      return await securityManager.getAuthToken() != null;
    } catch (e) {
      return false;
    }
  }

  Future<UserCredential?> registerWithEmailAndPassword({
    required String email,
    required String password,
    required String displayName,
  }) async {
    try {
      final UserCredential userCredential =
          await _authRepository.createUserWithEmailPassword(
        email,
        password,
      );

      // Update user profile with display name
      await userCredential.user?.updateDisplayName(displayName);

      // Create user document in Firestore
      await _userRepository.saveUserData(userCredential.user!.uid, {
        'email': email,
        'displayName': displayName,
        'createdAt': FieldValue.serverTimestamp(),
        'role': 'user',
      });

      // Send welcome email
      try {
        await emailService.sendEmail(
          to: email,
          subject: 'Welcome to Drive-On!',
          body: '''
            Dear $displayName,

            Welcome to Drive-On! We're excited to have you on board.

            Your account has been successfully created. Please complete your profile to start using our platform.

            Best regards,
            The Drive-On Team
          ''',
        );
      } catch (e) {
        _logger.error('Failed to send welcome email', error: e);
        // Don't fail the registration if email sending fails
      }

      return userCredential;
    } catch (e) {
      _logger.error('Error during registration', error: e);
      return null;
    }
  }
}