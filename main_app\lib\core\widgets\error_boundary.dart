import 'package:flutter/material.dart';
import '../utils/error_handler.dart';

/// A widget that catches errors in its child widget tree and displays a fallback UI
class ErrorBoundary extends StatefulWidget {
  /// The child widget that might throw errors
  final Widget child;

  /// Creates an error boundary widget
  const ErrorBoundary({
    Key? key,
    required this.child,
  }) : super(key: key);

  @override
  State<ErrorBoundary> createState() => _ErrorBoundaryState();
}

class _ErrorBoundaryState extends State<ErrorBoundary> {
  bool _hasError = false;
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();
    // Set custom error builder
    ErrorWidget.builder = _buildErrorWidget;
  }

  Widget _buildErrorWidget(FlutterErrorDetails errorDetails) {
    // Report the error
    ErrorHandler.reportHandledException(
      errorDetails.exception,
      errorDetails.stack,
      context: 'Widget error',
      severity: ErrorSeverity.high,
    );
    
    // Update state to show error UI
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        setState(() {
          _hasError = true;
          _errorMessage = 'An unexpected error occurred in the app. Please try again.';
        });
      }
    });
    
    // Return an empty container as a placeholder until the error UI is built
    return Container();
  }

  @override
  Widget build(BuildContext context) {
    if (_hasError) {
      // Return fallback UI when an error occurs
      return Scaffold(
        body: Center(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.error_outline,
                  color: Colors.red,
                  size: 64,
                ),
                const SizedBox(height: 16),
                const Text(
                  'Something Went Wrong',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  _errorMessage,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                ElevatedButton(
                  onPressed: () {
                    setState(() {
                      _hasError = false;
                    });
                  },
                  child: const Text('Try Again'),
                ),
              ],
            ),
          ),
        ),
      );
    }

    // No error, return the normal UI
    return widget.child;
  }
} 