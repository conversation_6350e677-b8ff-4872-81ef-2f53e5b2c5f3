'use client'

import { useState, useRef } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Image, X, Upload, Send } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'

interface ImageUploaderProps {
  onImagesSelected: (images: File[], caption?: string) => void
  onCancel: () => void
  maxImages?: number
  maxSizePerImage?: number // in MB
}

export function ImageUploader({
  onImagesSelected,
  onCancel,
  maxImages = 5,
  maxSizePerImage = 10
}: ImageUploaderProps) {
  const [selectedImages, setSelectedImages] = useState<File[]>([])
  const [previews, setPreviews] = useState<string[]>([])
  const [caption, setCaption] = useState('')
  const [dragOver, setDragOver] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fileInputRef = useRef<HTMLInputElement>(null)

  // Handle file selection
  const handleFileSelect = (files: FileList | null) => {
    if (!files) return

    const newFiles: File[] = []
    const newPreviews: string[] = []
    let errorMessage = ''

    Array.from(files).forEach((file) => {
      // Check file type
      if (!file.type.startsWith('image/')) {
        errorMessage = 'Only image files are allowed'
        return
      }

      // Check file size
      if (file.size > maxSizePerImage * 1024 * 1024) {
        errorMessage = `Image size must be less than ${maxSizePerImage}MB`
        return
      }

      // Check total count
      if (selectedImages.length + newFiles.length >= maxImages) {
        errorMessage = `Maximum ${maxImages} images allowed`
        return
      }

      newFiles.push(file)
      newPreviews.push(URL.createObjectURL(file))
    })

    if (errorMessage) {
      setError(errorMessage)
      setTimeout(() => setError(null), 3000)
      return
    }

    setSelectedImages(prev => [...prev, ...newFiles])
    setPreviews(prev => [...prev, ...newPreviews])
    setError(null)
  }

  // Handle drag and drop
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(false)
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(false)
    handleFileSelect(e.dataTransfer.files)
  }

  // Remove image
  const removeImage = (index: number) => {
    URL.revokeObjectURL(previews[index])
    setSelectedImages(prev => prev.filter((_, i) => i !== index))
    setPreviews(prev => prev.filter((_, i) => i !== index))
  }

  // Send images
  const sendImages = () => {
    if (selectedImages.length > 0) {
      onImagesSelected(selectedImages, caption)
    }
  }

  // Open file dialog
  const openFileDialog = () => {
    fileInputRef.current?.click()
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: 20 }}
      className="bg-white border rounded-lg p-4 shadow-lg space-y-4"
    >
      {/* Error message */}
      <AnimatePresence>
        {error && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="bg-red-50 border border-red-200 text-red-700 px-3 py-2 rounded text-sm"
          >
            {error}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Drop zone */}
      <div
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={openFileDialog}
        className={`
          border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors
          ${dragOver
            ? 'border-yellow-400 bg-yellow-50'
            : 'border-gray-300 hover:border-gray-400'
          }
        `}
      >
        <Upload className="w-8 h-8 text-gray-400 mx-auto mb-2" />
        <p className="text-sm text-gray-600">
          Drop images here or click to select
        </p>
        <p className="text-xs text-gray-500 mt-1">
          Max {maxImages} images, {maxSizePerImage}MB each
        </p>
      </div>

      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        multiple
        onChange={(e) => handleFileSelect(e.target.files)}
        className="hidden"
      />

      {/* Image previews */}
      {previews.length > 0 && (
        <div className="space-y-3">
          <h4 className="text-sm font-medium text-gray-700">
            Selected Images ({selectedImages.length}/{maxImages})
          </h4>

          <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
            {previews.map((preview, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                className="relative group"
              >
                <img
                  src={preview}
                  alt={`Preview ${index + 1}`}
                  className="w-full h-24 object-cover rounded border"
                />
                <button
                  onClick={(e) => {
                    e.stopPropagation()
                    removeImage(index)
                  }}
                  className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity"
                >
                  <X className="w-3 h-3" />
                </button>
                <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white text-xs p-1 rounded-b">
                  {(selectedImages[index].size / 1024 / 1024).toFixed(1)}MB
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      )}

      {/* Caption input */}
      {selectedImages.length > 0 && (
        <div>
          <Input
            placeholder="Add a caption (optional)..."
            value={caption}
            onChange={(e) => setCaption(e.target.value)}
            className="w-full"
          />
        </div>
      )}

      {/* Actions */}
      <div className="flex justify-end space-x-3">
        <Button
          onClick={onCancel}
          variant="outline"
          size="sm"
        >
          Cancel
        </Button>

        {selectedImages.length > 0 && (
          <Button
            onClick={sendImages}
            size="sm"
            className="bg-yellow-500 hover:bg-yellow-600 text-white"
          >
            <Send className="w-4 h-4 mr-2" />
            Send {selectedImages.length} image{selectedImages.length > 1 ? 's' : ''}
          </Button>
        )}
      </div>
    </motion.div>
  )
}
