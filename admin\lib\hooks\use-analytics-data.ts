import { useState, useEffect } from 'react'
import { useUsers } from './use-users'
import { useDrivers } from './use-drivers'

interface ChartDataPoint {
  month: string
  users: number
  drivers: number
}

interface DriverStatusData {
  name: string
  value: number
  color: string
}

export function useAnalyticsData() {
  const [userGrowthData, setUserGrowthData] = useState<ChartDataPoint[]>([])
  const [driverStatusData, setDriverStatusData] = useState<DriverStatusData[]>([])
  const [isLoading, setIsLoading] = useState(true)

  const { users, isLoading: usersLoading } = useUsers()
  const { drivers, driverRequests, isLoading: driversLoading } = useDrivers()

  // Generate month names for the last 6 months
  const getLastSixMonths = () => {
    const months = []
    const now = new Date()
    
    for (let i = 5; i >= 0; i--) {
      const date = new Date(now.getFullYear(), now.getMonth() - i, 1)
      months.push({
        month: date.toLocaleDateString('en-US', { month: 'short' }),
        year: date.getFullYear(),
        monthIndex: date.getMonth(),
        fullDate: date
      })
    }
    
    return months
  }

  // Count registrations by month
  const countByMonth = (data: any[], dateField: string = 'createdAt') => {
    const months = getLastSixMonths()
    
    return months.map(monthInfo => {
      const count = data.filter(item => {
        const itemDate = item[dateField]
        if (!itemDate) return false
        
        return itemDate.getMonth() === monthInfo.monthIndex && 
               itemDate.getFullYear() === monthInfo.year
      }).length
      
      return {
        month: monthInfo.month,
        count
      }
    })
  }

  useEffect(() => {
    if (usersLoading || driversLoading) {
      return
    }

    try {
      // Generate user growth data for the last 6 months
      const months = getLastSixMonths()
      const usersByMonth = countByMonth(users || [])
      
      // Combine drivers and driver requests for total driver applications
      const allDriverApplications = [...(drivers || []), ...(driverRequests || [])]
      const driversByMonth = countByMonth(allDriverApplications)

      const chartData: ChartDataPoint[] = months.map((monthInfo, index) => ({
        month: monthInfo.month,
        users: usersByMonth[index]?.count || 0,
        drivers: driversByMonth[index]?.count || 0
      }))

      setUserGrowthData(chartData)

      // Generate driver status data for pie chart
      const verifiedDrivers = drivers?.filter(driver => driver.status === 'verified').length || 0
      const pendingDrivers = driverRequests?.length || 0
      const rejectedDrivers = drivers?.filter(driver => driver.status === 'rejected').length || 0
      const underReviewDrivers = drivers?.filter(driver => driver.status === 'pending').length || 0

      const statusData: DriverStatusData[] = [
        { name: 'Verified', value: verifiedDrivers, color: '#22C55E' },
        { name: 'Pending', value: pendingDrivers, color: '#F59E0B' },
        { name: 'Rejected', value: rejectedDrivers, color: '#EF4444' },
        { name: 'Under Review', value: underReviewDrivers, color: '#3B82F6' },
      ].filter(item => item.value > 0) // Only show categories with data

      setDriverStatusData(statusData)
      setIsLoading(false)
    } catch (error) {
      console.error('Error generating analytics data:', error)
      setIsLoading(false)
    }
  }, [users, drivers, driverRequests, usersLoading, driversLoading])

  return {
    userGrowthData,
    driverStatusData,
    isLoading
  }
}
