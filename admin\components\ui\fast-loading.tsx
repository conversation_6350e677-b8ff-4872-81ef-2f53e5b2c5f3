'use client'

import { memo } from 'react'

interface FastLoadingProps {
  size?: 'sm' | 'md' | 'lg'
  text?: string
  className?: string
}

function FastLoadingComponent({
  size = 'md',
  text = 'Loading...',
  className = ''
}: FastLoadingProps) {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8'
  }

  return (
    <div className={`flex items-center justify-center space-x-2 ${className}`}>
      <div
        className={`${sizeClasses[size]} border-2 border-border border-t-primary rounded-full animate-spin`}
        style={{ animationDuration: '0.4s' }} // Faster animation
      />
      {text && (
        <span className="text-muted-foreground text-sm font-medium">{text}</span>
      )}
    </div>
  )
}

// Memoize to prevent unnecessary re-renders
export const FastLoading = memo(FastLoadingComponent)
