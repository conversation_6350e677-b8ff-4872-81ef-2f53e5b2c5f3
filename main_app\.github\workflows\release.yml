name: Release Build and Deploy

on:
  push:
    branches: [ main, master ]
    tags:
      - 'v*'
  workflow_dispatch:
    inputs:
      version_type:
        description: 'Version bump type'
        required: true
        default: 'patch'
        type: choice
        options:
          - patch
          - minor
          - major
      release_notes:
        description: 'Release notes'
        required: false
        type: string

env:
  FLUTTER_VERSION: '3.19.0'
  JAVA_VERSION: '17'

jobs:
  test:
    name: Test Suite
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./main_app
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: ${{ env.FLUTTER_VERSION }}
          channel: 'stable'
          cache: true

      - name: Install dependencies
        run: flutter pub get

      - name: Verify formatting
        run: dart format --output=none --set-exit-if-changed .

      - name: Analyze project source
        run: flutter analyze --no-fatal-infos

      - name: Run all tests
        run: flutter test --coverage

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: ./main_app/coverage/lcov.info
          fail_ci_if_error: false

  build_android:
    name: Build Android Release
    needs: test
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./main_app

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Java
        uses: actions/setup-java@v4
        with:
          distribution: 'zulu'
          java-version: ${{ env.JAVA_VERSION }}

      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: ${{ env.FLUTTER_VERSION }}
          channel: 'stable'
          cache: true

      - name: Get Flutter dependencies
        run: flutter pub get

      - name: Generate version
        id: version
        run: |
          if [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
            # Manual version bump
            CURRENT_VERSION=$(grep "version:" pubspec.yaml | cut -d' ' -f2 | cut -d'+' -f1)
            echo "Current version: $CURRENT_VERSION"

            IFS='.' read -ra VERSION_PARTS <<< "$CURRENT_VERSION"
            MAJOR=${VERSION_PARTS[0]}
            MINOR=${VERSION_PARTS[1]}
            PATCH=${VERSION_PARTS[2]}

            case "${{ github.event.inputs.version_type }}" in
              "major")
                MAJOR=$((MAJOR + 1))
                MINOR=0
                PATCH=0
                ;;
              "minor")
                MINOR=$((MINOR + 1))
                PATCH=0
                ;;
              "patch")
                PATCH=$((PATCH + 1))
                ;;
            esac

            NEW_VERSION="$MAJOR.$MINOR.$PATCH"
          else
            # Auto version from tag or commit
            if [[ "${{ github.ref }}" == refs/tags/* ]]; then
              NEW_VERSION=${GITHUB_REF#refs/tags/v}
            else
              CURRENT_VERSION=$(grep "version:" pubspec.yaml | cut -d' ' -f2 | cut -d'+' -f1)
              IFS='.' read -ra VERSION_PARTS <<< "$CURRENT_VERSION"
              PATCH=$((${VERSION_PARTS[2]} + 1))
              NEW_VERSION="${VERSION_PARTS[0]}.${VERSION_PARTS[1]}.$PATCH"
            fi
          fi

          BUILD_NUMBER=$((1000 + ${{ github.run_number }}))
          echo "version=$NEW_VERSION" >> $GITHUB_OUTPUT
          echo "build_number=$BUILD_NUMBER" >> $GITHUB_OUTPUT
          echo "New version: $NEW_VERSION+$BUILD_NUMBER"

      - name: Update version in pubspec.yaml
        run: |
          sed -i "s/version: .*/version: ${{ steps.version.outputs.version }}+${{ steps.version.outputs.build_number }}/" pubspec.yaml

      - name: Build APK
        run: |
          flutter build apk --release \
            --build-number=${{ steps.version.outputs.build_number }} \
            --build-name=${{ steps.version.outputs.version }} \
            --dart-define=ENVIRONMENT=production \
            --obfuscate \
            --split-debug-info=build/debug-info

      - name: Build App Bundle
        run: |
          flutter build appbundle --release \
            --build-number=${{ steps.version.outputs.build_number }} \
            --build-name=${{ steps.version.outputs.version }} \
            --dart-define=ENVIRONMENT=production \
            --obfuscate \
            --split-debug-info=build/debug-info

      - name: Upload APK artifact
        uses: actions/upload-artifact@v4
        with:
          name: release-apk-v${{ steps.version.outputs.version }}
          path: main_app/build/app/outputs/flutter-apk/app-release.apk
          retention-days: 90

      - name: Upload App Bundle artifact
        uses: actions/upload-artifact@v4
        with:
          name: release-bundle-v${{ steps.version.outputs.version }}
          path: main_app/build/app/outputs/bundle/release/app-release.aab
          retention-days: 90

      - name: Upload debug symbols
        uses: actions/upload-artifact@v4
        with:
          name: debug-symbols-v${{ steps.version.outputs.version }}
          path: main_app/build/debug-info/
          retention-days: 90

  build_web:
    name: Build Web Release
    needs: test
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./main_app

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: ${{ env.FLUTTER_VERSION }}
          channel: 'stable'
          cache: true

      - name: Get Flutter dependencies
        run: flutter pub get

      - name: Build web app
        run: |
          flutter build web --release \
            --web-renderer html \
            --dart-define=ENVIRONMENT=production \
            --source-maps \
            --pwa-strategy=offline-first

      - name: Upload web build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: release-web
          path: main_app/build/web/
          retention-days: 90

  deploy:
    name: Deploy Release
    needs: [build_android, build_web]
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./main_app

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Download APK artifact
        uses: actions/download-artifact@v4
        with:
          pattern: release-apk-*
          merge-multiple: true
          path: main_app/build/app/outputs/flutter-apk/

      - name: Download web build artifacts
        uses: actions/download-artifact@v4
        with:
          name: release-web
          path: main_app/build/web/

      - name: Get version from artifacts
        id: version
        run: |
          VERSION=$(ls ../build/app/outputs/flutter-apk/ | grep -o 'release-apk-v[0-9.]*' | head -1 | sed 's/release-apk-v//')
          echo "version=$VERSION" >> $GITHUB_OUTPUT

      - name: Generate release notes
        id: release_notes
        run: |
          if [[ "${{ github.event.inputs.release_notes }}" != "" ]]; then
            NOTES="${{ github.event.inputs.release_notes }}"
          else
            NOTES="🚀 Production Release v${{ steps.version.outputs.version }}

          **What's New:**
          - Latest features and improvements
          - Bug fixes and performance enhancements
          - Enhanced user experience

          **Technical Details:**
          - Build: ${{ github.run_number }}
          - Commit: ${{ github.sha }}
          - Branch: ${{ github.ref_name }}

          **Download:**
          - 🌐 Web App: https://drive-on-b2af8.web.app
          - 📱 Android APK: Available in Firebase App Distribution"
          fi
          echo "notes<<EOF" >> $GITHUB_OUTPUT
          echo "$NOTES" >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT

      - name: Deploy APK to Firebase App Distribution
        uses: wzieba/Firebase-Distribution-Github-Action@v1
        with:
          appId: ${{ secrets.FIREBASE_APP_ID }}
          serviceCredentialsFileContent: ${{ secrets.FIREBASE_SERVICE_ACCOUNT_DRIVE_ON_B2AF8 }}
          groups: production-testers
          file: main_app/build/app/outputs/flutter-apk/app-release.apk
          releaseNotes: ${{ steps.release_notes.outputs.notes }}

      - name: Deploy to Firebase Hosting
        uses: FirebaseExtended/action-hosting-deploy@v0
        with:
          repoToken: '${{ secrets.GITHUB_TOKEN }}'
          firebaseServiceAccount: '${{ secrets.FIREBASE_SERVICE_ACCOUNT_DRIVE_ON_B2AF8 }}'
          projectId: drive-on-b2af8
          target: driveon
          channelId: live
          entryPoint: main_app

      - name: Create GitHub Release
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: v${{ steps.version.outputs.version }}
          release_name: Drive-On v${{ steps.version.outputs.version }}
          body: ${{ steps.release_notes.outputs.notes }}
          draft: false
          prerelease: false

      - name: Upload APK to GitHub Release
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ steps.create_release.outputs.upload_url }}
          asset_path: main_app/build/app/outputs/flutter-apk/app-release.apk
          asset_name: drive-on-v${{ steps.version.outputs.version }}.apk
          asset_content_type: application/vnd.android.package-archive

  build_android:
    name: Build Android Release
    needs: test
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        with:
          fetch-depth: 0
      
      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.16.x'
          channel: 'stable'
          cache: true
      
      - name: Install dependencies
        run: flutter pub get
      
      - name: Create .env files
        run: |
          cp .env.example .env.development
          cp .env.example .env.staging
          cp .env.example .env.production
      
      - name: Setup Java
        uses: actions/setup-java@v3
        with:
          distribution: 'zulu'
          java-version: '17'
      
      - name: Setup Android keystore
        env:
          KEYSTORE_BASE64: ${{ secrets.ANDROID_KEYSTORE_BASE64 }}
          KEY_PROPERTIES: ${{ secrets.ANDROID_KEY_PROPERTIES }}
        if: ${{ env.KEYSTORE_BASE64 != '' && env.KEY_PROPERTIES != '' }}
        run: |
          echo "$KEYSTORE_BASE64" | base64 --decode > android/app/keystore.jks
          echo "$KEY_PROPERTIES" > android/key.properties
      
      - name: Build Android App Bundle
        run: flutter build appbundle --dart-define=ENVIRONMENT=production
      
      - name: Build Android APK
        run: flutter build apk --dart-define=ENVIRONMENT=production
      
      - name: Upload App Bundle
        uses: actions/upload-artifact@v3
        with:
          name: appbundle
          path: build/app/outputs/bundle/release/app-release.aab
      
      - name: Upload APK
        uses: actions/upload-artifact@v3
        with:
          name: apk
          path: build/app/outputs/flutter-apk/app-release.apk
      
      - name: Upload to Play Store
        if: startsWith(github.ref, 'refs/tags/v') && github.event_name == 'push'
        uses: r0adkll/upload-google-play@v1
        with:
          serviceAccountJsonPlainText: ${{ secrets.PLAY_STORE_SERVICE_ACCOUNT_JSON }}
          packageName: com.driver.drive_on
          releaseFiles: build/app/outputs/bundle/release/app-release.aab
          track: internal
          status: completed
          whatsNewDirectory: distribution/whatsnew
          mappingFile: build/app/outputs/mapping/release/mapping.txt

  build_ios:
    name: Build iOS Release
    needs: test
    runs-on: macos-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        with:
          fetch-depth: 0
      
      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.16.x'
          channel: 'stable'
          cache: true
      
      - name: Install dependencies
        run: flutter pub get
      
      - name: Create .env files
        run: |
          cp .env.example .env.development
          cp .env.example .env.staging
          cp .env.example .env.production
      
      - name: Build iOS
        run: |
          flutter build ios --release --no-codesign --dart-define=ENVIRONMENT=production
      
      - name: Setup Ruby
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: '3.2'
          bundler-cache: true
      
      - name: Install Fastlane
        run: gem install fastlane
      
      - name: Setup provisioning profiles
        if: ${{ github.event_name == 'push' && startsWith(github.ref, 'refs/tags/v') }}
        env:
          APPLE_CERTIFICATE: ${{ secrets.APPLE_CERTIFICATE }}
          APPLE_CERTIFICATE_PASSWORD: ${{ secrets.APPLE_CERTIFICATE_PASSWORD }}
          APPLE_PROVISIONING_PROFILE: ${{ secrets.APPLE_PROVISIONING_PROFILE }}
          MATCH_PASSWORD: ${{ secrets.MATCH_PASSWORD }}
          FASTLANE_APPLE_ID: ${{ secrets.FASTLANE_APPLE_ID }}
          FASTLANE_APPLE_APPLICATION_SPECIFIC_PASSWORD: ${{ secrets.FASTLANE_APPLE_APPLICATION_SPECIFIC_PASSWORD }}
          FASTLANE_PASSWORD: ${{ secrets.FASTLANE_PASSWORD }}
        run: |
          if [ -n "$APPLE_CERTIFICATE" ] && [ -n "$APPLE_CERTIFICATE_PASSWORD" ] && [ -n "$APPLE_PROVISIONING_PROFILE" ]; then
            # Create keychain
            KEYCHAIN_PATH=$RUNNER_TEMP/app-signing.keychain-db
            KEYCHAIN_PASSWORD=temporary
            
            security create-keychain -p "$KEYCHAIN_PASSWORD" "$KEYCHAIN_PATH"
            security set-keychain-settings -lut 21600 "$KEYCHAIN_PATH"
            security unlock-keychain -p "$KEYCHAIN_PASSWORD" "$KEYCHAIN_PATH"
            
            # Import certificate
            echo -n "$APPLE_CERTIFICATE" | base64 --decode > /tmp/certificate.p12
            security import /tmp/certificate.p12 -P "$APPLE_CERTIFICATE_PASSWORD" -A -t cert -f pkcs12 -k "$KEYCHAIN_PATH"
            security list-keychain -d user -s "$KEYCHAIN_PATH"
            
            # Setup provisioning profile
            mkdir -p ~/Library/MobileDevice/Provisioning\ Profiles
            echo -n "$APPLE_PROVISIONING_PROFILE" | base64 --decode > ~/Library/MobileDevice/Provisioning\ Profiles/profile.mobileprovision
            
            # Build signed IPA
            cd ios && fastlane build
          else
            echo "Skipping iOS signing setup - required secrets not available"
          fi
      
      - name: Upload build artifacts
        uses: actions/upload-artifact@v3
        with:
          name: ios-build
          path: ios/build/
      
      - name: Upload to TestFlight
        if: ${{ github.event_name == 'push' && startsWith(github.ref, 'refs/tags/v') }}
        env:
          FASTLANE_APPLE_ID: ${{ secrets.FASTLANE_APPLE_ID }}
          FASTLANE_PASSWORD: ${{ secrets.FASTLANE_PASSWORD }}
          FASTLANE_APPLE_APPLICATION_SPECIFIC_PASSWORD: ${{ secrets.FASTLANE_APPLE_APPLICATION_SPECIFIC_PASSWORD }}
        run: |
          if [ -n "$FASTLANE_APPLE_ID" ] && [ -n "$FASTLANE_PASSWORD" ]; then
            cd ios && fastlane upload_to_testflight
          else
            echo "Skipping upload to TestFlight - required secrets not available"
          fi

  create_release:
    name: Create GitHub Release
    needs: [build_android, build_ios]
    if: startsWith(github.ref, 'refs/tags/v')
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        with:
          fetch-depth: 0
      
      - name: Download APK
        uses: actions/download-artifact@v3
        with:
          name: apk
          path: artifacts
      
      - name: Get version from tag
        id: get_version
        run: echo "VERSION=${GITHUB_REF#refs/tags/v}" >> $GITHUB_OUTPUT
      
      - name: Generate changelog
        id: changelog
        run: |
          changelog=$(git log --pretty=format:"- %s" $(git describe --tags --abbrev=0 HEAD^)..HEAD)
          echo "CHANGELOG<<EOF" >> $GITHUB_OUTPUT
          echo "$changelog" >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT
      
      - name: Create Release
        uses: softprops/action-gh-release@v1
        with:
          tag_name: ${{ github.ref }}
          name: Release ${{ steps.get_version.outputs.VERSION }}
          body: |
            Changes in this release:
            ${{ steps.changelog.outputs.CHANGELOG }}
          files: artifacts/app-release.apk
          draft: false
          prerelease: false 