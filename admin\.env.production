# Firebase Configuration
NEXT_PUBLIC_FIREBASE_API_KEY=AIzaSyBQehlAZFI-WngAMwzblEu7JBdMcCr-P80
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=drive-on-b2af8.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=drive-on-b2af8
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=drive-on-b2af8.firebasestorage.app
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=206767723448
NEXT_PUBLIC_FIREBASE_APP_ID=1:206767723448:android:6a8e1d9c3c8992992d754d
NEXT_PUBLIC_FIREBASE_DATABASE_URL=https://drive-on-b2af8-default-rtdb.firebaseio.com

# Environment
NODE_ENV=production
NEXT_PUBLIC_ENV=production

# Admin Configuration
NEXT_PUBLIC_ADMIN_EMAIL=<EMAIL>
NEXT_PUBLIC_APP_NAME=Drive-On Admin Panel
NEXT_PUBLIC_APP_VERSION=1.0.0

# Feature Flags
NEXT_PUBLIC_ENABLE_ANALYTICS=true
NEXT_PUBLIC_ENABLE_NOTIFICATIONS=true
NEXT_PUBLIC_ENABLE_REAL_TIME=true

# Performance
NEXT_PUBLIC_ENABLE_SW=true
NEXT_PUBLIC_ENABLE_COMPRESSION=true
