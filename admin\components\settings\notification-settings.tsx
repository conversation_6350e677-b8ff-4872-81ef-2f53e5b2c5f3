'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import {
  Bell,
  Mail,
  MessageSquare,
  Smartphone,
  Users,
  Shield,
  TrendingUp,
  Calendar,
  Save,
  RefreshCw,
  Volume2,
  VolumeX,
} from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import { useSettings } from '@/lib/hooks/use-settings'
import { useNotificationSettings } from '@/lib/hooks/use-notifications'

const notificationCategories = [
  {
    id: 'emailNotifications',
    title: 'Email Notifications',
    description: 'Receive notifications via email',
    icon: <Mail className="w-5 h-5" />,
    color: 'blue'
  },
  {
    id: 'pushNotifications',
    title: 'Push Notifications',
    description: 'Browser and mobile push notifications',
    icon: <Bell className="w-5 h-5" />,
    color: 'green'
  },
  {
    id: 'smsNotifications',
    title: 'SMS Notifications',
    description: 'Text message notifications for urgent alerts',
    icon: <MessageSquare className="w-5 h-5" />,
    color: 'purple'
  }
]

const alertTypes = [
  {
    id: 'newUserAlerts',
    title: 'New User Registrations',
    description: 'Get notified when new users sign up',
    icon: <Users className="w-5 h-5" />,
    priority: 'medium'
  },
  {
    id: 'systemAlerts',
    title: 'System Alerts',
    description: 'Server issues, downtime, and system errors',
    icon: <Shield className="w-5 h-5" />,
    priority: 'high'
  },
  {
    id: 'securityAlerts',
    title: 'Security Alerts',
    description: 'Failed login attempts and security breaches',
    icon: <Shield className="w-5 h-5" />,
    priority: 'critical'
  },
  {
    id: 'marketingEmails',
    title: 'Marketing Emails',
    description: 'Product updates and promotional content',
    icon: <TrendingUp className="w-5 h-5" />,
    priority: 'low'
  },
  {
    id: 'weeklyReports',
    title: 'Weekly Reports',
    description: 'Analytics and performance summaries',
    icon: <Calendar className="w-5 h-5" />,
    priority: 'medium'
  }
]

export function NotificationSettings() {
  const { settings: oldSettings, updateNotificationSettings } = useSettings()
  const { settings, updateSettings, testNotification } = useNotificationSettings()
  const [isLoading, setIsLoading] = useState(false)
  const [formData, setFormData] = useState(settings)
  const [testingNotification, setTestingNotification] = useState<string | null>(null)

  const handleToggle = (field: string, value: boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleSave = async () => {
    setIsLoading(true)
    try {
      await updateSettings(formData)
      // Also update old settings for backward compatibility
      await updateNotificationSettings({
        emailNotifications: formData.emailNotifications,
        pushNotifications: formData.browserNotifications,
        newUserAlerts: formData.userRegistrationAlerts,
        systemAlerts: formData.systemAlerts
      })
      // Show success message
    } catch (error) {
      console.error('Error saving notification settings:', error)
      // Show error message
    } finally {
      setIsLoading(false)
    }
  }

  const handleTestNotification = async (type: string) => {
    setTestingNotification(type)
    try {
      // Simulate sending test notification
      await new Promise(resolve => setTimeout(resolve, 2000))
      // Show success message
    } catch (error) {
      console.error('Error sending test notification:', error)
    } finally {
      setTestingNotification(null)
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
      case 'high':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400'
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'
      case 'low':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
    }
  }

  return (
    <div className="space-y-6">
      {/* Notification Channels */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Bell className="w-5 h-5" />
            <span>Notification Channels</span>
          </CardTitle>
          <CardDescription>
            Choose how you want to receive notifications
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {notificationCategories.map((category) => (
            <div key={category.id} className="flex items-center justify-between p-4 border rounded-lg dark:border-gray-700">
              <div className="flex items-center space-x-4">
                <div className={`w-10 h-10 bg-${category.color}-100 dark:bg-${category.color}-900/20 rounded-full flex items-center justify-center`}>
                  <div className={`text-${category.color}-600 dark:text-${category.color}-400`}>
                    {category.icon}
                  </div>
                </div>
                <div>
                  <h3 className="font-medium text-gray-900 dark:text-white">{category.title}</h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400">{category.description}</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <Switch
                  checked={formData[category.id as keyof typeof formData] as boolean}
                  onCheckedChange={(checked) => handleToggle(category.id, checked)}
                />
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleTestNotification(category.id)}
                  disabled={testingNotification === category.id || !formData[category.id as keyof typeof formData]}
                >
                  {testingNotification === category.id ? (
                    <RefreshCw className="w-4 h-4 animate-spin" />
                  ) : (
                    'Test'
                  )}
                </Button>
              </div>
            </div>
          ))}
        </CardContent>
      </Card>

      {/* Alert Types */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Shield className="w-5 h-5" />
            <span>Alert Types</span>
          </CardTitle>
          <CardDescription>
            Configure which events trigger notifications
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {alertTypes.map((alert) => (
            <motion.div
              key={alert.id}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              className="flex items-center justify-between p-4 border rounded-lg dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors"
            >
              <div className="flex items-center space-x-4">
                <div className="w-10 h-10 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center">
                  <div className="text-gray-600 dark:text-gray-400">
                    {alert.icon}
                  </div>
                </div>
                <div className="flex-1">
                  <div className="flex items-center space-x-2">
                    <h3 className="font-medium text-gray-900 dark:text-white">{alert.title}</h3>
                    <Badge className={`text-xs ${getPriorityColor(alert.priority)}`}>
                      {alert.priority}
                    </Badge>
                  </div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">{alert.description}</p>
                </div>
              </div>
              <Switch
                checked={formData[alert.id as keyof typeof formData] as boolean}
                onCheckedChange={(checked) => handleToggle(alert.id, checked)}
              />
            </motion.div>
          ))}
        </CardContent>
      </Card>

      {/* Notification Schedule */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Calendar className="w-5 h-5" />
            <span>Notification Schedule</span>
          </CardTitle>
          <CardDescription>
            Set quiet hours and delivery preferences
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="quietStart">Quiet Hours Start</Label>
              <input
                id="quietStart"
                type="time"
                defaultValue="22:00"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="quietEnd">Quiet Hours End</Label>
              <input
                id="quietEnd"
                type="time"
                defaultValue="08:00"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </div>
          </div>

          <div className="space-y-4">
            <Label>Delivery Preferences</Label>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div>
                  <Label>Batch Notifications</Label>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Group similar notifications together
                  </p>
                </div>
                <Switch defaultChecked />
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <Label>Instant Critical Alerts</Label>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Bypass quiet hours for critical alerts
                  </p>
                </div>
                <Switch defaultChecked />
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <Label>Sound Notifications</Label>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Play sound for push notifications
                  </p>
                </div>
                <Switch defaultChecked />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Notification Summary */}
      <Card>
        <CardHeader>
          <CardTitle>Notification Summary</CardTitle>
          <CardDescription>
            Overview of your current notification settings
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <div className="w-12 h-12 bg-blue-100 dark:bg-blue-800 rounded-full flex items-center justify-center mx-auto mb-3">
                {formData.emailNotifications ? (
                  <Mail className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                ) : (
                  <VolumeX className="w-6 h-6 text-gray-400" />
                )}
              </div>
              <h3 className="font-medium text-gray-900 dark:text-white">Email</h3>
              <Badge variant={formData.emailNotifications ? 'success' : 'secondary'}>
                {formData.emailNotifications ? 'Enabled' : 'Disabled'}
              </Badge>
            </div>

            <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
              <div className="w-12 h-12 bg-green-100 dark:bg-green-800 rounded-full flex items-center justify-center mx-auto mb-3">
                {formData.browserNotifications ? (
                  <Bell className="w-6 h-6 text-green-600 dark:text-green-400" />
                ) : (
                  <VolumeX className="w-6 h-6 text-gray-400" />
                )}
              </div>
              <h3 className="font-medium text-gray-900 dark:text-white">Push</h3>
              <Badge variant={formData.browserNotifications ? 'success' : 'secondary'}>
                {formData.browserNotifications ? 'Enabled' : 'Disabled'}
              </Badge>
            </div>

            <div className="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
              <div className="w-12 h-12 bg-purple-100 dark:bg-purple-800 rounded-full flex items-center justify-center mx-auto mb-3">
                {formData.soundEnabled ? (
                  <MessageSquare className="w-6 h-6 text-purple-600 dark:text-purple-400" />
                ) : (
                  <VolumeX className="w-6 h-6 text-gray-400" />
                )}
              </div>
              <h3 className="font-medium text-gray-900 dark:text-white">Sound</h3>
              <Badge variant={formData.soundEnabled ? 'success' : 'secondary'}>
                {formData.soundEnabled ? 'Enabled' : 'Disabled'}
              </Badge>
            </div>
          </div>

          <div className="mt-6 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <h4 className="font-medium text-gray-900 dark:text-white mb-2">Active Alerts</h4>
            <div className="flex flex-wrap gap-2">
              {alertTypes.map((alert) => (
                formData[alert.id as keyof typeof formData] && (
                  <Badge key={alert.id} variant="secondary" className="text-xs">
                    {alert.title}
                  </Badge>
                )
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Save Button */}
      <div className="flex justify-end space-x-3">
        <Button
          variant="outline"
          onClick={() => setFormData(settings)}
        >
          Reset Changes
        </Button>
        <Button
          onClick={handleSave}
          disabled={isLoading}
          className="gradient-primary"
        >
          {isLoading ? (
            <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
          ) : (
            <Save className="w-4 h-4 mr-2" />
          )}
          {isLoading ? 'Saving...' : 'Save Changes'}
        </Button>
      </div>
    </div>
  )
}
