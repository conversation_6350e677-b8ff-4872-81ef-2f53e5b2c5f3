import {
  collection,
  doc,
  addDoc,
  updateDoc,
  onSnapshot,
  query,
  orderBy,
  serverTimestamp,
  getDoc,
  getDocs,
  where,
  limit
} from 'firebase/firestore'
import {
  ref,
  uploadBytes,
  getDownloadURL,
  deleteObject
} from 'firebase/storage'
import { db, storage } from '@/lib/firebase/config'

export interface ChatMessage {
  id: string
  text: string
  senderId: string
  senderName: string
  senderAvatar?: string
  timestamp: Date
  type: 'text' | 'voice' | 'image' | 'file'
  voiceNote?: {
    url: string
    duration: number
  }
  imageUrls?: string[]
  attachments?: {
    name: string
    url: string
    type: string
    size: number
  }[]
  isAdmin: boolean
  isEdited: boolean
  replyTo?: {
    id: string
    text: string
    senderName: string
  }
}

export interface ChatRoom {
  id: string
  type: 'query' | 'forum'
  title: string
  participants: string[]
  participantNames: { [key: string]: string }
  lastMessage: string
  lastMessageTime: Date
  lastSenderId: string
  messageCount: number
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

class ChatService {
  // Get messages for a chat room
  getMessages(roomId: string, roomType: 'query' | 'forum', callback: (messages: ChatMessage[]) => void) {
    const messagesRef = collection(db, roomType === 'query' ? 'queries' : 'forums', roomId, 'messages')
    const q = query(messagesRef, orderBy('timestamp', 'asc'))

    return onSnapshot(q, (snapshot) => {
      const messages: ChatMessage[] = []
      snapshot.forEach((doc) => {
        const data = doc.data()
        messages.push({
          id: doc.id,
          text: data.text || '',
          senderId: data.senderId,
          senderName: data.senderName,
          senderAvatar: data.senderAvatar,
          timestamp: data.timestamp?.toDate() || new Date(),
          type: data.type || 'text',
          voiceNote: data.voiceNote,
          imageUrls: data.imageUrls || [],
          attachments: data.attachments || [],
          isAdmin: data.isAdmin || false,
          isEdited: data.isEdited || false,
          replyTo: data.replyTo
        })
      })
      callback(messages)
    })
  }

  // Send a text message
  async sendMessage(
    roomId: string,
    roomType: 'query' | 'forum',
    text: string,
    senderId: string,
    senderName: string,
    senderAvatar?: string,
    isAdmin: boolean = true,
    replyTo?: { id: string; text: string; senderName: string }
  ) {
    try {
      const messagesRef = collection(db, roomType === 'query' ? 'queries' : 'forums', roomId, 'messages')

      const messageData = {
        text,
        senderId,
        senderName,
        senderAvatar: senderAvatar || '',
        timestamp: serverTimestamp(),
        type: 'text',
        isAdmin,
        isEdited: false,
        replyTo: replyTo || null
      }

      const docRef = await addDoc(messagesRef, messageData)

      // Update room's last message info
      const roomRef = doc(db, roomType === 'query' ? 'queries' : 'forums', roomId)
      await updateDoc(roomRef, {
        lastMessage: text,
        lastMessageTime: serverTimestamp(),
        lastSenderId: senderId,
        messageCount: (await this.getMessageCount(roomId, roomType)) + 1,
        updatedAt: serverTimestamp()
      })

      return docRef.id
    } catch (error) {
      console.error('Error sending message:', error)
      throw error
    }
  }

  // Send voice message
  async sendVoiceMessage(
    roomId: string,
    roomType: 'query' | 'forum',
    audioBlob: Blob,
    duration: number,
    senderId: string,
    senderName: string,
    senderAvatar?: string,
    isAdmin: boolean = true
  ) {
    try {
      // Upload voice file
      const fileName = `voice_${Date.now()}.webm`
      const voiceRef = ref(storage, `${roomType}s/${roomId}/voice/${fileName}`)

      await uploadBytes(voiceRef, audioBlob)
      const voiceUrl = await getDownloadURL(voiceRef)

      const messagesRef = collection(db, roomType === 'query' ? 'queries' : 'forums', roomId, 'messages')

      const messageData = {
        text: '',
        senderId,
        senderName,
        senderAvatar: senderAvatar || '',
        timestamp: serverTimestamp(),
        type: 'voice',
        voiceNote: {
          url: voiceUrl,
          duration
        },
        isAdmin,
        isEdited: false
      }

      const docRef = await addDoc(messagesRef, messageData)

      // Update room's last message info
      const roomRef = doc(db, roomType === 'query' ? 'queries' : 'forums', roomId)
      await updateDoc(roomRef, {
        lastMessage: '🎵 Voice message',
        lastMessageTime: serverTimestamp(),
        lastSenderId: senderId,
        messageCount: (await this.getMessageCount(roomId, roomType)) + 1,
        updatedAt: serverTimestamp()
      })

      return docRef.id
    } catch (error) {
      console.error('Error sending voice message:', error)
      throw error
    }
  }

  // Send image message
  async sendImageMessage(
    roomId: string,
    roomType: 'query' | 'forum',
    images: File[],
    text: string,
    senderId: string,
    senderName: string,
    senderAvatar?: string,
    isAdmin: boolean = true
  ) {
    try {
      const imageUrls: string[] = []

      // Upload all images
      for (const image of images) {
        const fileName = `image_${Date.now()}_${Math.random().toString(36).substr(2, 9)}.${image.name.split('.').pop()}`
        const imageRef = ref(storage, `${roomType}s/${roomId}/images/${fileName}`)

        await uploadBytes(imageRef, image)
        const imageUrl = await getDownloadURL(imageRef)
        imageUrls.push(imageUrl)
      }

      const messagesRef = collection(db, roomType === 'query' ? 'queries' : 'forums', roomId, 'messages')

      const messageData = {
        text: text || '',
        senderId,
        senderName,
        senderAvatar: senderAvatar || '',
        timestamp: serverTimestamp(),
        type: 'image',
        imageUrls,
        isAdmin,
        isEdited: false
      }

      const docRef = await addDoc(messagesRef, messageData)

      // Update room's last message info
      const roomRef = doc(db, roomType === 'query' ? 'queries' : 'forums', roomId)
      await updateDoc(roomRef, {
        lastMessage: text || '📷 Image',
        lastMessageTime: serverTimestamp(),
        lastSenderId: senderId,
        messageCount: (await this.getMessageCount(roomId, roomType)) + 1,
        updatedAt: serverTimestamp()
      })

      return docRef.id
    } catch (error) {
      console.error('Error sending image message:', error)
      throw error
    }
  }

  // Get message count for a room
  private async getMessageCount(roomId: string, roomType: 'query' | 'forum'): Promise<number> {
    try {
      const messagesRef = collection(db, roomType === 'query' ? 'queries' : 'forums', roomId, 'messages')
      const snapshot = await getDocs(messagesRef)
      return snapshot.size
    } catch (error) {
      console.error('Error getting message count:', error)
      return 0
    }
  }

  // Get room info
  async getRoomInfo(roomId: string, roomType: 'query' | 'forum'): Promise<ChatRoom | null> {
    try {
      const roomRef = doc(db, roomType === 'query' ? 'queries' : 'forums', roomId)
      const roomDoc = await getDoc(roomRef)

      if (!roomDoc.exists()) {
        return null
      }

      const data = roomDoc.data()
      return {
        id: roomDoc.id,
        type: roomType,
        title: data.subject || data.title || 'Chat Room',
        participants: data.participants || [],
        participantNames: data.participantNames || {},
        lastMessage: data.lastMessage || '',
        lastMessageTime: data.lastMessageTime?.toDate() || new Date(),
        lastSenderId: data.lastSenderId || '',
        messageCount: data.messageCount || 0,
        isActive: data.status !== 'closed',
        createdAt: data.createdAt?.toDate() || new Date(),
        updatedAt: data.updatedAt?.toDate() || new Date()
      }
    } catch (error) {
      console.error('Error getting room info:', error)
      return null
    }
  }

  // Mark messages as read
  async markAsRead(roomId: string, roomType: 'query' | 'forum', userId: string) {
    try {
      const roomRef = doc(db, roomType === 'query' ? 'queries' : 'forums', roomId)
      await updateDoc(roomRef, {
        [`readBy.${userId}`]: serverTimestamp()
      })
    } catch (error) {
      console.error('Error marking as read:', error)
    }
  }
}

export const chatService = new ChatService()
