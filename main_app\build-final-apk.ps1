# Final APK Build Script - Handles all compatibility issues
Write-Host "🚀 Building Drive-On Final Release APK" -ForegroundColor Green
Write-Host "=======================================" -ForegroundColor Green

# Set Android SDK environment variables
$env:ANDROID_HOME = "C:\Users\<USER>\AppData\Local\Android\Sdk"
$env:ANDROID_SDK_ROOT = "C:\Users\<USER>\AppData\Local\Android\Sdk"
$env:PATH = "$env:ANDROID_HOME\platform-tools;$env:ANDROID_HOME\build-tools\35.0.1;$env:PATH"

Write-Host "📍 Android SDK Path: $env:ANDROID_HOME" -ForegroundColor Yellow

# Clean everything
Write-Host "🧹 Deep cleaning project..." -ForegroundColor Yellow
flutter clean
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Flutter clean failed" -ForegroundColor Red
    exit 1
}

# Clean Android build cache
if (Test-Path "android\.gradle") {
    Remove-Item -Recurse -Force "android\.gradle"
    Write-Host "✅ Android .gradle cache cleared" -ForegroundColor Green
}

if (Test-Path "android\app\build") {
    Remove-Item -Recurse -Force "android\app\build"
    Write-Host "✅ Android app build cache cleared" -ForegroundColor Green
}

# Get dependencies
Write-Host "📦 Getting updated dependencies..." -ForegroundColor Yellow
flutter pub get
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Flutter pub get failed" -ForegroundColor Red
    exit 1
}

Write-Host "✅ Dependencies updated!" -ForegroundColor Green

# Build APK with compatibility flags
Write-Host "🔨 Building APK with compatibility settings..." -ForegroundColor Yellow
Write-Host "This may take several minutes..." -ForegroundColor Yellow

# Try building with different approaches
$buildSuccess = $false

# Approach 1: Build with no obfuscation and no shrinking
Write-Host "Trying build approach 1: No obfuscation, no shrinking..." -ForegroundColor Cyan
flutter build apk --release --no-obfuscate --no-shrink --dart-define=ENVIRONMENT=production
if ($LASTEXITCODE -eq 0) {
    $buildSuccess = $true
    Write-Host "✅ Build successful with approach 1!" -ForegroundColor Green
} else {
    Write-Host "⚠️ Approach 1 failed, trying approach 2..." -ForegroundColor Yellow
    
    # Approach 2: Build with debug symbols
    Write-Host "Trying build approach 2: With debug symbols..." -ForegroundColor Cyan
    flutter build apk --release --no-obfuscate --dart-define=ENVIRONMENT=production
    if ($LASTEXITCODE -eq 0) {
        $buildSuccess = $true
        Write-Host "✅ Build successful with approach 2!" -ForegroundColor Green
    } else {
        Write-Host "⚠️ Approach 2 failed, trying approach 3..." -ForegroundColor Yellow
        
        # Approach 3: Simple release build
        Write-Host "Trying build approach 3: Simple release build..." -ForegroundColor Cyan
        flutter build apk --release
        if ($LASTEXITCODE -eq 0) {
            $buildSuccess = $true
            Write-Host "✅ Build successful with approach 3!" -ForegroundColor Green
        }
    }
}

if ($buildSuccess) {
    Write-Host ""
    Write-Host "🎉 APK BUILD SUCCESSFUL!" -ForegroundColor Green
    Write-Host "========================" -ForegroundColor Green
    Write-Host ""
    Write-Host "📱 Your APK is ready at:" -ForegroundColor Yellow
    Write-Host "build\app\outputs\flutter-apk\app-release.apk" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "🎯 Features in your updated app:" -ForegroundColor Yellow
    Write-Host "✅ Google-only login screen" -ForegroundColor Green
    Write-Host "✅ Persistent authentication" -ForegroundColor Green
    Write-Host "✅ All animations preserved" -ForegroundColor Green
    Write-Host "✅ Production optimized" -ForegroundColor Green
    Write-Host "✅ All compatibility issues fixed" -ForegroundColor Green
    Write-Host ""
    
    # Check APK size
    $apkPath = "build\app\outputs\flutter-apk\app-release.apk"
    if (Test-Path $apkPath) {
        $apkSize = (Get-Item $apkPath).Length / 1MB
        Write-Host "APK Size: $([math]::Round($apkSize, 2)) MB" -ForegroundColor Cyan
    }
} else {
    Write-Host ""
    Write-Host "All build approaches failed" -ForegroundColor Red
    Write-Host "Please check the error messages above for details." -ForegroundColor Red
    exit 1
}
