import 'dart:convert';
import 'package:http/http.dart' as http;
import 'app_logger.dart';

/// A logger for HTTP network requests and responses
///
/// This class provides detailed logging of HTTP requests and responses,
/// with special handling for common content types (JSON, text, binary).
/// It includes features for:
///   - Logging request details (URL, method, headers, body)
///   - Logging response details (status code, headers, body)
///   - Pretty-printing JSON responses
///   - Redacting sensitive information in headers and bodies
///   - Truncating large response bodies
///
/// It integrates with the app's main logging system to provide
/// consistent formatting and persistence of network logs.
class NetworkLogger {
  /// Tag used for network-related logs
  static const String _tag = 'Network';
  
  /// Maximum size for response body logging (to avoid excessive logs)
  static const int _maxResponseSize = 10000;
  
  /// Logger instance for network logs
  static final AppLogger _logger = AppLogger(_tag);
  
  /// Whether logging is enabled
  static bool _enabled = true;
  
  /// Whether to truncate large responses
  static bool _truncateLargeResponses = true;
  
  /// List of headers that contain sensitive information that should be redacted
  static const List<String> _sensitiveHeaders = [
    'authorization',
    'cookie',
    'set-cookie',
    'x-api-key',
    'api-key',
    'password',
    'token',
  ];

  /// Create a client with logging enabled
  static http.Client createLoggingClient() {
    return _LoggingClient();
  }

  /// Configure the logger settings
  ///
  /// Parameters:
  /// - [enabled] Whether logging is enabled
  /// - [truncateLargeResponses] Whether to truncate large responses
  static void configure({
    bool? enabled,
    bool? truncateLargeResponses,
  }) {
    if (enabled != null) _enabled = enabled;
    if (truncateLargeResponses != null) _truncateLargeResponses = truncateLargeResponses;
  }

  /// Log an HTTP request before it is sent
  ///
  /// This captures details about the outgoing request including URL,
  /// method, headers, and body content when available.
  ///
  /// Parameters:
  /// - [request] The HTTP request to log
  static void logRequest(http.BaseRequest request) {
    if (!_enabled) return;

    try {
      final requestLog = StringBuffer();
      requestLog.writeln('➡️ REQUEST: ${request.method} ${request.url.toString()}');
      
      if (request.headers.isNotEmpty) {
        requestLog.writeln('HEADERS:');
        _logHeaders(requestLog, request.headers);
      }
      
      if (request is http.Request && request.body.isNotEmpty) {
        requestLog.writeln('BODY:');
        _logBody(requestLog, request.body, request.headers['content-type']);
      }
      
      _logger.info(requestLog.toString());
    } catch (e, stack) {
      _logger.error('Failed to log request', error: e, stackTrace: stack);
    }
  }

  /// Log an HTTP response after it is received
  ///
  /// This captures details about the incoming response including status code,
  /// headers, and body content when available. Response body formatting
  /// is adjusted based on content type.
  ///
  /// Parameters:
  /// - [response] The HTTP response to log
  /// - [requestDuration] Optional duration of the request
  static void logResponse(http.BaseResponse response, {Duration? requestDuration}) {
    if (!_enabled) return;

    try {
      final responseLog = StringBuffer();
      final statusColor = _getStatusCodeColor(response.statusCode);
      
      responseLog.writeln('⬅️ RESPONSE: ${response.request?.method ?? "Unknown"} ${response.request?.url.toString() ?? "Unknown URL"}');
      responseLog.writeln('$statusColor STATUS: ${response.statusCode} ${response.reasonPhrase}');
      
      if (requestDuration != null) {
        responseLog.writeln('TIME: ${requestDuration.inMilliseconds}ms');
      }
      
      if (response.headers.isNotEmpty) {
        responseLog.writeln('HEADERS:');
        _logHeaders(responseLog, response.headers);
      }
      
      if (response is http.Response && response.body.isNotEmpty) {
        responseLog.writeln('BODY:');
        _logResponseBody(responseLog, response);
      }
      
      final logLevel = _getLogLevelForStatusCode(response.statusCode);
      switch (logLevel) {
        case 'info':
          _logger.info(responseLog.toString());
          break;
        case 'warning':
          _logger.warning(responseLog.toString());
          break;
        case 'error':
          _logger.error(responseLog.toString());
          break;
      }
    } catch (e, stack) {
      _logger.error('Failed to log response', error: e, stackTrace: stack);
    }
  }

  /// Log an HTTP error that occurred during the request
  ///
  /// This method is used when an exception is thrown during HTTP communication,
  /// capturing details about what went wrong.
  ///
  /// Parameters:
  /// - [error] The error that occurred
  /// - [stackTrace] The stack trace for the error
  /// - [url] The request URL that failed
  /// - [method] The HTTP method that was used
  static void logError(dynamic error, StackTrace stackTrace, Uri url, String method) {
    if (!_enabled) return;

    try {
      final errorLog = StringBuffer();
      errorLog.writeln('❌ ERROR: $method $url');
      errorLog.writeln(error.toString());
      
      _logger.error(errorLog.toString(), error: error, stackTrace: stackTrace);
    } catch (e, stack) {
      _logger.error('Failed to log network error', error: e, stackTrace: stack);
    }
  }

  /// Log HTTP request headers, redacting sensitive information
  ///
  /// Helper method to log headers while protecting sensitive data.
  ///
  /// Parameters:
  /// - [buffer] The string buffer to write to
  /// - [headers] The headers to log
  static void _logHeaders(StringBuffer buffer, Map<String, dynamic> headers) {
    headers.forEach((key, value) {
      final lowerKey = key.toLowerCase();
      if (_sensitiveHeaders.contains(lowerKey)) {
        buffer.writeln('  $key: [REDACTED]');
      } else {
        buffer.writeln('  $key: $value');
      }
    });
  }

  /// Log HTTP request body based on content type
  ///
  /// Helper method to format the request body based on content type.
  ///
  /// Parameters:
  /// - [buffer] The string buffer to write to
  /// - [body] The body content to log
  /// - [contentType] The content type header value
  static void _logBody(StringBuffer buffer, dynamic body, String? contentType) {
    // Handle different body types
    if (body is String) {
      if (_isJsonContentType(contentType) && _isJsonString(body)) {
        _logJsonBody(buffer, body);
      } else {
        buffer.writeln('  $body');
      }
    } else if (body is Map || body is List) {
      try {
        final jsonString = const JsonEncoder.withIndent('  ').convert(body);
        buffer.writeln('  $jsonString');
      } catch (e) {
        buffer.writeln('  $body');
      }
    } else if (body is List<int>) {
      buffer.writeln('  Binary data (${body.length} bytes)');
    } else {
      buffer.writeln('  $body');
    }
  }

  /// Log HTTP response body based on content type
  ///
  /// Helper method to format the response body based on content type,
  /// with special handling for JSON responses.
  ///
  /// Parameters:
  /// - [buffer] The string buffer to write to
  /// - [response] The HTTP response
  static void _logResponseBody(StringBuffer buffer, http.Response response) {
    final contentType = response.headers['content-type'];
    var body = response.body;
    
    // Handle truncation for large responses
    if (_truncateLargeResponses && body.length > _maxResponseSize) {
      final truncatedBody = body.substring(0, _maxResponseSize);
      body = '$truncatedBody... [truncated ${body.length - _maxResponseSize} more bytes]';
    }
    
    // Handle different content types
    if (_isJsonContentType(contentType) && _isJsonString(body)) {
      _logJsonBody(buffer, body);
    } else if (contentType?.contains('image/') == true || 
               contentType?.contains('application/octet-stream') == true ||
               contentType?.contains('application/pdf') == true) {
      buffer.writeln('  Binary data (${response.bodyBytes.length} bytes)');
    } else {
      buffer.writeln('  $body');
    }
  }

  /// Format and log JSON content with indentation
  ///
  /// Helper method to pretty-print JSON content.
  ///
  /// Parameters:
  /// - [buffer] The string buffer to write to
  /// - [jsonString] The JSON string to format and log
  static void _logJsonBody(StringBuffer buffer, String jsonString) {
    try {
      final dynamic jsonData = json.decode(jsonString);
      final prettyJson = const JsonEncoder.withIndent('  ').convert(jsonData);
      buffer.writeln('  $prettyJson');
    } catch (e) {
      // If we can't parse it as JSON, log it as-is
      buffer.writeln('  $jsonString');
    }
  }

  /// Check if a content type is JSON
  ///
  /// Helper method to identify JSON content types.
  ///
  /// Parameters:
  /// - [contentType] The content type header value
  /// - Returns true if the content is JSON
  static bool _isJsonContentType(String? contentType) {
    return contentType != null && 
           (contentType.contains('application/json') || 
            contentType.contains('application/javascript') ||
            contentType.contains('+json'));
  }

  /// Check if a string is valid JSON
  ///
  /// Helper method to identify if a string can be parsed as JSON.
  ///
  /// Parameters:
  /// - [str] The string to check
  /// - Returns true if the string is valid JSON
  static bool _isJsonString(String str) {
    try {
      final trimmed = str.trim();
      return (trimmed.startsWith('{') && trimmed.endsWith('}')) || 
             (trimmed.startsWith('[') && trimmed.endsWith(']'));
    } catch (e) {
      return false;
    }
  }

  /// Get the appropriate emoji for HTTP status code
  ///
  /// Helper method to determine the color/emoji indicator for a status code.
  ///
  /// Parameters:
  /// - [statusCode] The HTTP status code
  /// - Returns a colored indicator string for the status code
  static String _getStatusCodeColor(int statusCode) {
    if (statusCode >= 200 && statusCode < 300) {
      return '🟢'; // Success
    } else if (statusCode >= 300 && statusCode < 400) {
      return '🔵'; // Redirection
    } else if (statusCode >= 400 && statusCode < 500) {
      return '🟠'; // Client error
    } else if (statusCode >= 500) {
      return '🔴'; // Server error
    } else {
      return '⚪'; // Unknown/other
    }
  }

  /// Get the appropriate log level based on status code
  ///
  /// Helper method to determine which log level to use for a response.
  ///
  /// Parameters:
  /// - [statusCode] The HTTP status code
  /// - Returns a string representing the log level to use
  static String _getLogLevelForStatusCode(int statusCode) {
    if (statusCode >= 200 && statusCode < 400) {
      return 'info';
    } else if (statusCode >= 400 && statusCode < 500) {
      return 'warning';
    } else {
      return 'error';
    }
  }
}

/// HTTP client that logs all requests and responses
///
/// An implementation of http.Client that wraps another client and logs
/// all network activity before and after each request.
class _LoggingClient extends http.BaseClient {
  final http.Client _inner = http.Client();
  
  @override
  Future<http.StreamedResponse> send(http.BaseRequest request) async {
    // Log request
    NetworkLogger.logRequest(request);
    
    // Clone the request for sending
    final http.BaseRequest clonedRequest = _copyRequest(request);
    
    // Start timing
    final stopwatch = Stopwatch()..start();
    
    try {
      // Send the actual request
      final response = await _inner.send(clonedRequest);
      stopwatch.stop();
      
      try {
        // Log response
        final http.Response fullResponse = await http.Response.fromStream(response);
        NetworkLogger.logResponse(fullResponse, requestDuration: stopwatch.elapsed);
        
        // Return a new StreamedResponse with the same data
        return http.StreamedResponse(
          Stream.value(fullResponse.bodyBytes),
          fullResponse.statusCode,
          contentLength: fullResponse.contentLength,
          request: fullResponse.request,
          headers: fullResponse.headers,
          isRedirect: fullResponse.isRedirect,
          persistentConnection: fullResponse.persistentConnection,
          reasonPhrase: fullResponse.reasonPhrase,
        );
      } catch (e) {
        // If we fail to read the response, still log what we can
        NetworkLogger.logResponse(response, requestDuration: stopwatch.elapsed);
        rethrow;
      }
    } catch (e, stackTrace) {
      stopwatch.stop();
      // Log error
      NetworkLogger.logError(e, stackTrace, request.url, request.method);
      rethrow;
    }
  }
  
  @override
  void close() {
    _inner.close();
    super.close();
  }
  
  /// Copy a request so the original can still be read
  http.BaseRequest _copyRequest(http.BaseRequest request) {
    http.BaseRequest requestCopy;
    
    if (request is http.Request) {
      requestCopy = http.Request(request.method, request.url)
        ..encoding = request.encoding
        ..bodyBytes = request.bodyBytes;
    } else if (request is http.MultipartRequest) {
      requestCopy = http.MultipartRequest(request.method, request.url)
        ..fields.addAll(request.fields)
        ..files.addAll(request.files);
    } else {
      requestCopy = http.Request(request.method, request.url);
    }
    
    requestCopy.headers.addAll(request.headers);
    requestCopy.followRedirects = request.followRedirects;
    requestCopy.persistentConnection = request.persistentConnection;
    
    return requestCopy;
  }
} 