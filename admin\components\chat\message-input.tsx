'use client'

import { useState, useRef, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  Send,
  Mic,
  Image,
  X
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { VoiceRecorder } from './voice-recorder'
import { ImageUploader } from './image-uploader'
import { ChatMessage } from '@/lib/services/chat-service'

interface MessageInputProps {
  onSendMessage: (text: string) => void
  onSendVoiceMessage: (audioBlob: Blob, duration: number) => void
  onSendImageMessage: (images: File[], caption?: string) => void
  disabled?: boolean
  placeholder?: string
  replyTo?: ChatMessage | null
  onCancelReply?: () => void
  roomType?: 'query' | 'forum'
}

export function MessageInput({
  onSendMessage,
  onSendVoiceMessage,
  onSendImageMessage,
  disabled = false,
  placeholder = "Type a message...",
  replyTo,
  onCancelReply,
  roomType = 'forum'
}: MessageInputProps) {
  const [message, setMessage] = useState('')
  const [showVoiceRecorder, setShowVoiceRecorder] = useState(false)
  const [showImageUploader, setShowImageUploader] = useState(false)
  const [isRecording, setIsRecording] = useState(false)
  const [isTyping, setIsTyping] = useState(false)

  // Voice recording is disabled for queries
  const isVoiceEnabled = roomType === 'forum'

  const textareaRef = useRef<HTMLTextAreaElement>(null)
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto'
      textareaRef.current.style.height = `${Math.min(textareaRef.current.scrollHeight, 120)}px`
    }
  }, [message])

  // Handle typing indicator
  useEffect(() => {
    if (message.trim() && !isTyping) {
      setIsTyping(true)
    }

    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current)
    }

    typingTimeoutRef.current = setTimeout(() => {
      setIsTyping(false)
    }, 1000)

    return () => {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current)
      }
    }
  }, [message, isTyping])

  // Send text message
  const handleSendMessage = () => {
    const trimmedMessage = message.trim()
    if (!trimmedMessage || disabled) return

    onSendMessage(trimmedMessage)
    setMessage('')
    setIsTyping(false)
  }

  // Handle Enter key
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  // Handle voice recording
  const handleVoiceRecordingComplete = (audioBlob: Blob, duration: number) => {
    onSendVoiceMessage(audioBlob, duration)
    setShowVoiceRecorder(false)
    setIsRecording(false)
  }

  // Handle image upload
  const handleImageUpload = (images: File[], caption?: string) => {
    onSendImageMessage(images, caption)
    setShowImageUploader(false)
  }

  // Cancel voice recording
  const cancelVoiceRecording = () => {
    setShowVoiceRecorder(false)
    setIsRecording(false)
  }

  // Cancel image upload
  const cancelImageUpload = () => {
    setShowImageUploader(false)
  }

  return (
    <div className="border-t bg-white p-4 space-y-3">
      {/* Reply indicator */}
      <AnimatePresence>
        {replyTo && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="bg-gray-50 border-l-4 border-yellow-500 p-3 rounded"
          >
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="text-sm font-medium text-gray-700">
                  Replying to {replyTo.senderName}
                </div>
                <div className="text-sm text-gray-600 truncate">
                  {replyTo.type === 'text' && replyTo.text}
                  {replyTo.type === 'voice' && '🎵 Voice message'}
                  {replyTo.type === 'image' && '📷 Image'}
                </div>
              </div>
              {onCancelReply && (
                <Button
                  onClick={onCancelReply}
                  size="sm"
                  variant="ghost"
                >
                  <X className="w-4 h-4" />
                </Button>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Voice recorder - only for forums */}
      <AnimatePresence>
        {showVoiceRecorder && isVoiceEnabled && (
          <VoiceRecorder
            onRecordingComplete={handleVoiceRecordingComplete}
            onCancel={cancelVoiceRecording}
            isRecording={isRecording}
            onStartRecording={() => setIsRecording(true)}
            onStopRecording={() => setIsRecording(false)}
          />
        )}
      </AnimatePresence>

      {/* Image uploader */}
      <AnimatePresence>
        {showImageUploader && (
          <ImageUploader
            onImagesSelected={handleImageUpload}
            onCancel={cancelImageUpload}
          />
        )}
      </AnimatePresence>

      {/* Main input area */}
      {!showVoiceRecorder && !showImageUploader && (
        <div className="flex items-end space-x-3">
          {/* Attachment buttons */}
          <div className="flex space-x-1">
            <Button
              onClick={() => setShowImageUploader(true)}
              size="sm"
              variant="ghost"
              disabled={disabled}
              className="text-gray-500 hover:text-gray-700"
            >
              <Image className="w-5 h-5" />
            </Button>

            {/* Voice button - only for forums */}
            {isVoiceEnabled && (
              <Button
                onClick={() => setShowVoiceRecorder(true)}
                size="sm"
                variant="ghost"
                disabled={disabled}
                className="text-gray-500 hover:text-gray-700"
              >
                <Mic className="w-5 h-5" />
              </Button>
            )}
          </div>

          {/* Text input */}
          <div className="flex-1 relative">
            <textarea
              ref={textareaRef}
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder={placeholder}
              disabled={disabled}
              rows={1}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent"
              style={{ minHeight: '40px', maxHeight: '120px' }}
            />

            {/* Typing indicator */}
            {isTyping && (
              <div className="absolute -top-6 left-2 text-xs text-gray-500">
                Typing...
              </div>
            )}
          </div>

          {/* Send button */}
          <Button
            onClick={handleSendMessage}
            disabled={!message.trim() || disabled}
            size="sm"
            className="bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2"
          >
            <Send className="w-4 h-4" />
          </Button>
        </div>
      )}
    </div>
  )
}
