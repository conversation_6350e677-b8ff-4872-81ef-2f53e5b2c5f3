import 'package:cloud_firestore/cloud_firestore.dart';

class JobModel {
  final String jobId;
  final String posterId;
  final String posterName;
  final String title;
  final String type; // InDrive, Household, Company
  final String employmentType; // Full-time, Part-time
  final String salary;
  final String city;
  final String benefits; // Residency & food benefits
  final List<String> dutyHours;
  final DateTime createdAt;
  final String? status; // active, suspended, deleted

  JobModel({
    required this.jobId,
    required this.posterId,
    required this.posterName,
    required this.title,
    required this.type,
    required this.employmentType,
    required this.salary,
    required this.city,
    required this.benefits,
    required this.dutyHours,
    required this.createdAt,
    this.status,
  });

  factory JobModel.fromDocument(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;

    return JobModel(
      jobId: doc.id,
      posterId: data['posterId'] ?? '',
      posterName: data['posterName'] ?? 'Anonymous',
      title: data['title'] ?? 'Untitled Job',
      type: data['type'] ?? 'Other',
      employmentType: data['employmentType'] ?? 'Not specified',
      salary: data['salary'] ?? 'Not specified',
      city: data['city'] ?? 'Not specified',
      benefits: data['benefits'] ?? 'Not specified',
      dutyHours: List<String>.from(data['dutyHours'] ?? []),
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      status: data['status'] as String?,
    );
  }

  Map<String, dynamic> toMap() {
    final map = {
      'posterId': posterId,
      'posterName': posterName,
      'title': title,
      'type': type,
      'employmentType': employmentType,
      'salary': salary,
      'city': city,
      'benefits': benefits,
      'dutyHours': dutyHours,
      'createdAt': Timestamp.fromDate(createdAt),
    };

    // Only include status if it's not null
    if (status != null) {
      map['status'] = status!;
    }

    return map;
  }

  JobModel copyWith({
    String? jobId,
    String? posterId,
    String? posterName,
    String? title,
    String? type,
    String? employmentType,
    String? salary,
    String? city,
    String? benefits,
    List<String>? dutyHours,
    DateTime? createdAt,
    String? status,
  }) {
    return JobModel(
      jobId: jobId ?? this.jobId,
      posterId: posterId ?? this.posterId,
      posterName: posterName ?? this.posterName,
      title: title ?? this.title,
      type: type ?? this.type,
      employmentType: employmentType ?? this.employmentType,
      salary: salary ?? this.salary,
      city: city ?? this.city,
      benefits: benefits ?? this.benefits,
      dutyHours: dutyHours ?? this.dutyHours,
      createdAt: createdAt ?? this.createdAt,
      status: status ?? this.status,
    );
  }
}