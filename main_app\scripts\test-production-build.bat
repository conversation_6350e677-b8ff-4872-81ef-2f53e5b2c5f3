@echo off
REM Test Production Build for Google Play Store
REM This script validates that production builds can be generated successfully

setlocal enabledelayedexpansion

echo 🧪 Drive-On Production Build Test
echo =================================

REM Check prerequisites
echo 🔍 Checking prerequisites...

REM Check Flutter
where flutter >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ Error: Flutter not found in PATH
    echo Please install Flutter and ensure it's in your PATH
    pause
    exit /b 1
)

echo ✅ Flutter found
flutter --version

REM Check Flutter doctor
echo.
echo 🩺 Running Flutter doctor...
flutter doctor
if %errorlevel% neq 0 (
    echo ⚠️  Warning: Flutter doctor found issues
    echo Please resolve any critical issues before proceeding
    set /p CONTINUE="Continue anyway? (y/N): "
    if /i not "!CONTINUE!"=="y" (
        pause
        exit /b 1
    )
)

echo.
echo 🔧 Testing build configuration...

REM Test debug build first
echo 📱 Testing debug build...
flutter build apk --debug
if %errorlevel% neq 0 (
    echo ❌ Error: Debug build failed
    echo Please fix build issues before proceeding
    pause
    exit /b 1
)
echo ✅ Debug build successful

REM Clean for release build
echo 🧹 Cleaning for release build...
flutter clean
flutter pub get

REM Test if keystore exists
if exist "android\key.properties" (
    echo ✅ Production keystore configuration found
    
    REM Test release build with keystore
    echo 📱 Testing release build with production keystore...
    flutter build apk --release --dart-define=ENVIRONMENT=production
    if %errorlevel% neq 0 (
        echo ❌ Error: Release build with keystore failed
        echo Check keystore configuration and passwords
        pause
        exit /b 1
    )
    echo ✅ Release build with keystore successful
    
    REM Test app bundle build
    echo 📦 Testing App Bundle build...
    flutter build appbundle --release --dart-define=ENVIRONMENT=production
    if %errorlevel% neq 0 (
        echo ❌ Error: App Bundle build failed
        pause
        exit /b 1
    )
    echo ✅ App Bundle build successful
    
) else (
    echo ⚠️  Production keystore not found
    echo Testing release build with debug keystore...
    
    flutter build apk --release --dart-define=ENVIRONMENT=production
    if %errorlevel% neq 0 (
        echo ❌ Error: Release build failed
        pause
        exit /b 1
    )
    echo ✅ Release build with debug keystore successful
    echo.
    echo 🔑 To enable production signing:
    echo 1. Run: scripts\generate-keystore.bat
    echo 2. Re-run this test script
)

echo.
echo 🔍 Analyzing build outputs...

REM Check APK
if exist "build\app\outputs\flutter-apk\app-release.apk" (
    echo ✅ APK generated successfully
    for %%A in ("build\app\outputs\flutter-apk\app-release.apk") do (
        echo    Size: %%~zA bytes
        set /a size_mb=%%~zA/1024/1024
        echo    Size: !size_mb! MB
        
        if !size_mb! gtr 100 (
            echo ⚠️  Warning: APK size is quite large (>100MB)
            echo Consider optimizing assets and dependencies
        )
    )
) else (
    echo ❌ APK not found
)

REM Check App Bundle
if exist "build\app\outputs\bundle\release\app-release.aab" (
    echo ✅ App Bundle generated successfully
    for %%A in ("build\app\outputs\bundle\release\app-release.aab") do (
        echo    Size: %%~zA bytes
        set /a size_mb=%%~zA/1024/1024
        echo    Size: !size_mb! MB
    )
) else (
    echo ❌ App Bundle not found
)

REM Check ProGuard mapping
if exist "build\app\outputs\mapping\release\mapping.txt" (
    echo ✅ ProGuard mapping file generated
) else (
    echo ⚠️  ProGuard mapping file not found
    echo This is needed for crash reporting in production
)

echo.
echo 📋 BUILD VALIDATION SUMMARY:
echo ============================

set VALIDATION_PASSED=true

REM Validate APK
if exist "build\app\outputs\flutter-apk\app-release.apk" (
    echo ✅ APK Build: PASSED
) else (
    echo ❌ APK Build: FAILED
    set VALIDATION_PASSED=false
)

REM Validate App Bundle
if exist "build\app\outputs\bundle\release\app-release.aab" (
    echo ✅ App Bundle Build: PASSED
) else (
    echo ❌ App Bundle Build: FAILED
    set VALIDATION_PASSED=false
)

REM Validate Keystore
if exist "android\key.properties" (
    echo ✅ Production Keystore: CONFIGURED
) else (
    echo ⚠️  Production Keystore: NOT CONFIGURED
    echo    This is required for Play Store deployment
)

REM Validate Store Assets
if exist "fastlane\metadata\android\en-US\title.txt" (
    echo ✅ Store Metadata: FOUND
) else (
    echo ⚠️  Store Metadata: MISSING
    echo    Run: scripts\prepare-play-store-assets.bat
)

echo.
if "%VALIDATION_PASSED%"=="true" (
    echo 🎉 PRODUCTION BUILD TEST PASSED!
    echo ================================
    echo.
    echo Your app is ready for Google Play Store deployment!
    echo.
    echo 📋 NEXT STEPS:
    echo 1. Generate production keystore (if not done): scripts\generate-keystore.bat
    echo 2. Prepare store assets: scripts\prepare-play-store-assets.bat
    echo 3. Build for production: scripts\build-production.bat
    echo 4. Follow Play Store setup guide: play-store-setup-guide.md
) else (
    echo ❌ PRODUCTION BUILD TEST FAILED!
    echo ================================
    echo.
    echo Please resolve the issues above before proceeding with deployment.
    echo.
    echo 🔧 TROUBLESHOOTING:
    echo - Check Flutter doctor output for environment issues
    echo - Ensure all dependencies are properly installed
    echo - Verify Android SDK and build tools are up to date
    echo - Check for any compilation errors in the output above
)

echo.
pause
