'use client'

import { useState, memo } from 'react'
import { usePathname } from 'next/navigation'
import {
  LayoutDashboard,
  Users,
  Car,
  Building2,
  Briefcase,
  Newspaper,
  MessageSquare,
  HelpCircle,
  Settings,
  Shield,
  ChevronLeft,
  ChevronRight,
  Bell,
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { useAuth } from '@/lib/stores/auth-store'
import { useNavigationOptimization } from '@/lib/hooks/use-navigation-optimization'

const navigation = [
  {
    name: 'Dashboard',
    href: '/dashboard',
    icon: LayoutDashboard,
    description: 'Overview and analytics'
  },
  {
    name: 'Users',
    href: '/users',
    icon: Users,
    description: 'Manage app users'
  },
  {
    name: 'Drivers',
    href: '/drivers',
    icon: Car,
    description: 'Driver applications & verification'
  },
  {
    name: 'Partners',
    href: '/partners',
    icon: Building2,
    description: 'Partner management'
  },
  {
    name: 'Jobs',
    href: '/jobs',
    icon: Briefcase,
    description: 'Job postings & management'
  },
  {
    name: 'News',
    href: '/news',
    icon: Newspaper,
    description: 'News articles & updates'
  },
  {
    name: 'Forums',
    href: '/forums',
    icon: MessageSquare,
    description: 'Community forums'
  },
  {
    name: 'Queries',
    href: '/queries',
    icon: HelpCircle,
    description: 'User support queries'
  },

  {
    name: 'Notifications',
    href: '/notifications',
    icon: Bell,
    description: 'System notifications'
  },
  {
    name: 'Settings',
    href: '/settings',
    icon: Settings,
    description: 'System settings'
  },
]

function SidebarComponent() {
  const [collapsed, setCollapsed] = useState(false)
  const pathname = usePathname()
  const { user } = useAuth()
  const { optimizedNavigate } = useNavigationOptimization()

  return (
    <div
      className={`fixed inset-y-0 left-0 z-50 bg-background shadow-lg border-r border-border transition-all duration-200 ease-in-out ${
        collapsed ? 'w-20' : 'w-64'
      }`}
    >
      <div className="flex h-full flex-col">
        {/* Logo and Toggle */}
        <div className="flex items-center justify-between p-4 border-b border-border">
          <div className="flex items-center space-x-3">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-gradient-primary rounded-lg flex items-center justify-center">
                <Shield className="w-5 h-5 text-white" />
              </div>
            </div>
            {!collapsed && (
              <div className="transition-opacity duration-200">
                <h1 className="text-dynamic-lg font-semibold text-foreground">Drive-On</h1>
                <p className="text-dynamic-sm text-muted-foreground">Admin Panel</p>
              </div>
            )}
          </div>

          <button
            onClick={() => setCollapsed(!collapsed)}
            className="p-1.5 rounded-lg hover:bg-accent transition-colors"
          >
            {collapsed ? (
              <ChevronRight className="w-4 h-4 text-muted-foreground" />
            ) : (
              <ChevronLeft className="w-4 h-4 text-muted-foreground" />
            )}
          </button>
        </div>

        {/* Navigation */}
        <nav className="flex-1 px-3 py-4 space-y-1 overflow-y-auto custom-scrollbar">
          {navigation.map((item) => {
            const isActive = pathname === item.href || pathname.startsWith(item.href + '/')

            return (
              <button
                key={item.name}
                onClick={() => optimizedNavigate(item.href)}
                className={cn(
                  'group flex items-center px-3 py-2.5 text-dynamic-sm font-medium rounded-lg transition-all duration-100 w-full text-left relative',
                  isActive
                    ? 'bg-primary/10 text-primary border-l-4 border-primary'
                    : 'text-foreground hover:bg-primary/5 hover:text-primary hover:border-l-4 hover:border-primary/30 border-l-4 border-transparent'
                )}
              >
                <item.icon
                  className={cn(
                    'flex-shrink-0 w-5 h-5',
                    collapsed ? 'mx-auto' : 'mr-3',
                    isActive ? 'text-primary' : 'text-muted-foreground group-hover:text-primary'
                  )}
                />

                {!collapsed && (
                  <div className="flex-1 min-w-0 transition-opacity duration-200">
                    <p className={cn(
                      'truncate',
                      isActive ? 'text-primary font-semibold' : 'text-foreground group-hover:text-primary'
                    )}>
                      {item.name}
                    </p>
                    <p className={cn(
                      'text-dynamic-sm truncate',
                      isActive ? 'text-primary/70' : 'text-muted-foreground group-hover:text-primary/70'
                    )}>
                      {item.description}
                    </p>
                  </div>
                )}
              </button>
            )
          })}
        </nav>

        {/* User Info */}
        <div className="border-t border-border p-4">
          <div className="flex items-center space-x-3">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center">
                <span className="text-sm font-medium text-primary-700 dark:text-primary-300">
                  {user?.displayName?.charAt(0) || user?.email?.charAt(0) || 'A'}
                </span>
              </div>
            </div>
            {!collapsed && (
              <div className="flex-1 min-w-0 transition-opacity duration-200">
                <p className="text-dynamic-sm font-medium text-foreground truncate">
                  {user?.displayName || 'Admin User'}
                </p>
                <p className="text-dynamic-sm text-muted-foreground truncate">
                  {user?.email}
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

// Memoize the sidebar to prevent unnecessary re-renders
export const Sidebar = memo(SidebarComponent)
