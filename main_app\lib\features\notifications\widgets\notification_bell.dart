import 'package:flutter/material.dart';
import '../services/notification_history_service.dart';
import '../screens/notifications_screen.dart';
import '../../../core/theme/colors.dart';

class NotificationBell extends StatefulWidget {
  final Color? iconColor;
  final double? iconSize;

  const NotificationBell({
    Key? key,
    this.iconColor,
    this.iconSize = 24,
  }) : super(key: key);

  @override
  State<NotificationBell> createState() => _NotificationBellState();
}

class _NotificationBellState extends State<NotificationBell>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _shakeAnimation;
  final NotificationHistoryService _notificationService = NotificationHistoryService.instance;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    _shakeAnimation = Tween<double>(
      begin: 0,
      end: 1,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _triggerShakeAnimation() {
    _animationController.forward().then((_) {
      _animationController.reset();
    });
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final iconColor = widget.iconColor ??
        (isDarkMode ? AppColors.darkText : AppColors.lightText);

    return StreamBuilder<int>(
      stream: _notificationService.getUnreadCount(),
      builder: (context, snapshot) {
        final unreadCount = snapshot.data ?? 0;

        // Trigger shake animation when unread count increases
        if (unreadCount > 0) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            _triggerShakeAnimation();
          });
        }

        return Stack(
          children: [
            // Bell icon with shake animation
            AnimatedBuilder(
              animation: _shakeAnimation,
              builder: (context, child) {
                return Transform.rotate(
                  angle: _shakeAnimation.value * 0.3 *
                      (unreadCount > 0 ? 1 : 0),
                  child: IconButton(
                    onPressed: () => _navigateToNotifications(context),
                    icon: Icon(
                      unreadCount > 0
                          ? Icons.notifications_active
                          : Icons.notifications_outlined,
                      color: iconColor,
                      size: widget.iconSize,
                    ),
                    tooltip: 'Notifications',
                  ),
                );
              },
            ),

            // Unread count badge
            if (unreadCount > 0)
              Positioned(
                right: 8,
                top: 8,
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 300),
                  curve: Curves.elasticOut,
                  padding: const EdgeInsets.all(4),
                  constraints: const BoxConstraints(
                    minWidth: 18,
                    minHeight: 18,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.red,
                    borderRadius: BorderRadius.circular(10),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.red.withOpacity(0.3),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Text(
                    unreadCount > 99 ? '99+' : unreadCount.toString(),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
          ],
        );
      },
    );
  }

  void _navigateToNotifications(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const NotificationsScreen(),
      ),
    );
  }
}

/// Simplified notification bell for use in app bars
class SimpleNotificationBell extends StatelessWidget {
  final Color? iconColor;
  final double? iconSize;

  const SimpleNotificationBell({
    Key? key,
    this.iconColor,
    this.iconSize = 24,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<int>(
      stream: NotificationHistoryService.instance.getUnreadCount(),
      builder: (context, snapshot) {
        final unreadCount = snapshot.data ?? 0;

        return Badge(
          isLabelVisible: unreadCount > 0,
          label: Text(
            unreadCount > 99 ? '99+' : unreadCount.toString(),
            style: const TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.bold,
            ),
          ),
          backgroundColor: Colors.red,
          child: IconButton(
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const NotificationsScreen(),
                ),
              );
            },
            icon: Icon(
              unreadCount > 0
                  ? Icons.notifications_active
                  : Icons.notifications_outlined,
              color: iconColor,
              size: iconSize,
            ),
            tooltip: 'Notifications',
          ),
        );
      },
    );
  }
}

/// Notification bell with dropdown preview
class NotificationBellWithDropdown extends StatefulWidget {
  final Color? iconColor;
  final double? iconSize;

  const NotificationBellWithDropdown({
    Key? key,
    this.iconColor,
    this.iconSize = 24,
  }) : super(key: key);

  @override
  State<NotificationBellWithDropdown> createState() => _NotificationBellWithDropdownState();
}

class _NotificationBellWithDropdownState extends State<NotificationBellWithDropdown> {
  @override
  Widget build(BuildContext context) {
    return PopupMenuButton<String>(
      icon: StreamBuilder<int>(
        stream: NotificationHistoryService.instance.getUnreadCount(),
        builder: (context, snapshot) {
          final unreadCount = snapshot.data ?? 0;

          return Badge(
            isLabelVisible: unreadCount > 0,
            label: Text(
              unreadCount > 99 ? '99+' : unreadCount.toString(),
              style: const TextStyle(
                fontSize: 10,
                fontWeight: FontWeight.bold,
              ),
            ),
            backgroundColor: Colors.red,
            child: Icon(
              unreadCount > 0
                  ? Icons.notifications_active
                  : Icons.notifications_outlined,
              color: widget.iconColor,
              size: widget.iconSize,
            ),
          );
        },
      ),
      onSelected: (value) {
        if (value == 'view_all') {
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => const NotificationsScreen(),
            ),
          );
        }
      },
      itemBuilder: (context) => [
        const PopupMenuItem(
          value: 'view_all',
          child: Row(
            children: [
              Icon(Icons.list),
              SizedBox(width: 8),
              Text('View All Notifications'),
            ],
          ),
        ),
      ],
      tooltip: 'Notifications',
    );
  }
}
