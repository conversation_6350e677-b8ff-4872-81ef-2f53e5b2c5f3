import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:sqflite/sqflite.dart';
import '../models/job_model.dart';
import '../../../core/services/enhanced_cache_service.dart';
import '../../../core/services/offline_storage_service.dart';
import '../../../core/utils/app_logger.dart';

/// Offline-first repository for jobs data
/// 
/// Features:
/// - Offline-first data access
/// - Automatic synchronization
/// - Conflict resolution
/// - Background sync
class OfflineJobsRepository {
  static final OfflineJobsRepository _instance = OfflineJobsRepository._internal();
  factory OfflineJobsRepository() => _instance;
  OfflineJobsRepository._internal();

  static const String _tag = 'OfflineJobsRepository';
  static final AppLogger _logger = AppLogger(_tag);

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final EnhancedCacheService _enhancedCache = EnhancedCacheService();
  final OfflineStorageService _offlineStorage = OfflineStorageService();

  // Collection reference
  CollectionReference<Map<String, dynamic>> get _jobsCollection =>
      _firestore.collection('jobs');

  /// Initialize the repository
  Future<void> initialize() async {
    await _enhancedCache.initialize();
  }

  /// Get jobs with offline-first approach
  Future<List<JobModel>> getJobs({
    int limit = 20,
    String? category,
    String? employmentType,
    String? location,
  }) async {
    final cacheKey = 'jobs_${category ?? 'all'}_${employmentType ?? 'all'}_${location ?? 'all'}_$limit';
    
    return await _enhancedCache.get<List<JobModel>>(
      key: cacheKey,
      fetcher: () => _fetchJobsFromFirestore(
        limit: limit,
        category: category,
        employmentType: employmentType,
        location: location,
      ),
      memoryTtl: const Duration(minutes: 10),
      hiveTtl: const Duration(hours: 2),
      sqliteTtl: const Duration(days: 1),
    ) ?? [];
  }

  /// Get jobs with offline storage fallback
  Future<List<JobModel>> getJobsOfflineFirst({
    int limit = 20,
    String? category,
    String? employmentType,
    String? location,
  }) async {
    try {
      // First try to get from offline storage
      final offlineJobs = await _getJobsFromOfflineStorage(
        limit: limit,
        category: category,
        employmentType: employmentType,
        location: location,
      );
      
      // If we have offline data and we're offline, return it
      if (offlineJobs.isNotEmpty && !_offlineStorage.isOnline) {
        _logger.info('Returning ${offlineJobs.length} jobs from offline storage');
        return offlineJobs;
      }
      
      // If we're online, try to fetch fresh data
      if (_offlineStorage.isOnline) {
        try {
          final freshJobs = await _fetchJobsFromFirestore(
            limit: limit,
            category: category,
            employmentType: employmentType,
            location: location,
          );
          if (freshJobs.isNotEmpty) {
            // Save to offline storage
            await _saveJobsToOfflineStorage(freshJobs);
            return freshJobs;
          }
        } catch (e) {
          _logger.warning('Failed to fetch fresh jobs, falling back to offline data', error: e);
        }
      }
      
      // Return offline data as fallback
      return offlineJobs;
    } catch (e) {
      _logger.error('Error getting jobs', error: e);
      return [];
    }
  }

  /// Fetch jobs from Firestore
  Future<List<JobModel>> _fetchJobsFromFirestore({
    int limit = 20,
    String? category,
    String? employmentType,
    String? location,
  }) async {
    try {
      Query<Map<String, dynamic>> query = _jobsCollection
          .where('status', isEqualTo: 'active')
          .orderBy('createdAt', descending: true);

      if (category != null && category.isNotEmpty && category != 'All') {
        query = query.where('type', isEqualTo: category);
      }

      if (employmentType != null && employmentType.isNotEmpty && employmentType != 'All') {
        query = query.where('employmentType', isEqualTo: employmentType);
      }

      if (location != null && location.isNotEmpty && location != 'All') {
        query = query.where('city', isEqualTo: location);
      }

      query = query.limit(limit);

      final QuerySnapshot<Map<String, dynamic>> snapshot = await query.get();
      return snapshot.docs.map((doc) => JobModel.fromDocument(doc)).toList();
    } catch (e) {
      _logger.error('Error fetching jobs from Firestore', error: e);
      return [];
    }
  }

  /// Get jobs from offline storage (SQLite)
  Future<List<JobModel>> _getJobsFromOfflineStorage({
    int limit = 20,
    String? category,
    String? employmentType,
    String? location,
  }) async {
    try {
      final db = _offlineStorage.database;
      if (db == null) return [];

      String whereClause = 'status = ?';
      List<dynamic> whereArgs = ['active'];

      if (category != null && category.isNotEmpty && category != 'All') {
        whereClause += ' AND type = ?';
        whereArgs.add(category);
      }

      if (employmentType != null && employmentType.isNotEmpty && employmentType != 'All') {
        whereClause += ' AND employment_type = ?';
        whereArgs.add(employmentType);
      }

      if (location != null && location.isNotEmpty && location != 'All') {
        whereClause += ' AND city = ?';
        whereArgs.add(location);
      }

      final List<Map<String, dynamic>> maps = await db.query(
        'jobs',
        where: whereClause,
        whereArgs: whereArgs,
        orderBy: 'created_at DESC',
        limit: limit,
      );

      return maps.map((map) => _jobModelFromMap(map)).toList();
    } catch (e) {
      _logger.error('Error getting jobs from offline storage', error: e);
      return [];
    }
  }

  /// Save jobs to offline storage (SQLite)
  Future<void> _saveJobsToOfflineStorage(List<JobModel> jobs) async {
    try {
      final db = _offlineStorage.database;
      if (db == null) return;

      final batch = db.batch();

      for (final job in jobs) {
        batch.insert(
          'jobs',
          _jobModelToMap(job),
          conflictAlgorithm: ConflictAlgorithm.replace,
        );
      }

      await batch.commit();
      _logger.debug('Saved ${jobs.length} jobs to offline storage');
    } catch (e) {
      _logger.error('Error saving jobs to offline storage', error: e);
    }
  }

  /// Convert JobModel to Map for SQLite storage
  Map<String, dynamic> _jobModelToMap(JobModel job) {
    return {
      'id': job.jobId,
      'title': job.title,
      'poster_id': job.posterId,
      'poster_name': job.posterName,
      'type': job.type,
      'employment_type': job.employmentType,
      'salary': job.salary,
      'city': job.city,
      'benefits': job.benefits,
      'duty_hours': job.dutyHours.join(','), // Store as comma-separated string
      'status': job.status,
      'created_at': job.createdAt.millisecondsSinceEpoch,
      'updated_at': DateTime.now().millisecondsSinceEpoch,
      'sync_status': 'synced',
    };
  }

  /// Convert Map from SQLite to JobModel
  JobModel _jobModelFromMap(Map<String, dynamic> map) {
    return JobModel(
      jobId: map['id'] ?? '',
      posterId: map['poster_id'] ?? '',
      posterName: map['poster_name'] ?? '',
      title: map['title'] ?? '',
      type: map['type'] ?? '',
      employmentType: map['employment_type'] ?? '',
      salary: map['salary'] ?? '',
      city: map['city'] ?? '',
      benefits: map['benefits'] ?? '',
      dutyHours: (map['duty_hours'] as String? ?? '').split(',').where((s) => s.isNotEmpty).toList(),
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['created_at'] ?? 0),
      status: map['status'] ?? 'active',
    );
  }

  /// Get job categories from offline storage
  Future<List<String>> getJobCategories() async {
    try {
      final db = _offlineStorage.database;
      if (db == null) return [];

      final List<Map<String, dynamic>> maps = await db.rawQuery(
        'SELECT DISTINCT type FROM jobs WHERE type IS NOT NULL AND type != ""'
      );

      return maps.map((map) => map['type'] as String).toList();
    } catch (e) {
      _logger.error('Error getting job categories from offline storage', error: e);
      return [];
    }
  }

  /// Get employment types from offline storage
  Future<List<String>> getEmploymentTypes() async {
    try {
      final db = _offlineStorage.database;
      if (db == null) return [];

      final List<Map<String, dynamic>> maps = await db.rawQuery(
        'SELECT DISTINCT employment_type FROM jobs WHERE employment_type IS NOT NULL AND employment_type != ""'
      );

      return maps.map((map) => map['employment_type'] as String).toList();
    } catch (e) {
      _logger.error('Error getting employment types from offline storage', error: e);
      return [];
    }
  }

  /// Clear offline jobs data
  Future<void> clearOfflineData() async {
    try {
      final db = _offlineStorage.database;
      if (db != null) {
        await db.delete('jobs');
      }
      _logger.info('Cleared offline jobs data');
    } catch (e) {
      _logger.error('Error clearing offline jobs data', error: e);
    }
  }

  /// Get offline data statistics
  Future<Map<String, dynamic>> getOfflineStats() async {
    try {
      final db = _offlineStorage.database;
      if (db == null) return {'jobs': 0, 'categories': 0};

      final jobCount = Sqflite.firstIntValue(
        await db.rawQuery('SELECT COUNT(*) FROM jobs')
      ) ?? 0;

      final categoryCount = Sqflite.firstIntValue(
        await db.rawQuery('SELECT COUNT(DISTINCT type) FROM jobs WHERE type IS NOT NULL')
      ) ?? 0;

      return {
        'jobs': jobCount,
        'categories': categoryCount,
        'cacheStats': _enhancedCache.getStats(),
      };
    } catch (e) {
      _logger.error('Error getting offline stats', error: e);
      return {'jobs': 0, 'categories': 0};
    }
  }
}
