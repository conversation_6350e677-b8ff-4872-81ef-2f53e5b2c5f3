import { useState, useEffect, useCallback } from 'react'
import {
  notificationService,
  AdminNotification,
  NotificationSettings
} from '@/lib/services/notification-service'

export function useNotifications() {
  const [notifications, setNotifications] = useState<AdminNotification[]>([])
  const [unreadCount, setUnreadCount] = useState(0)
  const [isLoading, setIsLoading] = useState(true)
  const [settings, setSettings] = useState<NotificationSettings>(notificationService.getSettings())

  useEffect(() => {
    // Initialize notification service
    notificationService.initialize().then(() => {
      setIsLoading(false)
    })

    // Subscribe to notifications
    const unsubscribeNotifications = notificationService.subscribe((newNotifications) => {
      setNotifications(newNotifications)
    })

    // Subscribe to unread count
    const unsubscribeUnreadCount = notificationService.subscribeToUnreadCount((count) => {
      setUnreadCount(count)
    })

    return () => {
      unsubscribeNotifications()
      unsubscribeUnreadCount()
    }
  }, [])

  const markAsRead = useCallback(async (notificationId: string) => {
    await notificationService.markAsRead(notificationId)
  }, [])

  const markAllAsRead = useCallback(async () => {
    await notificationService.markAllAsRead()
  }, [])

  const updateSettings = useCallback(async (newSettings: Partial<NotificationSettings>) => {
    await notificationService.updateSettings(newSettings)
    setSettings(notificationService.getSettings())
  }, [])

  const sendTestNotification = useCallback(async () => {
    await notificationService.sendTestNotification()
  }, [])

  const getNotificationsByType = useCallback((type: AdminNotification['type']) => {
    return notifications.filter(notification => notification.type === type)
  }, [notifications])

  const getUnreadNotifications = useCallback(() => {
    return notifications.filter(notification => !notification.isRead)
  }, [notifications])

  const getNotificationsByPriority = useCallback((priority: AdminNotification['priority']) => {
    return notifications.filter(notification => notification.priority === priority)
  }, [notifications])

  const getRecentNotifications = useCallback((hours: number = 24) => {
    const cutoff = new Date(Date.now() - hours * 60 * 60 * 1000)
    return notifications.filter(notification => notification.createdAt > cutoff)
  }, [notifications])

  return {
    notifications,
    unreadCount,
    isLoading,
    settings,
    markAsRead,
    markAllAsRead,
    updateSettings,
    sendTestNotification,
    getNotificationsByType,
    getUnreadNotifications,
    getNotificationsByPriority,
    getRecentNotifications
  }
}

export function useNotificationSettings() {
  const [settings, setSettings] = useState<NotificationSettings>(notificationService.getSettings())
  const [isLoading, setIsLoading] = useState(false)

  const updateSettings = useCallback(async (newSettings: Partial<NotificationSettings>) => {
    setIsLoading(true)
    try {
      await notificationService.updateSettings(newSettings)
      setSettings(notificationService.getSettings())
    } catch (error) {
      console.error('Error updating notification settings:', error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }, [])

  const resetToDefaults = useCallback(async () => {
    const defaultSettings: NotificationSettings = {
      soundEnabled: true,
      browserNotifications: true,
      emailNotifications: true,
      driverRequestAlerts: true,
      partnerRequestAlerts: true,
      forumMessageAlerts: true,
      queryAlerts: true,
      userRegistrationAlerts: true,
      systemAlerts: true,
      urgentAlertsOnly: false,
      quietHoursEnabled: false,
      quietHoursStart: '22:00',
      quietHoursEnd: '08:00'
    }
    await updateSettings(defaultSettings)
  }, [updateSettings])

  const testNotification = useCallback(async () => {
    await notificationService.sendTestNotification()
  }, [])

  return {
    settings,
    isLoading,
    updateSettings,
    resetToDefaults,
    testNotification
  }
}
