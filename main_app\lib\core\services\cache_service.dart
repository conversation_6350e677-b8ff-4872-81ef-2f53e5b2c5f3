import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../utils/app_logger.dart';

/// A high-performance caching service with memory and disk caching
/// 
/// Features:
/// - In-memory cache for frequently accessed data
/// - Disk cache for persistent storage
/// - TTL (Time To Live) support
/// - Size-based eviction
/// - Background cache cleanup
class CacheService {
  static final CacheService _instance = CacheService._internal();
  factory CacheService() => _instance;
  CacheService._internal();

  static const String _tag = 'CacheService';
  static final AppLogger _logger = AppLogger(_tag);

  // Memory cache with TTL
  final Map<String, _CacheEntry> _memoryCache = {};
  
  // Cache configuration
  static const int _maxMemoryCacheSize = 100; // Max items in memory
  static const int _maxMemoryCacheBytes = 10 * 1024 * 1024; // 10MB
  static const Duration _defaultTtl = Duration(minutes: 30);
  static const Duration _diskCacheTtl = Duration(hours: 24);

  int _currentMemoryBytes = 0;
  SharedPreferences? _prefs;

  /// Initialize the cache service
  Future<void> initialize() async {
    try {
      _prefs = await SharedPreferences.getInstance();
      _cleanupExpiredMemoryCache();
      _cleanupExpiredDiskCache();
      _logger.info('Cache service initialized');
    } catch (e) {
      _logger.error('Failed to initialize cache service', error: e);
    }
  }

  /// Get data from cache or fetch using the provided function
  Future<T?> get<T>({
    required String key,
    required Future<T> Function() fetcher,
    Duration? ttl,
    bool useMemoryCache = true,
    bool useDiskCache = true,
  }) async {
    ttl ??= _defaultTtl;

    // Try memory cache first
    if (useMemoryCache) {
      final memoryResult = _getFromMemoryCache<T>(key);
      if (memoryResult != null) {
        _logger.debug('Cache hit (memory): $key');
        return memoryResult;
      }
    }

    // Try disk cache
    if (useDiskCache) {
      final diskResult = await _getFromDiskCache<T>(key);
      if (diskResult != null) {
        _logger.debug('Cache hit (disk): $key');
        // Store in memory cache for faster access
        if (useMemoryCache) {
          _putInMemoryCache(key, diskResult, ttl);
        }
        return diskResult;
      }
    }

    // Cache miss - fetch data
    _logger.debug('Cache miss: $key');
    try {
      final data = await fetcher();
      
      // Store in caches
      if (useMemoryCache) {
        _putInMemoryCache(key, data, ttl);
      }
      if (useDiskCache) {
        await _putInDiskCache(key, data, _diskCacheTtl);
      }
      
      return data;
    } catch (e) {
      _logger.error('Failed to fetch data for key: $key', error: e);
      rethrow;
    }
  }

  /// Put data directly into cache
  Future<void> put<T>({
    required String key,
    required T data,
    Duration? ttl,
    bool useMemoryCache = true,
    bool useDiskCache = true,
  }) async {
    ttl ??= _defaultTtl;

    if (useMemoryCache) {
      _putInMemoryCache(key, data, ttl);
    }
    if (useDiskCache) {
      await _putInDiskCache(key, data, _diskCacheTtl);
    }
  }

  /// Remove data from cache
  Future<void> remove(String key) async {
    _memoryCache.remove(key);
    await _prefs?.remove('cache_$key');
    await _prefs?.remove('cache_ttl_$key');
  }

  /// Clear all cache
  Future<void> clear() async {
    _memoryCache.clear();
    _currentMemoryBytes = 0;
    
    final keys = _prefs?.getKeys().where((key) => key.startsWith('cache_')).toList() ?? [];
    for (final key in keys) {
      await _prefs?.remove(key);
    }
  }

  /// Get cache statistics
  Map<String, dynamic> getStats() {
    return {
      'memoryItems': _memoryCache.length,
      'memoryBytes': _currentMemoryBytes,
      'maxMemoryItems': _maxMemoryCacheSize,
      'maxMemoryBytes': _maxMemoryCacheBytes,
    };
  }

  // Memory cache operations
  T? _getFromMemoryCache<T>(String key) {
    final entry = _memoryCache[key];
    if (entry == null) return null;
    
    if (entry.isExpired) {
      _memoryCache.remove(key);
      _currentMemoryBytes -= entry.sizeBytes;
      return null;
    }
    
    return entry.data as T?;
  }

  void _putInMemoryCache<T>(String key, T data, Duration ttl) {
    try {
      final serialized = _serializeData(data);
      final sizeBytes = serialized.length;
      
      // Check if data is too large
      if (sizeBytes > _maxMemoryCacheBytes) {
        _logger.warning('Data too large for memory cache: $key ($sizeBytes bytes)');
        return;
      }
      
      // Remove existing entry if present
      final existing = _memoryCache[key];
      if (existing != null) {
        _currentMemoryBytes -= existing.sizeBytes;
      }
      
      // Evict items if necessary
      _evictMemoryCacheIfNeeded(sizeBytes);
      
      // Add new entry
      _memoryCache[key] = _CacheEntry(
        data: data,
        expiresAt: DateTime.now().add(ttl),
        sizeBytes: sizeBytes,
      );
      _currentMemoryBytes += sizeBytes;
      
    } catch (e) {
      _logger.error('Failed to put data in memory cache: $key', error: e);
    }
  }

  void _evictMemoryCacheIfNeeded(int newDataSize) {
    // Evict by size
    while (_currentMemoryBytes + newDataSize > _maxMemoryCacheBytes && _memoryCache.isNotEmpty) {
      _evictOldestMemoryCacheEntry();
    }
    
    // Evict by count
    while (_memoryCache.length >= _maxMemoryCacheSize) {
      _evictOldestMemoryCacheEntry();
    }
  }

  void _evictOldestMemoryCacheEntry() {
    if (_memoryCache.isEmpty) return;
    
    final oldestKey = _memoryCache.keys.first;
    final entry = _memoryCache.remove(oldestKey);
    if (entry != null) {
      _currentMemoryBytes -= entry.sizeBytes;
    }
  }

  // Disk cache operations
  Future<T?> _getFromDiskCache<T>(String key) async {
    try {
      final data = _prefs?.getString('cache_$key');
      final ttlString = _prefs?.getString('cache_ttl_$key');
      
      if (data == null || ttlString == null) return null;
      
      final ttl = DateTime.parse(ttlString);
      if (DateTime.now().isAfter(ttl)) {
        await remove(key);
        return null;
      }
      
      return await compute(_deserializeData<T>, data);
    } catch (e) {
      _logger.error('Failed to get data from disk cache: $key', error: e);
      return null;
    }
  }

  Future<void> _putInDiskCache<T>(String key, T data, Duration ttl) async {
    try {
      final serialized = await compute(_serializeData, data);
      final expiresAt = DateTime.now().add(ttl);
      
      await _prefs?.setString('cache_$key', serialized);
      await _prefs?.setString('cache_ttl_$key', expiresAt.toIso8601String());
    } catch (e) {
      _logger.error('Failed to put data in disk cache: $key', error: e);
    }
  }

  // Cleanup operations
  void _cleanupExpiredMemoryCache() {
    final expiredKeys = _memoryCache.entries
        .where((entry) => entry.value.isExpired)
        .map((entry) => entry.key)
        .toList();
    
    for (final key in expiredKeys) {
      final entry = _memoryCache.remove(key);
      if (entry != null) {
        _currentMemoryBytes -= entry.sizeBytes;
      }
    }
    
    if (expiredKeys.isNotEmpty) {
      _logger.debug('Cleaned up ${expiredKeys.length} expired memory cache entries');
    }
  }

  Future<void> _cleanupExpiredDiskCache() async {
    try {
      final keys = _prefs?.getKeys().where((key) => key.startsWith('cache_ttl_')).toList() ?? [];
      final now = DateTime.now();
      
      for (final ttlKey in keys) {
        final ttlString = _prefs?.getString(ttlKey);
        if (ttlString != null) {
          final ttl = DateTime.parse(ttlString);
          if (now.isAfter(ttl)) {
            final dataKey = ttlKey.replaceFirst('cache_ttl_', 'cache_');
            await _prefs?.remove(dataKey);
            await _prefs?.remove(ttlKey);
          }
        }
      }
    } catch (e) {
      _logger.error('Failed to cleanup expired disk cache', error: e);
    }
  }

  // Serialization helpers
  static String _serializeData<T>(T data) {
    if (data is String) return data;
    if (data is num || data is bool) return data.toString();
    if (data is List || data is Map) return jsonEncode(data);
    return jsonEncode(data);
  }

  static T _deserializeData<T>(String data) {
    try {
      return jsonDecode(data) as T;
    } catch (e) {
      return data as T;
    }
  }
}

/// Internal cache entry class
class _CacheEntry {
  final dynamic data;
  final DateTime expiresAt;
  final int sizeBytes;

  _CacheEntry({
    required this.data,
    required this.expiresAt,
    required this.sizeBytes,
  });

  bool get isExpired => DateTime.now().isAfter(expiresAt);
}
