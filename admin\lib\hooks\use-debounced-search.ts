import { useState, useEffect, useMemo } from 'react'

interface UseDebounceSearchOptions {
  delay?: number
  minLength?: number
}

export function useDebounceSearch<T>(
  data: T[],
  searchFields: (keyof T)[],
  options: UseDebounceSearchOptions = {}
) {
  const { delay = 300, minLength = 1 } = options
  const [searchQuery, setSearchQuery] = useState('')
  const [debouncedQuery, setDebouncedQuery] = useState('')

  // Debounce the search query
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedQuery(searchQuery)
    }, delay)

    return () => clearTimeout(timer)
  }, [searchQuery, delay])

  // Memoized filtered results
  const filteredData = useMemo(() => {
    if (!debouncedQuery || debouncedQuery.length < minLength) {
      return data
    }

    const query = debouncedQuery.toLowerCase().trim()
    
    return data.filter(item => {
      return searchFields.some(field => {
        const value = item[field]
        if (value == null) return false
        
        const stringValue = String(value).toLowerCase()
        return stringValue.includes(query)
      })
    })
  }, [data, debouncedQuery, searchFields, minLength])

  return {
    searchQuery,
    setSearchQuery,
    filteredData,
    isSearching: searchQuery !== debouncedQuery,
    hasResults: filteredData.length > 0,
    resultCount: filteredData.length
  }
}

// Advanced search with multiple filters
export function useAdvancedSearch<T>(
  data: T[],
  searchConfig: {
    textFields: (keyof T)[]
    filters: {
      [key: string]: {
        field: keyof T
        type: 'select' | 'range' | 'boolean' | 'date'
        value: any
      }
    }
  },
  options: UseDebounceSearchOptions = {}
) {
  const { delay = 300, minLength = 1 } = options
  const [searchQuery, setSearchQuery] = useState('')
  const [debouncedQuery, setDebouncedQuery] = useState('')
  const [filters, setFilters] = useState(searchConfig.filters)

  // Debounce the search query
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedQuery(searchQuery)
    }, delay)

    return () => clearTimeout(timer)
  }, [searchQuery, delay])

  // Memoized filtered results
  const filteredData = useMemo(() => {
    let result = [...data]

    // Apply text search
    if (debouncedQuery && debouncedQuery.length >= minLength) {
      const query = debouncedQuery.toLowerCase().trim()
      result = result.filter(item => {
        return searchConfig.textFields.some(field => {
          const value = item[field]
          if (value == null) return false
          
          const stringValue = String(value).toLowerCase()
          return stringValue.includes(query)
        })
      })
    }

    // Apply filters
    Object.entries(filters).forEach(([filterKey, filter]) => {
      if (filter.value == null || filter.value === '' || filter.value === 'all') {
        return
      }

      result = result.filter(item => {
        const itemValue = item[filter.field]
        
        switch (filter.type) {
          case 'select':
            return itemValue === filter.value
          
          case 'boolean':
            return Boolean(itemValue) === Boolean(filter.value)
          
          case 'range':
            if (Array.isArray(filter.value) && filter.value.length === 2) {
              const [min, max] = filter.value
              const numValue = Number(itemValue)
              return numValue >= min && numValue <= max
            }
            return true
          
          case 'date':
            if (Array.isArray(filter.value) && filter.value.length === 2) {
              const [startDate, endDate] = filter.value
              const itemDate = new Date(itemValue as any)
              return itemDate >= startDate && itemDate <= endDate
            }
            return true
          
          default:
            return true
        }
      })
    })

    return result
  }, [data, debouncedQuery, filters, searchConfig.textFields, minLength])

  const updateFilter = (filterKey: string, value: any) => {
    setFilters(prev => ({
      ...prev,
      [filterKey]: {
        ...prev[filterKey],
        value
      }
    }))
  }

  const clearFilters = () => {
    const clearedFilters = Object.keys(filters).reduce((acc, key) => {
      acc[key] = {
        ...filters[key],
        value: filters[key].type === 'boolean' ? false : ''
      }
      return acc
    }, {} as typeof filters)
    
    setFilters(clearedFilters)
    setSearchQuery('')
  }

  const hasActiveFilters = useMemo(() => {
    return searchQuery.length > 0 || Object.values(filters).some(filter => {
      if (filter.type === 'boolean') {
        return filter.value === true
      }
      return filter.value != null && filter.value !== '' && filter.value !== 'all'
    })
  }, [searchQuery, filters])

  return {
    searchQuery,
    setSearchQuery,
    filters,
    updateFilter,
    clearFilters,
    filteredData,
    isSearching: searchQuery !== debouncedQuery,
    hasResults: filteredData.length > 0,
    resultCount: filteredData.length,
    hasActiveFilters
  }
}

// Hook for search suggestions/autocomplete
export function useSearchSuggestions<T>(
  data: T[],
  field: keyof T,
  maxSuggestions: number = 5
) {
  const [query, setQuery] = useState('')
  const [debouncedQuery, setDebouncedQuery] = useState('')

  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedQuery(query)
    }, 150) // Faster for suggestions

    return () => clearTimeout(timer)
  }, [query])

  const suggestions = useMemo(() => {
    if (!debouncedQuery || debouncedQuery.length < 2) {
      return []
    }

    const queryLower = debouncedQuery.toLowerCase()
    const uniqueValues = new Set<string>()
    
    data.forEach(item => {
      const value = item[field]
      if (value != null) {
        const stringValue = String(value)
        if (stringValue.toLowerCase().includes(queryLower)) {
          uniqueValues.add(stringValue)
        }
      }
    })

    return Array.from(uniqueValues)
      .slice(0, maxSuggestions)
      .sort((a, b) => {
        // Prioritize matches that start with the query
        const aStartsWith = a.toLowerCase().startsWith(queryLower)
        const bStartsWith = b.toLowerCase().startsWith(queryLower)
        
        if (aStartsWith && !bStartsWith) return -1
        if (!aStartsWith && bStartsWith) return 1
        
        return a.localeCompare(b)
      })
  }, [data, field, debouncedQuery, maxSuggestions])

  return {
    query,
    setQuery,
    suggestions,
    isLoading: query !== debouncedQuery
  }
}
