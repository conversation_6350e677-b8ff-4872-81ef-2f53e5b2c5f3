import 'dart:async'; // Add this import for runZonedGuarded
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import 'package:provider/provider.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/rendering.dart';
import 'core/core.dart';
import 'core/utils/ios_ui_integrator.dart';
import 'core/utils/error_handler.dart';
import 'core/config/config_initializer.dart';
import 'core/config/app_config.dart';
import 'core/di/service_locator.dart';
import 'core/services/cache_service.dart';
import 'core/services/offline_initialization_service.dart';
import 'features/splash/splash_screen.dart';
import 'features/home/<USER>';
import 'features/auth/login_screen.dart';
import 'features/auth/auth_service.dart';
import 'features/auth/repositories/auth_repository.dart';
import 'features/auth/repositories/firebase_auth_repository.dart';
import 'features/auth/repositories/user_repository.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:firebase_core/firebase_core.dart';
import 'core/firebase/firebase_options.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'core/services/notification_manager.dart';
import 'features/news/services/youtube_notification_service.dart';
// Services are imported via core/core.dart

/// Background message handler for Firebase Cloud Messaging
@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  // Initialize Firebase if not already initialized
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );

  // Handle the background message
  final logger = AppLogger('BackgroundHandler');
  logger.info('Handling background message: ${message.messageId}');
  logger.debug('Message data: ${message.data}');
  logger.debug('Message notification: ${message.notification?.title}');
}

// Create a global logger instance for the main app
final AppLogger _logger = AppLogger('App');



void main() {
  // Create a single zone for all Flutter operations
  runZonedGuarded(() async {
    // Ensure the WidgetsBinding is initialized first - INSIDE the zone
    WidgetsFlutterBinding.ensureInitialized();

    // Initialize only critical services for fast startup
    await _initializeCriticalServices();

    // Start the app immediately
    runApp(const MyApp());
  }, (error, stackTrace) {
    // This captures errors that occur outside the Flutter framework
    _logger.wtf('Uncaught error', error: error, stackTrace: stackTrace);

    // Try to report to Crashlytics if possible
    try {
      FirebaseCrashlytics.instance.recordError(error, stackTrace, reason: 'Uncaught app error');
    } catch (e) {
      if (kDebugMode) {
        print('Failed to report error to Crashlytics: $e');
      }
    }
  });
}

/// Initialize only critical services needed for app startup
Future<void> _initializeCriticalServices() async {
  // Initialize app configuration first (critical)
  try {
    await ConfigInitializer.initialize();
    _logger.info('Configuration initialized: ${AppConfig.instance.environment}');
  } catch (e, stackTrace) {
    if (kDebugMode) {
      print('Error initializing configuration: $e');
      print('Stack trace: $stackTrace');
    }
  }

  // Initialize Firebase FIRST (critical for AuthService)
  try {
    if (Firebase.apps.isEmpty) {
      await Firebase.initializeApp(
        options: DefaultFirebaseOptions.currentPlatform,
      );
      _logger.info('Firebase Core initialized');
    }
  } catch (e, stackTrace) {
    if (kDebugMode) {
      print('Error initializing Firebase Core: $e');
      print('Stack trace: $stackTrace');
    }
  }

  // Initialize the service locator (critical)
  try {
    await setupServiceLocator();
    _logger.info('Service locator initialized');
  } catch (e, stackTrace) {
    if (kDebugMode) {
      print('Error initializing service locator: $e');
      print('Stack trace: $stackTrace');
    }
  }

  // Basic performance optimizations
  _applyBasicOptimizations();

  // Initialize non-critical services in background
  _initializeNonCriticalServicesInBackground();
}

/// Apply basic performance optimizations
void _applyBasicOptimizations() {
  // Performance optimizations
  if (kReleaseMode) {
    // Disable certain debugging features in release mode for better performance
    debugPrintRebuildDirtyWidgets = false;
    debugPrintLayouts = false;
    _logger.info('Release mode optimizations applied');
  }

  // Configure image cache for better performance (optimized for mobile devices)
  PaintingBinding.instance.imageCache.maximumSizeBytes = 1024 * 1024 * 30; // 30 MB
  PaintingBinding.instance.imageCache.maximumSize = 100; // Max 100 images in cache

  // Configure cached network image defaults
  CachedNetworkImage.logLevel = CacheManagerLogLevel.none;

  // Set preferred orientations
  SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  // Performance optimizations applied without monitoring overhead

  // Set system UI overlay style
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
    ),
  );
}

/// Initialize non-critical services in background after app starts
void _initializeNonCriticalServicesInBackground() {
  // Run in background after app starts
  WidgetsBinding.instance.addPostFrameCallback((_) {
    Future.microtask(() async {
      await _initializeBackgroundServices();
    });
  });
}

/// Initialize services that are not critical for app startup
Future<void> _initializeBackgroundServices() async {
  // Initialize logging system
  try {
    // Initialize Sentry for remote logging if configured
    if (AppConfig.instance.sentryDsn.isNotEmpty) {
      await AppLogger.initSentry(AppConfig.instance.sentryDsn);
      _logger.info('Sentry initialized');
    } else if (!kDebugMode) {
      _logger.warning('Sentry DSN not provided for production build');
    }

    _logger.info('Logging system initialized');
  } catch (e, stackTrace) {
    // Log locally since logging system initialization failed
    if (kDebugMode) {
      print('Error initializing logging system: $e');
      print('Stack trace: $stackTrace');
    }
  }

  // Initialize Firebase FIRST, before any services that depend on it
  try {
    // Check if Firebase is already initialized
    if (Firebase.apps.isEmpty) {
      // Initialize Firebase Core
      await Firebase.initializeApp(
        options: DefaultFirebaseOptions.currentPlatform,
      );

      // Configure Crashlytics
      if (!kDebugMode) {
        // Enable crash collection in production only
        await FirebaseCrashlytics.instance.setCrashlyticsCollectionEnabled(true);

        // Pass all uncaught Flutter errors to Crashlytics
        FlutterError.onError = FirebaseCrashlytics.instance.recordFlutterError;
      } else {
        await FirebaseCrashlytics.instance.setCrashlyticsCollectionEnabled(false);
      }

      _logger.info('Firebase initialized successfully');
    } else {
      _logger.info('Firebase was already initialized');
    }

    // Initialize Firebase services via the service (this is safe to call multiple times)
    await FirebaseService.initializeFirebase(
      projectId: AppConfig.instance.firebaseProjectId
    );

    // Register error handlers
    FlutterError.onError = FirebaseCrashlytics.instance.recordFlutterError;

    // Set the background message handler AFTER Firebase is initialized
    FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);
  } catch (e, stackTrace) {
    // Use our error handler to report the error
    _logger.error('Error initializing Firebase', error: e, stackTrace: stackTrace);
  }

  // Initialize error handling (after Firebase is initialized)
  try {
    await ErrorHandler.init();
    _logger.info('Error handling system initialized');
  } catch (e, stackTrace) {
    _logger.error('Error initializing error handling system', error: e, stackTrace: stackTrace);
  }

  // Initialize notification manager (after Firebase is initialized)
  try {
    await NotificationManager.instance.initialize();
    _logger.info('Notification manager initialized successfully');
  } catch (e, stackTrace) {
    _logger.error('Error initializing notification manager', error: e, stackTrace: stackTrace);
  }

  // Initialize StorageService with correct bucket
  StorageService.initialize();

  // Initialize cache service for better performance
  try {
    await CacheService().initialize();
    _logger.info('Cache service initialized');
  } catch (e, stackTrace) {
    _logger.error('Error initializing cache service', error: e, stackTrace: stackTrace);
  }

  // Initialize YouTube notification service
  try {
    await YouTubeNotificationService().initialize();
    _logger.info('YouTube notification service initialized');
  } catch (e, stackTrace) {
    _logger.error('Error initializing YouTube notification service', error: e, stackTrace: stackTrace);
  }

  // Initialize offline storage services
  try {
    await OfflineInitializationService().initialize();
    _logger.info('Offline storage services initialized');
  } catch (e, stackTrace) {
    _logger.error('Error initializing offline storage services', error: e, stackTrace: stackTrace);
  }

  // Note: News services are initialized on-demand when the news page is accessed
  // This prevents blocking app startup with non-critical services
  _logger.info('News services will be initialized on-demand');

  // Add app metadata for better tracking
  final appLogger = AppLogger.instance;
  appLogger.setTag('app.version', AppConfig.instance.fullVersionString);
  appLogger.setTag('app.environment', AppConfig.instance.environment.toString().split('.').last);

  _logger.info('Background services initialized');
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> with WidgetsBindingObserver {
  final AppLogger _logger = AppLogger('MyApp');
  final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

  /// Safely create AuthService with proper error handling
  AuthService _createAuthService() {
    try {
      // Ensure Firebase is initialized before creating AuthService
      if (Firebase.apps.isEmpty) {
        _logger.error('Firebase not initialized when creating AuthService');
        throw Exception('Firebase must be initialized before creating AuthService');
      }

      // Try to get from service locator first
      if (serviceLocator.isRegistered<AuthService>()) {
        return serviceLocator<AuthService>();
      }

      // If not registered, create directly (this should not happen in normal flow)
      _logger.warning('AuthService not found in service locator, creating directly');
      return AuthService(
        authRepository: serviceLocator<AuthRepository>(),
        userRepository: serviceLocator<UserRepository>(),
      );
    } catch (e) {
      _logger.error('Error creating AuthService, using fallback', error: e);
      // Last resort fallback - create with default implementations
      // This will only work if Firebase is initialized
      try {
        if (Firebase.apps.isEmpty) {
          throw Exception('Cannot create fallback AuthService: Firebase not initialized');
        }
        return AuthService(
          authRepository: FirebaseAuthRepository(),
          userRepository: UserRepository(),
        );
      } catch (fallbackError) {
        _logger.error('Fallback AuthService creation failed', error: fallbackError);
        // Return a minimal AuthService that won't crash
        rethrow;
      }
    }
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);

    // Set up notification click listeners after background services are initialized
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      // Wait a bit for background services to initialize
      await Future.delayed(const Duration(milliseconds: 500));
      _setupNotificationHandlers();
    });
  }

  // Setup notification handlers
  void _setupNotificationHandlers() {
    // Handle when a notification is clicked and the app is opened from background
    NotificationService.instance.onMessageOpenedApp.listen(_handleNotificationClick);
  }

  // Handle notification click
  void _handleNotificationClick(RemoteMessage message) {
    _logger.info('Handling notification click: ${message.messageId}');

    try {
      final data = message.data;
      if (data.isEmpty) return;

      // Navigate based on notification type
      if (data.containsKey('screen')) {
        final screen = data['screen'];
        final params = data['params'];

        _navigateToScreen(screen, params);
      }
    } catch (e, stackTrace) {
      _logger.error('Error handling notification click', error: e, stackTrace: stackTrace);
      ErrorHandler.reportHandledException(
        e,
        stackTrace,
        context: 'Handle notification click',
        severity: ErrorSeverity.medium,
      );
    }
  }

  // Navigate to a screen based on notification data
  void _navigateToScreen(String screen, dynamic params) {
    _logger.info('Navigation requested to: $screen with params: $params');

    try {
      // Get the current navigator context
      final context = navigatorKey.currentContext;
      if (context == null) {
        _logger.warning('Navigator context not available for navigation');
        return;
      }

      switch (screen) {
        case 'news_detail':
          if (params != null) {
            // Navigate to news detail page
            // This would need to be implemented with proper routing
            _logger.info('Would navigate to news article: $params');
          }
          break;
        case 'home':
          // Navigate to home screen
          _logger.info('Would navigate to home screen');
          break;
        default:
          _logger.warning('Unknown navigation screen: $screen');
      }
    } catch (e, stackTrace) {
      _logger.error('Error navigating to screen', error: e, stackTrace: stackTrace);
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    // Simple lifecycle logging without performance overhead
    _logger.debug('App lifecycle state changed to: $state');
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);

    // Clean up notification manager resources
    NotificationManager.instance.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => ThemeProvider()),
        ChangeNotifierProvider(
          create: (_) => _createAuthService(),
        ),
      ],
      child: Consumer<ThemeProvider>(
        builder: (context, themeProvider, child) {
          return MaterialApp(
            title: 'Drive-On',
            debugShowCheckedModeBanner: false,
            navigatorKey: navigatorKey,
            theme: IOSUIIntegrator.applyIOSTheme(themeProvider.themeData),
            home: const AuthWrapper(
              showSplash: true,
              child: HomeScreen(),
            ),
            builder: (context, child) {
              // Apply iOS-style scrolling physics globally
              return ScrollConfiguration(
                behavior: IOSUIIntegrator.getIOSScrollBehavior(),
                child: MediaQuery(
                  // Prevent text scaling for consistent iOS-like appearance
                  data: MediaQuery.of(context).copyWith(textScaler: const TextScaler.linear(1.0)),
                  child: ErrorBoundary(child: child!),
                ),
              );
            },
            // Optimized for smooth UI performance
            checkerboardRasterCacheImages: false,
            checkerboardOffscreenLayers: false,
          );
        },
      ),
    );
  }
}



/// Widget that catches errors in the widget tree
class ErrorBoundary extends StatefulWidget {
  final Widget child;

  const ErrorBoundary({
    Key? key,
    required this.child,
  }) : super(key: key);

  @override
  State<ErrorBoundary> createState() => _ErrorBoundaryState();
}

class _ErrorBoundaryState extends State<ErrorBoundary> {
  bool _hasError = false;
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();
    // Set custom error builder
    ErrorWidget.builder = _buildErrorWidget;
  }

  Widget _buildErrorWidget(FlutterErrorDetails errorDetails) {
    // Report the error
    ErrorHandler.reportHandledException(
      errorDetails.exception,
      errorDetails.stack,
      context: 'Widget error',
      severity: ErrorSeverity.high,
    );

    // Update state to show error UI
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        setState(() {
          _hasError = true;
          _errorMessage = 'An unexpected error occurred in the app. Please try again.';
        });
      }
    });

    // Return an empty container as a placeholder until the error UI is built
    return Container();
  }

  @override
  Widget build(BuildContext context) {
    if (_hasError) {
      // Return fallback UI when an error occurs
      return Scaffold(
        body: Center(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.error_outline,
                  color: Colors.red,
                  size: 64,
                ),
                const SizedBox(height: 16),
                const Text(
                  'Something Went Wrong',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  _errorMessage,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                ElevatedButton(
                  onPressed: () {
                    setState(() {
                      _hasError = false;
                    });
                  },
                  child: const Text('Try Again'),
                ),
              ],
            ),
          ),
        ),
      );
    }

    // No error, return the normal UI
    return widget.child;
  }
}

class AuthWrapper extends StatefulWidget {
  final bool showSplash;
  final Widget child;

  const AuthWrapper({
    super.key,
    required this.showSplash,
    required this.child,
  });

  @override
  State<AuthWrapper> createState() => _AuthWrapperState();
}

class _AuthWrapperState extends State<AuthWrapper> {
  @override
  void initState() {
    super.initState();
    // Initialize auth service safely
    WidgetsBinding.instance.addPostFrameCallback((_) {
      try {
        final authService = Provider.of<AuthService>(context, listen: false);
        authService.init().catchError((error) {
          _logger.error('Error initializing auth service', error: error);
        });
      } catch (e) {
        _logger.error('Error accessing auth service provider', error: e);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedSplashScreen(
      nextScreen: Consumer<AuthService>(
        builder: (context, authService, _) {
          try {
            if (authService.isLoggedIn) {
              // Set user ID for error tracking when logged in
              try {
                final userId = FirebaseService.getCurrentUser()?.uid;
                if (userId != null) {
                  ErrorHandler.setUserIdentifier(userId);
                }
              } catch (e) {
                _logger.warning('Could not set user identifier for error tracking', error: e);
              }
              return widget.child;
            } else {
              return const LoginScreen();
            }
          } catch (e) {
            _logger.error('Error in AuthWrapper Consumer', error: e);
            // Fallback to login screen
            return const LoginScreen();
          }
        },
      ),
      duration: widget.showSplash ? const Duration(milliseconds: 3000) : Duration.zero,
    );
  }
}

// Removed AppWrapper to eliminate duplicate MaterialApp and providers

class SplashScreen extends StatelessWidget {
  const SplashScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // App Logo
            Expanded(
              flex: 3,
              child: Center(
                child: AppLogo(
                  size: 120,
                  isVertical: true,
                ),
              ),
            ),

            // Theme Switcher
            Expanded(
              flex: 1,
              child: Align(
                alignment: Alignment.topCenter,
                child: ThemeSwitcher(
                  showText: true,
                  size: 24,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
