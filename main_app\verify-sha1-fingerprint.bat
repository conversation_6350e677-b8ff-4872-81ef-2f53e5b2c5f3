@echo off
echo ========================================
echo SHA-1 FINGERPRINT VERIFICATION
echo ========================================
echo.

echo 🔍 CHECKING CURRENT DEBUG KEYSTORE SHA-1...
echo.

set JAVA_HOME=%JAVA_HOME%
if "%JAVA_HOME%"=="" (
    echo ⚠️ JAVA_HOME not set, trying to find Java...
    for /f "tokens=2*" %%i in ('reg query "HKEY_LOCAL_MACHINE\SOFTWARE\JavaSoft\Java Development Kit" /s /v JavaHome 2^>nul ^| find "JavaHome"') do set JAVA_HOME=%%j
)

if "%JAVA_HOME%"=="" (
    echo ❌ Java not found. Please install Java JDK or set JAVA_HOME
    pause
    exit /b 1
)

set KEYTOOL="%JAVA_HOME%\bin\keytool.exe"
if not exist %KEYTOOL% (
    set KEYTOOL="keytool"
)

echo Using keytool: %KEYTOOL%
echo.

echo 📋 CURRENT DEBUG KEYSTORE SHA-1:
echo.

%KEYTOOL% -list -v -keystore "%USERPROFILE%\.android\debug.keystore" -alias androiddebugkey -storepass android -keypass android 2>nul | findstr "SHA1:"

if %errorlevel% neq 0 (
    echo ❌ Could not read debug keystore
    echo.
    echo Possible solutions:
    echo 1. Debug keystore doesn't exist
    echo 2. Wrong keystore path
    echo 3. Java/keytool not properly installed
    echo.
    echo Creating new debug keystore...
    
    if not exist "%USERPROFILE%\.android" mkdir "%USERPROFILE%\.android"
    
    %KEYTOOL% -genkey -v -keystore "%USERPROFILE%\.android\debug.keystore" -storepass android -alias androiddebugkey -keypass android -keyalg RSA -keysize 2048 -validity 10000 -dname "CN=Android Debug,O=Android,C=US"
    
    if %errorlevel% neq 0 (
        echo ❌ Failed to create debug keystore
        pause
        exit /b 1
    )
    
    echo ✅ Debug keystore created successfully
    echo.
    echo 📋 NEW DEBUG KEYSTORE SHA-1:
    %KEYTOOL% -list -v -keystore "%USERPROFILE%\.android\debug.keystore" -alias androiddebugkey -storepass android -keypass android | findstr "SHA1:"
)

echo.
echo 🔑 EXPECTED SHA-1 IN FIREBASE:
echo 9A:8E:90:68:D4:97:C6:E2:5A:EF:B0:51:75:BC:CB:B5:CC:BD:55:DC
echo.

echo 📋 CONFIGURED SHA-1 IN google-services.json:
echo 9a8e9068d497c6e25aefb05175bccbb5ccbd55dc (debug)
echo c7e86afedb6d616988dff76f1f94a958cc7900c3 (debug alternative)
echo 85c3078a88353d1d15887693844f8d71741a94ba3 (production)
echo.

echo 🔍 VERIFICATION:
echo If the current SHA-1 matches any of the configured ones, the configuration is correct.
echo If not, you need to add the current SHA-1 to Firebase Console.
echo.

echo 🌐 TO UPDATE FIREBASE:
echo 1. Go to: https://console.firebase.google.com/project/drive-on-b2af8/settings/general/
echo 2. Find your Android app: com.driver.drive_on
echo 3. Click "Add fingerprint"
echo 4. Add the current SHA-1 fingerprint shown above
echo 5. Download updated google-services.json
echo 6. Replace android/app/google-services.json
echo.

echo Press any key to open Firebase Console...
pause >nul

start https://console.firebase.google.com/project/drive-on-b2af8/settings/general/

echo.
echo Press any key to exit...
pause >nul
