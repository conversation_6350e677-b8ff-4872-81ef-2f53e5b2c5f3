import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:logger/logger.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import '../config/app_config.dart';
import 'in_memory_logs.dart';

/// Simplified logging utility for easy access throughout the app
class Log {
  /// Log a verbose message for detailed tracing
  ///
  /// These logs are only shown in development builds and are used for the most
  /// granular and detailed logging.
  ///
  /// Parameters:
  /// - [message] The message to log
  /// - [tag] Optional tag to categorize the log
  /// - [error] Optional error object
  /// - [stackTrace] Optional stack trace
  static void v(String message, {String tag = '', Object? error, StackTrace? stackTrace}) {
    AppLogger.instance.verbose(message, tag: tag, error: error, stackTrace: stackTrace);
  }

  /// Log a debug message for information useful for debugging
  ///
  /// Debug logs provide information that's useful during development and debugging.
  /// These logs are only enabled in debug and development builds.
  ///
  /// Parameters:
  /// - [message] The message to log
  /// - [tag] Optional tag to categorize the log
  /// - [error] Optional error object
  /// - [stackTrace] Optional stack trace
  static void d(String message, {String tag = '', Object? error, StackTrace? stackTrace}) {
    AppLogger.instance.debug(message, tag: tag, error: error, stackTrace: stackTrace);
  }

  /// Log an informational message about general events
  ///
  /// Info logs capture general application flow events that highlight the progress
  /// of the application, such as successful operations or state changes.
  ///
  /// Parameters:
  /// - [message] The message to log
  /// - [tag] Optional tag to categorize the log
  /// - [error] Optional error object
  /// - [stackTrace] Optional stack trace
  static void i(String message, {String tag = '', Object? error, StackTrace? stackTrace}) {
    AppLogger.instance.info(message, tag: tag, error: error, stackTrace: stackTrace);
  }

  /// Log a warning message for potentially harmful situations
  ///
  /// Warning logs highlight situations that aren't errors but may lead to issues
  /// if not addressed. They're sent to Sentry in production.
  ///
  /// Parameters:
  /// - [message] The message to log
  /// - [tag] Optional tag to categorize the log
  /// - [error] Optional error object
  /// - [stackTrace] Optional stack trace
  static void w(String message, {String tag = '', Object? error, StackTrace? stackTrace}) {
    AppLogger.instance.warning(message, tag: tag, error: error, stackTrace: stackTrace);
  }

  /// Log an error message for actual errors that need attention
  ///
  /// Error logs represent runtime errors or unexpected conditions that impact
  /// functionality but don't crash the app. These are sent to Sentry in production.
  ///
  /// Parameters:
  /// - [message] The message to log
  /// - [tag] Optional tag to categorize the log
  /// - [error] Optional error object
  /// - [stackTrace] Optional stack trace
  static void e(String message, {String tag = '', Object? error, StackTrace? stackTrace}) {
    AppLogger.instance.error(message, tag: tag, error: error, stackTrace: stackTrace);
  }

  /// Log a critical error message for catastrophic situations
  ///
  /// "What a Terrible Failure" logs capture critical failures and exceptions
  /// that require immediate attention. These are always sent to Sentry.
  ///
  /// Parameters:
  /// - [message] The message to log
  /// - [tag] Optional tag to categorize the log
  /// - [error] Optional error object
  /// - [stackTrace] Optional stack trace
  static void wtf(String message, {String tag = '', Object? error, StackTrace? stackTrace}) {
    AppLogger.instance.wtf(message, tag: tag, error: error, stackTrace: stackTrace);
  }
  
  /// Set a tag for Sentry and logs
  ///
  /// Tags help categorize events and logs for easier filtering and searching.
  ///
  /// Parameters:
  /// - [key] The tag key
  /// - [value] The tag value
  static void setTag(String key, String value) {
    AppLogger.instance.setTag(key, value);
  }
  
  /// Add a breadcrumb for tracking user actions
  ///
  /// Breadcrumbs are a trail of events that happened prior to an issue and are
  /// attached to error reports to provide context for debugging.
  ///
  /// Parameters:
  /// - [message] The breadcrumb message
  /// - [category] Optional category
  /// - [level] Optional breadcrumb level
  static void addBreadcrumb(String message, {String? category, SentryLevel level = SentryLevel.info}) {
    AppLogger.instance.addBreadcrumb(message, category: category, level: level);
  }
  
  /// Set user context for logs and error reporting
  ///
  /// Attaches user information to logs and error reports to help with debugging
  /// and understanding user-specific issues.
  ///
  /// Parameters:
  /// - [userId] The user ID
  /// - [email] Optional user email
  /// - [extras] Optional additional user data
  static void setUserContext(String userId, {String? email, Map<String, dynamic>? extras}) {
    AppLogger.instance.setUserContext(userId, email: email, extras: extras);
  }
  
  /// Clear user context
  ///
  /// Removes user information from logs and error reports, typically called
  /// during logout to protect user privacy.
  static void clearUserContext() {
    AppLogger.instance.clearUserContext();
  }
}

/// A comprehensive logging implementation that supports both development
/// and production logging with structured formats and remote reporting.
///
/// Features:
/// - Color-coded console logging in development
/// - Sentry integration for remote reporting in production
/// - Support for different log levels based on environment
/// - Structured logs with tags and categories
/// - User context tracking for better debugging
/// - Breadcrumb support for tracing user actions
///
/// The logger is configured based on the current environment from AppConfig
/// and can be initialized with Sentry for production error tracking.
class AppLogger {
  // Singleton pattern implementation
  static final AppLogger _instance = AppLogger._internal();
  static AppLogger get instance => _instance;
  
  // Logger tag for this instance
  final String _tag;
  
  // The internal logger instance
  late Logger _logger;
  
  // In-memory logs instance for log viewer
  static InMemoryLogs inMemoryLogs = InMemoryLogs.instance;
  
  // App metadata
  String _appVersion = '';
  String _appEnvironment = '';
  
  /// Initialize the Logger with a specific tag
  ///
  /// The tag categorizes logs from this specific logger instance
  AppLogger(this._tag) {
    _logger = _createLogger();
  }
  
  AppLogger._internal() : _tag = 'AppLogger' {
    _logger = _createLogger();
  }
  
  /// Initialize the logger system and Sentry
  ///
  /// Should be called early in the app initialization process,
  /// typically in main() before runApp()
  static void init() {
    // Initialize default logger
    instance._logger = instance._createLogger();
  }
  
  /// Initialize Sentry for remote error tracking
  ///
  /// Parameters:
  /// - [dsn] The Sentry DSN (Data Source Name)
  /// - [environment] Optional environment name
  static Future<void> initSentry(String dsn, {String? environment}) async {
    try {
      await SentryFlutter.init(
        (options) {
          options.dsn = dsn;
          options.environment = environment ?? AppConfig.instance.environment.toString();
          options.tracesSampleRate = 1.0;
        },
      );
    } catch (e) {
      if (kDebugMode) {
        print('Failed to initialize Sentry: $e');
      }
    }
  }
  
  /// Set application metadata for tracking versions and environments
  ///
  /// Parameters:
  /// - [version] The app version
  /// - [environment] The app environment
  static void setAppMetadata({String? version, String? environment}) {
    if (version != null) {
      instance._appVersion = version;
    }
    if (environment != null) {
      instance._appEnvironment = environment;
    }
  }
  
  /// Create a new logger instance with appropriate configuration
  Logger _createLogger() {
    // Configure logger based on environment
    final isDebug = !kReleaseMode || AppConfig.instance.enableVerboseLogging;
    
    return Logger(
      level: isDebug ? Level.trace : Level.info,
      printer: PrettyPrinter(
        methodCount: isDebug ? 2 : 0,
        errorMethodCount: 8,
        lineLength: 120,
        colors: true,
        printEmojis: true,
        dateTimeFormat: DateTimeFormat.onlyTimeAndSinceStart,
      ),
      output: _getLogOutput(),
    );
  }
  
  /// Get the appropriate log output based on environment
  LogOutput _getLogOutput() {
    // In debug mode, log to console
    if (kDebugMode || AppConfig.instance.enableVerboseLogging) {
      return ConsoleOutput();
    }
    
    // In production, use multi output (console + optional file)
    return MultiOutput([
      ConsoleOutput(),
      if (kReleaseMode && !kIsWeb && (Platform.isIOS || Platform.isAndroid))
        // File output for production logging (currently using console as fallback)
        ConsoleOutput(),
    ]);
  }
  
  /// Log a verbose message for detailed tracing
  ///
  /// Parameters:
  /// - [message] The message to log
  /// - [tag] Optional tag to categorize the log
  /// - [error] Optional error object
  /// - [stackTrace] Optional stack trace
  void verbose(String message, {String tag = '', Object? error, StackTrace? stackTrace}) {
    final logTag = tag.isNotEmpty ? tag : _tag;
    _logger.t('[$logTag] $message');
    if (error != null || stackTrace != null) {
      _logger.t('Error: $error\nStackTrace: $stackTrace');
    }
  }
  
  /// Log a debug message for information useful for debugging
  ///
  /// Parameters:
  /// - [message] The message to log
  /// - [tag] Optional tag to categorize the log
  /// - [error] Optional error object
  /// - [stackTrace] Optional stack trace
  void debug(String message, {String tag = '', Object? error, StackTrace? stackTrace}) {
    final logTag = tag.isNotEmpty ? tag : _tag;
    _logger.d('[$logTag] $message');
    if (error != null || stackTrace != null) {
      _logger.d('Error: $error\nStackTrace: $stackTrace');
    }
  }
  
  /// Log an informational message about general events
  ///
  /// Parameters:
  /// - [message] The message to log
  /// - [tag] Optional tag to categorize the log
  /// - [error] Optional error object
  /// - [stackTrace] Optional stack trace
  void info(String message, {String tag = '', Object? error, StackTrace? stackTrace}) {
    final logTag = tag.isNotEmpty ? tag : _tag;
    _logger.i('[$logTag] $message');
    if (error != null || stackTrace != null) {
      _logger.i('Error: $error\nStackTrace: $stackTrace');
    }
    
    // Add breadcrumb to Sentry
    _addSentryBreadcrumb(message, logTag, SentryLevel.info);
  }
  
  /// Log a warning message for potentially harmful situations
  ///
  /// Parameters:
  /// - [message] The message to log
  /// - [tag] Optional tag to categorize the log
  /// - [error] Optional error object
  /// - [stackTrace] Optional stack trace
  void warning(String message, {String tag = '', Object? error, StackTrace? stackTrace}) {
    final logTag = tag.isNotEmpty ? tag : _tag;
    _logger.w('[$logTag] $message');
    if (error != null || stackTrace != null) {
      _logger.w('Error: $error\nStackTrace: $stackTrace');
    }
    
    // Add breadcrumb to Sentry
    _addSentryBreadcrumb(message, logTag, SentryLevel.warning);
    
    // Send to Sentry in production
    if (kReleaseMode) {
      _reportToSentry(
        message, 
        logTag, 
        error, 
        stackTrace, 
        SentryLevel.warning,
      );
    }
  }
  
  /// Log an error message for actual errors that need attention
  ///
  /// Parameters:
  /// - [message] The message to log
  /// - [tag] Optional tag to categorize the log
  /// - [error] Optional error object
  /// - [stackTrace] Optional stack trace
  void error(String message, {String tag = '', Object? error, StackTrace? stackTrace}) {
    final logTag = tag.isNotEmpty ? tag : _tag;
    _logger.e('[$logTag] $message');
    if (error != null || stackTrace != null) {
      _logger.e('Error: $error\nStackTrace: $stackTrace');
    }
    
    // Add breadcrumb to Sentry
    _addSentryBreadcrumb(message, logTag, SentryLevel.error);
    
    // Send to Sentry in production
    if (kReleaseMode) {
      _reportToSentry(
        message, 
        logTag, 
        error, 
        stackTrace, 
        SentryLevel.error,
      );
    }
  }
  
  /// Log a critical error message for catastrophic situations
  ///
  /// Parameters:
  /// - [message] The message to log
  /// - [tag] Optional tag to categorize the log
  /// - [error] Optional error object
  /// - [stackTrace] Optional stack trace
  void wtf(String message, {String tag = '', Object? error, StackTrace? stackTrace}) {
    final logTag = tag.isNotEmpty ? tag : _tag;
    _logger.f('[$logTag] $message');
    if (error != null || stackTrace != null) {
      _logger.f('Error: $error\nStackTrace: $stackTrace');
    }
    
    // Add breadcrumb to Sentry
    _addSentryBreadcrumb(message, logTag, SentryLevel.fatal);
    
    // Always send to Sentry
    _reportToSentry(
      message, 
      logTag, 
      error, 
      stackTrace, 
      SentryLevel.fatal,
    );
  }
  
  /// Add a breadcrumb to Sentry for tracking user actions
  ///
  /// Parameters:
  /// - [message] The breadcrumb message
  /// - [category] Optional category
  /// - [level] Optional breadcrumb level
  void addBreadcrumb(String message, {String? category, SentryLevel level = SentryLevel.info}) {
    if (kDebugMode) return;
    
    try {
      Sentry.addBreadcrumb(
        Breadcrumb(
          message: message,
          category: category,
          level: level,
          timestamp: DateTime.now(),
        ),
      );
    } catch (e) {
      if (kDebugMode) {
        print('Failed to add Sentry breadcrumb: $e');
      }
    }
  }
  
  /// Set a tag for Sentry and logs
  ///
  /// Parameters:
  /// - [key] The tag key
  /// - [value] The tag value
  void setTag(String key, String value) {
    if (kDebugMode) return;
    
    try {
      Sentry.configureScope((scope) {
        scope.setTag(key, value);
      });
    } catch (e) {
      if (kDebugMode) {
        print('Failed to set Sentry tag: $e');
      }
    }
  }
  
  /// Set user context for logs and error reporting
  ///
  /// Parameters:
  /// - [userId] The user ID
  /// - [email] Optional user email
  /// - [extras] Optional additional user data
  void setUserContext(String userId, {String? email, Map<String, dynamic>? extras}) {
    if (kDebugMode) return;
    
    try {
      Sentry.configureScope((scope) {
        scope.setUser(SentryUser(
          id: userId,
          email: email,
          data: extras,
        ));
      });
    } catch (e) {
      if (kDebugMode) {
        print('Failed to set Sentry user context: $e');
      }
    }
  }
  
  /// Clear user context
  void clearUserContext() {
    if (kDebugMode) return;
    
    try {
      Sentry.configureScope((scope) {
        scope.setUser(null);
      });
    } catch (e) {
      if (kDebugMode) {
        print('Failed to clear Sentry user context: $e');
      }
    }
  }
  
  /// Add a breadcrumb to Sentry internal helper method
  void _addSentryBreadcrumb(String message, String category, SentryLevel level) {
    if (kDebugMode) return;
    
    try {
      Sentry.addBreadcrumb(
        Breadcrumb(
          message: message,
          category: category,
          level: level,
          timestamp: DateTime.now(),
        ),
      );
    } catch (e) {
      if (kDebugMode) {
        print('Failed to add Sentry breadcrumb: $e');
      }
    }
  }
  
  /// Report an error to Sentry internal helper method
  Future<void> _reportToSentry(
    String message,
    String tag,
    Object? error,
    StackTrace? stackTrace,
    SentryLevel level,
  ) async {
    if (kDebugMode) return;
    
    try {
      final sentryEvent = SentryEvent(
        message: SentryMessage(message),
        level: level,
        tags: {
          'tag': tag,
          'version': _appVersion,
          'environment': _appEnvironment,
        },
        throwable: error,
      );
      
      // If stackTrace exists, capture with full context
      if (stackTrace != null) {
        await Sentry.captureEvent(sentryEvent, stackTrace: stackTrace);
      } else {
        await Sentry.captureEvent(sentryEvent);
      }
    } catch (e) {
      if (kDebugMode) {
        print('Failed to report to Sentry: $e');
      }
    }
  }

  /// Export logs to a file for sharing
  static Future<String?> exportLogs() async {
    try {
      // This is just a placeholder implementation
      // In a real app, you would use path_provider and file system access
      // to save logs to a file and return the file path
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Failed to export logs: $e');
      }
      return null;
    }
  }
}