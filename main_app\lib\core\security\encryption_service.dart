import 'dart:convert';
import 'package:encrypt/encrypt.dart' as encrypt;
import 'package:crypto/crypto.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

/// Service for encrypting and decrypting sensitive data
class EncryptionService {
  static final EncryptionService _instance = EncryptionService._internal();
  
  /// Singleton instance of EncryptionService
  factory EncryptionService() => _instance;
  
  EncryptionService._internal();
  
  /// Storage key for the encryption key
  static const String _encryptionKeyStorageKey = 'encryption_key';
  
  /// Storage for secure keys
  final FlutterSecureStorage _secureStorage = const FlutterSecureStorage();
  
  /// Initialize encryption key - creates a new one if none exists
  Future<void> initializeEncryptionKey() async {
    final String? storedKey = await _secureStorage.read(key: _encryptionKeyStorageKey);
    
    if (storedKey == null) {
      // Generate a new encryption key
      final encrypt.Key key = encrypt.Key.fromSecureRandom(32);
      final String base64Key = base64Encode(key.bytes);
      
      // Store it securely
      await _secureStorage.write(
        key: _encryptionKeyStorageKey,
        value: base64Key,
      );
    }
  }
  
  /// Get the encryption key from secure storage
  Future<encrypt.Key> _getEncryptionKey() async {
    String? storedKey = await _secureStorage.read(key: _encryptionKeyStorageKey);
    
    if (storedKey == null) {
      await initializeEncryptionKey();
      storedKey = await _secureStorage.read(key: _encryptionKeyStorageKey);
    }
    
    final Uint8List keyBytes = base64Decode(storedKey!);
    return encrypt.Key(keyBytes);
  }
  
  /// Generate an IV (Initialization Vector)
  encrypt.IV _generateIV() {
    return encrypt.IV.fromSecureRandom(16);
  }
  
  /// Encrypt a string
  /// Returns a base64 encoded string containing the IV and encrypted data
  Future<String> encryptData(String plainText) async {
    try {
      final encrypt.Key key = await _getEncryptionKey();
      final encrypt.IV iv = _generateIV();
      final encrypt.Encrypter encrypter = encrypt.Encrypter(encrypt.AES(key, mode: encrypt.AESMode.cbc));
      
      final encrypt.Encrypted encrypted = encrypter.encrypt(plainText, iv: iv);
      
      // Combine IV and encrypted data for storage
      final Map<String, String> encryptedData = {
        'iv': base64Encode(iv.bytes),
        'data': encrypted.base64
      };
      
      return base64Encode(utf8.encode(jsonEncode(encryptedData)));
    } catch (e) {
      if (kDebugMode) {
        print('Encryption error: $e');
      }
      rethrow;
    }
  }
  
  /// Decrypt an encrypted string
  Future<String> decrypt(String encryptedText) async {
    try {
      final Map<String, dynamic> encryptedData = jsonDecode(
        utf8.decode(base64Decode(encryptedText))
      );
      
      final encrypt.Key key = await _getEncryptionKey();
      final encrypt.IV iv = encrypt.IV(base64Decode(encryptedData['iv']));
      final encrypt.Encrypter encrypter = encrypt.Encrypter(encrypt.AES(key, mode: encrypt.AESMode.cbc));
      
      final String decrypted = encrypter.decrypt(
        encrypt.Encrypted.fromBase64(encryptedData['data']), 
        iv: iv
      );
      
      return decrypted;
    } catch (e) {
      if (kDebugMode) {
        print('Decryption error: $e');
      }
      throw Exception('Failed to decrypt data: $e');
    }
  }
  
  /// Hash a string using SHA-256
  String hashString(String input) {
    final bytes = utf8.encode(input);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }
  
  /// Compare a plaintext password with a hashed password
  bool verifyPassword(String plainTextPassword, String hashedPassword) {
    final hashedInput = hashString(plainTextPassword);
    return hashedInput == hashedPassword;
  }
  
  /// Encrypt a map of data
  Future<String> encryptMap(Map<String, dynamic> data) async {
    return encryptData(jsonEncode(data));
  }
  
  /// Decrypt a map of data
  Future<Map<String, dynamic>> decryptMap(String encryptedData) async {
    final decrypted = await decrypt(encryptedData);
    return jsonDecode(decrypted) as Map<String, dynamic>;
  }
} 