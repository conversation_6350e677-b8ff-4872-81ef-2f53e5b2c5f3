# Drive-On Application

A comprehensive ride-hailing and driver management platform built with Flutter. This application connects drivers with customers and provides a complete management system for vehicle fleets.

## Table of Contents

- [Overview](#overview)
- [Features](#features)
- [Project Setup](#project-setup)
- [Running the Application](#running-the-application)
- [Testing](#testing)
- [Project Structure](#project-structure)
- [Architecture](#architecture)
- [Configuration System](#configuration-system)
- [Logging System](#logging-system)
- [Security Implementation](#security-implementation)
- [Contribution Guidelines](#contribution-guidelines)
- [License](#license)
- [CI/CD Pipeline](#ci-cd-pipeline)
- [Performance Monitoring](#performance-monitoring)
- [Google Play Store Deployment](#google-play-store-deployment)

## Overview

Drive-On is a ride-hailing application designed for the Pakistani market, focusing on providing a reliable and secure platform for both drivers and customers. The application uses Flutter for the mobile frontend and Firebase for backend services.

## Features

- **Authentication**: User registration, login, and profile management
- **Driver Management**: Driver registration, verification, and tracking
- **Job Listings**: Browse and apply for driving opportunities
- **Partner Portal**: Fleet management for partners
- **Forums**: Community discussions and announcements
- **Analytics**: Usage tracking and reporting
- **Notifications**: Real-time alerts and updates

## Project Setup

### Prerequisites

- Flutter SDK (version 3.3.0 or higher)
- Dart SDK (version 3.0.0 or higher)
- Android Studio / VS Code with Flutter extensions
- Firebase account and project
- Git

### Initial Setup

1. **Clone the repository**

```bash
git clone https://github.com/your-organization/drive-on.git
cd drive-on
```

2. **Install dependencies**

```bash
flutter pub get
```

3. **Configure Firebase**

- Create a Firebase project in the [Firebase Console](https://console.firebase.google.com/)
- Enable Authentication, Firestore, and Storage
- Download the `google-services.json` file for Android and place it in `android/app/`
- Download the `GoogleService-Info.plist` file for iOS and place it in `ios/Runner/`

4. **Environment Configuration**

- Copy the environment template files:
```bash
cp .env.example .env.development
cp .env.example .env.staging
cp .env.example .env.production
```

- Edit each file to include the appropriate values for each environment

## CI/CD Pipeline

The project uses GitHub Actions for continuous integration and deployment. The pipeline automates building, testing, and deploying the application to app stores.

### Pipeline Structure

- **PR Validation**: Runs on every pull request to main/develop branches.
  - Code formatting and analysis
  - Unit and widget tests
  - Integration tests
  - Debug APK build

- **Release**: Runs on tags starting with 'v' (e.g., v1.0.0).
  - Full test suite
  - Release builds for Android and iOS
  - Deployment to Play Store and TestFlight
  - GitHub release creation

### Triggering Releases

To create a new release:

```bash
# Tag the release
git tag v1.0.0

# Push the tag
git push origin v1.0.0
```

The pipeline will automatically build and deploy the release.

### Required Secrets

For the pipeline to work correctly, you need to set up the following GitHub repository secrets:

#### Android Secrets
- `ANDROID_KEYSTORE_BASE64`: Base64-encoded Android keystore file
- `ANDROID_KEY_PROPERTIES`: Contents of the `key.properties` file
- `PLAY_STORE_SERVICE_ACCOUNT_JSON`: Google Play Store service account credentials

#### iOS Secrets
- `APPLE_CERTIFICATE`: Base64-encoded distribution certificate
- `APPLE_CERTIFICATE_PASSWORD`: Certificate password
- `APPLE_PROVISIONING_PROFILE`: Base64-encoded provisioning profile
- `FASTLANE_APPLE_ID`: Apple ID email
- `FASTLANE_PASSWORD`: Apple ID password
- `FASTLANE_APPLE_APPLICATION_SPECIFIC_PASSWORD`: App-specific password
- `FASTLANE_TEAM_ID`: Developer Portal team ID
- `FASTLANE_ITC_TEAM_ID`: App Store Connect team ID

## Development Tools

During development, you can:

1. Use Flutter DevTools for local profiling
2. Monitor app performance through standard Flutter debugging tools

## Running the Application

### Development Environment

```bash
flutter run --dart-define=ENVIRONMENT=development
```

### Staging Environment

```bash
flutter run --dart-define=ENVIRONMENT=staging
```

### Production Environment

```bash
flutter run --dart-define=ENVIRONMENT=production
```

### Building Release Versions

#### Android

```bash
# Development APK
flutter build apk --dart-define=ENVIRONMENT=development

# Staging APK
flutter build apk --dart-define=ENVIRONMENT=staging

# Production APK
flutter build apk --dart-define=ENVIRONMENT=production

# App Bundle for Play Store (Production)
flutter build appbundle --dart-define=ENVIRONMENT=production
```

#### iOS

```bash
# Development IPA
flutter build ios --dart-define=ENVIRONMENT=development

# Staging IPA
flutter build ios --dart-define=ENVIRONMENT=staging

# Production IPA
flutter build ios --dart-define=ENVIRONMENT=production
```

## Testing

The application includes a comprehensive testing suite with unit, widget, and integration tests.

### Running Tests

```bash
# Run all tests
flutter test

# Run a specific test file
flutter test test/unit/auth_service_test.dart

# Run tests with coverage
flutter test --coverage

# View coverage report (requires lcov)
genhtml coverage/lcov.info -o coverage/html
```

### Running Integration Tests

```bash
# Run on a connected device
flutter test integration_test/app_test.dart
```

For more detailed information about testing, see [README_TESTING.md](README_TESTING.md).

## Project Structure

```
drive_on/
├── lib/
│   ├── core/            # Core functionality and utilities
│   │   ├── config/      # Configuration management
│   │   ├── di/          # Dependency injection
│   │   ├── security/    # Security implementations
│   │   ├── services/    # Common services
│   │   └── utils/       # Utility classes
│   ├── features/        # Feature modules
│   │   ├── auth/        # Authentication feature
│   │   ├── drivers/     # Driver management
│   │   ├── forum/       # Community forums
│   │   ├── home/        # Home screen and navigation
│   │   ├── jobs/        # Job listings
│   │   ├── news/        # News and updates
│   │   ├── partners/    # Partner portal
│   │   └── queries/     # Support queries
│   ├── main.dart        # Application entry point
│   └── app_config.dart  # App configuration

├── docs/                # Documentation
├── assets/              # Images, fonts, etc.
└── pubspec.yaml         # Dependencies
```

## Architecture

The application follows a feature-first architecture with clear separation of concerns:

- **Repository Pattern**: For data access
- **Service Layer**: For business logic
- **Provider**: For state management
- **Dependency Injection**: For testability and modularity

## Configuration System

The application uses a multi-environment configuration system:

```dart
// Access configuration values
final apiBaseUrl = AppConfig.instance.apiBaseUrl;

// Check current environment
if (AppConfig.instance.isDevelopment) {
  // Development-only code
}

// Use feature flags
if (AppConfig.instance.featureFlags.enableExperimentalFeatures) {
  // Enable experimental feature
}

// Access API endpoints
final loginEndpoint = ApiConfig.endpoints.auth.signIn;
```

For more details, see the [Configuration Documentation](docs/CONFIGURATION.md).

## Logging System

The application uses a structured logging system:

```dart
// Debug log (development only)
Log.d('Detailed information for debugging');

// Info log
Log.i('User logged in successfully');

// Warning log (sent to Sentry in production)
Log.w('Network connection unstable');

// Error log (sent to Sentry in production)
Log.e('Failed to load user data', error: exception, stackTrace: stackTrace);

// Critical error log (sent to Sentry in production)
Log.wtf('System failure', error: criticalError, stackTrace: stackTrace);
```

For more details, see the [Logging Documentation](docs/LOGGING.md).

## Security Implementation

The application implements several security measures:

- Secure storage for sensitive data
- Data encryption for local storage
- Input validation and sanitization
- Token-based authentication
- Secure API communication

For more details, see the [Security Documentation](lib/security_readme.md).

## Contribution Guidelines

### Code Style

This project follows the [Flutter style guide](https://flutter.dev/docs/development/tools/formatting) and uses the following tools:

- `flutter_lints` for linting rules
- `dart format` for code formatting

### Workflow

1. **Fork the repository** and create a new branch for your feature
2. **Implement changes** following the code style guidelines
3. **Write tests** for your changes
4. **Update documentation** if necessary
5. **Submit a pull request** with a clear description of your changes

### Pull Request Process

1. Ensure your code passes all tests and linting rules
2. Update the README.md or documentation with any necessary changes
3. The PR will be reviewed by maintainers
4. Address any feedback from code reviews
5. Once approved, your PR will be merged

### Commit Message Format

Follow the [Conventional Commits](https://www.conventionalcommits.org/) specification:

```
feat: add new feature
fix: fix a bug
docs: update documentation
style: format code
refactor: restructure code without changing behavior
test: add or update tests
chore: update build scripts, etc.
```

## Google Play Store Deployment

The Drive-On app is fully configured for Google Play Store deployment with automated CI/CD pipelines and comprehensive tooling.

### 🚀 Quick Start for Play Store Deployment

1. **Generate Production Keystore**
   ```bash
   scripts\generate-keystore.bat
   ```

2. **Prepare Store Assets**
   ```bash
   scripts\prepare-play-store-assets.bat
   ```

3. **Test Production Build**
   ```bash
   scripts\test-production-build.bat
   ```

4. **Build for Production**
   ```bash
   scripts\build-production.bat
   ```

### 📋 Complete Deployment Guide

- **Comprehensive Checklist**: `PLAY_STORE_DEPLOYMENT_CHECKLIST.md`
- **Step-by-Step Guide**: `play-store-setup-guide.md`
- **Asset Requirements**: `scripts/prepare-play-store-assets.bat`

### 🔧 Key Features for Play Store

- ✅ Production keystore configuration
- ✅ Signed APK and AAB generation
- ✅ ProGuard obfuscation and optimization
- ✅ Store metadata and assets structure
- ✅ Automated CI/CD deployment pipeline
- ✅ Fastlane integration for store uploads
- ✅ Multi-track deployment (Internal/Alpha/Beta/Production)

### 📱 Build Outputs

- **App Bundle (AAB)**: For Play Store submission
- **APK**: For testing and distribution
- **Mapping File**: For crash reporting and debugging

### 🔐 Security & Compliance

- Production-grade signing configuration
- Code obfuscation and optimization
- Secure keystore management
- Google Play policies compliance
- Data safety and privacy configurations

For detailed deployment instructions, see `PLAY_STORE_DEPLOYMENT_CHECKLIST.md`.

## License

This project is licensed under the MIT License - see the LICENSE file for details.
