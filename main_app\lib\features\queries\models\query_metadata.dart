import 'package:cloud_firestore/cloud_firestore.dart';

class QueryMetadata {
  final String id;
  final String userId;
  final String userName;
  final String? userPhotoUrl;
  final String lastMessage;
  final DateTime lastMessageTime;
  final int unreadCount;
  final bool isActive;

  QueryMetadata({
    required this.id,
    required this.userId,
    required this.userName,
    this.userPhotoUrl,
    required this.lastMessage,
    required this.lastMessageTime,
    this.unreadCount = 0,
    this.isActive = true,
  });

  factory QueryMetadata.fromMap(Map<String, dynamic> map, String documentId) {
    return QueryMetadata(
      id: documentId,
      userId: map['userId'] ?? '',
      userName: map['userName'] ?? 'Anonymous User',
      userPhotoUrl: map['userPhotoUrl'],
      lastMessage: map['lastMessage'] ?? 'New conversation',
      lastMessageTime: (map['lastMessageTime'] is Timestamp)
          ? (map['lastMessageTime'] as Timestamp).toDate()
          : DateTime.now(),
      unreadCount: map['unreadCount'] ?? 0,
      isActive: map['isActive'] ?? true,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'userId': userId,
      'userName': userName,
      'userPhotoUrl': userPhotoUrl,
      'lastMessage': lastMessage,
      'lastMessageTime': lastMessageTime,
      'unreadCount': unreadCount,
      'isActive': isActive,
    };
  }

  QueryMetadata copyWith({
    String? id,
    String? userId,
    String? userName,
    String? userPhotoUrl,
    String? lastMessage,
    DateTime? lastMessageTime,
    int? unreadCount,
    bool? isActive,
  }) {
    return QueryMetadata(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      userPhotoUrl: userPhotoUrl ?? this.userPhotoUrl,
      lastMessage: lastMessage ?? this.lastMessage,
      lastMessageTime: lastMessageTime ?? this.lastMessageTime,
      unreadCount: unreadCount ?? this.unreadCount,
      isActive: isActive ?? this.isActive,
    );
  }
} 