'use client'

import { Filter } from 'lucide-react'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'

interface PartnerFiltersProps {
  filters: {
    status: string
    businessType: string
    city: string
  }
  onFiltersChange: (filters: any) => void
}

export function PartnerFilters({ filters, onFiltersChange }: PartnerFiltersProps) {
  const updateFilter = (key: string, value: string) => {
    onFiltersChange({
      ...filters,
      [key]: value
    })
  }

  return (
    <div className="flex space-x-2">
      {/* Status Filter */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" size="sm">
            <Filter className="w-4 h-4 mr-2" />
            Status: {filters.status === 'all' ? 'All' : filters.status}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          <DropdownMenuLabel>Filter by Status</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={() => updateFilter('status', 'all')}>
            All Status
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('status', 'pending')}>
            Pending
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('status', 'approved')}>
            Approved
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('status', 'rejected')}>
            Rejected
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('status', 'suspended')}>
            Suspended
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Business Type Filter */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" size="sm">
            Type: {filters.businessType === 'all' ? 'All' : filters.businessType}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          <DropdownMenuLabel>Filter by Business Type</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={() => updateFilter('businessType', 'all')}>
            All Types
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('businessType', 'Transportation')}>
            Transportation
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('businessType', 'Logistics')}>
            Logistics
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('businessType', 'Technology')}>
            Technology
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('businessType', 'Finance')}>
            Finance
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('businessType', 'Other')}>
            Other
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* City Filter */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" size="sm">
            City: {filters.city === 'all' ? 'All' : filters.city}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          <DropdownMenuLabel>Filter by City</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={() => updateFilter('city', 'all')}>
            All Cities
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('city', 'Karachi')}>
            Karachi
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('city', 'Lahore')}>
            Lahore
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('city', 'Islamabad')}>
            Islamabad
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('city', 'Rawalpindi')}>
            Rawalpindi
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => updateFilter('city', 'Faisalabad')}>
            Faisalabad
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  )
}
