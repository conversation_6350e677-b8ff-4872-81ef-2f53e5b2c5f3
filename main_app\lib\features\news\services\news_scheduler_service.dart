import 'dart:async';
import 'package:shared_preferences/shared_preferences.dart';
import '../data/repositories/news_repository.dart';
import '../data/sources/news_api_service.dart';
import '../../../core/utils/app_logger.dart';

class NewsSchedulerService {
  static const String _lastRefreshKey = 'news_last_refresh';
  static const Duration _refreshInterval = Duration(minutes: 50);

  final NewsRepository _newsRepository;
  final NewsApiService _newsApiService;
  final AppLogger _logger = AppLogger('NewsSchedulerService');
  Timer? _refreshTimer;
  bool _isRefreshing = false;

  NewsSchedulerService({
    required NewsRepository newsRepository,
    required NewsApiService newsApiService,
  })  : _newsRepository = newsRepository,
        _newsApiService = newsApiService;

  /// Initialize the scheduler and start automatic refresh
  Future<void> initialize() async {
    await _checkAndRefreshIfNeeded();
    _startPeriodicRefresh();
  }

  /// Start periodic refresh every 50 minutes
  void _startPeriodicRefresh() {
    _refreshTimer?.cancel();
    _refreshTimer = Timer.periodic(_refreshInterval, (timer) {
      _refreshNews();
    });
  }

  /// Check if news needs refresh and refresh if needed
  Future<void> _checkAndRefreshIfNeeded() async {
    final prefs = await SharedPreferences.getInstance();
    final lastRefreshString = prefs.getString(_lastRefreshKey);
    
    if (lastRefreshString == null) {
      // First time, refresh immediately
      await _refreshNews();
      return;
    }

    final lastRefresh = DateTime.parse(lastRefreshString);
    final now = DateTime.now();
    final timeSinceLastRefresh = now.difference(lastRefresh);

    if (timeSinceLastRefresh >= _refreshInterval) {
      await _refreshNews();
    }
  }

  /// Refresh news from API and store in Firebase
  Future<void> _refreshNews() async {
    if (_isRefreshing) return; // Prevent concurrent refreshes
    
    _isRefreshing = true;
    
    try {
      _logger.info('Starting news refresh...');

      // Check if we can make API requests
      if (!await _newsApiService.canMakeRequest()) {
        _logger.warning('Cannot refresh: Daily API limit reached');
        _isRefreshing = false;
        return;
      }

      final remainingRequests = await _newsApiService.getRemainingRequests();
      _logger.debug('Remaining API requests: $remainingRequests');

      // Fetch enhanced news articles
      final articles = await _newsApiService.fetchAutomotiveNews();

      if (articles.isNotEmpty) {
        // Save articles to Firebase
        await _newsRepository.saveNewsArticles(articles);

        // Update last refresh time
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString(_lastRefreshKey, DateTime.now().toIso8601String());

        _logger.info('News refresh completed: ${articles.length} articles saved');
      } else {
        _logger.warning('No articles fetched during refresh');
      }

    } catch (e) {
      _logger.error('Error during news refresh', error: e);
    } finally {
      _isRefreshing = false;
    }
  }

  /// Force refresh news (manual trigger)
  Future<void> forceRefresh() async {
    if (_isRefreshing) {
      _logger.warning('Refresh already in progress');
      return;
    }

    await _refreshNews();
  }

  /// Get time until next refresh
  Future<Duration> getTimeUntilNextRefresh() async {
    final prefs = await SharedPreferences.getInstance();
    final lastRefreshString = prefs.getString(_lastRefreshKey);
    
    if (lastRefreshString == null) {
      return Duration.zero; // Needs immediate refresh
    }

    final lastRefresh = DateTime.parse(lastRefreshString);
    final nextRefresh = lastRefresh.add(_refreshInterval);
    final now = DateTime.now();
    
    if (now.isAfter(nextRefresh)) {
      return Duration.zero; // Overdue for refresh
    }
    
    return nextRefresh.difference(now);
  }

  /// Get refresh status information
  Future<Map<String, dynamic>> getRefreshStatus() async {
    final prefs = await SharedPreferences.getInstance();
    final lastRefreshString = prefs.getString(_lastRefreshKey);
    final remainingRequests = await _newsApiService.getRemainingRequests();
    final timeUntilNext = await getTimeUntilNextRefresh();
    
    return {
      'lastRefresh': lastRefreshString != null 
          ? DateTime.parse(lastRefreshString) 
          : null,
      'nextRefresh': lastRefreshString != null 
          ? DateTime.parse(lastRefreshString).add(_refreshInterval)
          : DateTime.now(),
      'timeUntilNextRefresh': timeUntilNext,
      'remainingApiRequests': remainingRequests,
      'isRefreshing': _isRefreshing,
      'refreshInterval': _refreshInterval,
    };
  }

  /// Check if refresh is currently in progress
  bool get isRefreshing => _isRefreshing;

  /// Stop the scheduler
  void dispose() {
    _refreshTimer?.cancel();
    _refreshTimer = null;
  }
}
