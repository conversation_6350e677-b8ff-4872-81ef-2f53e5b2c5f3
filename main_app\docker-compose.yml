version: '3.8'

services:
  drive-on-web:
    build: .
    ports:
      - "8080:80"
    environment:
      - ENVIRONMENT=development
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Optional: Add a reverse proxy for SSL termination in production
  nginx-proxy:
    image: nginx:alpine
    ports:
      - "443:443"
      - "80:80"
    volumes:
      - ./ssl:/etc/nginx/ssl:ro
      - ./proxy.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - drive-on-web
    restart: unless-stopped
    profiles:
      - production
