FIREBASE SHA FINGERPRINTS FOR DRIVE-ON v2.0.0
==============================================

COPY THESE EXACT VALUES TO FIREBASE CONSOLE:

SHA-1 Fingerprint (UPPERCASE with colons):
85:C3:07:8A:88:35:3D:1D:15:88:76:93:84:F8:D7:17:41:A9:4B:A3

SHA-256 Fingerprint (UPPERCASE with colons):
3A:F8:D8:2A:98:99:71:9C:4D:EC:F0:1A:9F:B8:9A:D9:9E:FF:B0:00:8C:A0:2C:64:C5:E9:1B:5E:30:F7:BC:5B

FIREBASE CONSOLE URL:
https://console.firebase.google.com/project/drive-on-b2af8/settings/general/

STEPS:
1. Open the Firebase Console URL above
2. Find your Android app: com.driver.drive_on
3. Click "Add fingerprint"
4. Add the SHA-1 fingerprint above
5. Add the SHA-256 fingerprint above
6. Save changes
7. Download updated google-services.json
8. Replace both google-services.json files in your project
9. Rebuild AAB: flutter build appbundle --release

ORIGINAL KEYSTORE FINGERPRINTS (for reference):
SHA-1:  85:C3:07:8A:88:35:3D:1D:15:88:76:93:84:F8:D7:17:41:A9:4B:A3
SHA-256: 3A:F8:D8:2A:98:99:71:9C:4D:EC:F0:1A:9F:B8:9A:D9:9E:FF:B0:00:8C:A0:2C:64:C5:E9:1B:5E:30:F7:BC:5B
