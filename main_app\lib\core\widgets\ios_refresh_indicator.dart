import 'package:flutter/material.dart';
import 'dart:math' as math;

/// iOS-style refresh indicator with the spinning activity indicator
class IOSRefreshIndicator extends StatefulWidget {
  final Widget child;
  final Future<void> Function() onRefresh;
  final Color? color;
  final double displacement;

  const IOSRefreshIndicator({
    Key? key,
    required this.child,
    required this.onRefresh,
    this.color,
    this.displacement = 40.0,
  }) : super(key: key);

  @override
  IOSRefreshIndicatorState createState() => IOSRefreshIndicatorState();
}

class IOSRefreshIndicatorState extends State<IOSRefreshIndicator> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  bool _isRefreshing = false;
  double _dragOffset = 0.0;
  static const double _kDragContainerExtent = 80.0;
  static const double _kDragSizeRatio = 0.25;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1000),
    );
    _controller.repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  Future<void> _handleRefresh() async {
    if (_isRefreshing) return;
    
    setState(() {
      _isRefreshing = true;
    });
    
    try {
      await widget.onRefresh();
    } finally {
      if (mounted) {
        setState(() {
          _isRefreshing = false;
          _dragOffset = 0.0;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return NotificationListener<ScrollNotification>(
      onNotification: (ScrollNotification notification) {
        if (notification is ScrollStartNotification) {
          if (notification.metrics.extentBefore == 0.0 && !_isRefreshing) {
            setState(() {
              _dragOffset = 0.0;
            });
          }
        } else if (notification is ScrollUpdateNotification) {
          if (notification.metrics.extentBefore == 0.0 && 
              notification.dragDetails != null &&
              !_isRefreshing) {
            setState(() {
              _dragOffset -= notification.dragDetails!.delta.dy;
              _dragOffset = math.max(0.0, _dragOffset);
            });
          }
        } else if (notification is OverscrollNotification) {
          if (notification.metrics.extentBefore == 0.0 && !_isRefreshing) {
            setState(() {
              _dragOffset -= notification.overscroll;
              _dragOffset = math.max(0.0, _dragOffset);
            });
          }
        } else if (notification is ScrollEndNotification) {
          if (_dragOffset > widget.displacement && !_isRefreshing) {
            _handleRefresh();
          } else if (!_isRefreshing) {
            setState(() {
              _dragOffset = 0.0;
            });
          }
        }
        return false;
      },
      child: Stack(
        children: <Widget>[
          widget.child,
          Positioned(
            top: 0.0,
            left: 0.0,
            right: 0.0,
            child: SizedBox(
              height: math.max(
                _dragOffset, _isRefreshing ? _kDragContainerExtent : 0.0),
              child: Center(
                child: SizedBox(
                  height: _kDragContainerExtent * _kDragSizeRatio,
                  width: _kDragContainerExtent * _kDragSizeRatio,
                  child: _isRefreshing || _dragOffset > 0.0
                      ? AnimatedBuilder(
                          animation: _controller,
                          builder: (context, child) {
                            return Transform.rotate(
                              angle: _controller.value * 2 * math.pi,
                              child: child,
                            );
                          },
                          child: CircularProgressIndicator(
                            strokeWidth: 2.0,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              widget.color ?? Theme.of(context).primaryColor,
                            ),
                          ),
                        )
                      : Container(),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
