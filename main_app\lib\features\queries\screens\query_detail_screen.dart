import 'package:flutter/material.dart';
import '../services/query_service.dart';
import '../models/message_model.dart';
import '../models/query_model.dart';
import '../widgets/message_bubble.dart';

class QueryDetailScreen extends StatefulWidget {
  final String queryId;

  const QueryDetailScreen({
    Key? key,
    required this.queryId,
  }) : super(key: key);

  @override
  State<QueryDetailScreen> createState() => _QueryDetailScreenState();
}

class _QueryDetailScreenState extends State<QueryDetailScreen> {
  final QueryService _queryService = QueryService();
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  Query? _query;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadQuery();
    
    // Mark messages as read when user enters the chat
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _queryService.markMessagesAsRead(widget.queryId, isAdmin: false);
    });
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _loadQuery() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final query = await _queryService.getQueryById(widget.queryId);
      setState(() {
        _query = query;
        _isLoading = false;
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading query: $e')),
        );
      }
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _scrollToBottom() {
    if (_scrollController.hasClients) {
      _scrollController.animateTo(
        _scrollController.position.maxScrollExtent,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    }
  }

  Future<void> _sendMessage() async {
    final message = _messageController.text.trim();
    if (message.isEmpty) return;

    _messageController.clear();

    try {
      await _queryService.sendMessage(queryId: widget.queryId, text: message);
      _scrollToBottom();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error sending message: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: _isLoading || _query == null
            ? const Text('Loading...')
            : Text(_query!.topic),
        actions: [
          if (_query != null && _query!.status == 'open')
            IconButton(
              icon: const Icon(Icons.check_circle_outline),
              tooltip: 'Close Query',
              onPressed: () async {
                await _queryService.closeQuery(widget.queryId);
                if (mounted && context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Query closed')),
                  );
                }
              },
            ),
          if (_query != null && _query!.status == 'closed')
            IconButton(
              icon: const Icon(Icons.refresh),
              tooltip: 'Reopen Query',
              onPressed: () async {
                await _queryService.reopenQuery(widget.queryId);
                if (mounted && context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Query reopened')),
                  );
                }
              },
            ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                // Status bar
                Container(
                  padding: const EdgeInsets.symmetric(
                    vertical: 8.0,
                    horizontal: 16.0,
                  ),
                  color: _query?.status == 'open'
                      ? Colors.green.withOpacity(0.1)
                      : Colors.red.withOpacity(0.1),
                  child: Row(
                    children: [
                      Icon(
                        _query?.status == 'open'
                            ? Icons.check_circle
                            : Icons.cancel,
                        color: _query?.status == 'open'
                            ? Colors.green
                            : Colors.red,
                        size: 18,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Status: ${_query?.status.toUpperCase() ?? ''}',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: _query?.status == 'open'
                              ? Colors.green
                              : Colors.red,
                        ),
                      ),
                    ],
                  ),
                ),
                
                // Messages list
                Expanded(
                  child: StreamBuilder<List<Message>>(
                    stream: _queryService.getQueryMessages(widget.queryId),
                    builder: (context, snapshot) {
                      if (snapshot.connectionState == ConnectionState.waiting && !snapshot.hasData) {
                        return const Center(child: CircularProgressIndicator());
                      }

                      if (snapshot.hasError) {
                        return Center(child: Text('Error: ${snapshot.error}'));
                      }

                      final messages = snapshot.data ?? [];
                      if (messages.isEmpty) {
                        return const Center(child: Text('No messages yet'));
                      }

                      // Scroll to bottom when new messages arrive
                      WidgetsBinding.instance.addPostFrameCallback((_) {
                        _scrollToBottom();
                      });

                      return ListView.builder(
                        controller: _scrollController,
                        padding: const EdgeInsets.all(8.0),
                        itemCount: messages.length,
                        itemBuilder: (context, index) {
                          final message = messages[index];
                          final isUserMessage = !message.isAdmin;
                          
                          return MessageBubble(
                            message: message,
                            isUserMessage: isUserMessage,
                          );
                        },
                      );
                    },
                  ),
                ),
                
                // Input area (only if query is open)
                if (_query?.status == 'open')
                  Container(
                    padding: const EdgeInsets.all(8.0),
                    decoration: BoxDecoration(
                      color: Theme.of(context).cardColor,
                      boxShadow: [
                        BoxShadow(
                          offset: const Offset(0, -1),
                          blurRadius: 3,
                          color: Colors.black.withOpacity(0.1),
                        ),
                      ],
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          child: TextField(
                            controller: _messageController,
                            textCapitalization: TextCapitalization.sentences,
                            decoration: const InputDecoration(
                              hintText: 'Type a message...',
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.all(Radius.circular(24)),
                              ),
                              contentPadding: EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 8,
                              ),
                            ),
                            maxLines: null,
                            textInputAction: TextInputAction.send,
                            onSubmitted: (_) => _sendMessage(),
                          ),
                        ),
                        const SizedBox(width: 8),
                        FloatingActionButton(
                          onPressed: _sendMessage,
                          mini: true,
                          child: const Icon(Icons.send),
                        ),
                      ],
                    ),
                  ),
                
                // Message if query is closed
                if (_query?.status == 'closed')
                  Container(
                    padding: const EdgeInsets.all(16.0),
                    color: Colors.grey[200],
                    child: Row(
                      children: [
                        const Icon(Icons.info, color: Colors.grey),
                        const SizedBox(width: 8),
                        const Expanded(
                          child: Text(
                            'This query is closed. Reopen it to send more messages.',
                            style: TextStyle(color: Colors.grey),
                          ),
                        ),
                        TextButton(
                          onPressed: () async {
                            await _queryService.reopenQuery(widget.queryId);
                          },
                          child: const Text('REOPEN'),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
    );
  }
} 