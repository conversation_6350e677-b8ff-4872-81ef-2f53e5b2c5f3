rules_version = '2';

service firebase.storage {
  match /b/{bucket}/o {
    
    // Helper functions for better security
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isOwner(uid) {
      return request.auth.uid == uid;
    }
    
    function isValidImage() {
      return request.resource.contentType.matches('image/.*');
    }
    
    function isValidFile() {
      return request.resource.contentType.matches('image/.*') ||
             request.resource.contentType.matches('video/.*') ||
             request.resource.contentType.matches('audio/.*') ||
             request.resource.contentType.matches('application/pdf');
    }
    
    function isReasonableSize() {
      // 10MB limit for most files
      return request.resource.size < 10 * 1024 * 1024;
    }
    
    function isLargeFileAllowed() {
      // 50MB limit for videos
      return request.resource.size < 50 * 1024 * 1024;
    }
    
    // User-specific directories - users can only access their own files
    match /users/{uid}/{allPaths=**} {
      allow read, write: if isAuthenticated() && isOwner(uid) && isValidFile() && isReasonableSize();
    }
    
    // Temporary uploads for authenticated users
    match /temp_uploads/{uid}/{allPaths=**} {
      allow read, write: if isAuthenticated() && isOwner(uid) && isValidFile() && isReasonableSize();
      // Auto-delete after 24 hours (handled by Cloud Functions)
    }
    
    // Driver registration documents
    match /driver_documents/{uid}/{allPaths=**} {
      allow read, write: if isAuthenticated() && isOwner(uid) && isValidFile() && isReasonableSize();
    }
    
    // Query attachments - authenticated users only
    match /queries/{queryId}/messages/{messageId}/{allPaths=**} {
      allow read: if isAuthenticated();
      allow write: if isAuthenticated() && isValidFile() && isReasonableSize();
    }
    
    // Forum attachments - authenticated users only
    match /forum/{forumId}/{allPaths=**} {
      allow read: if isAuthenticated();
      allow write: if isAuthenticated() && isValidFile() && isReasonableSize();
    }
    
    // Profile pictures - optimized for images only
    match /profile_pictures/{uid}/{allPaths=**} {
      allow read: if true; // Profile pictures can be public
      allow write: if isAuthenticated() && isOwner(uid) && isValidImage() && 
                      request.resource.size < 5 * 1024 * 1024; // 5MB limit for profile pics
    }
    
    // Video content with larger size limits
    match /videos/{uid}/{allPaths=**} {
      allow read: if isAuthenticated();
      allow write: if isAuthenticated() && isOwner(uid) && 
                     request.resource.contentType.matches('video/.*') && isLargeFileAllowed();
    }
    
    // Public read-only content (for app assets, etc.)
    match /public/{allPaths=**} {
      allow read: if true;
      allow write: if false; // Only admin can write to public (via backend)
    }
    
    // Deny all other access by default
    match /{allPaths=**} {
      allow read, write: if false;
    }
  }
}