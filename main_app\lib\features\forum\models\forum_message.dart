import 'package:cloud_firestore/cloud_firestore.dart';

class ForumMessage {
  final String id;
  final String senderId;
  final String senderName;
  final String senderAvatar;
  final String text;
  final DateTime timestamp;
  final List<Reaction> reactions;
  final String? replyToId;
  final String? replyToText;
  final String? replyToSenderName;
  final List<String>? attachments;
  final String? voiceNote;
  final int? voiceNoteDuration;
  final bool isEdited;
  final bool isPending;
  final bool isUploading;

  ForumMessage({
    required this.id,
    required this.senderId,
    required this.senderName,
    required this.senderAvatar,
    required this.text,
    required this.timestamp,
    required this.reactions,
    this.replyToId,
    this.replyToText,
    this.replyToSenderName,
    this.attachments,
    this.voiceNote,
    this.voiceNoteDuration,
    this.isEdited = false,
    this.isPending = false,
    this.isUploading = false,
  });

  factory ForumMessage.fromFirestore(DocumentSnapshot doc) {
    try {
      final data = doc.data() as Map<String, dynamic>;

      // Parse reactions
      List<Reaction> reactionList = [];
      if (data['reactions'] != null) {
        try {
          final reactionsMap = data['reactions'] as Map<String, dynamic>;
          reactionsMap.forEach((emoji, users) {
            reactionList.add(Reaction(
              emoji: emoji,
              userIds: List<String>.from(users),
            ));
          });
        } catch (e) {
          // If reactions parsing fails, just use empty list
          reactionList = [];
        }
      }

      // Parse attachments if they exist - handle both attachments and imageUrls
      List<String>? attachments;
      if (data['attachments'] != null) {
        attachments = List<String>.from(data['attachments']);
      } else if (data['imageUrls'] != null) {
        // Admin panel uses imageUrls instead of attachments for images
        attachments = List<String>.from(data['imageUrls']);
      }

      // Parse voice note - handle both string URL and object format
      String? voiceNoteUrl;
      int? voiceNoteDuration;

      if (data['voiceNote'] != null) {
        if (data['voiceNote'] is String) {
          // Simple string URL format (from main app)
          voiceNoteUrl = data['voiceNote'] as String;
          voiceNoteDuration = data['voiceNoteDuration'] as int?;
        } else if (data['voiceNote'] is Map<String, dynamic>) {
          // Object format (from admin panel)
          final voiceNoteData = data['voiceNote'] as Map<String, dynamic>;
          voiceNoteUrl = voiceNoteData['url'] as String?;
          voiceNoteDuration = voiceNoteData['duration'] as int?;
        }
      }

      // Parse reply data - handle both separate fields and object format
      String? replyToId;
      String? replyToText;
      String? replyToSenderName;

      if (data['replyTo'] != null && data['replyTo'] is Map<String, dynamic>) {
        // Object format (from admin panel)
        final replyData = data['replyTo'] as Map<String, dynamic>;
        replyToId = replyData['id'] as String?;
        replyToText = replyData['text'] as String?;
        replyToSenderName = replyData['senderName'] as String?;
      } else {
        // Separate fields format (from main app)
        replyToId = data['replyToId'] as String?;
        replyToText = data['replyToText'] as String?;
        replyToSenderName = data['replyToSenderName'] as String?;
      }

      return ForumMessage(
        id: doc.id,
        senderId: data['senderId'] ?? '',
        senderName: data['senderName'] ?? 'Unknown User',
        senderAvatar: data['senderAvatar'] ?? '',
        text: data['text'] ?? '',
        timestamp: (data['timestamp'] as Timestamp?)?.toDate() ?? DateTime.now(),
        reactions: reactionList,
        replyToId: replyToId,
        replyToText: replyToText,
        replyToSenderName: replyToSenderName,
        attachments: attachments,
        voiceNote: voiceNoteUrl,
        voiceNoteDuration: voiceNoteDuration,
        isEdited: data['isEdited'] ?? false,
        isPending: false, // Firestore messages are never pending
        isUploading: false, // Firestore messages are never uploading
      );
    } catch (e) {
      // If parsing fails completely, return a basic message
      return ForumMessage(
        id: doc.id,
        senderId: 'unknown',
        senderName: 'Unknown User',
        senderAvatar: '',
        text: 'Error loading message',
        timestamp: DateTime.now(),
        reactions: [],
        isEdited: false,
        isPending: false,
        isUploading: false,
      );
    }
  }

  // Factory for creating pending messages
  factory ForumMessage.pending({
    required String senderId,
    required String senderName,
    required String senderAvatar,
    String text = '',
    String? replyToId,
    String? replyToText,
    String? replyToSenderName,
    List<String>? attachments,
    String? voiceNote,
    int? voiceNoteDuration,
    bool isUploading = false,
  }) {
    return ForumMessage(
      id: 'pending_${DateTime.now().millisecondsSinceEpoch}',
      senderId: senderId,
      senderName: senderName,
      senderAvatar: senderAvatar,
      text: text,
      timestamp: DateTime.now(),
      reactions: [],
      replyToId: replyToId,
      replyToText: replyToText,
      replyToSenderName: replyToSenderName,
      attachments: attachments,
      voiceNote: voiceNote,
      voiceNoteDuration: voiceNoteDuration,
      isEdited: false,
      isPending: true,
      isUploading: isUploading,
    );
  }

  Map<String, dynamic> toFirestore() {
    // Convert reactions to the format for Firestore
    Map<String, List<String>> reactionsMap = {};
    for (var reaction in reactions) {
      reactionsMap[reaction.emoji] = reaction.userIds;
    }

    return {
      'senderId': senderId,
      'senderName': senderName,
      'senderAvatar': senderAvatar,
      'text': text,
      'timestamp': Timestamp.fromDate(timestamp),
      'reactions': reactionsMap,
      'replyToId': replyToId,
      'replyToText': replyToText,
      'replyToSenderName': replyToSenderName,
      'attachments': attachments,
      'voiceNote': voiceNote,
      'voiceNoteDuration': voiceNoteDuration,
      'isEdited': isEdited,
    };
  }
}

class Reaction {
  final String emoji;
  final List<String> userIds;

  Reaction({
    required this.emoji,
    required this.userIds,
  });
}